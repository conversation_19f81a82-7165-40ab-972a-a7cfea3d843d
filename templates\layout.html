<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ title }} - نظام التدريب والتأهيل</title>
    <!-- الخطوط الأنيقة الموحدة للنظام -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Bootstrap RTL CSS - محلي -->
    <link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
    <!-- Font <PERSON>wesome - محلي -->
    <link rel="stylesheet" href="{{ url_for('static', filename='libs/fontawesome/all.min.css') }}">



    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- نظام التصميم الموحد الجديد -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-design.css') }}">

    <!-- التصميم الموحد النظيف -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/clean-design.css') }}">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-style.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container-fluid px-4">
                <!-- العلامة التجارية -->
                <a class="navbar-brand" href="{{ url_for('home') }}">
                    <i class="fas fa-graduation-cap"></i>نظام التدريب والتأهيل
                </a>

                <!-- زر القائمة للموبايل -->
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- القوائم -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('home') }}">الرئيسية</a>
                        </li>
                        {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
                        </li>
                        {% endif %}
                    </ul>

                    <!-- التاريخ والوقت في الجانب الأيمن -->
                    <div class="d-flex align-items-center">
                        <div class="navbar-datetime d-none d-lg-flex flex-column align-items-center">
                            <div class="hijri-date">التاريخ الهجري: <span id="hijri-date">17 ذو الحجة 1446هـ</span></div>
                            <div class="current-time">الوقت: <span id="current-time">12:00:00 م</span></div>
                        </div>

                        <!-- منطقة المستخدم المحسنة -->
                        <ul class="navbar-nav">
                            {% if current_user.is_authenticated %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user"></i>
                                    <span>{{ current_user.username }}</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item" href="#">
                                            <i class="fas fa-user-circle"></i>
                                            الملف الشخصي
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="{{ url_for('logout') }}">
                                            <i class="fas fa-sign-out-alt"></i>
                                            تسجيل الخروج
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('login') }}">
                                    <i class="fas fa-sign-in-alt"></i>تسجيل الدخول
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" style="border-radius: 15px; backdrop-filter: blur(10px); box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">جميع الحقوق محفوظة &copy; {{ now.year }} نظام التدريب والتأهيل</p>
        </div>
    </footer>

    <!-- jQuery - محلي -->
    <script src="{{ url_for('static', filename='libs/jquery/jquery-3.6.0.min.js') }}"></script>
    <!-- Bootstrap JS Bundle with Popper - محلي -->
    <script src="{{ url_for('static', filename='libs/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Unified Interactions JS -->
    <script src="{{ url_for('static', filename='js/unified-interactions.js') }}"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>



    <!-- التاريخ والوقت -->
    <script>
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            });

            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('hijri-date');

            if (timeElement) timeElement.textContent = timeString;
            if (dateElement) dateElement.textContent = '17 ذو الحجة 1446هـ';
        }

        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
        });
    </script>



    {% block scripts %}{% endblock %}
</body>
</html>
