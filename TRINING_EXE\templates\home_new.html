{% extends "layout.html" %}

{% block styles %}
<style>
    .welcome-section {
        text-align: right;
        margin-bottom: 20px;
        padding: 25px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        color: white;
        position: relative;
        overflow: hidden;
    }

    .welcome-section::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(180deg); }
    }

    .welcome-title {
        font-size: 2.5rem;
        font-weight: bold;
        color: white;
        margin-bottom: 15px;
        text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.4);
        position: relative;
        z-index: 2;
        background: linear-gradient(45deg, #ffffff, #f0f8ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .welcome-subtitle {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.95);
        margin-bottom: 20px;
        position: relative;
        z-index: 2;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    }

    .matrix-container {
        position: relative;
        width: 100%;
        height: 500px;
        margin: 10px auto;
        background:
            linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%),
            radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.08) 0%, transparent 70%);
        border-radius: 25px;
        padding: 20px;
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.9),
            0 0 0 1px rgba(99, 102, 241, 0.2);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .matrix-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
        /* animation: matrixShine 4s ease-in-out infinite; */
        pointer-events: none;
        z-index: 1;
    }

    @keyframes matrixShine {
        0%, 100% {
            transform: translateX(-100%) skewX(-15deg);
        }
        50% {
            transform: translateX(100%) skewX(-15deg);
        }
    }

    .matrix-node {
        position: absolute;
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 18px;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        font-weight: bold;
        box-shadow:
            0 8px 25px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        cursor: pointer;
        z-index: 10;
        text-decoration: none;
        color: #333;
        font-size: 1rem;
        backdrop-filter: blur(5px);
        padding: 8px;
        line-height: 1.2;
        transition: filter 0.2s ease, border-color 0.2s ease;
    }

    a.matrix-node {
        cursor: pointer;
    }

    .matrix-node:hover {
        text-decoration: none;
        border-color: rgba(255, 255, 255, 0.6);
        filter: brightness(1.05);
    }

    .matrix-center {
        width: 200px;
        height: 120px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #6366F1 0%, #4F46E5 100%);
        color: white;
        z-index: 20;
        border: 3px solid rgba(255, 255, 255, 0.3);
        font-size: 1.2rem;
        font-weight: bold;
        box-shadow:
            0 20px 50px rgba(99, 102, 241, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.5);
        line-height: 1.3;
        padding: 10px;
        transition: filter 0.2s ease, border-color 0.2s ease;
    }

    .matrix-center:hover {
        border-color: rgba(255, 255, 255, 0.5);
        filter: brightness(1.05);
    }

    .matrix-materials {
        width: 120px;
        height: 80px;
        top: 25%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #10B981 0%, #059669 100%);
        color: white;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .matrix-levels {
        width: 120px;
        height: 80px;
        top: 25%;
        left: 70%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
        color: white;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .matrix-participant-types {
        width: 120px;
        height: 80px;
        top: 25%;
        left: 30%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
        color: white;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .matrix-payments {
        width: 120px;
        height: 80px;
        top: 25%;
        left: 90%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
        color: white;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .matrix-graduates {
        width: 120px;
        height: 80px;
        top: 25%;
        left: 10%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);
        color: white;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .matrix-enrollees {
        width: 120px;
        height: 80px;
        top: 50%;
        left: 80%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
        color: white;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .matrix-trainers {
        width: 120px;
        height: 80px;
        top: 50%;
        left: 20%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #1E40AF 0%, #1E3A8A 100%);
        color: white;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .matrix-financial {
        width: 120px;
        height: 80px;
        top: 75%;
        left: 70%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
        color: white;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .matrix-centers {
        width: 120px;
        height: 80px;
        top: 75%;
        left: 30%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
        color: white;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .stats-card {
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        box-shadow:
            0 10px 30px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        padding: 25px;
        margin-top: 30px;
        text-align: center;
        border: 1px solid rgba(102, 126, 234, 0.1);
        backdrop-filter: blur(5px);
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        /* animation: shimmer 3s infinite; */
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .stats-card:hover {
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow:
            0 15px 40px rgba(102, 126, 234, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    .stats-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        z-index: 2;
        filter: drop-shadow(2px 2px 4px rgba(102, 126, 234, 0.3));
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        z-index: 2;
    }

    .stats-label {
        font-size: 0.95rem;
        color: #6c757d;
        margin-bottom: 0;
        position: relative;
        z-index: 2;
        font-weight: 500;
    }

    #matrix-svg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 5;
        pointer-events: none;
    }

    .svg-line {
        stroke: #6366F1;
        stroke-width: 4;
        stroke-dasharray: 10, 5;
        stroke-linecap: round;
        opacity: 0.8;
        filter: drop-shadow(0 3px 6px rgba(99, 102, 241, 0.4));
        animation: lineFlow 2.5s ease-in-out infinite;
    }

    @keyframes lineFlow {
        0%, 100% {
            stroke-dashoffset: 0;
            opacity: 0.6;
        }
        50% {
            stroke-dashoffset: 15;
            opacity: 1;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="welcome-section">
                <h1 class="welcome-title">نظام التدريب والتأهيل</h1>
                <p class="welcome-subtitle">منصة متكاملة لإدارة الدورات التدريبية والتأهيلية</p>
            </div>
        </div>
    </div>

    <div class="text-center mb-3">
        <h3 style="
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(102, 126, 234, 0.2);
            margin-bottom: 10px;
        ">مصفوفة إدارة النظام</h3>
        <p style="
            color: #6c757d;
            font-size: 1.1rem;
            font-weight: 500;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        ">اختر القسم المطلوب للوصول إلى الوظائف المختلفة</p>
    </div>

    <div class="matrix-container">
        <!-- SVG للخطوط -->
        <svg id="matrix-svg">
            <!-- سيتم إنشاء الخطوط بواسطة JavaScript -->
        </svg>

        <!-- العقد (المربعات) -->
        <div class="matrix-node matrix-center">مصفوفة التدريب والتأهيل</div>

        <a href="{{ url_for('courses') }}" class="matrix-node matrix-materials" style="color: white !important;">المواد</a>
        <a href="{{ url_for('course_levels') }}" class="matrix-node matrix-levels" style="color: white !important;">المستويات</a>
        <a href="{{ url_for('participant_types') }}" class="matrix-node matrix-participant-types" style="color: white !important;">أنواع المشاركين</a>
        <a href="{{ url_for('reports') }}" class="matrix-node matrix-payments" style="color: white !important;">الدفعات</a>
        <a href="{{ url_for('graduates') }}" class="matrix-node matrix-graduates" style="color: white !important;">الخريجين</a>
        <a href="{{ url_for('personal_data_list') }}" class="matrix-node matrix-enrollees" style="color: white !important;">الملتحقين</a>
        <a href="{{ url_for('users') }}" class="matrix-node matrix-trainers" style="color: white !important;">المدربين</a>
        <a href="{{ url_for('reports') }}" class="matrix-node matrix-financial" style="color: white !important;">المالي والإحصاءات</a>
        <a href="{{ url_for('training_centers') }}" class="matrix-node matrix-centers" style="color: white !important;">المراكز</a>
    </div>

    <div class="row mt-4">
        <div class="col-md-4 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-graduation-cap text-primary"></i>
                </div>
                <div class="stats-number">85</div>
                <div class="stats-label">إجمالي الشهادات الممنوحة</div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-clock text-success"></i>
                </div>
                <div class="stats-number">320</div>
                <div class="stats-label">إجمالي ساعات التدريب</div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-users text-info"></i>
                </div>
                <div class="stats-number">150</div>
                <div class="stats-label">إجمالي المتدربين</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // الحصول على عناصر DOM
        const svg = document.getElementById('matrix-svg');
        const center = document.querySelector('.matrix-center');
        const materials = document.querySelector('.matrix-materials');
        const levels = document.querySelector('.matrix-levels');
        const participantTypes = document.querySelector('.matrix-participant-types');
        const payments = document.querySelector('.matrix-payments');
        const graduates = document.querySelector('.matrix-graduates');
        const enrollees = document.querySelector('.matrix-enrollees');
        const trainers = document.querySelector('.matrix-trainers');
        const financial = document.querySelector('.matrix-financial');
        const centers = document.querySelector('.matrix-centers');

        // تأكد من أن SVG يتم تحديث حجمه بشكل صحيح
        function updateSVGSize() {
            const container = document.querySelector('.matrix-container');
            svg.setAttribute('width', container.offsetWidth);
            svg.setAttribute('height', container.offsetHeight);
        }

        // تحديث حجم SVG عند تحميل الصفحة وعند تغيير حجم النافذة
        updateSVGSize();
        window.addEventListener('resize', updateSVGSize);

        // وظيفة لإنشاء خط بين عنصرين
        function createLine(from, to) {
            // الحصول على إحداثيات العناصر
            const fromRect = from.getBoundingClientRect();
            const toRect = to.getBoundingClientRect();
            const svgRect = svg.getBoundingClientRect();

            // حساب نقاط البداية والنهاية
            const fromX = (fromRect.left + fromRect.right) / 2 - svgRect.left;
            const fromY = (fromRect.top + fromRect.bottom) / 2 - svgRect.top;
            const toX = (toRect.left + toRect.right) / 2 - svgRect.left;
            const toY = (toRect.top + toRect.bottom) / 2 - svgRect.top;

            // إنشاء عنصر الخط
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', fromX);
            line.setAttribute('y1', fromY);
            line.setAttribute('x2', toX);
            line.setAttribute('y2', toY);
            line.setAttribute('class', 'svg-line');

            // إضافة الخط إلى SVG
            svg.appendChild(line);
        }

        // إنشاء الخطوط
        setTimeout(function() {
            // إنشاء الخطوط بعد تأخير قصير للتأكد من أن كل العناصر تم تحميلها بشكل صحيح
            createLine(center, materials);
            createLine(materials, levels);
            createLine(materials, participantTypes);
            createLine(materials, payments);
            createLine(materials, graduates);
            createLine(center, enrollees);
            createLine(center, trainers);
            createLine(center, financial);
            createLine(center, centers);
        }, 100);
    });
</script>
{% endblock %}
