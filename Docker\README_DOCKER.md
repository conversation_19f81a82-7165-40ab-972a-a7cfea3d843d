# 🐳 نظام التدريب والتأهيل - Docker

## 📋 الملفات المتاحة

### ملفات التشغيل السريع
- `docker_start.bat` - تشغيل النظام على المنفذ 5000
- `docker_start_5001.bat` - تشغيل النظام على المنفذ 5001  
- `docker_start_8080.bat` - تشغيل النظام على المنفذ 8080
- `docker_custom_port.bat` - تشغيل النظام على منفذ مخصص
- `docker_stop.bat` - إيقاف جميع نسخ النظام

### ملفات Docker
- `Dockerfile` - ملف بناء الصورة
- `docker-compose.yml` - تشغيل على المنفذ 5000
- `docker-compose-5001.yml` - تشغيل على المنفذ 5001
- `docker-compose-8080.yml` - تشغيل على المنفذ 8080

### ملفات التوثيق
- `DOCKER_README.md` - دليل شامل لاستخدام Docker
- `تعليمات_Docker.txt` - تعليمات باللغة العربية

## 🚀 التشغيل السريع

### الطريقة الأولى: ملفات التشغيل السريع
1. تأكد من تشغيل Docker Desktop
2. انقر نقراً مزدوجاً على أحد ملفات التشغيل:
   - `docker_start.bat` للمنفذ 5000
   - `docker_start_5001.bat` للمنفذ 5001
   - `docker_start_8080.bat` للمنفذ 8080

### الطريقة الثانية: الأوامر اليدوية
```bash
# بناء الصورة
docker build -t training_system .

# تشغيل على المنفذ 5000
docker run -d -p 5000:5000 --name training_system training_system

# تشغيل على المنفذ 5001
docker run -d -p 5001:5000 --name training_system_5001 training_system

# تشغيل على المنفذ 8080
docker run -d -p 8080:5000 --name training_system_8080 training_system
```

### الطريقة الثالثة: Docker Compose
```bash
# المنفذ 5000
docker-compose up -d

# المنفذ 5001
docker-compose -f docker-compose-5001.yml up -d

# المنفذ 8080
docker-compose -f docker-compose-8080.yml up -d
```

## 🔑 بيانات تسجيل الدخول
- **الإيميل**: <EMAIL>
- **كلمة المرور**: admin123

## 🌐 الوصول إلى النظام
- المنفذ 5000: http://localhost:5000
- المنفذ 5001: http://localhost:5001
- المنفذ 8080: http://localhost:8080

## 📋 أوامر مفيدة

### عرض الحاويات المشغلة
```bash
docker ps
```

### عرض السجلات
```bash
docker logs training_system
```

### إيقاف النظام
```bash
# إيقاف حاوية واحدة
docker stop training_system

# إيقاف جميع الحاويات (استخدم الملف)
docker_stop.bat
```

### تشغيل عدة نسخ
```bash
docker run -d -p 3000:5000 --name training_system_3000 training_system
docker run -d -p 4000:5000 --name training_system_4000 training_system
docker run -d -p 9000:5000 --name training_system_9000 training_system
```

## 💾 إدارة البيانات

### النسخ الاحتياطي
```bash
docker cp training_system:/app/training_system.db ./backup_$(date +%Y%m%d).db
```

### الاستعادة
```bash
docker cp ./backup.db training_system:/app/training_system.db
docker restart training_system
```

## 🔧 استكشاف الأخطاء

### Docker غير مشغل
1. تشغيل Docker Desktop
2. انتظار اكتمال التشغيل
3. إعادة المحاولة

### المنفذ مستخدم
1. تغيير رقم المنفذ
2. أو إيقاف البرنامج المستخدم للمنفذ

### فشل البناء
1. التحقق من اتصال الإنترنت
2. إعادة البناء: `docker build -t training_system . --no-cache`

## ✨ المميزات

✅ **سهولة النشر**: نسخة واحدة تعمل على أي نظام  
✅ **عزل كامل**: لا يؤثر على النظام المضيف  
✅ **منافذ متعددة**: إمكانية تشغيل عدة نسخ  
✅ **حفظ البيانات**: البيانات محفوظة خارج الحاوية  
✅ **تحديثات سهلة**: إعادة بناء الصورة فقط  
✅ **أمان عالي**: بيئة معزولة وآمنة  

## ✅ الإصلاحات الحديثة (آخر تحديث)

### 🔧 المشاكل التي تم حلها:
- **إصلاح مشكلة القائمة المنسدلة**: تم إضافة z-index مناسب لضمان ظهور القائمة المنسدلة فوق العناصر الأخرى
- **إصلاح مشكلة إعادة التوجيه**: تم إضافة ProxyFix لحل مشكلة اختلاف المنافذ في Docker
- **إصلاح مشكلة تسجيل الخروج**: زر تسجيل الخروج يظهر الآن بشكل صحيح في القائمة المنسدلة
- **تحسين الأمان**: إضافة التحقق من صحة روابط إعادة التوجيه لمنع الهجمات الخارجية
- **إصلاح مشكلة الاستيراد**: تم حل مشكلة werkzeug.urls وإضافة ملف backup_utils.py

### 🚀 التحسينات التقنية:
- **ProxyFix Middleware**: حل مشكلة المنافذ في بيئة Docker
- **CSS محسن**: تحسين z-index للقوائم المنسدلة (z-index: 9999)
- **أمان محسن**: تحقق من صحة روابط إعادة التوجيه باستخدام urlparse
- **استقرار أفضل**: حل مشاكل الاستيراد والتبعيات

### ❌➡️✅ قبل وبعد الإصلاح:
- ❌ القائمة المنسدلة مخفية خلف العناصر → ✅ تظهر فوق جميع العناصر
- ❌ زر تسجيل الخروج غير مرئي → ✅ يظهر بوضوح في القائمة
- ❌ إعادة توجيه خاطئة بين المنافذ → ✅ إعادة توجيه صحيحة
- ❌ خطأ ImportError: werkzeug.urls → ✅ يعمل بدون أخطاء

## 📞 الدعم الفني

للحصول على المساعدة:
1. راجع ملف `تعليمات_Docker.txt`
2. تحقق من السجلات: `docker logs training_system`
3. أعد تشغيل Docker Desktop
4. استخدم `docker_stop.bat` ثم أعد التشغيل

---
**إصدار النظام**: 2025.1 - Docker Edition  
**تاريخ الإصدار**: يونيو 2025
