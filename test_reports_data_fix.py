#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بيانات التقارير بعد الإصلاح
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Course, CourseParticipant, PersonData, TrainingCenter, Agency
from datetime import datetime, timedelta
import json

def test_database_data():
    """اختبار البيانات في قاعدة البيانات"""
    
    with app.app_context():
        print("🔍 اختبار بيانات قاعدة البيانات...")
        
        # 1. فحص الدورات
        all_courses = Course.query.all()
        print(f"\n📚 إجمالي الدورات: {len(all_courses)}")
        
        if all_courses:
            print("📋 عينة من الدورات:")
            for i, course in enumerate(all_courses[:5]):
                participants_count = CourseParticipant.query.filter_by(course_id=course.id).count()
                graduates_count = CourseParticipant.query.filter(
                    CourseParticipant.course_id == course.id,
                    CourseParticipant.status.in_(['مكتمل', 'completed', 'مكمل'])
                ).count()
                print(f"   {i+1}. {course.title}")
                print(f"      - رقم الدورة: {course.course_number}")
                print(f"      - تاريخ البدء: {course.start_date}")
                print(f"      - المستوى: {course.level}")
                print(f"      - المشاركين: {participants_count}")
                print(f"      - الخريجين: {graduates_count}")
                print(f"      - المركز: {course.center.name if course.center else 'غير محدد'}")
                print()
        
        # 2. فحص المشاركين
        all_participants = CourseParticipant.query.all()
        print(f"👥 إجمالي المشاركين: {len(all_participants)}")
        
        # 3. فحص الخريجين
        graduates = CourseParticipant.query.filter(
            CourseParticipant.status.in_(['مكتمل', 'completed', 'مكمل'])
        ).all()
        print(f"🎓 إجمالي الخريجين: {len(graduates)}")
        
        # 4. فحص المراكز
        centers = TrainingCenter.query.all()
        print(f"🏢 إجمالي المراكز: {len(centers)}")
        
        # 5. فحص الجهات
        agencies = Agency.query.all()
        print(f"🏛️ إجمالي الجهات: {len(agencies)}")
        
        # 6. اختبار فترة زمنية محددة
        print("\n📅 اختبار فترة زمنية (آخر سنة):")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        
        courses_in_period = Course.query.filter(
            Course.start_date >= start_date,
            Course.start_date <= end_date
        ).all()
        
        print(f"📚 دورات في آخر سنة: {len(courses_in_period)}")
        
        if courses_in_period:
            course_ids = [c.id for c in courses_in_period]
            participants_in_period = CourseParticipant.query.filter(
                CourseParticipant.course_id.in_(course_ids)
            ).count()
            graduates_in_period = CourseParticipant.query.filter(
                CourseParticipant.course_id.in_(course_ids),
                CourseParticipant.status.in_(['مكتمل', 'completed', 'مكمل'])
            ).count()
            
            print(f"👥 مشاركين في آخر سنة: {participants_in_period}")
            print(f"🎓 خريجين في آخر سنة: {graduates_in_period}")
        
        # 7. اختبار توزيع المستويات
        print("\n📊 توزيع المستويات:")
        level_stats = {}
        for course in all_courses:
            if course.level:
                level_name = course.level.lower()
                participants_count = CourseParticipant.query.filter_by(course_id=course.id).count()
                
                if any(keyword in level_name for keyword in ['مبتدئ', 'أساسي', 'beginner', '1', 'أول']):
                    level_stats['المستوى الأول'] = level_stats.get('المستوى الأول', 0) + participants_count
                elif any(keyword in level_name for keyword in ['متوسط', 'intermediate', '2', 'ثان']):
                    level_stats['المستوى الثاني'] = level_stats.get('المستوى الثاني', 0) + participants_count
                elif any(keyword in level_name for keyword in ['متقدم', 'advanced', '3', 'ثالث']):
                    level_stats['المستوى الثالث'] = level_stats.get('المستوى الثالث', 0) + participants_count
                else:
                    level_stats['غير محدد'] = level_stats.get('غير محدد', 0) + participants_count
        
        for level, count in level_stats.items():
            print(f"   {level}: {count} مشارك")

def test_report_generation():
    """اختبار توليد التقرير"""
    
    with app.app_context():
        print("\n🔧 اختبار توليد التقرير...")
        
        # محاكاة طلب التقرير
        from datetime import datetime
        
        # تحديد فترة زمنية واسعة
        start_date = '2020-01-01'
        end_date = '2025-12-31'
        
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        
        print(f"📅 الفترة: من {start_date} إلى {end_date}")
        
        # جلب الدورات
        courses = Course.query.filter(
            Course.start_date >= start_date_obj,
            Course.start_date <= end_date_obj
        ).all()
        
        print(f"📚 دورات في الفترة: {len(courses)}")
        
        if courses:
            # حساب الإحصائيات
            course_ids = [course.id for course in courses]
            total_participants = CourseParticipant.query.filter(
                CourseParticipant.course_id.in_(course_ids)
            ).count()
            
            total_graduates = CourseParticipant.query.filter(
                CourseParticipant.course_id.in_(course_ids),
                CourseParticipant.status.in_(['مكتمل', 'completed', 'مكمل'])
            ).count()
            
            print(f"👥 إجمالي المشاركين: {total_participants}")
            print(f"🎓 إجمالي الخريجين: {total_graduates}")
            
            # حساب المراكز
            centers = set()
            for course in courses:
                if course.center and course.center.name:
                    centers.add(course.center.name)
            
            print(f"🏢 عدد المراكز: {len(centers)}")
            
            # توزيع المستويات
            level_data = {'level1': 0, 'level2': 0, 'level3': 0}
            for course in courses:
                course_participants_count = CourseParticipant.query.filter_by(course_id=course.id).count()
                
                if course.level:
                    level_name = course.level.lower()
                    if any(keyword in level_name for keyword in ['مبتدئ', 'أساسي', 'beginner', '1', 'أول']):
                        level_data['level1'] += course_participants_count
                    elif any(keyword in level_name for keyword in ['متوسط', 'intermediate', '2', 'ثان']):
                        level_data['level2'] += course_participants_count
                    elif any(keyword in level_name for keyword in ['متقدم', 'advanced', '3', 'ثالث']):
                        level_data['level3'] += course_participants_count
            
            print(f"📊 توزيع المستويات:")
            print(f"   المستوى الأول: {level_data['level1']}")
            print(f"   المستوى الثاني: {level_data['level2']}")
            print(f"   المستوى الثالث: {level_data['level3']}")
            
            return True
        else:
            print("⚠️ لا توجد دورات في الفترة المحددة!")
            return False

if __name__ == '__main__':
    print("🚀 بدء اختبار بيانات التقارير...")
    
    # اختبار البيانات
    test_database_data()
    
    # اختبار توليد التقرير
    success = test_report_generation()
    
    if success:
        print("\n✅ الاختبار مكتمل بنجاح!")
    else:
        print("\n❌ فشل الاختبار!")
