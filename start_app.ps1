# نظام تحليل الأسماء - ملف التشغيل التلقائي
# Training System - Auto Start Script

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    تشغيل نظام تحليل الأسماء" -ForegroundColor Yellow
Write-Host "    Training System Startup" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# الانتقال لمجلد التطبيق
Set-Location "E:\app\TRINING"

Write-Host "تفعيل البيئة الافتراضية..." -ForegroundColor Green
Write-Host "Activating virtual environment..." -ForegroundColor Green

# تفعيل البيئة الافتراضية
& ".\.venv\Scripts\Activate.ps1"

Write-Host ""
Write-Host "فحص المكتبات المطلوبة..." -ForegroundColor Green
Write-Host "Checking required libraries..." -ForegroundColor Green

# فحص المكتبات الأساسية
try {
    python -c "import flask, flask_sqlalchemy, pandas, openpyxl" 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Missing libraries"
    }
    Write-Host "✅ جميع المكتبات متوفرة" -ForegroundColor Green
    Write-Host "✅ All libraries available" -ForegroundColor Green
}
catch {
    Write-Host "⚠️ تثبيت المكتبات المفقودة..." -ForegroundColor Yellow
    Write-Host "⚠️ Installing missing libraries..." -ForegroundColor Yellow
    pip install -r requirements.txt
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    بدء تشغيل التطبيق..." -ForegroundColor Yellow
Write-Host "    Starting Application..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🌐 يمكنك الوصول للتطبيق على:" -ForegroundColor Green
Write-Host "🌐 Application available at:" -ForegroundColor Green
Write-Host "   http://localhost:5000" -ForegroundColor Cyan
Write-Host ""
Write-Host "⏹️ لإيقاف التطبيق اضغط Ctrl+C" -ForegroundColor Yellow
Write-Host "⏹️ To stop the application press Ctrl+C" -ForegroundColor Yellow
Write-Host ""

# تشغيل التطبيق
python app.py

Write-Host ""
Write-Host "تم إيقاف التطبيق" -ForegroundColor Red
Write-Host "Application stopped" -ForegroundColor Red
Read-Host "اضغط Enter للخروج / Press Enter to exit"
