@echo off
title Training System - Docker - Start Here

echo.
echo ========================================
echo    Training System - Docker
echo ========================================
echo.
echo Welcome to Training System
echo Path: E:\app\TRINING\Docker
echo.

echo 🔍 التحقق من Docker...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker غير مشغل
    echo.
    echo 📋 يرجى اتباع الخطوات التالية:
    echo    1. تشغيل Docker Desktop
    echo    2. انتظار اكتمال التشغيل
    echo    3. إعادة تشغيل هذا الملف
    echo.
    pause
    exit /b 1
)

echo ✅ Docker متاح ومشغل
echo.

echo 📋 اختر طريقة التشغيل:
echo.
echo    1. المنفذ 5000 (افتراضي)
echo    2. المنفذ 5001
echo    3. المنفذ 8080
echo    4. منفذ مخصص
echo    5. عرض التعليمات
echo    6. إيقاف جميع النسخ
echo.

set /p choice="أدخل اختيارك (1-6): "

if "%choice%"=="1" (
    echo.
    echo 🚀 تشغيل النظام على المنفذ 5000...
    call docker_start.bat
) else if "%choice%"=="2" (
    echo.
    echo 🚀 تشغيل النظام على المنفذ 5001...
    call docker_start_5001.bat
) else if "%choice%"=="3" (
    echo.
    echo 🚀 تشغيل النظام على المنفذ 8080...
    call docker_start_8080.bat
) else if "%choice%"=="4" (
    echo.
    echo 🚀 تشغيل النظام على منفذ مخصص...
    call docker_custom_port.bat
) else if "%choice%"=="5" (
    echo.
    echo 📚 فتح ملف التعليمات...
    start notepad "تعليمات_Docker.txt"
    echo.
    echo 📖 يمكنك أيضاً مراجعة الملفات التالية:
    echo    - README.md
    echo    - README_DOCKER.md
    echo    - DOCKER_README.md
) else if "%choice%"=="6" (
    echo.
    echo 🛑 إيقاف جميع نسخ النظام...
    call docker_stop.bat
) else (
    echo.
    echo ❌ اختيار غير صحيح
    echo يرجى اختيار رقم من 1 إلى 6
)

echo.
echo 📋 للعودة إلى هذه القائمة، شغل ملف START_HERE.bat
echo.
pause
