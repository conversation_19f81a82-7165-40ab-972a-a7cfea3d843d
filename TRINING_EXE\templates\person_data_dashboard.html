{% extends "layout.html" %}

{% block styles %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .dashboard-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e3f2fd;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .stats-number {
        font-size: 3rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 10px;
    }

    .stats-label {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 15px;
    }

    .stats-icon {
        font-size: 3rem;
        color: #667eea;
        opacity: 0.7;
        float: right;
        margin-top: -10px;
    }

    .chart-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e3f2fd;
    }

    .chart-title {
        font-size: 1.3rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }

    .progress-item {
        margin-bottom: 15px;
    }

    .progress-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
        font-weight: 500;
    }

    .progress {
        height: 10px;
        border-radius: 10px;
        background-color: #f0f0f0;
    }

    .progress-bar {
        border-radius: 10px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .recent-persons-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e3f2fd;
    }

    .person-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.3s ease;
    }

    .person-item:hover {
        background-color: #f8f9ff;
    }

    .person-item:last-child {
        border-bottom: none;
    }

    .person-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
        margin-left: 15px;
    }

    .person-info {
        flex: 1;
    }

    .person-name {
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }

    .person-details {
        color: #666;
        font-size: 0.9rem;
    }

    .action-buttons {
        text-align: center;
        margin-top: 30px;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        color: white;
        font-weight: bold;
        text-decoration: none;
        display: inline-block;
        margin: 0 10px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .search-box {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e3f2fd;
    }

    .search-input {
        width: 100%;
        padding: 12px 20px;
        border: 2px solid #e0e0e0;
        border-radius: 25px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

    .search-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .empty-state {
        text-align: center;
        padding: 50px;
        color: #666;
    }

    .empty-state i {
        font-size: 4rem;
        color: #ddd;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="dashboard-header">
        <div class="dashboard-title">
            <i class="fas fa-users me-3"></i>الملتحقين
        </div>
        <div class="dashboard-subtitle">
            إدارة ومتابعة جميع الأشخاص المسجلين في النظام
        </div>
    </div>

    <!-- Statistics Row -->
    <div class="row">
        <div class="col-md-3">
            <div class="stats-card">
                <i class="fas fa-users stats-icon"></i>
                <div class="stats-number">{{ total_persons }}</div>
                <div class="stats-label">إجمالي الملتحقين</div>
                <div class="text-muted">
                    <i class="fas fa-chart-line me-1"></i>
                    جميع الأشخاص المسجلين
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="stats-card">
                <i class="fas fa-map-marker-alt stats-icon"></i>
                <div class="stats-number">{{ governorate_stats|length }}</div>
                <div class="stats-label">المحافظات</div>
                <div class="text-muted">
                    <i class="fas fa-globe me-1"></i>
                    التوزيع الجغرافي
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="stats-card">
                <i class="fas fa-graduation-cap stats-icon"></i>
                <div class="stats-number">{{ qualification_stats|length }}</div>
                <div class="stats-label">المؤهلات العلمية</div>
                <div class="text-muted">
                    <i class="fas fa-certificate me-1"></i>
                    التنوع الأكاديمي
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="stats-card">
                <i class="fas fa-clock stats-icon"></i>
                <div class="stats-number">{{ recent_persons|length }}</div>
                <div class="stats-label">المضافين حديثاً</div>
                <div class="text-muted">
                    <i class="fas fa-plus me-1"></i>
                    آخر 10 أشخاص
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Recent Data Row -->
    <div class="row">
        <!-- Course Path Statistics -->
        <div class="col-md-6">
            <div class="chart-card">
                <div class="chart-title">
                    <i class="fas fa-route me-2"></i>توزيع الدورات حسب مسارات التدريب
                </div>
                {% if course_path_stats %}
                    {% set max_count = course_path_stats|map(attribute='count')|max %}
                    {% for stat in course_path_stats[:5] %}
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>{{ stat.name or 'غير محدد' }}</span>
                            <span>{{ stat.count }}</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: {{ (stat.count / max_count * 100) if max_count > 0 else 0 }}%"></div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-route"></i>
                        <p>لا توجد بيانات إحصائية متاحة</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Center Agency Statistics -->
        <div class="col-md-6">
            <div class="chart-card">
                <div class="chart-title">
                    <i class="fas fa-building me-2"></i>توزيع المراكز حسب الجهات
                </div>
                {% if center_agency_stats %}
                    {% set max_count = center_agency_stats|map(attribute='count')|max %}
                    {% for stat in center_agency_stats[:5] %}
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>{{ stat.name or 'غير محدد' }}</span>
                            <span>{{ stat.count }}</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: {{ (stat.count / max_count * 100) if max_count > 0 else 0 }}%"></div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-building"></i>
                        <p>لا توجد بيانات إحصائية متاحة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Second Row of Charts -->
    <div class="row mt-4">
        <!-- Course Location Statistics -->
        <div class="col-md-6">
            <div class="chart-card">
                <div class="chart-title">
                    <i class="fas fa-map-marker-alt me-2"></i>توزيع الدورات حسب المواقع
                </div>
                {% if course_location_stats %}
                    {% set max_count = course_location_stats|map(attribute='count')|max %}
                    {% for stat in course_location_stats[:5] %}
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>{{ stat.name or 'غير محدد' }}</span>
                            <span>{{ stat.count }}</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: {{ (stat.count / max_count * 100) if max_count > 0 else 0 }}%"></div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-map-marker-alt"></i>
                        <p>لا توجد بيانات إحصائية متاحة</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Participant Type Statistics -->
        <div class="col-md-6">
            <div class="chart-card">
                <div class="chart-title">
                    <i class="fas fa-users me-2"></i>توزيع المشاركين حسب أنواع المشاركين
                </div>
                {% if participant_type_stats %}
                    {% set max_count = participant_type_stats|map(attribute='count')|max %}
                    {% for stat in participant_type_stats[:5] %}
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>{{ stat.job or 'غير محدد' }}</span>
                            <span>{{ stat.count }}</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: {{ (stat.count / max_count * 100) if max_count > 0 else 0 }}%"></div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-users"></i>
                        <p>لا توجد بيانات إحصائية متاحة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Third Row of Charts -->
    <div class="row mt-4">
        <!-- Force Classification Statistics -->
        <div class="col-md-6">
            <div class="chart-card">
                <div class="chart-title">
                    <i class="fas fa-shield-alt me-2"></i>توزيع المشاركين حسب تصنيفات القوة
                </div>
                {% if force_classification_stats %}
                    {% set max_count = force_classification_stats|map(attribute='count')|max %}
                    {% for stat in force_classification_stats[:5] %}
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>{{ stat.agency or 'غير محدد' }}</span>
                            <span>{{ stat.count }}</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: {{ (stat.count / max_count * 100) if max_count > 0 else 0 }}%"></div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-shield-alt"></i>
                        <p>لا توجد بيانات إحصائية متاحة</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Empty space for future charts -->
        <div class="col-md-6">
            <!-- يمكن إضافة إحصائية أخرى هنا لاحقاً -->
        </div>
    </div>

    <!-- Recent Persons -->
    <div class="row">
        <div class="col-12">
            <div class="recent-persons-card">
                <div class="chart-title">
                    <i class="fas fa-clock me-2"></i>آخر الملتحقين المضافين
                </div>

                <!-- Search Box -->
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="🔍 البحث في الأسماء..." id="searchInput">
                </div>

                {% if recent_persons %}
                    <div id="personsList">
                        {% for person in recent_persons %}
                        <div class="person-item" data-name="{{ person.full_name|lower }}">
                            <div class="person-avatar">
                                {{ person.full_name[0] if person.full_name else '؟' }}
                            </div>
                            <div class="person-info">
                                <div class="person-name">{{ person.full_name }}</div>
                                <div class="person-details">
                                    {% if person.governorate %}
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ person.governorate }}
                                    {% endif %}
                                    {% if person.qualification %}
                                        <i class="fas fa-graduation-cap me-1 ms-3"></i>{{ person.qualification }}
                                    {% endif %}
                                    {% if person.phone %}
                                        <i class="fas fa-phone me-1 ms-3"></i>{{ person.phone }}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-users"></i>
                        <p>لا يوجد أشخاص مسجلين في النظام</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{{ url_for('personal_data_table') }}" class="btn-primary-custom">
            <i class="fas fa-table me-2"></i>عرض جدول مفصل
        </a>
        <a href="{{ url_for('person_data.name_analysis') }}" class="btn-primary-custom">
            <i class="fas fa-plus me-2"></i>إضافة ملتحقين جدد
        </a>
        <a href="{{ url_for('personal_data_excel') }}" class="btn-primary-custom">
            <i class="fas fa-file-excel me-2"></i>إدارة بالإكسل
        </a>
    </div>
</div>

<script>
// البحث في الأسماء
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const personItems = document.querySelectorAll('.person-item');

    personItems.forEach(function(item) {
        const name = item.getAttribute('data-name');
        if (name.includes(searchTerm)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
});

// تأثيرات بصرية
document.addEventListener('DOMContentLoaded', function() {
    // تحريك الأرقام
    const numbers = document.querySelectorAll('.stats-number');
    numbers.forEach(function(number) {
        const finalNumber = parseInt(number.textContent);
        let currentNumber = 0;
        const increment = finalNumber / 50;

        const timer = setInterval(function() {
            currentNumber += increment;
            if (currentNumber >= finalNumber) {
                number.textContent = finalNumber;
                clearInterval(timer);
            } else {
                number.textContent = Math.floor(currentNumber);
            }
        }, 30);
    });
});
</script>
{% endblock %}
