#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار حذف روابط الترحيب
"""

import requests

def test_welcome_routes_removed():
    """اختبار أن روابط الترحيب تم حذفها"""
    print("🔍 اختبار حذف روابط الترحيب")
    print("=" * 50)
    
    # قائمة روابط الترحيب التي يجب أن تكون محذوفة
    welcome_routes = [
        '/welcome',
        '/welcome-simple', 
        '/welcome-text',
        '/hello',
        '/static-welcome',
        '/welcome-direct',
        '/welcome-new',
        '/welcome-final'
    ]
    
    base_url = 'http://127.0.0.1:5000'
    
    print("📋 اختبار الروابط المحذوفة:")
    
    removed_count = 0
    still_working_count = 0
    
    for route in welcome_routes:
        try:
            response = requests.get(f'{base_url}{route}', timeout=5)
            if response.status_code == 404:
                print(f"   ✅ {route}: محذوف بنجاح (404)")
                removed_count += 1
            else:
                print(f"   ❌ {route}: ما زال يعمل ({response.status_code})")
                still_working_count += 1
        except requests.exceptions.RequestException as e:
            print(f"   ⚠️ {route}: خطأ في الاتصال ({str(e)})")
    
    print(f"\n📊 النتائج:")
    print(f"   ✅ روابط محذوفة: {removed_count}")
    print(f"   ❌ روابط ما زالت تعمل: {still_working_count}")
    print(f"   📝 إجمالي الروابط: {len(welcome_routes)}")
    
    return removed_count, still_working_count

def test_dashboard_page():
    """اختبار صفحة لوحة التحكم"""
    print("\n🏠 اختبار صفحة لوحة التحكم:")
    print("=" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5000/dashboard', allow_redirects=False)
        print(f"   📋 حالة الصفحة: {response.status_code}")
        
        if response.status_code == 302:
            print("   🔐 تم إعادة التوجيه للتسجيل (طبيعي)")
            return True
        elif response.status_code == 200:
            print("   ✅ الصفحة تعمل بنجاح")
            # فحص المحتوى للتأكد من عدم وجود روابط الترحيب
            if 'روابط الترحيب' in response.text:
                print("   ❌ ما زالت تحتوي على روابط الترحيب!")
                return False
            else:
                print("   ✅ لا تحتوي على روابط الترحيب")
                return True
        else:
            print(f"   ❌ خطأ غير متوقع: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {str(e)}")
        return False

def test_main_routes():
    """اختبار الروابط الرئيسية للتأكد من أنها ما زالت تعمل"""
    print("\n🔗 اختبار الروابط الرئيسية:")
    print("=" * 50)
    
    main_routes = [
        '/',
        '/dashboard',
        '/courses',
        '/person_data/person_data_table',
        '/person_data/person_data_excel'
    ]
    
    working_count = 0
    
    for route in main_routes:
        try:
            response = requests.get(f'http://127.0.0.1:5000{route}', allow_redirects=False, timeout=5)
            if response.status_code in [200, 302]:
                status = "✅" if response.status_code == 200 else "🔐"
                print(f"   {status} {route}: {response.status_code}")
                working_count += 1
            else:
                print(f"   ❌ {route}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {route}: خطأ ({str(e)})")
    
    print(f"\n   📊 الروابط العاملة: {working_count}/{len(main_routes)}")
    return working_count == len(main_routes)

def main():
    """الدالة الرئيسية"""
    print("🧹 اختبار حذف روابط الترحيب من النظام")
    print("=" * 60)
    
    # اختبار حذف روابط الترحيب
    removed_count, still_working_count = test_welcome_routes_removed()
    
    # اختبار صفحة لوحة التحكم
    dashboard_ok = test_dashboard_page()
    
    # اختبار الروابط الرئيسية
    main_routes_ok = test_main_routes()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📋 الملخص النهائي:")
    
    if removed_count >= 6 and dashboard_ok and main_routes_ok:
        print("🎉 تم حذف روابط الترحيب بنجاح!")
        print("✅ النظام يعمل بشكل طبيعي")
        print("✅ لوحة التحكم نظيفة من روابط الترحيب")
        print("✅ الروابط الرئيسية تعمل بشكل صحيح")
    else:
        print("⚠️ هناك مشاكل تحتاج إلى إصلاح:")
        if removed_count < 6:
            print(f"   - {still_working_count} روابط ترحيب ما زالت تعمل")
        if not dashboard_ok:
            print("   - مشكلة في صفحة لوحة التحكم")
        if not main_routes_ok:
            print("   - مشكلة في الروابط الرئيسية")
    
    print(f"\n🔗 للوصول إلى النظام: http://127.0.0.1:5000/dashboard")

if __name__ == "__main__":
    main()
