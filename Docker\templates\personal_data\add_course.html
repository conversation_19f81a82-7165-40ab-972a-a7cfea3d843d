{% extends "layout.html" %}

{% block styles %}
<style>
    .form-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .form-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: bold;
        margin-bottom: 8px;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #4a6bff;
        box-shadow: 0 0 0 0.25rem rgba(74, 107, 255, 0.25);
    }
    
    .form-text {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 5px;
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
    }
    
    .btn-cancel {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-cancel:hover {
        transform: translateY(-2px);
    }
    
    .invalid-feedback {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
    }
    
    .sidebar {
        background-color: #343a40;
        color: white;
        min-height: calc(100vh - 56px);
        padding-top: 20px;
    }
    
    .sidebar-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 10px 15px;
        display: block;
        text-decoration: none;
        transition: all 0.3s;
        border-radius: 5px;
        margin: 5px 10px;
    }
    
    .sidebar-link:hover, .sidebar-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar-link i {
        margin-left: 10px;
        width: 20px;
        text-align: center;
    }
    
    .sidebar-dropdown-menu {
        display: none;
        padding-right: 20px;
    }
    
    .sidebar-dropdown-menu.show {
        display: block;
    }
    
    .dropdown-toggle::after {
        display: inline-block;
        margin-right: 5px;
        vertical-align: middle;
        content: "";
        border-top: 0.3em solid;
        border-left: 0.3em solid transparent;
        border-right: 0.3em solid transparent;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_list') }}" class="sidebar-link active">
                <i class="fas fa-id-card"></i> البيانات الشخصية
            </a>
            <div class="sidebar-dropdown">
                <a href="#" class="sidebar-link dropdown-toggle">
                    <i class="fas fa-table"></i> الجداول الترميزية
                </a>
                <div class="sidebar-dropdown-menu">
                    <a href="{{ url_for('governorates') }}" class="sidebar-link">
                        <i class="fas fa-map-marker-alt"></i> المحافظات
                    </a>
                    <a href="{{ url_for('directorates') }}" class="sidebar-link">
                        <i class="fas fa-map"></i> المديريات
                    </a>
                    <!-- يمكن إضافة المزيد من الروابط للجداول الترميزية الأخرى هنا -->
                </div>
            </div>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </div>
    </div>
    
    <div class="col-md-9">
        <h2 class="mb-4">إضافة دورة سابقة</h2>
        
        <div class="form-card">
            <div class="form-header">
                <h4><i class="fas fa-plus-circle me-2"></i> إضافة دورة سابقة</h4>
                <p class="text-muted">إضافة دورة سابقة للشخص: {{ personal_data.full_name }}</p>
            </div>
            
            <form method="POST">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.course_type_id.label(class="form-label") }}
                            {% if form.course_type_id.errors %}
                                {{ form.course_type_id(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.course_type_id.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.course_type_id(class="form-select") }}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.course_name.label(class="form-label") }}
                            {% if form.course_name.errors %}
                                {{ form.course_name(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.course_name.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.course_name(class="form-control", placeholder="أدخل اسم الدورة") }}
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.course_place.label(class="form-label") }}
                            {% if form.course_place.errors %}
                                {{ form.course_place(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.course_place.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.course_place(class="form-control", placeholder="أدخل مكان الدورة") }}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.course_date.label(class="form-label") }}
                            {% if form.course_date.errors %}
                                {{ form.course_date(class="form-control is-invalid", type="date") }}
                                <div class="invalid-feedback">
                                    {% for error in form.course_date.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.course_date(class="form-control", type="date") }}
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            {{ form.course_duration.label(class="form-label") }}
                            {% if form.course_duration.errors %}
                                {{ form.course_duration(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.course_duration.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.course_duration(class="form-control", placeholder="أدخل مدة الدورة") }}
                            {% endif %}
                            <small class="form-text">مثال: 3 أشهر، 2 أسبوع، 30 يوم، إلخ.</small>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ url_for('view_personal_data', personal_data_id=personal_data.id) }}" class="btn btn-secondary btn-cancel">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                    {{ form.submit(class="btn btn-primary btn-submit") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تفعيل القائمة المنسدلة للجداول الترميزية
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
        
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const dropdownMenu = this.nextElementSibling;
                dropdownMenu.classList.toggle('show');
            });
        });
    });
</script>
{% endblock %}
