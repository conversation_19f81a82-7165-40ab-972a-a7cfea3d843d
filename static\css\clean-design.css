/* ===== التصميم النظيف الموحد ===== */

/* إعادة تعيين عامة */
* {
    box-sizing: border-box;
}

/* الخطوط الموحدة */
body, html {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    min-height: 100vh;
}

/* الشريط العلوي الأزرق الاحترافي - تدرج معكوس */
.navbar {
    background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 15%, #3b82f6 35%, #2563eb 55%, #1e40af 75%, #1e3a8a 85%, #0f172a 100%) !important;
    border: none !important;
    box-shadow: 0 4px 20px rgba(30, 58, 138, 0.3) !important;
    padding: 0.8rem 0 !important;
    backdrop-filter: blur(10px) !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 1050 !important;
}

.navbar-brand {
    color: white !important;
    font-weight: 700 !important;
    font-size: 1.4rem !important;
    text-decoration: none !important;
}

.navbar-brand:hover {
    color: #93c5fd !important;
}

.navbar-brand i {
    margin-left: 8px !important;
    color: #93c5fd !important;
}

/* التاريخ والوقت - تصميم محسن في الجانب الأيمن */
.navbar-datetime {
    background: rgba(15, 23, 42, 0.4) !important;
    backdrop-filter: blur(15px) !important;
    border-radius: 10px !important;
    padding: 6px 12px !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
    color: white !important;
    font-size: 0.75rem !important;
    text-align: center !important;
    margin: 0 10px !important;
    min-width: 180px !important;
    transition: all 0.3s ease !important;
}

.navbar-datetime:hover {
    background: rgba(15, 23, 42, 0.6) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
}

.hijri-date, .current-time {
    margin: 1px 0 !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
    letter-spacing: 0.3px !important;
}

.hijri-date {
    font-size: 0.7rem !important;
    opacity: 0.95 !important;
    color: #e2e8f0 !important;
}

.current-time {
    font-size: 0.8rem !important;
    font-family: 'Courier New', monospace !important;
    color: #f1f5f9 !important;
    font-weight: 700 !important;
}

/* روابط التنقل */
.navbar-nav .nav-link {
    color: white !important;
    font-weight: 500 !important;
    font-size: 0.95rem !important;
    padding: 0.5rem 1rem !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    margin: 0 4px !important;
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #93c5fd !important;
    transform: translateY(-1px) !important;
}

/* القائمة المنسدلة */
.dropdown-menu {
    background: white !important;
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
    padding: 0.5rem 0 !important;
    margin-top: 8px !important;
}

.dropdown-item {
    color: #374151 !important;
    font-size: 0.9rem !important;
    padding: 0.6rem 1.2rem !important;
    transition: all 0.3s ease !important;
}

.dropdown-item:hover {
    background: #f3f4f6 !important;
    color: #1e40af !important;
}

.dropdown-item i {
    margin-left: 8px !important;
    width: 16px !important;
    color: #6b7280 !important;
}

/* المحتوى الرئيسي */
.container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 15px !important;
}

main.container {
    background: white !important;
    border-radius: 15px !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08) !important;
    padding: 2rem !important;
    margin-top: 2rem !important;
    margin-bottom: 2rem !important;
}

/* البطاقات */
.card {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s ease !important;
    background: white !important;
}

.card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12) !important;
}

/* بطاقات الإحصائيات المدمجة مع الألوان الاحترافية */
.stat-card {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    background: white !important;
    padding: 20px 15px !important;
    text-align: center !important;
    margin-bottom: 20px !important;
    height: 140px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    position: relative !important;
    overflow: hidden !important;
}

.stat-card:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.stat-icon {
    font-size: 2rem !important;
    margin-bottom: 10px !important;
    color: white !important;
}

.stat-number {
    font-size: 2rem !important;
    font-weight: 700 !important;
    margin: 8px 0 !important;
    line-height: 1 !important;
    color: white !important;
}

.stat-label {
    font-size: 14px !important;
    font-weight: 500 !important;
    margin: 0 !important;
    line-height: 1.2 !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* ألوان البطاقات المختلفة */
.stat-primary {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3) !important;
}

.stat-success {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%) !important;
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3) !important;
}

.stat-info {
    background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%) !important;
    box-shadow: 0 4px 15px rgba(8, 145, 178, 0.3) !important;
}

.stat-warning {
    background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%) !important;
    box-shadow: 0 4px 15px rgba(217, 119, 6, 0.3) !important;
}

/* تأثيرات إضافية للبطاقات */
.stat-card::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%) !important;
    pointer-events: none !important;
}

.stat-card:hover::before {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%) !important;
}

/* تحسين الصفوف والأعمدة */
.row {
    margin-left: -15px !important;
    margin-right: -15px !important;
}

.col-md-3, .col-md-6 {
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-bottom: 20px !important;
}

/* ضمان عدم كسر الأعمدة */
.row.mb-5 {
    display: flex !important;
    flex-wrap: wrap !important;
}

.row.mb-5 .col-md-3 {
    flex: 0 0 25% !important;
    max-width: 25% !important;
}

/* تحسين البطاقات الكبيرة مع الألوان الاحترافية */
.category-card {
    border-radius: 15px !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    padding: 30px !important;
    text-align: center !important;
    border: none !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    position: relative !important;
    overflow: hidden !important;
}

.category-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
}

.category-icon {
    font-size: 3rem !important;
    margin-bottom: 20px !important;
    color: white !important;
}

.category-title {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    color: white !important;
    margin-bottom: 15px !important;
}

.category-description {
    font-size: 14px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    line-height: 1.5 !important;
    margin: 0 !important;
}

/* ألوان مختلفة للبطاقات الكبيرة */
.category-primary {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
    box-shadow: 0 5px 20px rgba(30, 64, 175, 0.3) !important;
}

.category-primary:hover {
    box-shadow: 0 10px 30px rgba(30, 64, 175, 0.4) !important;
}

.category-success {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%) !important;
    box-shadow: 0 5px 20px rgba(5, 150, 105, 0.3) !important;
}

.category-success:hover {
    box-shadow: 0 10px 30px rgba(5, 150, 105, 0.4) !important;
}

.category-info {
    background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%) !important;
    box-shadow: 0 5px 20px rgba(8, 145, 178, 0.3) !important;
}

.category-info:hover {
    box-shadow: 0 10px 30px rgba(8, 145, 178, 0.4) !important;
}

.category-warning {
    background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%) !important;
    box-shadow: 0 5px 20px rgba(217, 119, 6, 0.3) !important;
}

.category-warning:hover {
    box-shadow: 0 10px 30px rgba(217, 119, 6, 0.4) !important;
}

/* تأثيرات إضافية للبطاقات الكبيرة */
.category-card::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%) !important;
    pointer-events: none !important;
}

.category-card:hover::before {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%) !important;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .row.mb-5 .col-md-3 {
        flex: 0 0 50% !important;
        max-width: 50% !important;
    }
}

@media (max-width: 576px) {
    .row.mb-5 .col-md-3 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }
}

.card-header {
    background: linear-gradient(135deg, #1e40af, #3b82f6) !important;
    color: white !important;
    border: none !important;
    border-radius: 12px 12px 0 0 !important;
    padding: 1rem 1.5rem !important;
    font-weight: 600 !important;
}

.card-body {
    padding: 1.5rem !important;
}

/* الأزرار */
.btn {
    border-radius: 8px !important;
    font-weight: 500 !important;
    padding: 0.6rem 1.2rem !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #1e40af, #3b82f6) !important;
    color: white !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1e3a8a, #2563eb) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3) !important;
}

.btn-success {
    background: linear-gradient(135deg, #059669, #10b981) !important;
    color: white !important;
}

.btn-danger {
    background: linear-gradient(135deg, #dc2626, #ef4444) !important;
    color: white !important;
}

/* الجداول */
.table {
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
}

.table thead th {
    background: linear-gradient(135deg, #1e40af, #3b82f6) !important;
    color: white !important;
    border: none !important;
    font-weight: 600 !important;
    padding: 1rem !important;
}

.table tbody td {
    padding: 0.8rem 1rem !important;
    border-color: #e5e7eb !important;
    vertical-align: middle !important;
}

.table tbody tr:hover {
    background: #f8fafc !important;
}

/* النماذج */
.form-control {
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    padding: 0.7rem 1rem !important;
    font-size: 0.95rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.form-label {
    font-weight: 600 !important;
    color: #374151 !important;
    margin-bottom: 0.5rem !important;
}

/* التنبيهات */
.alert {
    border: none !important;
    border-radius: 10px !important;
    padding: 1rem 1.5rem !important;
    margin-bottom: 1.5rem !important;
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0) !important;
    color: #065f46 !important;
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2, #fecaca) !important;
    color: #991b1b !important;
}

.alert-info {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe) !important;
    color: #1e40af !important;
}

/* التذييل */
footer {
    background: linear-gradient(135deg, #0f172a, #1e3a8a) !important;
    color: white !important;
    margin-top: auto !important;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem !important;
    }

    .navbar-datetime {
        display: none !important;
    }

    main.container {
        padding: 1rem !important;
        margin-top: 1rem !important;
    }

    .card-body {
        padding: 1rem !important;
    }
}

@media (max-width: 992px) {
    .navbar-datetime {
        min-width: 160px !important;
        font-size: 0.7rem !important;
        padding: 4px 8px !important;
    }

    .hijri-date {
        font-size: 0.65rem !important;
    }

    .current-time {
        font-size: 0.75rem !important;
    }
}

/* إصلاح الأيقونات */
.fas, .far, .fab {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
}

/* تحسين الطباعة */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin-bottom: 1rem !important;
}

h1 { font-size: 2rem !important; }
h2 { font-size: 1.75rem !important; }
h3 { font-size: 1.5rem !important; }
h4 { font-size: 1.25rem !important; }
h5 { font-size: 1.1rem !important; }
h6 { font-size: 1rem !important; }

p {
    font-size: 0.95rem !important;
    line-height: 1.6 !important;
    color: #4b5563 !important;
    margin-bottom: 1rem !important;
}

/* تحسين التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
