#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏗️ نظام البناء والتغليف الشامل
Comprehensive Build and Packaging System
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path
import json
import zipfile
import time
from datetime import datetime

class SystemBuilder:
    """فئة بناء النظام الشامل"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.temp_dir = self.project_root / "temp_build"
        self.system_info = self.get_system_info()
        
    def get_system_info(self):
        """الحصول على معلومات النظام"""
        return {
            'platform': platform.system(),
            'architecture': platform.architecture()[0],
            'python_version': platform.python_version(),
            'machine': platform.machine(),
            'processor': platform.processor()
        }
    
    def create_requirements_file(self):
        """إنشاء ملف المتطلبات الشامل"""
        requirements = [
            "Flask==2.3.3",
            "Flask-SQLAlchemy==3.0.5",
            "Flask-Login==0.6.3",
            "Flask-WTF==1.1.1",
            "WTForms==3.0.1",
            "Werkzeug==2.3.7",
            "pandas==2.1.1",
            "openpyxl==3.1.2",
            "xlsxwriter==3.1.9",
            "arabic-reshaper==3.0.0",
            "python-bidi==0.4.2",
            "Pillow==10.0.1",
            "requests==2.31.0",
            "python-dateutil==2.8.2",
            "pytz==2023.3",
            "click==8.1.7",
            "itsdangerous==2.1.2",
            "Jinja2==3.1.2",
            "MarkupSafe==2.1.3",
            "SQLAlchemy==2.0.21",
            "typing-extensions==4.8.0"
        ]
        
        req_file = self.project_root / "requirements_build.txt"
        with open(req_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(requirements))
        
        print(f"✅ تم إنشاء ملف المتطلبات: {req_file}")
        return req_file
    
    def create_pyinstaller_spec(self):
        """إنشاء ملف PyInstaller spec محسن"""
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# إعداد المسارات
project_root = Path(r"{self.project_root}")
static_dir = project_root / "static"
templates_dir = project_root / "templates"

# البيانات المضافة
added_files = [
    (str(static_dir), "static"),
    (str(templates_dir), "templates"),
    (str(project_root / "training_system.db"), "."),
]

# المكتبات المخفية
hidden_imports = [
    'flask',
    'flask_sqlalchemy',
    'flask_login',
    'flask_wtf',
    'wtforms',
    'werkzeug',
    'pandas',
    'openpyxl',
    'xlsxwriter',
    'arabic_reshaper',
    'bidi',
    'PIL',
    'requests',
    'dateutil',
    'pytz',
    'click',
    'itsdangerous',
    'jinja2',
    'markupsafe',
    'sqlalchemy',
    'sqlite3',
    'email.mime.multipart',
    'email.mime.text',
    'email.mime.base',
    'encodings.idna',
    'encodings.utf_8',
    'encodings.cp1256'
]

# تحليل التطبيق الرئيسي
a = Analysis(
    ['app.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'numpy', 'scipy'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إزالة الملفات غير المرغوب فيها
a.datas = [x for x in a.datas if not x[0].startswith('tcl')]
a.datas = [x for x in a.datas if not x[0].startswith('tk')]

# إنشاء ملف PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء الملف التنفيذي
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TrainingSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None
)

# إنشاء مجلد التوزيع
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='TrainingSystem'
)
'''
        
        spec_file = self.project_root / "training_system.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"✅ تم إنشاء ملف PyInstaller spec: {spec_file}")
        return spec_file
    
    def install_build_dependencies(self):
        """تثبيت أدوات البناء"""
        print("📦 تثبيت أدوات البناء...")
        
        build_tools = [
            "pyinstaller==5.13.2",
            "auto-py-to-exe==2.40.0",
            "cx-Freeze==6.15.10",
            "nuitka==1.8.4"
        ]
        
        for tool in build_tools:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", tool], 
                             check=True, capture_output=True)
                print(f"✅ تم تثبيت: {tool}")
            except subprocess.CalledProcessError as e:
                print(f"⚠️ فشل في تثبيت: {tool}")
    
    def build_with_pyinstaller(self):
        """بناء النظام باستخدام PyInstaller"""
        print("🔨 بناء النظام باستخدام PyInstaller...")
        
        # إنشاء ملف spec
        spec_file = self.create_pyinstaller_spec()
        
        # تنظيف المجلدات السابقة
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        
        try:
            # تشغيل PyInstaller
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                str(spec_file)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                print("✅ تم بناء النظام بنجاح باستخدام PyInstaller")
                return True
            else:
                print(f"❌ فشل في البناء: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في البناء: {e}")
            return False
    
    def create_portable_package(self):
        """إنشاء حزمة محمولة شاملة"""
        print("📦 إنشاء الحزمة المحمولة...")
        
        package_dir = self.project_root / f"TrainingSystem_Portable_{self.system_info['platform']}"
        
        if package_dir.exists():
            shutil.rmtree(package_dir)
        
        package_dir.mkdir(exist_ok=True)
        
        # نسخ الملفات الأساسية
        essential_files = [
            "app.py",
            "training_system.db",
            "requirements_build.txt"
        ]
        
        for file in essential_files:
            src = self.project_root / file
            if src.exists():
                shutil.copy2(src, package_dir)
        
        # نسخ المجلدات
        essential_dirs = ["static", "templates"]
        for dir_name in essential_dirs:
            src_dir = self.project_root / dir_name
            if src_dir.exists():
                shutil.copytree(src_dir, package_dir / dir_name)
        
        # إنشاء ملف تشغيل محسن
        self.create_startup_script(package_dir)
        
        # إنشاء ملف معلومات النظام
        self.create_system_info_file(package_dir)
        
        print(f"✅ تم إنشاء الحزمة المحمولة: {package_dir}")
        return package_dir
    
    def create_startup_script(self, package_dir):
        """إنشاء سكريبت تشغيل محسن"""
        
        # سكريبت Python
        startup_py = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام التدريب والتأهيل - تشغيل محمول
Training System - Portable Launcher
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_python():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    req_file = Path(__file__).parent / "requirements_build.txt"
    if req_file.exists():
        print("📦 تثبيت المتطلبات...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(req_file)], 
                         check=True, capture_output=True)
            print("✅ تم تثبيت المتطلبات")
            return True
        except:
            print("⚠️ فشل في تثبيت بعض المتطلبات")
            return False
    return True

def main():
    print("🚀 نظام التدريب والتأهيل - الإصدار المحمول")
    print("=" * 50)
    print(f"🐍 Python {{sys.version}}")
    print(f"💻 النظام: {{sys.platform}}")
    print("=" * 50)
    
    if not check_python():
        input("اضغط Enter للخروج...")
        return
    
    # تثبيت المتطلبات
    install_requirements()
    
    try:
        # استيراد التطبيق
        from app import app, db
        
        # إنشاء قاعدة البيانات
        with app.app_context():
            db.create_all()
        
        print("✅ النظام جاهز!")
        print("🌐 http://localhost:5000")
        print("🔑 <EMAIL> / admin123")
        print("=" * 50)
        
        # فتح المتصفح
        time.sleep(2)
        webbrowser.open('http://localhost:5000')
        
        # تشغيل الخادم
        app.run(host='0.0.0.0', port=5000, debug=False)
        
    except Exception as e:
        print(f"❌ خطأ: {{e}}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
        
        with open(package_dir / "start.py", 'w', encoding='utf-8') as f:
            f.write(startup_py)
        
        # سكريبت Windows Batch
        if self.system_info['platform'] == 'Windows':
            batch_script = '''@echo off
chcp 65001 > nul
title نظام التدريب والتأهيل
echo 🚀 نظام التدريب والتأهيل
echo ========================
python start.py
pause
'''
            with open(package_dir / "start.bat", 'w', encoding='utf-8') as f:
                f.write(batch_script)
        
        # سكريبت Linux/Mac Shell
        else:
            shell_script = '''#!/bin/bash
echo "🚀 نظام التدريب والتأهيل"
echo "========================"
python3 start.py
read -p "اضغط Enter للخروج..."
'''
            script_file = package_dir / "start.sh"
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(shell_script)
            script_file.chmod(0o755)
    
    def create_system_info_file(self, package_dir):
        """إنشاء ملف معلومات النظام"""
        info = {
            "name": "نظام التدريب والتأهيل",
            "version": "2.0.0",
            "build_date": datetime.now().isoformat(),
            "system_info": self.system_info,
            "features": [
                "نظام محمول بالكامل",
                "لا يحتاج إنترنت للتشغيل",
                "قاعدة بيانات SQLite مدمجة",
                "واجهة ويب متجاوبة",
                "دعم اللغة العربية",
                "تقارير Excel متقدمة",
                "نظام مستخدمين متكامل"
            ],
            "requirements": {
                "python": "3.8+",
                "memory": "512MB",
                "storage": "100MB",
                "network": "اختياري"
            },
            "usage": {
                "windows": "تشغيل start.bat",
                "linux_mac": "تشغيل start.sh أو python3 start.py",
                "url": "http://localhost:5000",
                "login": "<EMAIL> / admin123"
            }
        }
        
        with open(package_dir / "system_info.json", 'w', encoding='utf-8') as f:
            json.dump(info, f, ensure_ascii=False, indent=2)
        
        # ملف README
        readme = f'''# 🚀 نظام التدريب والتأهيل - الإصدار المحمول

## 📋 معلومات النظام
- **الاسم**: نظام التدريب والتأهيل
- **الإصدار**: 2.0.0
- **تاريخ البناء**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **النظام**: {self.system_info['platform']} {self.system_info['architecture']}

## ✨ المميزات
- ✅ نظام محمول بالكامل - لا يحتاج تثبيت
- ✅ يعمل بدون إنترنت
- ✅ قاعدة بيانات SQLite مدمجة
- ✅ واجهة ويب حديثة ومتجاوبة
- ✅ دعم كامل للغة العربية
- ✅ تقارير Excel متقدمة
- ✅ نظام مستخدمين وصلاحيات

## 🚀 طريقة التشغيل

### Windows:
```
start.bat
```

### Linux/Mac:
```
./start.sh
```
أو
```
python3 start.py
```

## 🌐 الوصول للنظام
- **الرابط**: http://localhost:5000
- **المستخدم**: <EMAIL>
- **كلمة المرور**: admin123

## 📋 المتطلبات
- Python 3.8 أو أحدث
- 512MB ذاكرة
- 100MB مساحة تخزين
- اتصال إنترنت (للتثبيت الأولي فقط)

## 🔧 استكشاف الأخطاء
إذا واجهت مشاكل:
1. تأكد من تثبيت Python 3.8+
2. شغل: `pip install -r requirements_build.txt`
3. شغل: `python3 start.py`

## 📞 الدعم
للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.

---
© 2024 نظام التدريب والتأهيل - جميع الحقوق محفوظة
'''
        
        with open(package_dir / "README.md", 'w', encoding='utf-8') as f:
            f.write(readme)
    
    def create_zip_package(self, package_dir):
        """إنشاء ملف ZIP للتوزيع"""
        zip_name = f"TrainingSystem_v2.0_{self.system_info['platform']}_{datetime.now().strftime('%Y%m%d')}.zip"
        zip_path = self.project_root / zip_name
        
        print(f"📦 إنشاء ملف ZIP: {zip_name}")
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = file_path.relative_to(package_dir)
                    zipf.write(file_path, arc_name)
        
        print(f"✅ تم إنشاء ملف ZIP: {zip_path}")
        print(f"📊 حجم الملف: {zip_path.stat().st_size / 1024 / 1024:.2f} MB")
        return zip_path
    
    def create_docker_config(self):
        """إنشاء إعدادات Docker"""
        dockerfile_content = '''FROM python:3.9-slim

# تثبيت المتطلبات النظام
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# إنشاء مجلد العمل
WORKDIR /app

# نسخ ملف المتطلبات
COPY requirements_build.txt .

# تثبيت المتطلبات
RUN pip install --no-cache-dir -r requirements_build.txt

# نسخ ملفات التطبيق
COPY . .

# إنشاء قاعدة البيانات
RUN python -c "from app import app, db; app.app_context().push(); db.create_all()"

# تعريف المنفذ
EXPOSE 5000

# تشغيل التطبيق
CMD ["python", "app.py"]
'''

        with open(self.project_root / "Dockerfile", 'w', encoding='utf-8') as f:
            f.write(dockerfile_content)

        # إنشاء docker-compose.yml
        compose_content = '''version: '3.8'

services:
  training-system:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./training_system.db:/app/training_system.db
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=False
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - training-system
    restart: unless-stopped
'''

        with open(self.project_root / "docker-compose.yml", 'w', encoding='utf-8') as f:
            f.write(compose_content)

        print("✅ تم إنشاء إعدادات Docker")

    def create_deployment_scripts(self):
        """إنشاء سكريبتات النشر"""

        # سكريبت نشر Linux
        linux_deploy = '''#!/bin/bash
echo "🚀 نشر نظام التدريب والتأهيل - Linux"
echo "=================================="

# فحص Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker غير مثبت. يرجى تثبيت Docker أولاً"
    exit 1
fi

# فحص Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose غير مثبت. يرجى تثبيت Docker Compose أولاً"
    exit 1
fi

# بناء وتشغيل الحاويات
echo "📦 بناء الحاويات..."
docker-compose build

echo "🚀 تشغيل النظام..."
docker-compose up -d

echo "✅ تم نشر النظام بنجاح!"
echo "🌐 يمكن الوصول للنظام على: http://localhost"
echo "📊 لمراقبة السجلات: docker-compose logs -f"
echo "🛑 لإيقاف النظام: docker-compose down"
'''

        script_file = self.project_root / "deploy_linux.sh"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(linux_deploy)
        script_file.chmod(0o755)

        # سكريبت نشر Windows
        windows_deploy = '''@echo off
chcp 65001 > nul
title نشر نظام التدريب والتأهيل
echo 🚀 نشر نظام التدريب والتأهيل - Windows
echo ==================================

REM فحص Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker غير مثبت. يرجى تثبيت Docker Desktop أولاً
    pause
    exit /b 1
)

REM فحص Docker Compose
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose غير مثبت. يرجى تثبيت Docker Compose أولاً
    pause
    exit /b 1
)

REM بناء وتشغيل الحاويات
echo 📦 بناء الحاويات...
docker-compose build

echo 🚀 تشغيل النظام...
docker-compose up -d

echo ✅ تم نشر النظام بنجاح!
echo 🌐 يمكن الوصول للنظام على: http://localhost
echo 📊 لمراقبة السجلات: docker-compose logs -f
echo 🛑 لإيقاف النظام: docker-compose down
pause
'''

        with open(self.project_root / "deploy_windows.bat", 'w', encoding='utf-8') as f:
            f.write(windows_deploy)

    def build_complete_system(self):
        """بناء النظام الكامل"""
        print("🏗️ بدء بناء النظام الشامل...")
        print("=" * 60)

        start_time = time.time()

        try:
            # 1. إنشاء ملف المتطلبات
            self.create_requirements_file()

            # 2. تثبيت أدوات البناء
            self.install_build_dependencies()

            # 3. إنشاء الحزمة المحمولة
            package_dir = self.create_portable_package()

            # 4. إنشاء إعدادات Docker
            self.create_docker_config()

            # 5. إنشاء سكريبتات النشر
            self.create_deployment_scripts()

            # 6. إنشاء ملف ZIP
            zip_file = self.create_zip_package(package_dir)

            # 7. محاولة بناء ملف تنفيذي
            print("\n🔨 محاولة إنشاء ملف تنفيذي...")
            exe_success = self.build_with_pyinstaller()

            end_time = time.time()
            build_time = end_time - start_time

            print("\n" + "=" * 60)
            print("🎉 تم إكمال عملية البناء!")
            print("=" * 60)
            print(f"⏱️ وقت البناء: {build_time:.2f} ثانية")
            print(f"📦 الحزمة المحمولة: {package_dir}")
            print(f"📦 ملف ZIP: {zip_file}")
            print(f"🐳 إعدادات Docker: Dockerfile, docker-compose.yml")
            print(f"🚀 سكريبتات النشر: deploy_linux.sh, deploy_windows.bat")

            if exe_success:
                print(f"📦 الملف التنفيذي: {self.dist_dir}")

            print("\n✅ النظام جاهز للتوزيع!")

            return True

        except Exception as e:
            print(f"❌ خطأ في عملية البناء: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    print("🏗️ نظام البناء والتغليف الشامل")
    print("=" * 50)
    
    builder = SystemBuilder()
    success = builder.build_complete_system()
    
    if success:
        print("\n🎉 تم بناء النظام بنجاح!")
        print("يمكنك الآن توزيع النظام على أي جهاز")
    else:
        print("\n❌ فشل في بناء النظام")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
