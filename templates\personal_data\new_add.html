{% extends "layout.html" %}

{% block styles %}
<style>
    .form-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }

    .form-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: bold;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4a6bff;
        box-shadow: 0 0 0 0.25rem rgba(74, 107, 255, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
    }

    .btn-secondary {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
    }

    .tab-content {
        padding: 20px;
        background-color: #fff;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .nav-tabs .nav-link {
        border-radius: 10px 10px 0 0;
        padding: 12px 20px;
        font-weight: bold;
        color: #495057;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        margin-right: 5px;
    }

    .nav-tabs .nav-link.active {
        color: #4a6bff;
        background-color: #fff;
        border-bottom-color: transparent;
    }

    .import-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        border: 2px dashed #ced4da;
        text-align: center;
    }

    .import-icon {
        font-size: 48px;
        color: #4a6bff;
        margin-bottom: 15px;
    }

    .import-title {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .import-description {
        color: #6c757d;
        margin-bottom: 20px;
    }

    .file-input {
        display: none;
    }

    .file-label {
        display: inline-block;
        padding: 10px 20px;
        background-color: #4a6bff;
        color: white;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .file-label:hover {
        background-color: #2541b2;
    }

    .file-name {
        margin-top: 10px;
        font-size: 14px;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_list') }}" class="sidebar-link active">
                <i class="fas fa-id-card"></i> البيانات الشخصية
            </a>
            <div class="sidebar-dropdown">
                <a href="#" class="sidebar-link dropdown-toggle">
                    <i class="fas fa-table"></i> الجداول الترميزية
                </a>
                <div class="sidebar-dropdown-menu">
                    <a href="{{ url_for('governorates') }}" class="sidebar-link">
                        <i class="fas fa-map-marker-alt"></i> المحافظات
                    </a>
                    <a href="{{ url_for('directorates') }}" class="sidebar-link">
                        <i class="fas fa-map"></i> المديريات
                    </a>
                </div>
            </div>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <h2 class="mb-4">إدارة البيانات الشخصية</h2>

        <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="manual-tab" data-bs-toggle="tab" data-bs-target="#manual" type="button" role="tab" aria-controls="manual" aria-selected="true">
                    <i class="fas fa-edit me-2"></i>إدخال يدوي
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import" type="button" role="tab" aria-controls="import" aria-selected="false">
                    <i class="fas fa-file-excel me-2"></i>استيراد من إكسل
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="compare-tab" data-bs-toggle="tab" data-bs-target="#compare" type="button" role="tab" aria-controls="compare" aria-selected="false">
                    <i class="fas fa-exchange-alt me-2"></i>مباينة ملفات
                </button>
            </li>
        </ul>

        <div class="tab-content" id="myTabContent">
            <!-- قسم الإدخال اليدوي -->
            <div class="tab-pane fade show active" id="manual" role="tabpanel" aria-labelledby="manual-tab">
                <div class="form-card">
                    <div class="form-header">
                        <h4><i class="fas fa-user-plus me-2"></i> إضافة بيانات شخصية جديدة</h4>
                        <p class="text-muted">أدخل البيانات الشخصية للفرد</p>
                    </div>

                    <form method="POST" action="{{ url_for('add_personal_data_new') }}">
                        {{ form.hidden_tag() }}

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.full_name.label(class="form-label") }}
                                    {{ form.full_name(class="form-control", placeholder="أدخل الاسم الكامل") }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.national_number.label(class="form-label") }}
                                    {{ form.national_number(class="form-control", placeholder="أدخل الرقم الوطني") }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.birth_date.label(class="form-label") }}
                                    {{ form.birth_date(class="form-control", type="date") }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.marital_status_id.label(class="form-label") }}
                                    {{ form.marital_status_id(class="form-select") }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.blood_type_id.label(class="form-label") }}
                                    {{ form.blood_type_id(class="form-select") }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.phone_yemen_mobile.label(class="form-label") }}
                                    {{ form.phone_yemen_mobile(class="form-control", placeholder="أدخل رقم الهاتف") }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.work_place_id.label(class="form-label") }}
                                    {{ form.work_place_id(class="form-select") }}
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ البيانات
                            </button>
                            <a href="{{ url_for('personal_data_list') }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قسم الاستيراد من إكسل -->
            <div class="tab-pane fade" id="import" role="tabpanel" aria-labelledby="import-tab">
                <div class="form-card">
                    <div class="form-header">
                        <h4><i class="fas fa-file-import me-2"></i> استيراد البيانات من ملف إكسل</h4>
                        <p class="text-muted">قم بتحميل ملف إكسل يحتوي على البيانات الشخصية</p>
                    </div>

                    <form method="POST" action="{{ url_for('import_excel_data') }}" enctype="multipart/form-data">

                        <div class="import-card">
                            <div class="import-icon">
                                <i class="fas fa-file-excel"></i>
                            </div>
                            <div class="import-title">اختر ملف إكسل</div>
                            <div class="import-description">يجب أن يكون الملف بتنسيق .xlsx أو .xls</div>

                            <input type="file" name="excel_file" id="excel_file" class="file-input" accept=".xlsx, .xls">
                            <label for="excel_file" class="file-label">
                                <i class="fas fa-upload me-2"></i>اختر ملف
                            </label>
                            <div id="file-name" class="file-name mt-2">لم يتم اختيار ملف</div>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-import me-2"></i>استيراد البيانات
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قسم المباينة -->
            <div class="tab-pane fade" id="compare" role="tabpanel" aria-labelledby="compare-tab">
                <div class="form-card">
                    <div class="form-header">
                        <h4><i class="fas fa-exchange-alt me-2"></i> مباينة ملفات إكسل</h4>
                        <p class="text-muted">قم بتحميل ملفين إكسل لمقارنة البيانات بينهما</p>
                    </div>

                    <form method="POST" action="{{ url_for('compare_excel_files') }}" enctype="multipart/form-data">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="import-card">
                                    <div class="import-icon">
                                        <i class="fas fa-file-excel"></i>
                                    </div>
                                    <div class="import-title">الملف الأول</div>
                                    <div class="import-description">اختر ملف إكسل</div>

                                    <input type="file" name="excel_file_1" id="excel_file_1" class="file-input" accept=".xlsx, .xls">
                                    <label for="excel_file_1" class="file-label">
                                        <i class="fas fa-upload me-2"></i>اختر ملف
                                    </label>
                                    <div id="file-name-1" class="file-name mt-2">لم يتم اختيار ملف</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="import-card">
                                    <div class="import-icon">
                                        <i class="fas fa-file-excel"></i>
                                    </div>
                                    <div class="import-title">الملف الثاني</div>
                                    <div class="import-description">اختر ملف إكسل</div>

                                    <input type="file" name="excel_file_2" id="excel_file_2" class="file-input" accept=".xlsx, .xls">
                                    <label for="excel_file_2" class="file-label">
                                        <i class="fas fa-upload me-2"></i>اختر ملف
                                    </label>
                                    <div id="file-name-2" class="file-name mt-2">لم يتم اختيار ملف</div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-exchange-alt me-2"></i>مباينة الملفات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.getElementById('excel_file').addEventListener('change', function() {
        var fileName = this.files[0] ? this.files[0].name : 'لم يتم اختيار ملف';
        document.getElementById('file-name').textContent = fileName;
    });

    document.getElementById('excel_file_1').addEventListener('change', function() {
        var fileName = this.files[0] ? this.files[0].name : 'لم يتم اختيار ملف';
        document.getElementById('file-name-1').textContent = fileName;
    });

    document.getElementById('excel_file_2').addEventListener('change', function() {
        var fileName = this.files[0] ? this.files[0].name : 'لم يتم اختيار ملف';
        document.getElementById('file-name-2').textContent = fileName;
    });
</script>
{% endblock %}
