# 🌟 نظام التدريب والتأهيل - المتطلبات الشاملة
# Complete Requirements for Enhanced Training System

# ===== المتطلبات الأساسية (Core Requirements) =====
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
Flask-WTF==1.1.1
WTForms==3.0.1
Werkzeug==2.3.7
SQLAlchemy==2.0.21
Jinja2==3.1.2
MarkupSafe==2.1.3
itsdangerous==2.1.2
click==8.1.7

# ===== قاعدة البيانات (Database) =====
# SQLite مدمج مع Python - لا يحتاج تثبيت إضافي

# ===== معالجة البيانات (Data Processing) =====
pandas==2.1.1
numpy==1.24.3
openpyxl==3.1.2
xlsxwriter==3.1.9
xlrd==2.0.1

# ===== اللغة العربية (Arabic Support) =====
arabic-reshaper==3.0.0
python-bidi==0.4.2

# ===== معالجة الصور (Image Processing) =====
Pillow==10.0.1

# ===== الشبكة والطلبات (Network & Requests) =====
requests==2.31.0
urllib3==2.0.4

# ===== التاريخ والوقت (Date & Time) =====
python-dateutil==2.8.2
pytz==2023.3

# ===== مراقبة النظام (System Monitoring) =====
psutil==5.9.5

# ===== الأمان والتشفير (Security & Encryption) =====
cryptography==41.0.4
bcrypt==4.0.1

# ===== التطوير والاختبار (Development & Testing) =====
pytest==7.4.2
pytest-flask==1.2.0
coverage==7.3.2

# ===== أدوات البناء (Build Tools) =====
PyInstaller==5.13.2
auto-py-to-exe==2.40.0
cx-Freeze==6.15.10
nuitka==1.8.4

# ===== خادم الويب (Web Server) =====
gunicorn==21.2.0
waitress==2.1.2

# ===== متطلبات إضافية للإنتاج (Production Requirements) =====
python-dotenv==1.0.0
email-validator==2.0.0

# ===== أدوات التطوير (Development Tools) =====
black==23.7.0
flake8==6.0.0
isort==5.12.0

# ===== مكتبات مساعدة (Utility Libraries) =====
pathlib2==2.3.7
typing-extensions==4.8.0

# ===== متطلبات النظام حسب نظام التشغيل =====
# Windows
pywin32==306; sys_platform == "win32"

# Linux/Unix
python-magic==0.4.27; sys_platform != "win32"

# ===== متطلبات اختيارية للميزات المتقدمة =====
# Redis للتخزين المؤقت المتقدم (اختياري)
# redis==4.6.0

# PostgreSQL للقواعد البيانات الكبيرة (اختياري)  
# psycopg2-binary==2.9.7

# MySQL للقواعد البيانات البديلة (اختياري)
# PyMySQL==1.1.0

# ===== أدوات المراقبة المتقدمة (اختياري) =====
# prometheus-client==0.17.1
# grafana-api==1.0.3

# ===== أدوات التحليل (اختياري) =====
# matplotlib==3.7.2
# seaborn==0.12.2
# plotly==5.15.0

# ===== ملاحظات التثبيت =====
# للتثبيت الأساسي:
# pip install flask flask-sqlalchemy flask-login flask-wtf pandas openpyxl

# للتثبيت الشامل:
# pip install -r requirements_complete.txt

# للتثبيت المحمول (بدون اتصال):
# pip download -r requirements_complete.txt -d packages/
# pip install --find-links packages/ -r requirements_complete.txt --no-index

# ===== معلومات الإصدارات =====
# تم اختبار هذه الإصدارات مع:
# - Python 3.8+
# - Windows 10/11
# - Ubuntu 20.04+
# - macOS 11+

# ===== الحد الأدنى للمتطلبات =====
# Python: 3.8+
# RAM: 512MB
# Storage: 100MB
# Network: اختياري (للتثبيت الأولي فقط)
