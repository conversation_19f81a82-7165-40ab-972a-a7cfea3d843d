{% extends "layout.html" %}
{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h3 class="mb-0">الرتب العسكرية</h3>
            <a href="{{ url_for('add_military_rank') }}" class="btn btn-light">
                <i class="fas fa-plus-circle"></i> إضافة رتبة جديدة
            </a>
        </div>
        <div class="card-body">
            {% if ranks %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">الرتبة</th>
                            <th scope="col">الرمز</th>
                            <th scope="col">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rank in ranks %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ rank.name }}</td>
                            <td>{{ rank.code }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('edit_military_rank', rank_id=rank.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <a href="{{ url_for('delete_military_rank', rank_id=rank.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الرتبة؟');">
                                        <i class="fas fa-trash-alt"></i> حذف
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> لا توجد رتب عسكرية مضافة حتى الآن.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
