{% extends "layout.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-plus-circle"></i> إضافة تصحيح مخصص جديد
                </h1>
                <a href="{{ url_for('person_data.manage_corrections') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>

            <!-- Instructions Card -->
            <div class="card border-info mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> تعليمات الاستخدام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-info"><i class="fas fa-lightbulb"></i> نصائح مهمة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> اكتب الاسم الخطأ كما يظهر في الملفات</li>
                                <li><i class="fas fa-check text-success"></i> اكتب الاسم الصحيح بالشكل المطلوب</li>
                                <li><i class="fas fa-check text-success"></i> اختر نوع التصحيح المناسب</li>
                                <li><i class="fas fa-check text-success"></i> تأكد من عدم وجود مسافات زائدة</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info"><i class="fas fa-examples"></i> أمثلة:</h6>
                            <div class="small">
                                <div class="mb-2">
                                    <span class="badge badge-danger">عيسي</span> →
                                    <span class="badge badge-success">عيسى</span>
                                    <span class="text-muted">(ألف مقصورة)</span>
                                </div>
                                <div class="mb-2">
                                    <span class="badge badge-danger">احمد</span> →
                                    <span class="badge badge-success">أحمد</span>
                                    <span class="text-muted">(همزة)</span>
                                </div>
                                <div class="mb-2">
                                    <span class="badge badge-danger">عبد الله</span> →
                                    <span class="badge badge-success">عبدالله</span>
                                    <span class="text-muted">(اسم مركب)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Correction Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit"></i> بيانات التصحيح الجديد
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.wrong_name.label(class="form-label font-weight-bold") }}
                                    {{ form.wrong_name(class="form-control form-control-lg") }}
                                    {% if form.wrong_name.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.wrong_name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        اكتب الاسم كما يظهر خطأ في الملفات
                                    </small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.correct_name.label(class="form-label font-weight-bold") }}
                                    {{ form.correct_name(class="form-control form-control-lg") }}
                                    {% if form.correct_name.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.correct_name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        <i class="fas fa-check-circle"></i>
                                        اكتب الاسم بالشكل الصحيح المطلوب
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            {{ form.correction_type.label(class="form-label font-weight-bold") }}
                            {{ form.correction_type(class="form-control form-control-lg") }}
                            {% if form.correction_type.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.correction_type.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                <i class="fas fa-tags"></i>
                                اختر نوع التصحيح لتسهيل التصنيف والبحث
                            </small>
                        </div>

                        <!-- Preview Section -->
                        <div class="card bg-light mt-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-eye"></i> معاينة التصحيح
                                </h6>
                                <div class="d-flex align-items-center">
                                    <span class="badge badge-danger badge-lg mr-2" id="previewWrong">الاسم الخطأ</span>
                                    <i class="fas fa-arrow-left text-primary mx-2"></i>
                                    <span class="badge badge-success badge-lg" id="previewCorrect">الاسم الصحيح</span>
                                </div>
                                <small class="text-muted mt-2 d-block">
                                    <i class="fas fa-info-circle"></i>
                                    سيتم تطبيق هذا التصحيح تلقائياً على جميع عمليات التحليل المستقبلية
                                </small>
                            </div>
                        </div>

                        <div class="form-group mt-4 text-center">
                            {{ form.submit(class="btn btn-primary btn-lg px-5") }}
                            <a href="{{ url_for('person_data.manage_corrections') }}" class="btn btn-secondary btn-lg px-5 mr-3">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Recent Corrections -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history"></i> آخر التصحيحات المضافة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <span class="badge badge-danger">عيسي</span> → <span class="badge badge-success">عيسى</span>
                                <div class="small text-muted mt-1">ألف مقصورة</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <span class="badge badge-danger">احمد</span> → <span class="badge badge-success">أحمد</span>
                                <div class="small text-muted mt-1">همزة</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <span class="badge badge-danger">عبد الله</span> → <span class="badge badge-success">عبدالله</span>
                                <div class="small text-muted mt-1">اسم مركب</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Live preview functionality
document.addEventListener('DOMContentLoaded', function() {
    const wrongNameInput = document.getElementById('wrong_name');
    const correctNameInput = document.getElementById('correct_name');
    const previewWrong = document.getElementById('previewWrong');
    const previewCorrect = document.getElementById('previewCorrect');

    function updatePreview() {
        const wrongValue = wrongNameInput.value.trim() || 'الاسم الخطأ';
        const correctValue = correctNameInput.value.trim() || 'الاسم الصحيح';

        previewWrong.textContent = wrongValue;
        previewCorrect.textContent = correctValue;
    }

    wrongNameInput.addEventListener('input', updatePreview);
    correctNameInput.addEventListener('input', updatePreview);
});
</script>

<style>
.badge-lg {
    font-size: 1rem;
    padding: 0.5rem 1rem;
}

.form-control-lg {
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
}

.card {
    border-radius: 0.5rem;
}

.form-label {
    color: #5a5c69;
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}
