{% extends "layout.html" %}

{% block title %}مشاركي الدورة - {{ course.title }}{% endblock %}



{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-users"></i>
                        مشاركي الدورة: {{ course.title }}
                    </h4>
                    <small>رقم الدورة: {{ course.course_number }}</small>
                </div>
                
                <div class="card-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs" id="participantTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="participants-tab" data-bs-toggle="tab" 
                                    data-bs-target="#participants" type="button" role="tab">
                                <i class="fas fa-users"></i> المشاركين ({{ participants|length }})
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="evaluation-tab" data-bs-toggle="tab" 
                                    data-bs-target="#evaluation" type="button" role="tab">
                                <i class="fas fa-star"></i> التقييم
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="reports-tab" data-bs-toggle="tab" 
                                    data-bs-target="#reports" type="button" role="tab">
                                <i class="fas fa-chart-bar"></i> التقارير
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="participantTabsContent">
                        
                        <!-- المشاركين Tab -->
                        <div class="tab-pane fade show active" id="participants" role="tabpanel">
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5>قائمة المشاركين</h5>
                                    <div>
                                        <a href="{{ url_for('add_course_participant', course_id=course.id) }}" 
                                           class="btn btn-success btn-sm">
                                            <i class="fas fa-plus"></i> إضافة مشارك
                                        </a>
                                        <button class="btn btn-info btn-sm" onclick="selectAll()">
                                            <i class="fas fa-check-square"></i> تحديد الكل
                                        </button>
                                        <button class="btn btn-warning btn-sm" onclick="clearSelection()">
                                            <i class="fas fa-square"></i> إلغاء التحديد
                                        </button>
                                    </div>
                                </div>

                                {% if participants %}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th width="40">
                                                    <input type="checkbox" id="selectAllCheckbox" onchange="toggleAll()">
                                                </th>
                                                <th>#</th>
                                                <th>الاسم الكامل</th>
                                                <th>الرقم الوطني</th>
                                                <th>الرقم العسكري</th>
                                                <th>الوظيفة</th>
                                                <th>الحالة</th>
                                                <th>تاريخ التسجيل</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for participant in participants %}
                                            <tr>
                                                <td>
                                                    <input type="checkbox" class="participant-checkbox" 
                                                           value="{{ participant.id }}" name="selected_participants">
                                                </td>
                                                <td>{{ loop.index }}</td>
                                                <td>
                                                    <strong>{{ participant.personal_data.full_name if participant.personal_data else 'غير محدد' }}</strong>
                                                </td>
                                                <td>{{ participant.personal_data.national_number if participant.personal_data else '-' }}</td>
                                                <td>{{ participant.personal_data.military_number if participant.personal_data else '-' }}</td>
                                                <td>{{ participant.personal_data.job if participant.personal_data else '-' }}</td>
                                                <td>
                                                    <span class="badge 
                                                        {% if participant.status == 'مكتمل' %}bg-success
                                                        {% elif participant.status == 'منسحب' %}bg-danger
                                                        {% elif participant.status == 'نشط' %}bg-primary
                                                        {% else %}bg-secondary{% endif %}">
                                                        {{ participant.status }}
                                                    </span>
                                                </td>
                                                <td>{{ participant.entry_date.strftime('%Y-%m-%d') if participant.entry_date else '-' }}</td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="{{ url_for('edit_course_participant', course_id=course.id, participant_id=participant.id) }}" 
                                                           class="btn btn-outline-primary btn-sm" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button class="btn btn-outline-success btn-sm" 
                                                                onclick="evaluateParticipant({{ participant.id }})" title="تقييم">
                                                            <i class="fas fa-star"></i>
                                                        </button>
                                                        <a href="{{ url_for('delete_course_participant', course_id=course.id, participant_id=participant.id) }}" 
                                                           class="btn btn-outline-danger btn-sm" 
                                                           onclick="return confirm('هل أنت متأكد من حذف هذا المشارك؟')" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-info-circle"></i>
                                    لا يوجد مشاركين في هذه الدورة حتى الآن.
                                    <a href="{{ url_for('add_course_participant', course_id=course.id) }}" class="btn btn-success btn-sm ms-2">
                                        إضافة أول مشارك
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- التقييم Tab -->
                        <div class="tab-pane fade" id="evaluation" role="tabpanel">
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5><i class="fas fa-star"></i> نظام التقييم</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label class="form-label">اختر المشاركين للتقييم:</label>
                                                    <div class="d-flex gap-2 mb-2">
                                                        <button class="btn btn-outline-primary btn-sm" onclick="selectAllForEvaluation()">
                                                            <i class="fas fa-check-square"></i> تحديد الكل
                                                        </button>
                                                        <button class="btn btn-outline-secondary btn-sm" onclick="clearEvaluationSelection()">
                                                            <i class="fas fa-square"></i> إلغاء التحديد
                                                        </button>
                                                        <button class="btn btn-success btn-sm" onclick="startBulkEvaluation()">
                                                            <i class="fas fa-star"></i> تقييم المحددين
                                                        </button>
                                                    </div>
                                                    <div class="evaluation-participants-list" style="max-height: 300px; overflow-y: auto;">
                                                        {% for participant in participants %}
                                                        <div class="form-check">
                                                            <input class="form-check-input evaluation-checkbox" type="checkbox" 
                                                                   value="{{ participant.id }}" id="eval_{{ participant.id }}">
                                                            <label class="form-check-label" for="eval_{{ participant.id }}">
                                                                {{ participant.personal_data.full_name if participant.personal_data else 'غير محدد' }}
                                                                <small class="text-muted">({{ participant.status }})</small>
                                                            </label>
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6><i class="fas fa-cog"></i> إعدادات التقييم</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label class="form-label">قالب التقييم:</label>
                                                    <select class="form-select" id="evaluationTemplate">
                                                        <option value="1">القالب الأساسي</option>
                                                        <option value="2">تقييم متقدم</option>
                                                        <option value="3">تقييم مهاري</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">نوع التقييم:</label>
                                                    <select class="form-select" id="evaluationType">
                                                        <option value="individual">فردي</option>
                                                        <option value="bulk">جماعي</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <button class="btn btn-primary w-100" onclick="createNewEvaluation()">
                                                        <i class="fas fa-plus"></i> إنشاء تقييم جديد
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- التقارير Tab -->
                        <div class="tab-pane fade" id="reports" role="tabpanel">
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6><i class="fas fa-print"></i> طباعة وتصدير</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-grid gap-2">
                                                    <button class="btn btn-outline-primary" onclick="printSelectedEvaluations()">
                                                        <i class="fas fa-print"></i> طباعة تقييمات المحددين
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="exportToExcel()">
                                                        <i class="fas fa-file-excel"></i> تصدير إلى Excel
                                                    </button>
                                                    <button class="btn btn-outline-info" onclick="exportToPDF()">
                                                        <i class="fas fa-file-pdf"></i> تصدير إلى PDF
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6><i class="fas fa-chart-bar"></i> إحصائيات التقييم</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row text-center">
                                                    <div class="col-6">
                                                        <div class="border rounded p-2">
                                                            <h4 class="text-success">{{ participants|selectattr('status', 'equalto', 'مكتمل')|list|length }}</h4>
                                                            <small>مكتمل</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="border rounded p-2">
                                                            <h4 class="text-danger">{{ participants|selectattr('status', 'equalto', 'منسحب')|list|length }}</h4>
                                                            <small>منسحب</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للتقييم -->
<div class="modal fade" id="evaluationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تقييم المشاركين</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="evaluationModalBody">
                <!-- سيتم تحميل محتوى التقييم هنا -->
            </div>
        </div>
    </div>
</div>

<script>
// JavaScript functions
function selectAll() {
    document.querySelectorAll('.participant-checkbox').forEach(cb => cb.checked = true);
}

function clearSelection() {
    document.querySelectorAll('.participant-checkbox').forEach(cb => cb.checked = false);
}

function toggleAll() {
    const selectAll = document.getElementById('selectAllCheckbox');
    document.querySelectorAll('.participant-checkbox').forEach(cb => cb.checked = selectAll.checked);
}

function selectAllForEvaluation() {
    document.querySelectorAll('.evaluation-checkbox').forEach(cb => cb.checked = true);
}

function clearEvaluationSelection() {
    document.querySelectorAll('.evaluation-checkbox').forEach(cb => cb.checked = false);
}

function evaluateParticipant(participantId) {
    // فتح نافذة التقييم لمشارك واحد
    openEvaluationModal([participantId]);
}

function startBulkEvaluation() {
    const selected = Array.from(document.querySelectorAll('.evaluation-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        alert('يرجى اختيار مشارك واحد على الأقل');
        return;
    }
    openEvaluationModal(selected);
}

function createNewEvaluation() {
    const selected = Array.from(document.querySelectorAll('.evaluation-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        alert('يرجى اختيار مشارك واحد على الأقل');
        return;
    }
    openEvaluationModal(selected);
}

function openEvaluationModal(participantIds = null) {
    if (!participantIds) {
        participantIds = Array.from(document.querySelectorAll('.evaluation-checkbox:checked')).map(cb => cb.value);
    }

    if (participantIds.length === 0) {
        alert('يرجى اختيار مشارك واحد على الأقل');
        return;
    }

    // الحصول على CSRF token
    const csrfTokenElement = document.querySelector('meta[name=csrf-token]');
    const csrfToken = csrfTokenElement ? csrfTokenElement.getAttribute('content') : '';

    // تحميل نموذج التقييم
    fetch(`/course/{{ course.id }}/evaluation/form`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            participant_ids: participantIds,
            template_id: document.getElementById('evaluationTemplate').value
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    })
    .then(html => {
        document.getElementById('evaluationModalBody').innerHTML = html;
        new bootstrap.Modal(document.getElementById('evaluationModal')).show();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في تحميل نموذج التقييم: ' + error.message);
    });
}

function printSelectedEvaluations() {
    const selected = Array.from(document.querySelectorAll('.evaluation-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        alert('يرجى اختيار مشارك واحد على الأقل');
        return;
    }
    
    window.open(`/course/{{ course.id }}/evaluation/print?participants=${selected.join(',')}`);
}

function exportToExcel() {
    const selected = Array.from(document.querySelectorAll('.evaluation-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        alert('يرجى اختيار مشارك واحد على الأقل');
        return;
    }
    
    window.location.href = `/course/{{ course.id }}/evaluation/export/excel?participants=${selected.join(',')}`;
}

function exportToPDF() {
    const selected = Array.from(document.querySelectorAll('.evaluation-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        alert('يرجى اختيار مشارك واحد على الأقل');
        return;
    }
    
    window.location.href = `/course/{{ course.id }}/evaluation/export/pdf?participants=${selected.join(',')}`;
}
</script>

{% endblock %}
