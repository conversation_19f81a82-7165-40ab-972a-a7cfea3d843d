/**
 * 🔧 إصلاح JavaScript لضمان تطبيق حجم الخط الصغير لوصف الدورات
 * JavaScript Fix to Ensure Small Font Size for Course Descriptions
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 بدء إصلاح أحجام خطوط وصف الدورات...');
    
    // دالة لإصلاح حجم الخط
    function fixCourseDescriptionFontSize() {
        // البحث عن جميع عناصر وصف الدورات
        const descriptions = document.querySelectorAll(
            '.course-description, ' +
            '.courses-page .course-description, ' +
            '.course-card .course-description, ' +
            'p.course-description, ' +
            '.courses-page .course-card .course-description'
        );
        
        console.log(`🔍 تم العثور على ${descriptions.length} عنصر وصف دورة`);
        
        descriptions.forEach((desc, index) => {
            // تطبيق الأنماط مباشرة
            desc.style.fontSize = '0.55rem';
            desc.style.lineHeight = '1.2';
            desc.style.height = '28px';
            desc.style.maxHeight = '28px';
            desc.style.overflow = 'hidden';
            desc.style.display = '-webkit-box';
            desc.style.webkitLineClamp = '2';
            desc.style.webkitBoxOrient = 'vertical';
            desc.style.textOverflow = 'ellipsis';
            desc.style.color = '#9ca3af';
            desc.style.fontWeight = '400';
            desc.style.marginBottom = '8px';
            desc.style.padding = '0 6px';
            desc.style.textAlign = 'center';
            desc.style.fontFamily = "'Cairo', 'Poppins', sans-serif";
            desc.style.wordWrap = 'break-word';
            desc.style.wordBreak = 'break-word';
            
            // إضافة class للتأكد
            desc.classList.add('description-fixed');
            
            console.log(`✅ تم إصلاح العنصر ${index + 1}: ${desc.textContent.substring(0, 30)}...`);
        });
        
        // إضافة CSS إضافي للتأكد
        addAdditionalCSS();
    }
    
    // دالة لإضافة CSS إضافي
    function addAdditionalCSS() {
        const style = document.createElement('style');
        style.id = 'course-description-fix';
        style.textContent = `
            /* CSS JavaScript إضافي */
            .courses-page .course-card .course-description,
            .courses-page .course-description,
            .course-card .course-description,
            p.course-description,
            .description-fixed {
                font-size: 0.55rem !important;
                line-height: 1.2 !important;
                height: 28px !important;
                max-height: 28px !important;
                overflow: hidden !important;
                display: -webkit-box !important;
                -webkit-line-clamp: 2 !important;
                -webkit-box-orient: vertical !important;
                text-overflow: ellipsis !important;
                color: #9ca3af !important;
                font-weight: 400 !important;
                margin-bottom: 8px !important;
                padding: 0 6px !important;
                text-align: center !important;
                font-family: 'Cairo', 'Poppins', sans-serif !important;
                word-wrap: break-word !important;
                word-break: break-word !important;
            }
            
            /* للهواتف */
            @media (max-width: 767px) {
                .courses-page .course-card .course-description,
                .courses-page .course-description,
                .course-card .course-description,
                p.course-description,
                .description-fixed {
                    font-size: 0.5rem !important;
                    height: 24px !important;
                    line-height: 1.1 !important;
                }
            }
            
            /* للأجهزة اللوحية */
            @media (min-width: 768px) and (max-width: 991px) {
                .courses-page .course-card .course-description,
                .courses-page .course-description,
                .course-card .course-description,
                p.course-description,
                .description-fixed {
                    font-size: 0.52rem !important;
                    height: 26px !important;
                    line-height: 1.1 !important;
                }
            }
        `;
        
        // إزالة أي style مشابه موجود
        const existingStyle = document.getElementById('course-description-fix');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        // إضافة الـ style الجديد
        document.head.appendChild(style);
        console.log('✅ تم إضافة CSS إضافي');
    }
    
    // تشغيل الإصلاح فوراً
    fixCourseDescriptionFontSize();
    
    // إعادة تشغيل الإصلاح بعد تحميل الصور
    window.addEventListener('load', function() {
        setTimeout(fixCourseDescriptionFontSize, 100);
        console.log('🔄 إعادة تطبيق الإصلاح بعد تحميل الصفحة');
    });
    
    // مراقبة التغييرات في DOM
    const observer = new MutationObserver(function(mutations) {
        let shouldFix = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.classList && (
                            node.classList.contains('course-description') ||
                            node.classList.contains('course-card') ||
                            node.querySelector && node.querySelector('.course-description')
                        )) {
                            shouldFix = true;
                        }
                    }
                });
            }
        });
        
        if (shouldFix) {
            setTimeout(fixCourseDescriptionFontSize, 50);
            console.log('🔄 إعادة تطبيق الإصلاح بعد تغيير DOM');
        }
    });
    
    // بدء مراقبة التغييرات
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    // إصلاح دوري كل 2 ثانية للتأكد
    setInterval(function() {
        const descriptions = document.querySelectorAll('.course-description');
        let needsFix = false;
        
        descriptions.forEach(function(desc) {
            const computedStyle = window.getComputedStyle(desc);
            const fontSize = parseFloat(computedStyle.fontSize);
            
            // إذا كان حجم الخط أكبر من 9px (0.55rem تقريباً)
            if (fontSize > 9) {
                needsFix = true;
            }
        });
        
        if (needsFix) {
            fixCourseDescriptionFontSize();
            console.log('🔄 إصلاح دوري تم تطبيقه');
        }
    }, 2000);
    
    console.log('✅ تم تهيئة نظام إصلاح أحجام خطوط وصف الدورات');
});

// دالة للتحقق من حالة الإصلاح
function checkDescriptionFix() {
    const descriptions = document.querySelectorAll('.course-description');
    console.log('📊 تقرير حالة وصف الدورات:');
    
    descriptions.forEach((desc, index) => {
        const computedStyle = window.getComputedStyle(desc);
        const fontSize = computedStyle.fontSize;
        const height = computedStyle.height;
        
        console.log(`العنصر ${index + 1}:`);
        console.log(`  - حجم الخط: ${fontSize}`);
        console.log(`  - الارتفاع: ${height}`);
        console.log(`  - النص: ${desc.textContent.substring(0, 30)}...`);
    });
}

// دالة لفرض الإصلاح يدوياً
function forceDescriptionFix() {
    console.log('🔧 فرض الإصلاح يدوياً...');
    
    const descriptions = document.querySelectorAll(
        '.course-description, ' +
        '.courses-page .course-description, ' +
        '.course-card .course-description, ' +
        'p.course-description'
    );
    
    descriptions.forEach(desc => {
        desc.style.cssText = `
            font-size: 0.55rem !important;
            line-height: 1.2 !important;
            height: 28px !important;
            max-height: 28px !important;
            overflow: hidden !important;
            display: -webkit-box !important;
            -webkit-line-clamp: 2 !important;
            -webkit-box-orient: vertical !important;
            text-overflow: ellipsis !important;
            color: #9ca3af !important;
            font-weight: 400 !important;
            margin-bottom: 8px !important;
            padding: 0 6px !important;
            text-align: center !important;
            font-family: 'Cairo', 'Poppins', sans-serif !important;
        `;
    });
    
    console.log(`✅ تم فرض الإصلاح على ${descriptions.length} عنصر`);
}

// إتاحة الدوال للاستخدام في console
window.checkDescriptionFix = checkDescriptionFix;
window.forceDescriptionFix = forceDescriptionFix;
