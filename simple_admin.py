#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from werkzeug.security import generate_password_hash

print("🔧 إنشاء مدير مباشرة في قاعدة البيانات...")

try:
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('training_system.db')
    cursor = conn.cursor()
    
    # حذف المديرين الحاليين
    cursor.execute("DELETE FROM user WHERE role = 'admin'")
    print("✅ تم حذف المديرين الحاليين")
    
    # إنشاء مدير جديد
    password_hash = generate_password_hash('admin123')
    cursor.execute("""
        INSERT INTO user (username, email, password, role, created_at)
        VALUES (?, ?, ?, ?, datetime('now'))
    """, ('admin', '<EMAIL>', password_hash, 'admin'))
    
    conn.commit()
    print("✅ تم إنشاء المدير بنجاح!")
    
    # التحقق من إنشاء المدير
    cursor.execute("SELECT * FROM user WHERE email = ?", ('<EMAIL>',))
    admin = cursor.fetchone()
    
    if admin:
        print("✅ تم التحقق من وجود المدير:")
        print(f"ID: {admin[0]}")
        print(f"اسم المستخدم: {admin[1]}")
        print(f"البريد: {admin[2]}")
        print(f"الدور: {admin[4]}")
        
        # اختبار كلمة المرور
        from werkzeug.security import check_password_hash
        if check_password_hash(admin[3], 'admin123'):
            print("✅ كلمة المرور صحيحة")
        else:
            print("❌ كلمة المرور غير صحيحة")
    else:
        print("❌ فشل في إنشاء المدير")
    
    conn.close()
    
except Exception as e:
    print(f"❌ خطأ: {str(e)}")

print("\n🌐 يمكنك الآن تسجيل الدخول على:")
print("http://127.0.0.1:5000/login")
print("📧 البريد: <EMAIL>")
print("🔑 كلمة المرور: admin123")
