{% extends "layout.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="sidebar-sticky">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('courses') }}">
                            <i class="fas fa-book"></i> الدورات
                        </a>
                    </li>
                    {% if current_user.role == 'admin' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('users') }}">
                            <i class="fas fa-users"></i> المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('personal_data_list') }}">
                            <i class="fas fa-id-card"></i> البيانات الشخصية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('reference_tables') }}">
                            <i class="fas fa-table"></i> الجداول الترميزية
                        </a>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-calendar-alt"></i> الجدول الزمني
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('settings') }}">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تصنيفات الدورات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ url_for('add_course_category') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-plus"></i> إضافة تصنيف جديد
                    </a>
                </div>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="table-responsive">
                <table class="table table-striped table-sm">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>التصنيف</th>
                            <th>الرمز</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ category.name }}</td>
                            <td>{{ category.code }}</td>
                            <td>
                                <a href="{{ url_for('edit_course_category', course_category_id=category.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{{ url_for('delete_course_category', course_category_id=category.id) }}" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذا التصنيف؟');">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </main>
    </div>
</div>
{% endblock %}
