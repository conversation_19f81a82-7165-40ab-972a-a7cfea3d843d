#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ترقية قاعدة البيانات لنظام إدارة المستخدمين والصلاحيات
Database Migration for Users and Permissions Management System
"""

import sqlite3
import os
from datetime import datetime

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    if os.path.exists('training_system.db'):
        backup_name = f'training_system_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        import shutil
        shutil.copy2('training_system.db', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    return None

def migrate_user_table():
    """ترقية جدول المستخدمين"""
    conn = sqlite3.connect('training_system.db')
    cursor = conn.cursor()
    
    try:
        # فحص الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(user)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # إضافة الأعمدة الجديدة إذا لم تكن موجودة
        new_columns = [
            ('first_name', 'VARCHAR(50)'),
            ('last_name', 'VARCHAR(50)'),
            ('phone', 'VARCHAR(20)'),
            ('department', 'VARCHAR(100)'),
            ('position', 'VARCHAR(100)'),
            ('is_active', 'BOOLEAN DEFAULT 1'),
            ('last_login', 'DATETIME'),
            ('login_attempts', 'INTEGER DEFAULT 0'),
            ('locked_until', 'DATETIME'),
            ('notes', 'TEXT')
        ]
        
        for column_name, column_type in new_columns:
            if column_name not in columns:
                cursor.execute(f'ALTER TABLE user ADD COLUMN {column_name} {column_type}')
                print(f"✅ تم إضافة العمود: {column_name}")
        
        # تحديث المستخدمين الموجودين
        cursor.execute("UPDATE user SET is_active = 1 WHERE is_active IS NULL")
        cursor.execute("UPDATE user SET login_attempts = 0 WHERE login_attempts IS NULL")
        
        conn.commit()
        print("✅ تم ترقية جدول المستخدمين")
        
    except Exception as e:
        print(f"❌ خطأ في ترقية جدول المستخدمين: {e}")
        conn.rollback()
    finally:
        conn.close()

def create_permissions_tables():
    """إنشاء جداول الصلاحيات والأدوار"""
    conn = sqlite3.connect('training_system.db')
    cursor = conn.cursor()
    
    try:
        # جدول الصلاحيات
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS permission (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) UNIQUE NOT NULL,
            display_name VARCHAR(200) NOT NULL,
            description TEXT,
            category VARCHAR(50) NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # جدول الأدوار
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS role (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(50) UNIQUE NOT NULL,
            display_name VARCHAR(100) NOT NULL,
            description TEXT,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            is_system_role BOOLEAN NOT NULL DEFAULT 0,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # جدول ربط المستخدمين بالأدوار
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_role (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            role_id INTEGER NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            assigned_by INTEGER,
            assigned_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME,
            FOREIGN KEY (user_id) REFERENCES user (id),
            FOREIGN KEY (role_id) REFERENCES role (id),
            FOREIGN KEY (assigned_by) REFERENCES user (id)
        )
        ''')
        
        # جدول الصلاحيات المباشرة للمستخدمين
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_permission (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            permission_name VARCHAR(100) NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            granted_by INTEGER,
            granted_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME,
            FOREIGN KEY (user_id) REFERENCES user (id),
            FOREIGN KEY (granted_by) REFERENCES user (id)
        )
        ''')
        
        # جدول ربط الأدوار بالصلاحيات
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS role_permission (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            role_id INTEGER NOT NULL,
            permission_name VARCHAR(100) NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (role_id) REFERENCES role (id)
        )
        ''')
        
        # جدول جلسات المستخدمين
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_session (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            session_token VARCHAR(255) UNIQUE NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            last_activity DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            FOREIGN KEY (user_id) REFERENCES user (id)
        )
        ''')
        
        # جدول سجل العمليات
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS audit_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action VARCHAR(100) NOT NULL,
            resource_type VARCHAR(50),
            resource_id INTEGER,
            details TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES user (id)
        )
        ''')
        
        conn.commit()
        print("✅ تم إنشاء جداول الصلاحيات والأدوار")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جداول الصلاحيات: {e}")
        conn.rollback()
    finally:
        conn.close()

def create_indexes():
    """إنشاء فهارس لتحسين الأداء"""
    conn = sqlite3.connect('training_system.db')
    cursor = conn.cursor()
    
    try:
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_user_email ON user(email)',
            'CREATE INDEX IF NOT EXISTS idx_user_username ON user(username)',
            'CREATE INDEX IF NOT EXISTS idx_user_role ON user(role)',
            'CREATE INDEX IF NOT EXISTS idx_user_is_active ON user(is_active)',
            'CREATE INDEX IF NOT EXISTS idx_user_role_user_id ON user_role(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_role_role_id ON user_role(role_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_permission_user_id ON user_permission(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_role_permission_role_id ON role_permission(role_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_session_user_id ON user_session(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_session_token ON user_session(session_token)',
            'CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_audit_log_action ON audit_log(action)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        print("✅ تم إنشاء الفهارس")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفهارس: {e}")
        conn.rollback()
    finally:
        conn.close()

def verify_migration():
    """التحقق من نجاح الترقية"""
    conn = sqlite3.connect('training_system.db')
    cursor = conn.cursor()
    
    try:
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = [
            'user', 'permission', 'role', 'user_role', 
            'user_permission', 'role_permission', 'user_session', 'audit_log'
        ]
        
        print("\n📋 فحص الجداول:")
        for table in required_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table}: {count} سجل")
            else:
                print(f"   ❌ {table}: غير موجود")
        
        # فحص أعمدة جدول المستخدمين
        cursor.execute("PRAGMA table_info(user)")
        user_columns = [column[1] for column in cursor.fetchall()]
        
        required_user_columns = [
            'id', 'username', 'email', 'password', 'role', 'created_at',
            'first_name', 'last_name', 'phone', 'department', 'position',
            'is_active', 'last_login', 'login_attempts', 'locked_until', 'notes'
        ]
        
        print("\n📋 فحص أعمدة جدول المستخدمين:")
        for column in required_user_columns:
            if column in user_columns:
                print(f"   ✅ {column}")
            else:
                print(f"   ❌ {column}: غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من الترقية: {e}")
        return False
    finally:
        conn.close()

def main():
    """الدالة الرئيسية للترقية"""
    print("🚀 بدء ترقية قاعدة البيانات لنظام إدارة المستخدمين والصلاحيات")
    print("=" * 70)
    
    try:
        # إنشاء نسخة احتياطية
        backup_file = backup_database()
        
        # ترقية جدول المستخدمين
        print("\n📝 ترقية جدول المستخدمين...")
        migrate_user_table()
        
        # إنشاء جداول الصلاحيات
        print("\n🔐 إنشاء جداول الصلاحيات والأدوار...")
        create_permissions_tables()
        
        # إنشاء الفهارس
        print("\n📊 إنشاء فهارس الأداء...")
        create_indexes()
        
        # التحقق من الترقية
        print("\n🔍 التحقق من نجاح الترقية...")
        if verify_migration():
            print("\n🎉 تم إكمال ترقية قاعدة البيانات بنجاح!")
            print(f"💾 النسخة الاحتياطية: {backup_file}")
            print("\n📝 الخطوة التالية: تشغيل النظام لتهيئة الصلاحيات والأدوار")
        else:
            print("\n❌ فشلت ترقية قاعدة البيانات")
            
    except Exception as e:
        print(f"❌ خطأ عام في الترقية: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
