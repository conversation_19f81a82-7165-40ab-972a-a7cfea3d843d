#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
دليل شامل لنظام إدارة الصلاحيات
Complete Guide to Permissions Management System
"""

# ========================================
# 1. كيفية تحديد الصلاحيات للمستخدمين
# ========================================

"""
الصلاحيات الموجودة في النظام:

📋 إدارة المستخدمين:
- users.view: عرض المستخدمين
- users.create: إنشاء مستخدمين جدد
- users.edit: تعديل بيانات المستخدمين
- users.delete: حذف المستخدمين
- users.manage_roles: إدارة أدوار المستخدمين
- users.reset_password: إعادة تعيين كلمات المرور

📚 إدارة الدورات:
- courses.view: عرض الدورات
- courses.create: إنشاء دورات جديدة
- courses.edit: تعديل الدورات
- courses.delete: حذف الدورات
- courses.manage_participants: إدارة المشاركين

👥 إدارة بيانات الأشخاص:
- persons.view: عرض بيانات الأشخاص
- persons.create: إضافة بيانات أشخاص جدد
- persons.edit: تعديل بيانات الأشخاص
- persons.delete: حذف بيانات الأشخاص

📊 التقارير:
- reports.view: عرض التقارير
- reports.create: إنشاء تقارير جديدة
- reports.export: تصدير التقارير
- reports.financial: التقارير المالية

🔧 إعدادات النظام:
- system.settings: إدارة إعدادات النظام
- system.logs: عرض سجلات النظام
- backup.create: إنشاء نسخ احتياطية
"""

# ========================================
# 2. الأدوار الافتراضية وصلاحياتها
# ========================================

"""
👑 admin (مدير النظام):
- جميع الصلاحيات
- يمكنه الوصول لكل شيء في النظام

👨‍💼 manager (مدير):
- users.view, users.edit
- courses.view, courses.create, courses.edit
- persons.view, persons.create, persons.edit
- reports.view, reports.create, reports.export
- backup.create

👨‍🏫 trainer (مدرب):
- courses.view, courses.edit, courses.manage_participants
- persons.view, persons.create, persons.edit
- reports.view

📝 data_entry (مدخل بيانات):
- courses.view
- persons.view, persons.create, persons.edit
- reference_tables.view

👁️ viewer (مشاهد):
- courses.view
- persons.view
- reports.view
- reference_tables.view
"""

# ========================================
# 3. كيفية استخدام الصلاحيات في الكود
# ========================================

# في الـ Routes (app.py):
"""
from permissions_manager import require_permission, require_role

# حماية صفحة بصلاحية معينة
@app.route('/admin/users')
@login_required
@require_permission('users.view')
def users_list():
    return render_template('users.html')

# حماية صفحة بدور معين
@app.route('/admin/settings')
@login_required
@require_role('admin')
def admin_settings():
    return render_template('settings.html')
"""

# ========================================
# 4. كيفية إخفاء/إظهار العناصر في Templates
# ========================================

template_examples = """
<!-- في أي template HTML -->

<!-- إخفاء/إظهار زر بناءً على صلاحية -->
{% if current_user.has_permission('users.create') %}
    <button class="btn btn-primary">إضافة مستخدم</button>
{% endif %}

<!-- إخفاء/إظهار قسم كامل بناءً على الدور -->
{% if current_user.role == 'admin' %}
    <div class="admin-panel">
        <h3>لوحة المدير</h3>
        <!-- محتوى خاص بالمديرين -->
    </div>
{% endif %}

<!-- إخفاء/إظهار عنصر بناءً على صلاحيات متعددة -->
{% if current_user.has_permission('reports.view') and current_user.has_permission('reports.export') %}
    <a href="/reports/export" class="btn btn-success">تصدير التقرير</a>
{% endif %}

<!-- قائمة منسدلة تظهر حسب الصلاحيات -->
<div class="dropdown">
    <button class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
        الإجراءات
    </button>
    <ul class="dropdown-menu">
        {% if current_user.has_permission('users.edit') %}
            <li><a class="dropdown-item" href="#" onclick="editUser()">تعديل</a></li>
        {% endif %}
        
        {% if current_user.has_permission('users.delete') %}
            <li><a class="dropdown-item" href="#" onclick="deleteUser()">حذف</a></li>
        {% endif %}
        
        {% if current_user.has_permission('users.reset_password') %}
            <li><a class="dropdown-item" href="#" onclick="resetPassword()">إعادة تعيين كلمة المرور</a></li>
        {% endif %}
    </ul>
</div>

<!-- شريط جانبي يتغير حسب الصلاحيات -->
<nav class="sidebar">
    <ul class="nav flex-column">
        <!-- الصفحة الرئيسية - متاحة للجميع -->
        <li class="nav-item">
            <a class="nav-link" href="/dashboard">
                <i class="fas fa-home"></i> الصفحة الرئيسية
            </a>
        </li>
        
        <!-- إدارة المستخدمين - فقط لمن لديه صلاحية -->
        {% if current_user.has_permission('users.view') %}
        <li class="nav-item">
            <a class="nav-link" href="/admin/users">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
        </li>
        {% endif %}
        
        <!-- إدارة الدورات - فقط لمن لديه صلاحية -->
        {% if current_user.has_permission('courses.view') %}
        <li class="nav-item">
            <a class="nav-link" href="/courses">
                <i class="fas fa-graduation-cap"></i> إدارة الدورات
            </a>
        </li>
        {% endif %}
        
        <!-- التقارير - فقط لمن لديه صلاحية -->
        {% if current_user.has_permission('reports.view') %}
        <li class="nav-item">
            <a class="nav-link" href="/reports">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
        </li>
        {% endif %}
        
        <!-- إعدادات النظام - فقط للمديرين -->
        {% if current_user.role == 'admin' %}
        <li class="nav-item">
            <a class="nav-link" href="/admin/settings">
                <i class="fas fa-cog"></i> إعدادات النظام
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
"""

# ========================================
# 5. كيفية فحص الصلاحيات في JavaScript
# ========================================

javascript_examples = """
<script>
// تمرير صلاحيات المستخدم من الخادم إلى JavaScript
var userPermissions = {{ current_user.get_permissions_list() | tojson }};
var userRole = "{{ current_user.role }}";

// دالة للتحقق من وجود صلاحية
function hasPermission(permission) {
    return userPermissions.includes(permission);
}

// دالة للتحقق من الدور
function hasRole(role) {
    return userRole === role;
}

// مثال: إخفاء/إظهار أزرار بناءً على الصلاحيات
$(document).ready(function() {
    // إخفاء زر الحذف إذا لم يكن لديه صلاحية
    if (!hasPermission('users.delete')) {
        $('.btn-delete').hide();
    }
    
    // إخفاء زر التعديل إذا لم يكن لديه صلاحية
    if (!hasPermission('users.edit')) {
        $('.btn-edit').hide();
    }
    
    // إخفاء قسم الإعدادات إذا لم يكن مديراً
    if (!hasRole('admin')) {
        $('.admin-settings').hide();
    }
});

// مثال: التحقق من الصلاحية قبل تنفيذ عملية
function deleteUser(userId) {
    // التحقق من الصلاحية أولاً
    if (!hasPermission('users.delete')) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'ليس لديك صلاحية لحذف المستخدمين'
        });
        return;
    }
    
    // إذا كان لديه صلاحية، تنفيذ العملية
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'سيتم حذف المستخدم نهائياً',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // تنفيذ عملية الحذف
            fetch('/admin/users/' + userId + '/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', 'تم حذف المستخدم بنجاح', 'success');
                    location.reload();
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            });
        }
    });
}

// مثال: إنشاء قائمة سياق (Context Menu) حسب الصلاحيات
function showContextMenu(userId, event) {
    var menuItems = [];
    
    // إضافة عناصر القائمة حسب الصلاحيات
    if (hasPermission('users.view')) {
        menuItems.push({
            text: 'عرض التفاصيل',
            icon: 'fas fa-eye',
            action: function() { viewUser(userId); }
        });
    }
    
    if (hasPermission('users.edit')) {
        menuItems.push({
            text: 'تعديل',
            icon: 'fas fa-edit',
            action: function() { editUser(userId); }
        });
    }
    
    if (hasPermission('users.reset_password')) {
        menuItems.push({
            text: 'إعادة تعيين كلمة المرور',
            icon: 'fas fa-key',
            action: function() { resetPassword(userId); }
        });
    }
    
    if (hasPermission('users.delete')) {
        menuItems.push({
            text: 'حذف',
            icon: 'fas fa-trash',
            action: function() { deleteUser(userId); }
        });
    }
    
    // عرض القائمة
    showContextMenuAt(event.pageX, event.pageY, menuItems);
}
</script>
"""

# ========================================
# 6. كيفية إضافة صلاحيات جديدة
# ========================================

def add_new_permissions():
    """
    لإضافة صلاحيات جديدة:
    
    1. أضف الصلاحية في permissions_manager.py في PERMISSIONS:
    
    PERMISSIONS = {
        # ... الصلاحيات الموجودة
        'new_module.view': 'عرض الوحدة الجديدة',
        'new_module.create': 'إنشاء في الوحدة الجديدة',
        'new_module.edit': 'تعديل في الوحدة الجديدة',
        'new_module.delete': 'حذف من الوحدة الجديدة',
    }
    
    2. أضف الصلاحيات للأدوار المناسبة في DEFAULT_ROLES:
    
    'admin': {
        'permissions': [
            # ... الصلاحيات الموجودة
            'new_module.view',
            'new_module.create',
            'new_module.edit',
            'new_module.delete'
        ]
    }
    
    3. استخدم الصلاحيات في الكود:
    
    @app.route('/new-module')
    @login_required
    @require_permission('new_module.view')
    def new_module():
        return render_template('new_module.html')
    
    4. استخدم الصلاحيات في Templates:
    
    {% if current_user.has_permission('new_module.create') %}
        <button>إضافة جديد</button>
    {% endif %}
    """
    pass

# ========================================
# 7. مثال عملي كامل
# ========================================

practical_example = """
<!-- مثال: صفحة إدارة الدورات مع الصلاحيات -->

<div class="container">
    <div class="row">
        <div class="col-12">
            <!-- العنوان والأزرار الرئيسية -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>إدارة الدورات</h2>
                
                <!-- أزرار تظهر حسب الصلاحيات -->
                <div class="btn-group">
                    {% if current_user.has_permission('courses.create') %}
                    <button class="btn btn-primary" onclick="addCourse()">
                        <i class="fas fa-plus"></i> إضافة دورة جديدة
                    </button>
                    {% endif %}
                    
                    {% if current_user.has_permission('courses.import') %}
                    <button class="btn btn-info" onclick="importCourses()">
                        <i class="fas fa-upload"></i> استيراد دورات
                    </button>
                    {% endif %}
                    
                    {% if current_user.has_permission('reports.export') %}
                    <button class="btn btn-success" onclick="exportCourses()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                    {% endif %}
                </div>
            </div>
            
            <!-- جدول الدورات -->
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>اسم الدورة</th>
                            <th>المدرب</th>
                            <th>تاريخ البداية</th>
                            <th>عدد المشاركين</th>
                            
                            <!-- عمود الحالة المالية - يظهر فقط لمن لديه صلاحية -->
                            {% if current_user.has_permission('courses.view_financial') %}
                            <th>الحالة المالية</th>
                            {% endif %}
                            
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for course in courses %}
                        <tr>
                            <td>{{ course.name }}</td>
                            <td>{{ course.trainer_name }}</td>
                            <td>{{ course.start_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ course.participants_count }}</td>
                            
                            <!-- البيانات المالية - تظهر فقط لمن لديه صلاحية -->
                            {% if current_user.has_permission('courses.view_financial') %}
                            <td>
                                <span class="badge bg-{{ 'success' if course.is_paid else 'warning' }}">
                                    {{ 'مدفوعة' if course.is_paid else 'غير مدفوعة' }}
                                </span>
                            </td>
                            {% endif %}
                            
                            <td>
                                <!-- أزرار الإجراءات حسب الصلاحيات -->
                                <div class="btn-group btn-group-sm">
                                    {% if current_user.has_permission('courses.view') %}
                                    <button class="btn btn-info" onclick="viewCourse({{ course.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% endif %}
                                    
                                    {% if current_user.has_permission('courses.edit') %}
                                    <button class="btn btn-warning" onclick="editCourse({{ course.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% endif %}
                                    
                                    {% if current_user.has_permission('courses.manage_participants') %}
                                    <button class="btn btn-success" onclick="manageParticipants({{ course.id }})">
                                        <i class="fas fa-users"></i>
                                    </button>
                                    {% endif %}
                                    
                                    {% if current_user.has_permission('courses.delete') %}
                                    <button class="btn btn-danger" onclick="deleteCourse({{ course.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- قسم الإحصائيات - يظهر حسب الصلاحيات -->
            {% if current_user.has_permission('reports.view') %}
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h5>إجمالي الدورات</h5>
                            <h3>{{ total_courses }}</h3>
                        </div>
                    </div>
                </div>
                
                {% if current_user.has_permission('courses.view_financial') %}
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h5>الإيرادات</h5>
                            <h3>{{ total_revenue }} ريال</h3>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// صلاحيات المستخدم
var userPermissions = {{ current_user.get_permissions_list() | tojson }};

function hasPermission(permission) {
    return userPermissions.includes(permission);
}

function deleteCourse(courseId) {
    if (!hasPermission('courses.delete')) {
        alert('ليس لديك صلاحية لحذف الدورات');
        return;
    }
    
    if (confirm('هل أنت متأكد من حذف هذه الدورة؟')) {
        // تنفيذ عملية الحذف
        fetch('/courses/' + courseId + '/delete', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('فشل في حذف الدورة: ' + data.message);
            }
        });
    }
}
</script>
"""

if __name__ == '__main__':
    print("🔐 دليل نظام إدارة الصلاحيات")
    print("=" * 50)
    print("هذا الملف يحتوي على شرح شامل لكيفية:")
    print("1. تحديد الصلاحيات للمستخدمين")
    print("2. إخفاء/إظهار الشاشات والأزرار")
    print("3. استخدام الصلاحيات في الكود")
    print("4. فحص الصلاحيات في JavaScript")
    print("5. إضافة صلاحيات جديدة")
    print("\nراجع الكود أعلاه للتفاصيل الكاملة")
