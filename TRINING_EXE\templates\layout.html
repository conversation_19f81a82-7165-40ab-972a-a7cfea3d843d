<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>{{ title }} - نظام التدريب والتأهيل</title>
    <!-- Bootstrap RTL CSS - محلي -->
    <link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
    <!-- Font Awesome - محلي -->
    <link rel="stylesheet" href="{{ url_for('static', filename='libs/fontawesome/all.min.css') }}">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-style.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark" style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100px;
            padding: 15px 0;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
            position: relative;
            z-index: 1050;
            overflow: visible;
        ">
            <div class="container-fluid px-4">
                <!-- العلامة التجارية -->
                <a class="navbar-brand" href="{{ url_for('index') }}" style="
                    font-size: 2.2rem;
                    font-weight: 900;
                    color: white !important;
                    text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
                    margin-left: 20px;
                    letter-spacing: 1px;
                ">
                    <i class="fas fa-graduation-cap me-3" style="
                        color: #FFD700;
                        font-size: 2.8rem;
                        text-shadow: 3px 3px 6px rgba(0,0,0,0.6);
                        filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.9));
                        transform: rotate(-10deg);
                        display: inline-block;
                    "></i>نظام التدريب والتأهيل
                </a>

                <!-- التاريخ والوقت في الوسط -->
                <div class="navbar-datetime d-none d-lg-flex flex-column align-items-center mx-auto" style="
                    background: rgba(255, 255, 255, 0.15);
                    backdrop-filter: blur(10px);
                    border-radius: 15px;
                    padding: 10px 20px;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                ">
                    <div class="date-section d-flex align-items-center mb-2">
                        <i class="fas fa-calendar-alt me-3" style="color: #FFD700; font-size: 1.2rem;"></i>
                        <span id="hijri-date" class="date-text" style="
                            color: white;
                            font-size: 1.1rem;
                            font-weight: 700;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
                            letter-spacing: 0.5px;
                        ">17 ذو الحجة 1446هـ</span>
                    </div>
                    <div class="time-section d-flex align-items-center">
                        <i class="fas fa-clock me-3" style="color: #FFD700; font-size: 1.2rem;"></i>
                        <span id="current-time" class="time-text" style="
                            color: white;
                            font-size: 1.3rem;
                            font-weight: 800;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
                            letter-spacing: 0.5px;
                        ">12:00:00 م</span>
                    </div>
                </div>

                <!-- زر القائمة للموبايل -->
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" style="
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 10px;
                ">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- القوائم -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('index') }}" style="
                                color: white !important;
                                font-weight: 600;
                                font-size: 1.1rem;
                                padding: 10px 15px;
                                border-radius: 10px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='transparent'">الرئيسية</a>
                        </li>
                        {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}" style="
                                color: white !important;
                                font-weight: 600;
                                font-size: 1.1rem;
                                padding: 10px 15px;
                                border-radius: 10px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.background='transparent'">لوحة التحكم</a>
                        </li>
                        {% endif %}
                    </ul>

                    <!-- منطقة المستخدم المحسنة -->
                    <ul class="navbar-nav">
                        {% if current_user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" style="
                                background: rgba(255, 255, 255, 0.2);
                                backdrop-filter: blur(10px);
                                border-radius: 25px;
                                padding: 12px 20px;
                                color: white !important;
                                font-weight: 800;
                                font-size: 1.2rem;
                                border: 2px solid rgba(255, 255, 255, 0.4);
                                transition: all 0.3s ease;
                                text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
                                min-width: 220px;
                                justify-content: center;
                                letter-spacing: 0.5px;
                            " onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
                                <div class="d-flex align-items-center">
                                    <div style="
                                        width: 40px;
                                        height: 40px;
                                        background: linear-gradient(135deg, #FFD700, #FFA500);
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        margin-left: 12px;
                                        box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
                                        border: 2px solid rgba(255, 255, 255, 0.3);
                                    ">
                                        <i class="fas fa-user" style="color: white; font-size: 1.2rem; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);"></i>
                                    </div>
                                    <span>{{ current_user.username }}</span>
                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" style="
                                background: rgba(255, 255, 255, 0.98);
                                backdrop-filter: blur(20px);
                                border: 2px solid rgba(255, 255, 255, 0.4);
                                border-radius: 20px;
                                box-shadow: 0 20px 45px rgba(0, 0, 0, 0.25);
                                padding: 20px 0;
                                min-width: 250px;
                                margin-top: 12px;
                                z-index: 1060;
                                position: absolute;
                                top: 100%;
                                right: 0;
                            ">
                                <li>
                                    <a class="dropdown-item d-flex align-items-center" href="#" style="
                                        padding: 15px 25px;
                                        color: #333;
                                        font-weight: 700;
                                        font-size: 1.1rem;
                                        border-radius: 12px;
                                        margin: 0 12px;
                                        transition: all 0.3s ease;
                                        letter-spacing: 0.5px;
                                    " onmouseover="this.style.background='rgba(102, 126, 234, 0.15)'; this.style.color='#667eea'; this.style.transform='translateX(5px)'" onmouseout="this.style.background='transparent'; this.style.color='#333'; this.style.transform='translateX(0)'">
                                        <i class="fas fa-user-circle me-3" style="color: #667eea; font-size: 1.4rem;"></i>
                                        الملف الشخصي
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider" style="margin: 10px 15px; border-color: rgba(102, 126, 234, 0.2);"></li>
                                <li>
                                    <a class="dropdown-item d-flex align-items-center" href="{{ url_for('logout') }}" style="
                                        padding: 15px 25px;
                                        color: #dc3545;
                                        font-weight: 700;
                                        font-size: 1.1rem;
                                        border-radius: 12px;
                                        margin: 0 12px;
                                        transition: all 0.3s ease;
                                        letter-spacing: 0.5px;
                                    " onmouseover="this.style.background='rgba(220, 53, 69, 0.15)'; this.style.transform='translateX(5px)'" onmouseout="this.style.background='transparent'; this.style.transform='translateX(0)'">
                                        <i class="fas fa-sign-out-alt me-3" style="font-size: 1.4rem;"></i>
                                        تسجيل الخروج
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('login') }}" style="
                                background: rgba(255, 255, 255, 0.2);
                                backdrop-filter: blur(10px);
                                border-radius: 25px;
                                padding: 12px 25px;
                                color: white !important;
                                font-weight: 600;
                                font-size: 1.1rem;
                                border: 1px solid rgba(255, 255, 255, 0.3);
                                transition: all 0.3s ease;
                            " onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" style="border-radius: 15px; backdrop-filter: blur(10px); box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">جميع الحقوق محفوظة &copy; {{ now.year }} نظام التدريب والتأهيل</p>
        </div>
    </footer>

    <!-- jQuery - محلي -->
    <script src="{{ url_for('static', filename='libs/jquery/jquery-3.6.0.min.js') }}"></script>
    <!-- Bootstrap JS Bundle with Popper - محلي -->
    <script src="{{ url_for('static', filename='libs/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- التاريخ الهجري والساعة -->
    <script>
        // عرض التاريخ الهجري الصحيح
        function getHijriDate() {
            // التاريخ الهجري الحالي (يجب تحديثه يدوياً أو استخدام API)
            return '17 ذو الحجة 1446هـ';
        }

        // عرض الوقت الحالي
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            });

            document.getElementById('current-time').textContent = timeString;
            document.getElementById('hijri-date').textContent = getHijriDate();
        }

        // تحديث الوقت كل ثانية
        setInterval(updateTime, 1000);

        // تحديث فوري عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', updateTime);

        // تأثير التمرير للشريط العلوي
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // التأكد من عمل القائمة المنسدلة
        document.addEventListener('DOMContentLoaded', function() {
            // تفعيل جميع القوائم المنسدلة
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });

            // إضافة معالج نقر يدوي للقائمة المنسدلة
            const dropdownToggle = document.getElementById('navbarDropdown');
            const dropdownMenu = dropdownToggle ? dropdownToggle.nextElementSibling : null;

            if (dropdownToggle && dropdownMenu) {
                // إضافة z-index عالي للقائمة
                dropdownMenu.style.zIndex = '1060';
                dropdownMenu.style.position = 'absolute';

                dropdownToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // إخفاء جميع القوائم الأخرى
                    document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                        if (menu !== dropdownMenu) {
                            menu.classList.remove('show');
                        }
                    });

                    // تبديل حالة القائمة الحالية
                    dropdownMenu.classList.toggle('show');

                    // التأكد من الموضع الصحيح
                    if (dropdownMenu.classList.contains('show')) {
                        const rect = dropdownToggle.getBoundingClientRect();
                        dropdownMenu.style.position = 'fixed';
                        dropdownMenu.style.top = (rect.bottom + 5) + 'px';
                        dropdownMenu.style.right = (window.innerWidth - rect.right) + 'px';
                        dropdownMenu.style.left = 'auto';
                        dropdownMenu.style.zIndex = '99999';
                        dropdownMenu.style.transform = 'none';
                    }
                });

                // إغلاق القائمة عند النقر خارجها
                document.addEventListener('click', function(e) {
                    if (!dropdownToggle.contains(e.target) && !dropdownMenu.contains(e.target)) {
                        dropdownMenu.classList.remove('show');
                    }
                });

                // إغلاق القائمة عند التمرير
                window.addEventListener('scroll', function() {
                    dropdownMenu.classList.remove('show');
                });
            }
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
