from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from sqlalchemy import or_

# سيتم استيراد النماذج من app.py
db = None
PersonData = None

def init_api(app_db, app_models):
    global db, PersonData
    db = app_db
    PersonData = app_models['PersonData']

api_simple = Blueprint('api_simple', __name__)

@api_simple.route('/person_data', methods=['GET'])
@login_required
def get_person_data():
    """
    الحصول على بيانات الأشخاص بتنسيق DataTables
    """
    # الحصول على معلمات DataTables
    draw = request.args.get('draw', type=int, default=1)
    start = request.args.get('start', type=int, default=0)
    length = request.args.get('length', type=int, default=10)
    search_value = request.args.get('search[value]', default='')

    # إنشاء استعلام أساسي
    query = PersonData.query

    # تطبيق البحث إذا تم توفير قيمة البحث
    if search_value:
        query = query.filter(
            or_(
                PersonData.full_name.like(f'%{search_value}%'),
                PersonData.nickname.like(f'%{search_value}%'),
                PersonData.governorate.like(f'%{search_value}%'),
                PersonData.directorate.like(f'%{search_value}%'),
                PersonData.uzla.like(f'%{search_value}%'),
                PersonData.village.like(f'%{search_value}%'),
                PersonData.qualification.like(f'%{search_value}%'),
                PersonData.marital_status.like(f'%{search_value}%'),
                PersonData.job.like(f'%{search_value}%'),
                PersonData.agency.like(f'%{search_value}%'),
                PersonData.work_place.like(f'%{search_value}%'),
                PersonData.national_number.like(f'%{search_value}%'),
                PersonData.military_number.like(f'%{search_value}%'),
                PersonData.phone.like(f'%{search_value}%')
            )
        )

    # الحصول على إجمالي عدد السجلات
    total_records = query.count()
    total_filtered = total_records

    # تطبيق الترتيب والتصفية
    order_column_idx = request.args.get('order[0][column]', type=int, default=1)
    order_dir = request.args.get('order[0][dir]', default='asc')

    # تحديد عمود الترتيب
    if order_column_idx == 1:
        order_column = PersonData.full_name
    elif order_column_idx == 2:
        order_column = PersonData.nickname
    elif order_column_idx == 3:
        order_column = PersonData.age
    elif order_column_idx == 4:
        order_column = PersonData.governorate
    elif order_column_idx == 5:
        order_column = PersonData.directorate
    elif order_column_idx == 6:
        order_column = PersonData.uzla
    elif order_column_idx == 7:
        order_column = PersonData.village
    elif order_column_idx == 8:
        order_column = PersonData.qualification
    elif order_column_idx == 9:
        order_column = PersonData.marital_status
    elif order_column_idx == 10:
        order_column = PersonData.job
    elif order_column_idx == 11:
        order_column = PersonData.agency
    elif order_column_idx == 12:
        order_column = PersonData.work_place
    elif order_column_idx == 13:
        order_column = PersonData.national_number
    elif order_column_idx == 14:
        order_column = PersonData.military_number
    elif order_column_idx == 15:
        order_column = PersonData.phone
    else:
        order_column = PersonData.full_name

    # تطبيق اتجاه الترتيب
    if order_dir == 'desc':
        query = query.order_by(order_column.desc())
    else:
        query = query.order_by(order_column.asc())

    # تطبيق الصفحات
    query = query.offset(start).limit(length)

    # الحصول على البيانات
    records = query.all()

    # تحويل البيانات إلى تنسيق JSON
    data = []
    for record in records:
        data.append({
            'id': record.id,
            'full_name': record.full_name,
            'nickname': record.nickname if record.nickname else '',
            'age': record.age if record.age else '',
            'governorate': record.governorate if record.governorate else '',
            'directorate': record.directorate if record.directorate else '',
            'uzla': record.uzla if record.uzla else '',
            'village': record.village if record.village else '',
            'qualification': record.qualification if record.qualification else '',
            'marital_status': record.marital_status if record.marital_status else '',
            'job': record.job if record.job else '',
            'agency': record.agency if record.agency else '',
            'work_place': record.work_place if record.work_place else '',
            'national_number': record.national_number if record.national_number else '',
            'military_number': record.military_number if record.military_number else '',
            'phone': record.phone if record.phone else ''
        })

    # إعداد استجابة DataTables
    response = {
        'draw': draw,
        'recordsTotal': total_records,
        'recordsFiltered': total_filtered,
        'data': data
    }

    return jsonify(response)

@api_simple.route('/person_data/<int:id>', methods=['GET'])
@login_required
def get_person_data_by_id(id):
    """
    الحصول على بيانات شخص محدد بواسطة المعرف
    """
    record = PersonData.query.get(id)

    if not record:
        return jsonify({
            'success': False,
            'message': 'لم يتم العثور على السجل'
        }), 404

    data = {
        'id': record.id,
        'full_name': record.full_name,
        'nickname': record.nickname if record.nickname else '',
        'age': record.age if record.age else '',
        'governorate': record.governorate if record.governorate else '',
        'directorate': record.directorate if record.directorate else '',
        'uzla': record.uzla if record.uzla else '',
        'village': record.village if record.village else '',
        'qualification': record.qualification if record.qualification else '',
        'marital_status': record.marital_status if record.marital_status else '',
        'job': record.job if record.job else '',
        'agency': record.agency if record.agency else '',
        'work_place': record.work_place if record.work_place else '',
        'national_number': record.national_number if record.national_number else '',
        'military_number': record.military_number if record.military_number else '',
        'phone': record.phone if record.phone else ''
    }

    return jsonify({
        'success': True,
        'data': data
    })
