#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار API التقارير للتأكد من عمل الإحصائيات الجديدة
"""

import requests
import json
from datetime import datetime, timedelta

def test_reports_api():
    """اختبار API التقارير"""
    print("🔍 اختبار API التقارير...")
    
    # إعداد البيانات
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)  # آخر سنة
    
    data = {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d')
    }
    
    try:
        # إرسال الطلب
        response = requests.post('http://localhost:5000/generate_report', data=data)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ API يعمل بشكل صحيح!")
                
                # طباعة الإحصائيات الأساسية
                print(f"📊 إجمالي الدورات: {result.get('totalCourses', 0)}")
                print(f"👥 إجمالي المشاركين: {result.get('totalParticipants', 0)}")
                print(f"🏢 إجمالي المراكز: {result.get('totalCenters', 0)}")
                print(f"🎓 إجمالي الخريجين: {result.get('totalGraduates', 0)}")
                
                # فحص البيانات الجديدة
                chart_data = result.get('chartData', {})
                
                print("\n📈 الإحصائيات الجديدة:")
                
                # 1. مسارات التدريب
                course_paths = chart_data.get('coursePathStats', [])
                print(f"🛤️ مسارات التدريب: {len(course_paths)} مسار")
                for path in course_paths[:3]:
                    print(f"   - {path.get('name', 'غير محدد')}: {path.get('count', 0)} دورة")
                
                # 2. الجهات
                agencies = chart_data.get('centerAgencyStats', [])
                print(f"🏛️ الجهات: {len(agencies)} جهة")
                for agency in agencies[:3]:
                    print(f"   - {agency.get('name', 'غير محدد')}: {agency.get('count', 0)} مركز")
                
                # 3. المواقع
                locations = chart_data.get('courseLocationStats', [])
                print(f"📍 المواقع: {len(locations)} موقع")
                for location in locations[:3]:
                    print(f"   - {location.get('name', 'غير محدد')}: {location.get('count', 0)} دورة")
                
                # 4. أنواع المشاركين
                participant_types = chart_data.get('participantTypeStats', [])
                print(f"👤 أنواع المشاركين: {len(participant_types)} نوع")
                for ptype in participant_types[:3]:
                    print(f"   - {ptype.get('name', 'غير محدد')}: {ptype.get('count', 0)} مشارك")
                
                # 5. تصنيفات القوة
                force_classifications = chart_data.get('forceClassificationStats', [])
                print(f"🛡️ تصنيفات القوة: {len(force_classifications)} تصنيف")
                for force in force_classifications[:3]:
                    print(f"   - {force.get('name', 'غير محدد')}: {force.get('count', 0)} مشارك")
                
                print("\n✅ جميع البيانات متوفرة!")
                
            else:
                print(f"❌ خطأ في API: {result.get('message', 'خطأ غير معروف')}")
                
        else:
            print(f"❌ خطأ في الاتصال: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_reports_api()
