{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<style>
    .control-center {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 40px rgba(102, 126, 234, 0.3);
    }

    .stats-overview {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 20px;
        margin-top: 20px;
        backdrop-filter: blur(10px);
    }

    .category-card {
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: none;
    }

    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .new-people-card {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border-left: 6px solid #00d4aa;
    }

    .existing-available-card {
        background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
        border-left: 6px solid #667eea;
    }

    .already-participants-card {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        border-left: 6px solid #ff6b6b;
    }

    .action-button {
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: bold;
        border: none;
        transition: all 0.3s ease;
        margin: 5px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .btn-magic {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        font-size: 1.1rem;
        padding: 15px 40px;
    }

    .btn-magic:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-new {
        background: linear-gradient(45deg, #00d4aa, #01a3a4);
        color: white;
    }

    .btn-existing {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
    }

    .btn-review {
        background: linear-gradient(45deg, #ff6b6b, #ffa726);
        color: white;
    }

    .person-item {
        background: white;
        border-radius: 12px;
        padding: 15px;
        margin: 8px 0;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #28a745;
        transition: all 0.3s ease;
    }

    .person-item:hover {
        transform: translateX(5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
    }

    .corrected-name {
        background: linear-gradient(45deg, #ffeaa7, #fab1a0);
        border-left-color: #e17055;
    }

    .preview-modal .modal-dialog {
        max-width: 80%;
    }

    .execution-progress {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        display: none;
    }

    .progress-step {
        display: flex;
        align-items: center;
        margin: 10px 0;
        padding: 10px;
        border-radius: 10px;
        background: #f8f9fa;
    }

    .progress-step.active {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
    }

    .progress-step.completed {
        background: linear-gradient(45deg, #00d4aa, #01a3a4);
        color: white;
    }

    .success-summary {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border-radius: 20px;
        padding: 30px;
        margin: 30px 0;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        display: none;
    }

    .count-badge {
        background: rgba(255, 255, 255, 0.9);
        color: #333;
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 1.1rem;
        margin: 0 10px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    }

    .file-info {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- مركز التحكم الذكي -->
    <div class="control-center">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    <i class="fas fa-brain"></i> مركز التحكم الذكي
                </h1>
                <h3 class="mb-2">📚 {{ course.title }}</h3>

                <div class="file-info">
                    <i class="fas fa-file-excel"></i> <strong>الملف:</strong> {{ filename }}
                    <span class="mx-3">|</span>
                    <i class="fas fa-users"></i> <strong>المشاركين الحاليين:</strong> {{ current_participants_count }}
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-overview">
                    <h4 class="text-center mb-3">📊 إحصائيات التحليل</h4>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="count-badge">{{ results.statistics.total_processed }}</div>
                            <small>إجمالي الأسماء</small>
                        </div>
                        <div class="col-6">
                            <div class="count-badge">{{ results.statistics.corrected_count }}</div>
                            <small>تم تصحيحها</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإضافة الذكية السريعة -->
        <div class="text-center mt-4">
            <button class="btn btn-magic action-button" id="smartAddBtn">
                <i class="fas fa-magic"></i> إضافة ذكية شاملة
                <span class="badge bg-light text-dark ms-2">
                    {{ results.statistics.new_count + results.statistics.existing_available_count }} شخص
                </span>
            </button>
            <a href="/course/{{ course.id }}/import_results_detailed" class="btn btn-outline-primary ms-3">
                <i class="fas fa-table"></i> عرض الجدول المفصل
            </a>
        </div>
    </div>

    <!-- كروت التصنيف الذكي -->
    <div class="row">
        <!-- الأشخاص الجدد -->
        <div class="col-lg-4">
            <div class="card category-card new-people-card">
                <div class="card-header bg-transparent border-0">
                    <h4 class="text-success mb-0">
                        <i class="fas fa-user-plus"></i> أشخاص جدد
                        <span class="badge bg-success ms-2">{{ results.statistics.new_count }}</span>
                    </h4>
                    <small class="text-muted">سيتم إضافتهم لقاعدة البيانات والدورة</small>
                </div>
                <div class="card-body">
                    {% if results.new_people %}
                        <div class="mb-3">
                            {% for person in results.new_people[:3] %}
                            <div class="person-item {% if person.original_name %}corrected-name{% endif %}">
                                <strong>{{ person.name }}</strong>
                                {% if person.original_name %}
                                <br><small class="text-muted">الأصلي: {{ person.original_name }}</small>
                                {% endif %}
                            </div>
                            {% endfor %}
                            {% if results.new_people|length > 3 %}
                            <p class="text-center text-muted">
                                <i class="fas fa-ellipsis-h"></i> و {{ results.new_people|length - 3 }} آخرين
                            </p>
                            {% endif %}
                        </div>
                        <div class="text-center">
                            <button class="btn btn-new action-button" onclick="previewCategory('new_people')">
                                <i class="fas fa-eye"></i> معاينة الكل
                            </button>
                            <button class="btn btn-new action-button" onclick="addCategory('new_people')">
                                <i class="fas fa-plus"></i> إضافة ({{ results.statistics.new_count }})
                            </button>
                        </div>
                    {% else %}
                        <p class="text-center text-muted">لا توجد أشخاص جدد</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- الموجودين والمتاحين -->
        <div class="col-lg-4">
            <div class="card category-card existing-available-card">
                <div class="card-header bg-transparent border-0">
                    <h4 class="text-primary mb-0">
                        <i class="fas fa-user-check"></i> موجودين ومتاحين
                        <span class="badge bg-primary ms-2">{{ results.statistics.existing_available_count }}</span>
                    </h4>
                    <small class="text-muted">موجودين في قاعدة البيانات، سيتم إضافتهم للدورة فقط</small>
                </div>
                <div class="card-body">
                    {% if results.existing_available %}
                        <div class="mb-3">
                            {% for person in results.existing_available[:3] %}
                            <div class="person-item {% if person.original_name %}corrected-name{% endif %}">
                                <strong>{{ person.name }}</strong>
                                {% if person.job %}
                                <br><small class="text-muted">{{ person.job }}</small>
                                {% endif %}
                                {% if person.original_name %}
                                <br><small class="text-warning">الأصلي: {{ person.original_name }}</small>
                                {% endif %}
                            </div>
                            {% endfor %}
                            {% if results.existing_available|length > 3 %}
                            <p class="text-center text-muted">
                                <i class="fas fa-ellipsis-h"></i> و {{ results.existing_available|length - 3 }} آخرين
                            </p>
                            {% endif %}
                        </div>
                        <div class="text-center">
                            <button class="btn btn-existing action-button" onclick="previewCategory('existing_available')">
                                <i class="fas fa-eye"></i> معاينة الكل
                            </button>
                            <button class="btn btn-existing action-button" onclick="addCategory('existing_available')">
                                <i class="fas fa-plus"></i> إضافة ({{ results.statistics.existing_available_count }})
                            </button>
                        </div>
                    {% else %}
                        <p class="text-center text-muted">لا توجد أشخاص متاحين</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- المشاركين بالفعل -->
        <div class="col-lg-4">
            <div class="card category-card already-participants-card">
                <div class="card-header bg-transparent border-0">
                    <h4 class="text-warning mb-0">
                        <i class="fas fa-user-times"></i> مشاركين بالفعل
                        <span class="badge bg-warning ms-2">{{ results.statistics.already_participants_count }}</span>
                    </h4>
                    <small class="text-muted">موجودين في الدورة حالياً</small>
                </div>
                <div class="card-body">
                    {% if results.already_participants %}
                        <div class="mb-3">
                            {% for person in results.already_participants[:3] %}
                            <div class="person-item" style="border-left-color: #ff6b6b;">
                                <strong>{{ person.name }}</strong>
                                {% if person.original_name %}
                                <br><small class="text-muted">الأصلي: {{ person.original_name }}</small>
                                {% endif %}
                                <br><small class="text-danger">مشارك بالفعل</small>
                            </div>
                            {% endfor %}
                            {% if results.already_participants|length > 3 %}
                            <p class="text-center text-muted">
                                <i class="fas fa-ellipsis-h"></i> و {{ results.already_participants|length - 3 }} آخرين
                            </p>
                            {% endif %}
                        </div>
                        <div class="text-center">
                            <button class="btn btn-review action-button" onclick="previewCategory('already_participants')">
                                <i class="fas fa-eye"></i> مراجعة الكل
                            </button>
                            <button class="btn btn-outline-secondary action-button">
                                <i class="fas fa-forward"></i> تجاهل
                            </button>
                        </div>
                    {% else %}
                        <p class="text-center text-success">
                            <i class="fas fa-check-circle"></i> لا توجد تكرارات!
                        </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- مؤشر التنفيذ -->
    <div class="execution-progress" id="executionProgress">
        <h5 class="text-center mb-4">
            <i class="fas fa-cogs"></i> جاري التنفيذ...
        </h5>
        <div class="progress-step" id="step1">
            <i class="fas fa-user-plus me-3"></i>
            <span>إضافة الأشخاص الجدد لقاعدة البيانات</span>
            <div class="ms-auto">
                <span class="badge bg-light text-dark" id="newCount">0/{{ results.statistics.new_count }}</span>
            </div>
        </div>
        <div class="progress-step" id="step2">
            <i class="fas fa-users me-3"></i>
            <span>إضافة المشاركين للدورة</span>
            <div class="ms-auto">
                <span class="badge bg-light text-dark" id="totalCount">0/{{ results.statistics.new_count + results.statistics.existing_available_count }}</span>
            </div>
        </div>
    </div>

    <!-- ملخص النجاح -->
    <div class="success-summary" id="successSummary">
        <h2 class="text-success mb-4">
            <i class="fas fa-check-circle"></i> تم الاستيراد بنجاح!
        </h2>
        <div class="row">
            <div class="col-md-4">
                <h4 class="text-primary" id="addedToDbCount">0</h4>
                <p>تم إضافتهم لقاعدة البيانات</p>
            </div>
            <div class="col-md-4">
                <h4 class="text-success" id="addedToCourseCount">0</h4>
                <p>تم إضافتهم للدورة</p>
            </div>
            <div class="col-md-4">
                <h4 class="text-info" id="totalParticipantsCount">{{ current_participants_count }}</h4>
                <p>إجمالي المشاركين الآن</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="/manage_participants/{{ course.id }}/"
               class="btn btn-primary btn-lg me-3">
                <i class="fas fa-users"></i> عرض قائمة المشاركين
            </a>
            <button class="btn btn-outline-primary btn-lg" onclick="exportReport()">
                <i class="fas fa-download"></i> تصدير التقرير
            </button>
        </div>
    </div>

    <!-- أزرار التنقل -->
    <div class="text-center mt-4">
        <a href="/course/{{ course.id }}/import_participants"
           class="btn btn-outline-secondary btn-lg me-3">
            <i class="fas fa-arrow-right"></i> العودة للاستيراد
        </a>
        <a href="/manage_participants/{{ course.id }}/"
           class="btn btn-outline-primary btn-lg">
            <i class="fas fa-users"></i> إدارة المشاركين
        </a>
    </div>
</div>

<!-- Modal للمعاينة -->
<div class="modal fade preview-modal" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalTitle">معاينة الأسماء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewModalBody">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// بيانات النتائج
const results = {{ results | tojson }};

// 🚀 الحل الإبداعي: استخراج course_id من URL
const currentUrl = window.location.pathname;
const courseId = currentUrl.match(/\/course\/(\d+)\//)?.[1] ||
                 currentUrl.match(/manage_participants\/(\d+)\//)?.[1] ||
                 {{ course.id if course else '1' }};

console.log('🎯 Course ID detected:', courseId);

// دالة المعاينة
function previewCategory(category) {
    const data = results[category];
    const titles = {
        'new_people': 'الأشخاص الجدد',
        'existing_available': 'الموجودين والمتاحين',
        'already_participants': 'المشاركين بالفعل'
    };

    $('#previewModalTitle').text('معاينة ' + titles[category]);

    let html = '<div class="row">';
    data.forEach((person, index) => {
        html += `
            <div class="col-md-6 mb-2">
                <div class="person-item">
                    <strong>${person.name}</strong>
                    ${person.original_name ? `<br><small class="text-muted">الأصلي: ${person.original_name}</small>` : ''}
                    ${person.job ? `<br><small class="text-muted">${person.job}</small>` : ''}
                    ${person.national_id ? `<br><small class="text-muted">الرقم الوطني: ${person.national_id}</small>` : ''}
                </div>
            </div>
        `;
    });
    html += '</div>';

    $('#previewModalBody').html(html);
    $('#previewModal').modal('show');
}

// دالة الإضافة الذكية الشاملة
$('#smartAddBtn').click(function() {
    if (confirm('هل تريد تنفيذ الإضافة الذكية الشاملة؟\n\nسيتم:\n- إضافة ' + results.statistics.new_count + ' شخص جديد لقاعدة البيانات\n- إضافة ' + (results.statistics.new_count + results.statistics.existing_available_count) + ' شخص للدورة\n- تجاهل ' + results.statistics.already_participants_count + ' مكرر')) {
        executeSmartAdd();
    }
});

// دالة إضافة فئة محددة
function addCategory(category) {
    const counts = {
        'new_people': results.statistics.new_count,
        'existing_available': results.statistics.existing_available_count
    };

    const messages = {
        'new_people': 'إضافة ' + counts[category] + ' شخص جديد لقاعدة البيانات والدورة؟',
        'existing_available': 'إضافة ' + counts[category] + ' شخص للدورة؟'
    };

    if (confirm(messages[category])) {
        executeAddCategory(category);
    }
}

// تنفيذ الإضافة الذكية
function executeSmartAdd() {
    $('#executionProgress').show();
    $('html, body').animate({scrollTop: $('#executionProgress').offset().top}, 500);

    // تعطيل جميع الأزرار
    $('.action-button').prop('disabled', true);

    const data = {
        action: 'smart_add',
        new_people: results.new_people,
        existing_available: results.existing_available
    };

    executeAddition(data);
}

// تنفيذ إضافة فئة محددة
function executeAddCategory(category) {
    $('#executionProgress').show();
    $('html, body').animate({scrollTop: $('#executionProgress').offset().top}, 500);

    $('.action-button').prop('disabled', true);

    const data = {
        action: 'add_category',
        category: category,
        people: results[category]
    };

    executeAddition(data);
}

// تنفيذ الإضافة
function executeAddition(data) {
    $('#step1').addClass('active');

    // 🎯 بناء URL ديناميكي للمعالجة
    const processUrl = `/course/${courseId}/process_import`;
    console.log('📡 Process URL:', processUrl);

    $.ajax({
        url: processUrl,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            if (response.success) {
                // تحديث مؤشرات التقدم
                $('#step1').removeClass('active').addClass('completed');
                $('#step2').addClass('active');

                setTimeout(function() {
                    $('#step2').removeClass('active').addClass('completed');
                    showSuccessSummary(response);
                }, 1000);
            } else {
                alert('خطأ: ' + response.message);
                resetButtons();
            }
        },
        error: function() {
            alert('حدث خطأ في التنفيذ');
            resetButtons();
        }
    });
}

// عرض ملخص النجاح
function showSuccessSummary(response) {
    $('#executionProgress').hide();

    $('#addedToDbCount').text(response.added_to_db || 0);
    $('#addedToCourseCount').text(response.added_to_course || 0);
    $('#totalParticipantsCount').text(response.total_participants || {{ current_participants_count }});

    $('#successSummary').show();
    $('html, body').animate({scrollTop: $('#successSummary').offset().top}, 500);
}

// إعادة تعيين الأزرار
function resetButtons() {
    $('.action-button').prop('disabled', false);
    $('#executionProgress').hide();
}

// تصدير التقرير
function exportReport() {
    const exportUrl = `/course/${courseId}/export_import_report`;
    console.log('📄 Export URL:', exportUrl);
    window.open(exportUrl, '_blank');
}
</script>
{% endblock %}
