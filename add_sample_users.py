#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة مستخدمين تجريبيين لاختبار النظام
Add Sample Users for Testing
"""

import sqlite3
from werkzeug.security import generate_password_hash
from datetime import datetime

def add_sample_users():
    """إضافة مستخدمين تجريبيين"""
    
    conn = sqlite3.connect('training_system.db')
    cursor = conn.cursor()
    
    sample_users = [
        {
            'username': 'manager1',
            'email': '<EMAIL>',
            'password': 'manager123',
            'role': 'manager',
            'first_name': 'أحمد',
            'last_name': 'المدير',
            'department': 'إدارة التدريب',
            'position': 'مدير التدريب',
            'phone': '777123456',
            'is_active': 1,
            'notes': 'مدير تدريب - مستخدم تجريبي'
        },
        {
            'username': 'trainer1',
            'email': '<EMAIL>',
            'password': 'trainer123',
            'role': 'trainer',
            'first_name': 'محمد',
            'last_name': 'المدرب',
            'department': 'التدريب التقني',
            'position': 'مدرب أول',
            'phone': '777234567',
            'is_active': 1,
            'notes': 'مدرب تقني - مستخدم تجريبي'
        },
        {
            'username': 'trainer2',
            'email': '<EMAIL>',
            'password': 'trainer123',
            'role': 'trainer',
            'first_name': 'فاطمة',
            'last_name': 'المدربة',
            'department': 'التدريب الإداري',
            'position': 'مدربة',
            'phone': '777345678',
            'is_active': 1,
            'notes': 'مدربة إدارية - مستخدم تجريبي'
        },
        {
            'username': 'data_entry1',
            'email': '<EMAIL>',
            'password': 'data123',
            'role': 'data_entry',
            'first_name': 'علي',
            'last_name': 'البيانات',
            'department': 'إدخال البيانات',
            'position': 'مدخل بيانات',
            'phone': '777456789',
            'is_active': 1,
            'notes': 'مدخل بيانات - مستخدم تجريبي'
        },
        {
            'username': 'data_entry2',
            'email': '<EMAIL>',
            'password': 'data123',
            'role': 'data_entry',
            'first_name': 'مريم',
            'last_name': 'الإدخال',
            'department': 'إدخال البيانات',
            'position': 'مدخلة بيانات',
            'phone': '777567890',
            'is_active': 1,
            'notes': 'مدخلة بيانات - مستخدم تجريبي'
        },
        {
            'username': 'viewer1',
            'email': '<EMAIL>',
            'password': 'viewer123',
            'role': 'viewer',
            'first_name': 'خالد',
            'last_name': 'المشاهد',
            'department': 'المراجعة',
            'position': 'مراجع',
            'phone': '777678901',
            'is_active': 1,
            'notes': 'مشاهد - مستخدم تجريبي'
        },
        {
            'username': 'viewer2',
            'email': '<EMAIL>',
            'password': 'viewer123',
            'role': 'viewer',
            'first_name': 'نورا',
            'last_name': 'المراجعة',
            'department': 'المراجعة',
            'position': 'مراجعة',
            'phone': '777789012',
            'is_active': 1,
            'notes': 'مراجعة - مستخدم تجريبي'
        },
        {
            'username': 'inactive_user',
            'email': '<EMAIL>',
            'password': 'inactive123',
            'role': 'viewer',
            'first_name': 'مستخدم',
            'last_name': 'معطل',
            'department': 'اختبار',
            'position': 'مختبر',
            'phone': '777890123',
            'is_active': 0,
            'notes': 'مستخدم معطل - للاختبار'
        }
    ]
    
    created_count = 0
    
    try:
        for user_data in sample_users:
            # التحقق من عدم وجود المستخدم
            cursor.execute("SELECT id FROM user WHERE username = ? OR email = ?", 
                         (user_data['username'], user_data['email']))
            if cursor.fetchone():
                print(f"⚠️ المستخدم {user_data['username']} موجود بالفعل")
                continue
            
            # تشفير كلمة المرور
            hashed_password = generate_password_hash(user_data['password'])
            
            # إدخال المستخدم
            cursor.execute('''
                INSERT INTO user (
                    username, email, password, role, first_name, last_name,
                    department, position, phone, is_active, notes, created_at,
                    login_attempts
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_data['username'],
                user_data['email'],
                hashed_password,
                user_data['role'],
                user_data['first_name'],
                user_data['last_name'],
                user_data['department'],
                user_data['position'],
                user_data['phone'],
                user_data['is_active'],
                user_data['notes'],
                datetime.now().isoformat(),
                0
            ))
            
            created_count += 1
            print(f"✅ تم إنشاء المستخدم: {user_data['username']} ({user_data['first_name']} {user_data['last_name']})")
        
        conn.commit()
        print(f"\n🎉 تم إنشاء {created_count} مستخدم جديد بنجاح!")
        
        # عرض ملخص المستخدمين
        cursor.execute("SELECT COUNT(*) FROM user")
        total_users = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM user WHERE is_active = 1")
        active_users = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM user WHERE is_active = 0")
        inactive_users = cursor.fetchone()[0]
        
        print(f"\n📊 إحصائيات المستخدمين:")
        print(f"   إجمالي المستخدمين: {total_users}")
        print(f"   المستخدمين النشطين: {active_users}")
        print(f"   المستخدمين المعطلين: {inactive_users}")
        
        # عرض قائمة المستخدمين
        cursor.execute('''
            SELECT username, email, role, first_name, last_name, 
                   department, is_active 
            FROM user 
            ORDER BY created_at DESC
        ''')
        
        users = cursor.fetchall()
        print(f"\n👥 قائمة جميع المستخدمين:")
        print("-" * 80)
        for user in users:
            status = "🟢 نشط" if user[6] else "🔴 معطل"
            print(f"👤 {user[3]} {user[4]} (@{user[0]})")
            print(f"   📧 {user[1]}")
            print(f"   🎭 {user[2]} - {user[5]}")
            print(f"   {status}")
            print(f"   🔑 كلمة المرور: {user[0].split('_')[0]}123")
            print("-" * 80)
        
        print(f"\n📝 معلومات تسجيل الدخول:")
        print("   المدير العام: <EMAIL> / admin123")
        print("   مدير التدريب: <EMAIL> / manager123")
        print("   المدرب التقني: <EMAIL> / trainer123")
        print("   المدربة الإدارية: <EMAIL> / trainer123")
        print("   مدخل البيانات: <EMAIL> / data123")
        print("   المشاهد: <EMAIL> / viewer123")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المستخدمين: {e}")
        conn.rollback()
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

def main():
    """الدالة الرئيسية"""
    print("👥 إضافة مستخدمين تجريبيين لنظام إدارة المستخدمين")
    print("=" * 60)
    
    add_sample_users()

if __name__ == '__main__':
    main()
