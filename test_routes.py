#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 اختبار routes المتاحة في التطبيق
"""

import requests

def test_route_exists():
    """اختبار وجود route"""
    print("🔍 اختبار routes...")
    
    routes_to_test = [
        ("manage_participants", "http://localhost:5001/manage_participants/1/"),
        ("import_participants_page", "http://localhost:5001/course/1/import_participants"),
        ("course_import_results", "http://localhost:5001/course/1/import_results"),
    ]
    
    for name, url in routes_to_test:
        try:
            print(f"\n📋 اختبار {name}:")
            print(f"   URL: {url}")
            
            response = requests.get(url, timeout=5)
            print(f"   📡 رمز الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ {name}: يعمل")
            elif response.status_code == 302:
                print(f"   🔄 {name}: يعيد التوجيه (تسجيل دخول مطلوب)")
                location = response.headers.get('Location', 'غير محدد')
                print(f"   📍 إعادة التوجيه إلى: {location}")
            elif response.status_code == 404:
                print(f"   ❌ {name}: غير موجود (404)")
            else:
                print(f"   ⚠️ {name}: خطأ {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {name}: خطأ في الاتصال ({e})")

def test_flask_routes():
    """اختبار Flask routes مباشرة"""
    print("\n🌐 اختبار Flask routes مباشرة...")
    
    try:
        # محاولة الوصول لصفحة تعرض جميع routes
        response = requests.get("http://localhost:5001/", timeout=5)
        print(f"📡 الصفحة الرئيسية: {response.status_code}")
        
        # اختبار route محدد
        response = requests.get("http://localhost:5001/course/1/import_participants", timeout=5)
        print(f"📡 import_participants: {response.status_code}")
        
        if response.status_code == 404:
            print("❌ route غير موجود - قد تكون هناك مشكلة في التسجيل")
        elif response.status_code == 302:
            print("🔄 route موجود لكن يتطلب تسجيل دخول")
        elif response.status_code == 200:
            print("✅ route يعمل بشكل طبيعي")
        else:
            print(f"⚠️ استجابة غير متوقعة: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار routes النظام")
    print("=" * 50)
    
    test_route_exists()
    test_flask_routes()
    
    print("\n" + "=" * 50)
    print("📊 خلاصة الاختبار:")
    print("إذا كانت routes تعمل (200 أو 302)، فالمشكلة في template")
    print("إذا كانت routes لا تعمل (404)، فالمشكلة في تسجيل route")

if __name__ == "__main__":
    main()
