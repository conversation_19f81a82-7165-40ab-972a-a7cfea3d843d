#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار العلاقة بين CourseParticipant و PersonData
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, CourseParticipant, PersonData

def test_relationship():
    """
    اختبار العلاقة بين CourseParticipant و PersonData
    """
    with app.app_context():
        try:
            # جلب المشاركين في الدورة 1
            participants = CourseParticipant.query.filter_by(course_id=1).all()
            print(f"📊 عدد المشاركين في الدورة 1: {len(participants)}")
            
            if not participants:
                print("⚠️ لا يوجد مشاركين في الدورة")
                return True
            
            # اختبار العلاقة لكل مشارك
            for i, participant in enumerate(participants, 1):
                print(f"\n👤 المشارك {i}:")
                print(f"   - ID: {participant.id}")
                print(f"   - personal_data_id: {participant.personal_data_id}")
                print(f"   - الحالة: {participant.status}")
                
                # اختبار العلاقة
                try:
                    person_name = participant.personal_data.full_name
                    person_phone = participant.personal_data.phone
                    print(f"   - ✅ الاسم: {person_name}")
                    print(f"   - ✅ الهاتف: {person_phone}")
                except Exception as e:
                    print(f"   - ❌ خطأ في العلاقة: {e}")
                    
                    # محاولة جلب البيانات يدوياً
                    person = PersonData.query.get(participant.personal_data_id)
                    if person:
                        print(f"   - ✅ تم العثور على الشخص يدوياً: {person.full_name}")
                    else:
                        print(f"   - ❌ لا يوجد شخص بالرقم {participant.personal_data_id}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار العلاقة: {e}")
            return False

if __name__ == "__main__":
    print("🧪 اختبار العلاقة بين CourseParticipant و PersonData...")
    success = test_relationship()
    if success:
        print("✅ تم الاختبار بنجاح!")
    else:
        print("❌ فشل الاختبار!")
