@echo off
title Stop Training System - Docker

echo.
echo ========================================
echo    Stop Training System
echo ========================================
echo.

echo Finding running containers...
docker ps --filter "name=training_system" --format "table {{.Names}}\t{{.Ports}}\t{{.Status}}"

echo.
echo Stopping all training system containers...

REM Stop different containers
docker stop training_system 2>nul
docker stop training_system_5000 2>nul
docker stop training_system_5001 2>nul
docker stop training_system_8080 2>nul

REM Stop docker-compose containers
docker-compose down 2>nul
docker-compose -f docker-compose-5001.yml down 2>nul
docker-compose -f docker-compose-8080.yml down 2>nul

echo.
echo Cleaning up stopped containers...
docker container prune -f

echo.
echo ========================================
echo SUCCESS: System stopped successfully
echo ========================================
echo.

echo To check status:
echo    docker ps
echo.
echo To restart:
echo    start_docker.bat
echo.

pause
