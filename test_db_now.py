#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار فوري لقاعدة البيانات والنظام
"""

import sqlite3
import os
import sys

def main():
    print("🔍 اختبار فوري لقاعدة البيانات...")
    print(f"📁 المجلد الحالي: {os.getcwd()}")
    
    # فحص ملفات قاعدة البيانات
    db_files = ['training_system.db', 'instance/training_system.db']
    db_found = False
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"✅ وجدت قاعدة البيانات: {db_file}")
            db_found = True
            
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # فحص الجداول
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [t[0] for t in cursor.fetchall()]
                print(f"📊 عدد الجداول: {len(tables)}")
                
                # فحص الجداول المهمة
                important_tables = ['person_data', 'course', 'course_participant']
                
                for table in important_tables:
                    if table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        print(f"📋 {table}: {count} سجل")
                        
                        # عرض أمثلة
                        if table == 'person_data' and count > 0:
                            cursor.execute("SELECT id, full_name FROM person_data LIMIT 3")
                            people = cursor.fetchall()
                            print(f"   أمثلة الأشخاص:")
                            for p in people:
                                print(f"     {p[0]}: {p[1]}")
                        
                        elif table == 'course' and count > 0:
                            cursor.execute("SELECT id, title FROM course LIMIT 3")
                            courses = cursor.fetchall()
                            print(f"   أمثلة الدورات:")
                            for c in courses:
                                print(f"     {c[0]}: {c[1]}")
                    else:
                        print(f"❌ جدول {table} غير موجود!")
                
                # اختبار الدورة رقم 1
                print("\n🎯 اختبار الدورة رقم 1:")
                cursor.execute("SELECT id, title FROM course WHERE id = 1")
                course_1 = cursor.fetchone()
                
                if course_1:
                    print(f"✅ الدورة رقم 1: {course_1[1]}")
                    
                    # فحص المشاركين
                    cursor.execute("""
                        SELECT cp.id, pd.full_name, cp.status
                        FROM course_participant cp 
                        JOIN person_data pd ON cp.personal_data_id = pd.id 
                        WHERE cp.course_id = 1
                    """)
                    participants = cursor.fetchall()
                    
                    print(f"👥 عدد المشاركين: {len(participants)}")
                    if participants:
                        print("   المشاركين:")
                        for p in participants:
                            print(f"     - {p[1]} (الحالة: {p[2]})")
                    else:
                        print("   لا يوجد مشاركين حالياً")
                else:
                    print("❌ الدورة رقم 1 غير موجودة!")
                
                # اختبار إضافة مشارك تجريبي
                print("\n🧪 اختبار إضافة مشارك تجريبي:")
                
                # البحث عن أول شخص في القاعدة
                cursor.execute("SELECT id, full_name FROM person_data LIMIT 1")
                first_person = cursor.fetchone()
                
                if first_person and course_1:
                    person_id = first_person[0]
                    person_name = first_person[1]
                    
                    # التحقق من عدم وجوده في الدورة
                    cursor.execute("""
                        SELECT id FROM course_participant 
                        WHERE course_id = 1 AND personal_data_id = ?
                    """, (person_id,))
                    existing = cursor.fetchone()
                    
                    if existing:
                        print(f"🔄 {person_name} موجود مسبقاً في الدورة")
                    else:
                        # إضافته للدورة
                        cursor.execute("""
                            INSERT INTO course_participant (course_id, personal_data_id, status)
                            VALUES (1, ?, 'active')
                        """, (person_id,))
                        conn.commit()
                        print(f"✅ تم إضافة {person_name} للدورة بنجاح!")
                        
                        # التحقق من الإضافة
                        cursor.execute("""
                            SELECT COUNT(*) FROM course_participant WHERE course_id = 1
                        """)
                        new_count = cursor.fetchone()[0]
                        print(f"📊 العدد الجديد للمشاركين: {new_count}")
                
                conn.close()
                
                print("\n🎉 اختبار قاعدة البيانات مكتمل!")
                print("✅ النظام يعمل بشكل صحيح")
                print("✅ يمكن إضافة وإزالة المشاركين")
                print("✅ العلاقات بين الجداول تعمل")
                
                return True
                
            except Exception as e:
                print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
                return False
    
    if not db_found:
        print("❌ لم يتم العثور على أي ملف قاعدة بيانات!")
        print("📁 الملفات الموجودة:")
        for file in os.listdir('.'):
            if file.endswith('.db'):
                print(f"   - {file}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🌐 يمكنك الآن اختبار الواجهة من المتصفح:")
        print("http://localhost:5001/manage_participants/1/")
    else:
        print("\n❌ فشل في اختبار النظام")
