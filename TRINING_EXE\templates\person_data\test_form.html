<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النموذج</title>
    <link href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار رفع الملف</h3>
                    </div>
                    <div class="card-body">
                        <form action="{{ url_for('person_data.name_analysis') }}" method="post" enctype="multipart/form-data" id="testForm">
                            <div class="mb-3">
                                <label for="excel_file" class="form-label">اختر ملف Excel:</label>
                                <input type="file" class="form-control" name="excel_file" id="excel_file" accept=".xlsx,.xls" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="course_id" class="form-label">اختر دورة (اختياري):</label>
                                <select class="form-select" name="course_id" id="course_id">
                                    <option value="">لا تضيف لأي دورة</option>
                                    {% for course in courses %}
                                    <option value="{{ course.id }}">{{ course.course_number }} - {{ course.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-upload"></i> رفع وتحليل
                            </button>
                        </form>
                        
                        <div id="debug" class="mt-3">
                            <h5>معلومات التشخيص:</h5>
                            <div id="debugInfo"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS - محلي -->
    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- jQuery - محلي -->
    <script src="/static/libs/jquery/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#debugInfo').append('<p>✅ JavaScript محمل</p>');
            
            $('#excel_file').change(function() {
                var fileName = $(this).val().split('\\').pop();
                $('#debugInfo').append('<p>📁 تم اختيار ملف: ' + fileName + '</p>');
            });
            
            $('#testForm').submit(function(e) {
                $('#debugInfo').append('<p>🔥 تم الضغط على زر الإرسال</p>');
                
                var formData = new FormData(this);
                $('#debugInfo').append('<p>📋 بيانات النموذج:</p>');
                
                for (var pair of formData.entries()) {
                    $('#debugInfo').append('<p>- ' + pair[0] + ': ' + pair[1] + '</p>');
                }
                
                $('#submitBtn').html('<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...');
                $('#submitBtn').prop('disabled', true);
                
                // لا نمنع الإرسال - دع النموذج يرسل بشكل طبيعي
                return true;
            });
        });
    </script>
</body>
</html>
