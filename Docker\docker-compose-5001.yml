version: '3.8'

services:
  training-system:
    build: .
    container_name: training_system_5001
    ports:
      - "5001:5000"
    volumes:
      - ./training_system.db:/app/training_system.db
      - ./static/uploads:/app/static/uploads
    environment:
      - FLASK_ENV=production
      - PORT=5000
    restart: unless-stopped
    networks:
      - training_network

networks:
  training_network:
    driver: bridge
