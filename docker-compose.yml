version: '3.8'

services:
  training-system:
    build: .
    container_name: training_system
    ports:
      - "5000:5000"  # يمكن تغيير المنفذ الخارجي: "8080:5000" مثلاً
    volumes:
      # ربط قاعدة البيانات للحفاظ على البيانات
      - ./training_system.db:/app/training_system.db
      # ربط مجلد الرفع للحفاظ على الملفات المرفوعة
      - ./static/uploads:/app/static/uploads
    environment:
      - FLASK_ENV=production
      - PORT=5000  # يمكن تغيير المنفذ الداخلي: PORT=8080 مثلاً
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - training_network

networks:
  training_network:
    driver: bridge
