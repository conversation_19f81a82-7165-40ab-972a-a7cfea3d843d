#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شرح عملي لنظام إدارة الصلاحيات
Practical Guide to Permissions Management System
"""

from flask import Flask, render_template_string, request, jsonify
from flask_login import current_user, login_required
from permissions_manager import require_permission, require_role

# مثال على كيفية استخدام الصلاحيات في الـ Routes

# ========================================
# 1. حماية الصفحات بالصلاحيات
# ========================================

@app.route('/admin/users')
@login_required
@require_permission('users.view')  # يحتاج صلاحية عرض المستخدمين
def users_list():
    """صفحة قائمة المستخدمين - تحتاج صلاحية users.view"""
    return render_template('admin/users_list.html')

@app.route('/admin/users/create', methods=['GET', 'POST'])
@login_required
@require_permission('users.create')  # يحتاج صلاحية إنشاء المستخدمين
def create_user():
    """صفحة إنشاء مستخدم جديد - تحتاج صلاحية users.create"""
    return render_template('admin/create_user.html')

@app.route('/admin/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@require_permission('users.edit')  # يحتاج صلاحية تعديل المستخدمين
def edit_user(user_id):
    """صفحة تعديل المستخدم - تحتاج صلاحية users.edit"""
    return render_template('admin/edit_user.html', user_id=user_id)

@app.route('/admin/users/<int:user_id>/delete', methods=['POST'])
@login_required
@require_permission('users.delete')  # يحتاج صلاحية حذف المستخدمين
def delete_user(user_id):
    """حذف المستخدم - تحتاج صلاحية users.delete"""
    # كود حذف المستخدم
    return jsonify({'success': True})

# ========================================
# 2. حماية الصفحات بالأدوار
# ========================================

@app.route('/admin/system-settings')
@login_required
@require_role('admin')  # فقط المديرين يمكنهم الوصول
def system_settings():
    """إعدادات النظام - فقط للمديرين"""
    return render_template('admin/system_settings.html')

@app.route('/trainer/courses')
@login_required
@require_role('trainer')  # فقط المدربين يمكنهم الوصول
def trainer_courses():
    """دورات المدرب - فقط للمدربين"""
    return render_template('trainer/courses.html')

# ========================================
# 3. فحص الصلاحيات في الـ Templates
# ========================================

# في الـ Template يمكنك استخدام:
template_example = """
<!-- فحص صلاحية عرض زر إضافة مستخدم -->
{% if current_user.has_permission('users.create') %}
    <button class="btn btn-primary" onclick="showAddUserModal()">
        <i class="fas fa-plus"></i> إضافة مستخدم جديد
    </button>
{% endif %}

<!-- فحص صلاحية عرض زر تعديل -->
{% if current_user.has_permission('users.edit') %}
    <button class="btn btn-warning" onclick="editUser({{ user.id }})">
        <i class="fas fa-edit"></i> تعديل
    </button>
{% endif %}

<!-- فحص صلاحية عرض زر حذف -->
{% if current_user.has_permission('users.delete') %}
    <button class="btn btn-danger" onclick="deleteUser({{ user.id }})">
        <i class="fas fa-trash"></i> حذف
    </button>
{% endif %}

<!-- فحص الدور -->
{% if current_user.role == 'admin' %}
    <div class="admin-panel">
        <!-- محتوى خاص بالمديرين فقط -->
    </div>
{% endif %}

<!-- فحص صلاحيات متعددة -->
{% if current_user.has_permission('reports.view') and current_user.has_permission('reports.export') %}
    <button class="btn btn-success" onclick="exportReport()">
        <i class="fas fa-download"></i> تصدير التقرير
    </button>
{% endif %}
"""

# ========================================
# 4. فحص الصلاحيات في JavaScript
# ========================================

javascript_example = """
<script>
// تمرير صلاحيات المستخدم إلى JavaScript
var userPermissions = {{ current_user.get_permissions_list() | tojson }};
var userRole = "{{ current_user.role }}";

// فحص الصلاحية في JavaScript
function hasPermission(permission) {
    return userPermissions.includes(permission);
}

// مثال على الاستخدام
function showContextMenu(userId) {
    var menu = [];
    
    if (hasPermission('users.edit')) {
        menu.push({
            text: 'تعديل',
            icon: 'fas fa-edit',
            action: function() { editUser(userId); }
        });
    }
    
    if (hasPermission('users.delete')) {
        menu.push({
            text: 'حذف',
            icon: 'fas fa-trash',
            action: function() { deleteUser(userId); }
        });
    }
    
    if (hasPermission('users.reset_password')) {
        menu.push({
            text: 'إعادة تعيين كلمة المرور',
            icon: 'fas fa-key',
            action: function() { resetPassword(userId); }
        });
    }
    
    // عرض القائمة
    showMenu(menu);
}

// إخفاء/إظهار العناصر بناءً على الصلاحيات
$(document).ready(function() {
    // إخفاء أزرار لا يملك المستخدم صلاحية لها
    if (!hasPermission('users.create')) {
        $('.btn-add-user').hide();
    }
    
    if (!hasPermission('users.delete')) {
        $('.btn-delete-user').hide();
    }
    
    // إخفاء أقسام كاملة
    if (userRole !== 'admin') {
        $('.admin-only').hide();
    }
});
</script>
"""

# ========================================
# 5. مثال شامل لصفحة إدارة المستخدمين
# ========================================

def demo_users_page():
    """مثال شامل لصفحة إدارة المستخدمين مع الصلاحيات"""
    
    template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>إدارة المستخدمين</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>إدارة المستخدمين</h2>
                        
                        <!-- زر إضافة مستخدم - يظهر فقط لمن لديه صلاحية -->
                        {% if current_user.has_permission('users.create') %}
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-plus"></i> إضافة مستخدم جديد
                        </button>
                        {% endif %}
                    </div>
                    
                    <!-- جدول المستخدمين -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.get_full_name() }}</td>
                                    <td>{{ user.email }}</td>
                                    <td>{{ user.role }}</td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-danger">معطل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- زر عرض - يظهر لمن لديه صلاحية عرض -->
                                            {% if current_user.has_permission('users.view') %}
                                            <button class="btn btn-sm btn-info" onclick="viewUser({{ user.id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% endif %}
                                            
                                            <!-- زر تعديل - يظهر لمن لديه صلاحية تعديل -->
                                            {% if current_user.has_permission('users.edit') %}
                                            <button class="btn btn-sm btn-warning" onclick="editUser({{ user.id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% endif %}
                                            
                                            <!-- زر إعادة تعيين كلمة المرور - يظهر لمن لديه صلاحية -->
                                            {% if current_user.has_permission('users.reset_password') %}
                                            <button class="btn btn-sm btn-secondary" onclick="resetPassword({{ user.id }})">
                                                <i class="fas fa-key"></i>
                                            </button>
                                            {% endif %}
                                            
                                            <!-- زر حذف - يظهر لمن لديه صلاحية حذف -->
                                            {% if current_user.has_permission('users.delete') %}
                                            <button class="btn btn-sm btn-danger" onclick="deleteUser({{ user.id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- قسم الإحصائيات - يظهر فقط للمديرين -->
                    {% if current_user.role == 'admin' %}
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5>إجمالي المستخدمين</h5>
                                    <h3>{{ total_users }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5>المستخدمين النشطين</h5>
                                    <h3>{{ active_users }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- قسم إدارة الصلاحيات - يظهر فقط لمن لديه صلاحية إدارة الصلاحيات -->
                    {% if current_user.has_permission('users.manage_permissions') %}
                    <div class="mt-4">
                        <h4>إدارة الصلاحيات</h4>
                        <button class="btn btn-outline-primary" onclick="managePermissions()">
                            <i class="fas fa-shield-alt"></i> إدارة الصلاحيات
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <script>
        // تمرير صلاحيات المستخدم إلى JavaScript
        var userPermissions = {{ current_user.get_permissions_list() | tojson }};
        
        function hasPermission(permission) {
            return userPermissions.includes(permission);
        }
        
        function editUser(userId) {
            if (!hasPermission('users.edit')) {
                alert('ليس لديك صلاحية لتعديل المستخدمين');
                return;
            }
            // كود تعديل المستخدم
            window.location.href = '/admin/users/' + userId + '/edit';
        }
        
        function deleteUser(userId) {
            if (!hasPermission('users.delete')) {
                alert('ليس لديك صلاحية لحذف المستخدمين');
                return;
            }
            
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                // كود حذف المستخدم
                fetch('/admin/users/' + userId + '/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('فشل في حذف المستخدم');
                    }
                });
            }
        }
        
        function resetPassword(userId) {
            if (!hasPermission('users.reset_password')) {
                alert('ليس لديك صلاحية لإعادة تعيين كلمة المرور');
                return;
            }
            
            if (confirm('هل أنت متأكد من إعادة تعيين كلمة مرور هذا المستخدم؟')) {
                // كود إعادة تعيين كلمة المرور
                fetch('/admin/users/' + userId + '/reset-password', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم إعادة تعيين كلمة المرور الجديدة: ' + data.new_password);
                    } else {
                        alert('فشل في إعادة تعيين كلمة المرور');
                    }
                });
            }
        }
        </script>
    </body>
    </html>
    """
    
    return template

if __name__ == '__main__':
    print("هذا ملف توضيحي لكيفية استخدام نظام الصلاحيات")
    print("راجع الكود أعلاه لفهم كيفية:")
    print("1. حماية الصفحات بالصلاحيات")
    print("2. إخفاء/إظهار الأزرار بناءً على الصلاحيات")
    print("3. فحص الصلاحيات في JavaScript")
    print("4. استخدام الأدوار والصلاحيات في Templates")
