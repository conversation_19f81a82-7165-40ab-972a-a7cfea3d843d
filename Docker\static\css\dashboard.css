/* تنسيقات خاصة بلوحة التحكم */
.dashboard-card {
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    border: none;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.dashboard-card-header {
    background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
    color: white;
    padding: 15px 20px;
    font-weight: bold;
}

.dashboard-icon {
    font-size: 40px;
    margin-bottom: 15px;
    color: #4a6bff;
}

.stat-card {
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    border: none;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-primary {
    background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
    color: white;
}

.stat-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.stat-warning {
    background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
    color: white;
}

.stat-danger {
    background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
    color: white;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 10px 0;
}

.sidebar {
    background-color: #343a40;
    color: white;
    min-height: calc(100vh - 56px);
    padding-top: 20px;
}

.sidebar-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 10px 15px;
    display: block;
    text-decoration: none;
    transition: all 0.3s;
    border-radius: 5px;
    margin: 5px 10px;
}

.sidebar-link:hover, .sidebar-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}
