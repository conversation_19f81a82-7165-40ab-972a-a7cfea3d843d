#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشغيل بسيط للخادم
"""

print("🔄 تحميل التطبيق...")
from app import app

print("✅ تم تحميل التطبيق")
print("🚀 بدء تشغيل الخادم على http://127.0.0.1:5000")
print("📝 للوصول إلى التقارير: http://127.0.0.1:5000/reports/dashboard")
print("🛑 اضغط Ctrl+C للإيقاف")
print("-" * 60)

try:
    app.run(
        debug=True,  # تفعيل وضع التصحيح لرؤية الأخطاء
        host='127.0.0.1',
        port=5000,
        threaded=True,
        use_reloader=False
    )
except KeyboardInterrupt:
    print("\n🛑 تم إيقاف الخادم")
except Exception as e:
    print(f"\n❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
