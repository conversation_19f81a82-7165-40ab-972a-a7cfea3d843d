from app import app, db, PersonalData
import os
import random
import string

def generate_random_string(length=10):
    """
    توليد سلسلة عشوائية بطول محدد
    """
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for i in range(length))

def test_add_person():
    """
    اختبار إضافة شخص جديد إلى قاعدة البيانات
    """
    # إنشاء سياق التطبيق
    with app.app_context():
        try:
            # توليد رقم وطني عشوائي لتجنب التكرار
            random_national_number = ''.join(random.choice(string.digits) for i in range(10))

            # طباعة جميع الأشخاص الموجودين في قاعدة البيانات
            print("الأشخاص الموجودون في قاعدة البيانات:")
            all_persons = PersonalData.query.all()
            for person in all_persons:
                print(f"الاسم: {person.full_name}, الرقم الوطني: {person.national_number}")

            # إنشاء كائن PersonalData جديد
            new_person = PersonalData(
                user_id=1,  # افتراض أن معرف المستخدم هو 1
                full_name="اختبار الاسم",
                national_number=random_national_number,
                military_number=None,
                nickname="اختبار",
                age="30",
                work_number="",
                job_title="مهندس",
                work_place=None,
                work_rank="",
                phone_yemen_mobile="777123456"
            )

            # طباعة نوع الكائن للتأكد
            print(f"نوع الكائن: {type(new_person)}")
            print(f"هل الكائن من نوع PersonalData: {isinstance(new_person, PersonalData)}")

            # طباعة خصائص الكائن
            print("خصائص الكائن:")
            print(f"full_name: {new_person.full_name}")
            print(f"national_number: {new_person.national_number}")
            print(f"military_number: {new_person.military_number}")
            print(f"nickname: {new_person.nickname}")
            print(f"age: {new_person.age}")
            print(f"job_title: {new_person.job_title}")
            print(f"work_place: {new_person.work_place}")

            # إضافة الكائن إلى قاعدة البيانات
            db.session.add(new_person)
            db.session.commit()

            print(f"تم إضافة الشخص بنجاح: {new_person.full_name}, الرقم الوطني: {new_person.national_number}")

            # البحث عن الشخص المضاف
            person = PersonalData.query.filter_by(national_number=random_national_number).first()
            if person:
                print(f"تم العثور على الشخص: {person.full_name}, الرقم الوطني: {person.national_number}")
            else:
                print("لم يتم العثور على الشخص")

            return True
        except Exception as e:
            db.session.rollback()
            print(f"حدث خطأ أثناء إضافة الشخص: {str(e)}")
            return False

if __name__ == "__main__":
    # تشغيل الاختبار
    success = test_add_person()
    print(f"نتيجة الاختبار: {'نجاح' if success else 'فشل'}")
