{% extends "layout.html" %}

{% block title %}إدارة المستخدمين{% endblock %}

{% block head %}
<style>
/* تصميم احترافي لإدارة المستخدمين */
.users-management-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.management-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    margin: 0 auto;
    max-width: 1400px;
}

.page-header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0.5rem 0 0 0;
}

/* بطاقات الإحصائيات المحسنة */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-left: 4px solid;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card.primary {
    border-left-color: #3b82f6;
}

.stat-card.success {
    border-left-color: #10b981;
}

.stat-card.warning {
    border-left-color: #f59e0b;
}

.stat-card.info {
    border-left-color: #06b6d4;
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card.primary .stat-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-card.info .stat-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    margin: 0.5rem 0 0 0;
}

/* فلاتر البحث المحسنة */
.filters-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.filters-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.filters-title i {
    margin-left: 0.5rem;
    color: #3b82f6;
}

/* جدول المستخدمين المحسن */
.users-table-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.table-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th {
    background: #f8fafc;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
}

.users-table td {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
}

.users-table tbody tr {
    transition: all 0.2s ease;
}

.users-table tbody tr:hover {
    background: #f8fafc;
    transform: scale(1.01);
}

/* أفاتار المستخدم */
.user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    margin-left: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-details h6 {
    margin: 0;
    font-weight: 600;
    color: #1f2937;
}

.user-details small {
    color: #6b7280;
}

/* شارات الحالة */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

.status-badge.inactive {
    background: #f3f4f6;
    color: #6b7280;
}

.status-badge.locked {
    background: #fef2f2;
    color: #dc2626;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.action-btn.edit {
    background: #dbeafe;
    color: #1d4ed8;
}

.action-btn.roles {
    background: #f3e8ff;
    color: #7c3aed;
}

.action-btn.password {
    background: #fef3c7;
    color: #d97706;
}

.action-btn.toggle {
    background: #dcfce7;
    color: #166534;
}

.action-btn.danger {
    background: #fef2f2;
    color: #dc2626;
}

/* Modal محسن */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.modal-header {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
}

.modal-title {
    font-weight: 600;
}

.btn-close {
    filter: invert(1);
}

/* أزرار محسنة */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        font-size: 2rem;
    }

    .users-table {
        font-size: 0.9rem;
    }

    .action-buttons {
        flex-direction: column;
    }
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #3b82f6;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block content %}
<div class="users-management-page">
    <div class="management-container"
         data-aos="fade-up"
         data-aos-duration="800">

        <!-- رأس الصفحة -->
        <div class="page-header" data-aos="fade-down" data-aos-delay="200">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">
                        <i class="fas fa-users-cog me-3"></i>
                        إدارة المستخدمين والصلاحيات
                    </h1>
                    <p class="page-subtitle">إدارة شاملة للمستخدمين والأدوار والصلاحيات</p>
                </div>
                <button class="btn btn-light btn-lg"
                        data-bs-toggle="modal"
                        data-bs-target="#addUserModal"
                        data-aos="zoom-in"
                        data-aos-delay="400">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستخدم جديد
                </button>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="stats-grid" data-aos="fade-up" data-aos-delay="300">
            <div class="stat-card primary" data-aos="flip-left" data-aos-delay="400">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                    </div>
                </div>
                <h3 class="stat-number" data-count="{{ total_users }}">0</h3>
                <p class="stat-label">إجمالي المستخدمين</p>
            </div>

            <div class="stat-card success" data-aos="flip-left" data-aos-delay="500">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up text-success"></i>
                    </div>
                </div>
                <h3 class="stat-number" data-count="{{ active_users }}">0</h3>
                <p class="stat-label">المستخدمين النشطين</p>
            </div>

            <div class="stat-card warning" data-aos="flip-left" data-aos-delay="600">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-user-lock"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-down text-danger"></i>
                    </div>
                </div>
                <h3 class="stat-number" data-count="{{ locked_users }}">0</h3>
                <p class="stat-label">المستخدمين المقفلين</p>
            </div>

            <div class="stat-card info" data-aos="flip-left" data-aos-delay="700">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-circle text-success"></i>
                    </div>
                </div>
                <h3 class="stat-number" data-count="{{ online_users }}">0</h3>
                <p class="stat-label">المتصلين الآن</p>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="filters-card" data-aos="fade-up" data-aos-delay="400">
            <h3 class="filters-title">
                <i class="fas fa-filter"></i>
                فلاتر البحث والتصفية
            </h3>
            <form method="GET" class="row g-3" id="filtersForm">
                <div class="col-md-3">
                    <label class="form-label">
                        <i class="fas fa-search me-1"></i>البحث السريع
                    </label>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               name="search"
                               value="{{ request.args.get('search', '') }}"
                               placeholder="اسم المستخدم، البريد، أو الاسم الكامل"
                               id="searchInput">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">
                        <i class="fas fa-user-tag me-1"></i>الدور
                    </label>
                    <select class="form-select" name="role" id="roleFilter">
                        <option value="">جميع الأدوار</option>
                        <option value="admin" {{ 'selected' if request.args.get('role') == 'admin' }}>مدير النظام</option>
                        <option value="manager" {{ 'selected' if request.args.get('role') == 'manager' }}>مدير</option>
                        <option value="trainer" {{ 'selected' if request.args.get('role') == 'trainer' }}>مدرب</option>
                        <option value="data_entry" {{ 'selected' if request.args.get('role') == 'data_entry' }}>مدخل بيانات</option>
                        <option value="viewer" {{ 'selected' if request.args.get('role') == 'viewer' }}>مشاهد</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">
                        <i class="fas fa-toggle-on me-1"></i>الحالة
                    </label>
                    <select class="form-select" name="status" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="active" {{ 'selected' if request.args.get('status') == 'active' }}>نشط</option>
                        <option value="inactive" {{ 'selected' if request.args.get('status') == 'inactive' }}>غير نشط</option>
                        <option value="locked" {{ 'selected' if request.args.get('status') == 'locked' }}>مقفل</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">
                        <i class="fas fa-building me-1"></i>القسم
                    </label>
                    <input type="text"
                           class="form-control"
                           name="department"
                           value="{{ request.args.get('department', '') }}"
                           placeholder="اسم القسم"
                           id="departmentFilter">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>تطبيق الفلاتر
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-eraser me-1"></i>مسح
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="exportUsers()">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- جدول المستخدمين -->
        <div class="users-table-card" data-aos="fade-up" data-aos-delay="500">
            <div class="table-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="table-title">
                        <i class="fas fa-table me-2"></i>
                        قائمة المستخدمين
                    </h3>
                    <div class="table-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshTable()">
                            <i class="fas fa-sync-alt me-1"></i>تحديث
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="bulkActions()">
                            <i class="fas fa-tasks me-1"></i>إجراءات متعددة
                        </button>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="users-table" id="usersTable">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الأدوار</th>
                            <th>القسم</th>
                            <th>الحالة</th>
                            <th>آخر دخول</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users.items %}
                        <tr data-user-id="{{ user.id }}" class="user-row">
                            <td>
                                <input type="checkbox" class="user-checkbox" value="{{ user.id }}">
                            </td>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar">
                                        {{ user.get_full_name()[:2].upper() if user.get_full_name() else user.username[:2].upper() }}
                                    </div>
                                    <div class="user-details">
                                        <h6>{{ user.get_full_name() }}</h6>
                                        <small>@{{ user.username }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <i class="fas fa-envelope me-1 text-muted"></i>
                                    {{ user.email }}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-primary mb-1">{{ user.role }}</span>
                                {% for user_role in user.user_roles %}
                                    {% if user_role.is_active %}
                                        <span class="badge bg-secondary mb-1">{{ user_role.role.display_name }}</span>
                                    {% endif %}
                                {% endfor %}
                            </td>
                            <td>
                                {% if user.department %}
                                    <i class="fas fa-building me-1 text-muted"></i>
                                    {{ user.department }}
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.locked_until and user.locked_until > now %}
                                    <span class="status-badge locked">
                                        <i class="fas fa-lock me-1"></i>مقفل
                                    </span>
                                {% elif user.is_active %}
                                    <span class="status-badge active">
                                        <i class="fas fa-check-circle me-1"></i>نشط
                                    </span>
                                {% else %}
                                    <span class="status-badge inactive">
                                        <i class="fas fa-pause-circle me-1"></i>غير نشط
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.last_login %}
                                    <div>
                                        <i class="fas fa-clock me-1 text-muted"></i>
                                        {{ user.last_login.strftime('%Y-%m-%d') }}
                                    </div>
                                    <small class="text-muted">{{ user.last_login.strftime('%H:%M') }}</small>
                                {% else %}
                                    <span class="text-muted">
                                        <i class="fas fa-minus me-1"></i>لم يدخل بعد
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    <i class="fas fa-calendar me-1 text-muted"></i>
                                    {{ user.created_at.strftime('%Y-%m-%d') }}
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-btn edit"
                                            onclick="editUser({{ user.id }})"
                                            title="تعديل المستخدم"
                                            data-bs-toggle="tooltip">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn roles"
                                            onclick="manageUserRoles({{ user.id }})"
                                            title="إدارة الأدوار"
                                            data-bs-toggle="tooltip">
                                        <i class="fas fa-user-tag"></i>
                                    </button>
                                    <button class="action-btn password"
                                            onclick="resetPassword({{ user.id }})"
                                            title="إعادة تعيين كلمة المرور"
                                            data-bs-toggle="tooltip">
                                        <i class="fas fa-key"></i>
                                    </button>
                                    {% if user.is_active %}
                                        <button class="action-btn danger"
                                                onclick="toggleUserStatus({{ user.id }}, false)"
                                                title="إلغاء التفعيل"
                                                data-bs-toggle="tooltip">
                                            <i class="fas fa-user-slash"></i>
                                        </button>
                                    {% else %}
                                        <button class="action-btn toggle"
                                                onclick="toggleUserStatus({{ user.id }}, true)"
                                                title="تفعيل المستخدم"
                                                data-bs-toggle="tooltip">
                                            <i class="fas fa-user-check"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if users.pages > 1 %}
                    <nav aria-label="صفحات المستخدمين">
                        <ul class="pagination justify-content-center">
                            {% if users.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('users_management', page=users.prev_num, **request.args) }}">السابق</a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in users.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != users.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('users_management', page=page_num, **request.args) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if users.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('users_management', page=users.next_num, **request.args) }}">التالي</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة مستخدم جديد -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم *</label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الأول</label>
                                <input type="text" class="form-control" name="first_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الأخير</label>
                                <input type="text" class="form-control" name="last_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور *</label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تأكيد كلمة المرور *</label>
                                <input type="password" class="form-control" name="confirm_password" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الدور الأساسي *</label>
                                <select class="form-select" name="role" required>
                                    <option value="">اختر الدور</option>
                                    <option value="admin">مدير النظام</option>
                                    <option value="manager">مدير</option>
                                    <option value="trainer">مدرب</option>
                                    <option value="data_entry">مدخل بيانات</option>
                                    <option value="viewer">مشاهد</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" name="phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                <input type="text" class="form-control" name="department">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المنصب</label>
                                <input type="text" class="form-control" name="position">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea class="form-control" name="notes" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" checked>
                                <label class="form-check-label">المستخدم نشط</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<!-- AOS Animation Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<!-- SweetAlert2 for beautiful alerts -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// تهيئة AOS للحركات
AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: true,
    mirror: false
});

// تهيئة Tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تحريك الأرقام في البطاقات
    animateCounters();

    // تفعيل البحث المباشر
    setupLiveSearch();
});

// تحريك الأرقام في بطاقات الإحصائيات
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number[data-count]');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-count'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 16);
    });
}

// البحث المباشر
function setupLiveSearch() {
    const searchInput = document.getElementById('searchInput');
    let searchTimeout;

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                filterTable(this.value);
            }, 300);
        });
    }
}

// فلترة الجدول محلياً
function filterTable(searchTerm) {
    const rows = document.querySelectorAll('.user-row');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const shouldShow = text.includes(searchTerm.toLowerCase());

        if (shouldShow) {
            row.style.display = '';
            row.style.animation = 'fadeIn 0.3s ease';
        } else {
            row.style.display = 'none';
        }
    });
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('filtersForm').reset();
    window.location.href = window.location.pathname;
}

// تصدير المستخدمين
function exportUsers() {
    Swal.fire({
        title: 'تصدير البيانات',
        text: 'اختر تنسيق التصدير',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Excel',
        cancelButtonText: 'PDF',
        showDenyButton: true,
        denyButtonText: 'CSV'
    }).then((result) => {
        if (result.isConfirmed) {
            exportToExcel();
        } else if (result.isDenied) {
            exportToCSV();
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            exportToPDF();
        }
    });
}

// تحديث الجدول
function refreshTable() {
    const refreshBtn = document.querySelector('[onclick="refreshTable()"]');
    const icon = refreshBtn.querySelector('i');

    icon.classList.add('fa-spin');

    setTimeout(() => {
        location.reload();
    }, 1000);
}

// تحديد/إلغاء تحديد الكل
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        const row = checkbox.closest('tr');
        if (selectAll.checked) {
            row.classList.add('selected');
        } else {
            row.classList.remove('selected');
        }
    });

    updateBulkActionsVisibility();
}

// تحديث رؤية الإجراءات المتعددة
function updateBulkActionsVisibility() {
    const selectedCount = document.querySelectorAll('.user-checkbox:checked').length;
    const bulkActionsBtn = document.querySelector('[onclick="bulkActions()"]');

    if (selectedCount > 0) {
        bulkActionsBtn.style.display = 'inline-block';
        bulkActionsBtn.innerHTML = `<i class="fas fa-tasks me-1"></i>إجراءات (${selectedCount})`;
    } else {
        bulkActionsBtn.style.display = 'none';
    }
}

// الإجراءات المتعددة
function bulkActions() {
    const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked'))
                              .map(cb => cb.value);

    if (selectedUsers.length === 0) {
        Swal.fire('تنبيه', 'يرجى تحديد مستخدم واحد على الأقل', 'warning');
        return;
    }

    Swal.fire({
        title: 'الإجراءات المتعددة',
        text: `تم تحديد ${selectedUsers.length} مستخدم`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'تفعيل الكل',
        cancelButtonText: 'إلغاء تفعيل الكل',
        showDenyButton: true,
        denyButtonText: 'حذف المحددين'
    }).then((result) => {
        if (result.isConfirmed) {
            bulkToggleStatus(selectedUsers, true);
        } else if (result.isDenied) {
            bulkDelete(selectedUsers);
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            bulkToggleStatus(selectedUsers, false);
        }
    });
}

// إضافة مستخدم جديد مع تحسينات
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());

    // التحقق من تطابق كلمات المرور
    if (data.password !== data.confirm_password) {
        Swal.fire('خطأ', 'كلمات المرور غير متطابقة', 'error');
        return;
    }

    // إظهار مؤشر التحميل
    Swal.fire({
        title: 'جاري إضافة المستخدم...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    fetch('/admin/users/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                title: 'نجح!',
                text: 'تم إضافة المستخدم بنجاح',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            }).then(() => {
                location.reload();
            });
        } else {
            Swal.fire('خطأ', data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire('خطأ', 'حدث خطأ أثناء إضافة المستخدم', 'error');
    });
});

// تعديل مستخدم
function editUser(userId) {
    Swal.fire({
        title: 'قريباً',
        text: 'سيتم تنفيذ تعديل المستخدم قريباً',
        icon: 'info'
    });
}

// إدارة أدوار المستخدم
function manageUserRoles(userId) {
    Swal.fire({
        title: 'قريباً',
        text: 'سيتم تنفيذ إدارة الأدوار قريباً',
        icon: 'info'
    });
}

// إعادة تعيين كلمة المرور مع تحسينات
function resetPassword(userId) {
    Swal.fire({
        title: 'إعادة تعيين كلمة المرور',
        text: 'هل أنت متأكد من إعادة تعيين كلمة المرور؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'نعم، أعد التعيين',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#f59e0b'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'جاري إعادة التعيين...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch(`/admin/users/${userId}/reset-password`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'تم بنجاح!',
                        html: `تم إعادة تعيين كلمة المرور<br><strong>كلمة المرور الجديدة: ${data.new_password}</strong>`,
                        icon: 'success',
                        confirmButtonText: 'نسخ كلمة المرور'
                    }).then(() => {
                        navigator.clipboard.writeText(data.new_password);
                    });
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                }
            });
        }
    });
}

// تغيير حالة المستخدم مع تحسينات
function toggleUserStatus(userId, status) {
    const action = status ? 'تفعيل' : 'إلغاء تفعيل';
    const color = status ? '#10b981' : '#ef4444';

    Swal.fire({
        title: `${action} المستخدم`,
        text: `هل أنت متأكد من ${action} هذا المستخدم؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: `نعم، ${action}`,
        cancelButtonText: 'إلغاء',
        confirmButtonColor: color
    }).then((result) => {
        if (result.isConfirmed) {
            const button = event.target.closest('button');
            button.classList.add('loading');

            fetch(`/admin/users/${userId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({status: status})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'تم بنجاح!',
                        text: data.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ', data.message, 'error');
                    button.classList.remove('loading');
                }
            });
        }
    });
}

// إضافة CSS للحركات
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .selected {
        background-color: #eff6ff !important;
        border-left: 3px solid #3b82f6 !important;
    }

    .user-row:hover {
        background-color: #f8fafc !important;
        transform: translateX(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
