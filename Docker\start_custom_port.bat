@echo off
title Training System - Docker - Custom Port

echo.
echo ========================================
echo  Training System - Custom Port
echo ========================================
echo.

set /p PORT="Enter port number (default 5000): "
if "%PORT%"=="" set PORT=5000

echo.
echo Checking Docker...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running
    pause
    exit /b 1
)

echo Docker is available
echo.

echo Building Docker image...
docker build -t training_system .

if %errorlevel% equ 0 (
    echo.
    echo Starting system on port %PORT%...
    docker run -d -p %PORT%:5000 --name training_system_%PORT% training_system
    
    if %errorlevel% equ 0 (
        echo.
        echo ========================================
        echo SUCCESS: System started successfully!
        echo ========================================
        echo.
        echo Access the system at:
        echo    http://localhost:%PORT%
        echo.
        echo Login credentials:
        echo    Email: <EMAIL>
        echo    Password: admin123
        echo.
        echo Useful commands:
        echo    View logs: docker logs training_system_%PORT%
        echo    Stop system: docker stop training_system_%PORT%
        echo    Remove container: docker rm training_system_%PORT%
        echo.
        echo Opening browser in 5 seconds...
        timeout /t 5 /nobreak > nul
        start http://localhost:%PORT%
    ) else (
        echo ERROR: Failed to start system
    )
) else (
    echo ERROR: Failed to build Docker image
)

echo.
pause
