# 🎓 نظام إدارة التدريب المتطور
## Training Management System

### 📋 **نظرة عامة**
نظام شامل ومتطور لإدارة التدريب والدورات التدريبية مع مميزات ذكية لتحليل الأسماء والبيانات.

---

## 🚀 **طرق التشغيل السريع**

### **1. التشغيل الموحد (الأفضل)**
```powershell
# إعداد النظام لأول مرة
.\start_system.ps1 -Setup

# تشغيل سريع
.\start_system.ps1 -Quick

# تشغيل عادي مع فحص المتطلبات
.\start_system.ps1
```

### **2. الطرق البديلة**
```powershell
# باستخدام ملفات الإعداد المنفصلة
.\setup_system.ps1    # إعداد
.\quick_start.ps1     # تشغيل

# الطريقة اليدوية
.venv\Scripts\Activate.ps1
python app.py
```

---

## 🎯 **المميزات الرئيسية**

### **1. إدارة الدورات التدريبية**
- ✅ إنشاء وتحرير الدورات
- ✅ إدارة المشاركين والمدربين
- ✅ الجدولة الزمنية للدورات
- ✅ تتبع التقدم والحضور
- ✅ إدارة المواد التدريبية

### **2. النظام الذكي لتحليل الأسماء**
- 🧠 **تحليل البيانات الشخصية** - مقارنة مع قاعدة البيانات الرئيسية
- 🧠 **تحليل كشوف الدورات** - إدارة المشاركين في الدورات
- 🧠 **تحليل التقييمات** - إدارة تقييمات المشاركين مع تمييز ذكي

### **3. مميزات التحليل الذكي**
- 🔍 **تصحيح الأسماء تلقائياً** - خوارزميات متقدمة للتصحيح
- 🔍 **كشف التطابق المتقدم** - بالرقم الوطني، الهاتف، الرقم العسكري
- 🔍 **تحليل التشابه** - ثلاثي، رباعي، خماسي، كامل
- 🔍 **فئات ذكية** - تصنيف تلقائي للحالات المختلفة

### **4. التقارير المتقدمة**
- 📊 **تقارير Excel مفصلة** - أوراق منفصلة لكل فئة
- 📊 **إحصائيات شاملة** - تحليل دقيق للبيانات
- 📊 **تقارير التقييمات** - تمييز بين التحديث والإضافة
- 📊 **تصدير ذكي** - تنسيق احترافي مع RTL

---

## 📁 **هيكل النظام**

### **الملفات الرئيسية**
```
📁 نظام إدارة التدريب/
├── 🚀 start_system.ps1          # ملف التشغيل الموحد
├── ⚙️ setup_system.ps1          # إعداد النظام
├── 🏃 quick_start.ps1           # تشغيل سريع
├── 🐍 app.py                    # التطبيق الرئيسي
├── 📊 person_data_routes.py     # مسارات تحليل الأسماء
├── 📈 reports_generator.py      # مولد التقارير
└── 💾 training_system.db       # قاعدة البيانات
```

### **المجلدات**
```
📁 templates/                   # قوالب HTML
├── 📄 person_data/             # صفحات تحليل الأسماء
├── 📄 dashboard.html           # لوحة التحكم
└── 📄 layout.html              # التخطيط الأساسي

📁 static/                      # الملفات الثابتة
├── 🎨 css/                     # ملفات التنسيق
├── 📜 js/                      # ملفات JavaScript
└── 📚 libs/                    # المكتبات

📁 uploads/                     # ملفات التحميل
📁 exports/                     # ملفات التصدير
📁 backups/                     # النسخ الاحتياطية
```

---

## 🔧 **متطلبات النظام**

### **البرامج المطلوبة**
- 🐍 **Python 3.8+** - لغة البرمجة الأساسية
- 💻 **PowerShell** - لتشغيل ملفات الإعداد
- 🌐 **متصفح ويب حديث** - Chrome, Firefox, Edge

### **المكتبات المطلوبة**
```
Flask==2.3.3                   # إطار العمل الأساسي
Flask-SQLAlchemy==3.0.5        # قاعدة البيانات
Flask-Login==0.6.3             # إدارة المستخدمين
pandas==2.1.1                  # معالجة البيانات
openpyxl==3.1.2                # ملفات Excel
xlsxwriter==3.1.9              # كتابة Excel
arabic-reshaper==3.0.0         # دعم العربية
python-bidi==0.4.2             # اتجاه النص العربي
```

---

## 🎮 **دليل الاستخدام**

### **الخطوة 1: الإعداد الأولي**
```powershell
# تشغيل الإعداد الشامل
.\start_system.ps1 -Setup
```

### **الخطوة 2: تشغيل النظام**
```powershell
# تشغيل سريع
.\start_system.ps1 -Quick
```

### **الخطوة 3: الوصول للنظام**
- 🌐 افتح المتصفح واذهب إلى: `http://localhost:5000`
- 👤 سجل دخول أو أنشئ حساب جديد
- 🎯 ابدأ باستخدام المميزات

---

## 🧠 **استخدام النظام الذكي لتحليل الأسماء**

### **1. تحليل البيانات الشخصية**
- 📁 ارفع ملف Excel بالبيانات الشخصية
- 🔍 اختر "تحليل البيانات الشخصية"
- 📊 احصل على تقرير مفصل بالموجودين والجدد

### **2. تحليل كشوف الدورات**
- 📁 ارفع ملف Excel بكشف المشاركين
- 🎯 اختر الدورة المطلوبة
- 🔍 اختر "تحليل كشف الدورة"
- 📊 احصل على تحليل ذكي للمشاركين

### **3. تحليل التقييمات**
- 📁 ارفع ملف Excel بالتقييمات
- 🎯 اختر الدورة المطلوبة
- 🔍 اختر "تحليل التقييمات"
- 📊 احصل على تقرير يميز بين:
  - ✅ **لديهم تقييم** - سيتم التحديث
  - ➕ **ليس لديهم تقييم** - سيتم الإضافة
  - 👤 **أشخاص جدد** - سيتم إنشاؤهم مع تقييمهم

---

## 📊 **فهم التقارير**

### **تقرير البيانات الشخصية**
- 📋 **موجود في قاعدة البيانات** - أشخاص مسجلين مسبقاً
- 📋 **غير موجود في قاعدة البيانات** - أشخاص جدد سيتم إضافتهم

### **تقرير الدورات**
- 📋 **موجود في قاعدة البيانات** - أشخاص مسجلين
- 📋 **مكرر في الدورة** - موجود مسبقاً في الدورة
- 📋 **سيتم إضافته للدورة** - سيتم تسجيله في الدورة
- 📋 **غير موجود** - أشخاص جدد

### **تقرير التقييمات**
- 📋 **لديهم تقييم - سيتم التحديث** - تحديث تقييم موجود
- 📋 **ليس لديهم تقييم - سيتم الإضافة** - إضافة تقييم جديد
- 📋 **غير موجود - سيتم إدخال تقييم لهم** - إنشاء شخص مع تقييمه

---

## 🛠️ **استكشاف الأخطاء**

### **مشاكل شائعة وحلولها**

#### **خطأ: Python غير مثبت**
```powershell
# تحميل وتثبيت Python من python.org
# ثم تشغيل:
.\start_system.ps1 -Setup
```

#### **خطأ: البيئة الافتراضية**
```powershell
# حذف البيئة القديمة وإعادة إنشائها
Remove-Item -Recurse -Force .venv
.\start_system.ps1 -Setup
```

#### **خطأ: المكتبات**
```powershell
# تحديث pip وإعادة تثبيت المكتبات
.venv\Scripts\Activate.ps1
python -m pip install --upgrade pip
pip install -r requirements.txt
```

#### **خطأ: قاعدة البيانات**
```powershell
# إعادة إنشاء قاعدة البيانات
.venv\Scripts\Activate.ps1
python -c "from app import app, db; app.app_context().push(); db.create_all()"
```

---

## 🔒 **الأمان والنسخ الاحتياطي**

### **النسخ الاحتياطي التلقائي**
- 💾 النظام ينشئ نسخ احتياطية تلقائياً
- 📁 النسخ محفوظة في مجلد `backups/`
- ⏰ نسخ احتياطية عند الإغلاق والتحديثات المهمة

### **استعادة النسخ الاحتياطية**
```powershell
# نسخ ملف قاعدة البيانات من النسخة الاحتياطية
copy "backups\backup_YYYYMMDD_HHMMSS.zip" .
# استخراج الملفات واستبدال قاعدة البيانات
```

---

## 📞 **الدعم والمساعدة**

### **للحصول على المساعدة**
```powershell
# عرض دليل المساعدة
.\start_system.ps1 -Help
```

### **ملفات السجلات**
- 📄 `import_errors.log` - أخطاء الاستيراد
- 📄 `backup_log.txt` - سجل النسخ الاحتياطية

---

## 🎉 **ابدأ الآن!**

```powershell
# إعداد وتشغيل النظام في خطوة واحدة
.\start_system.ps1 -Setup

# ثم تشغيل سريع في المرات القادمة
.\start_system.ps1 -Quick
```

**🌐 النظام سيكون متاحاً على: http://localhost:5000**

---

*نظام إدارة التدريب المتطور - حل شامل لإدارة التدريب والدورات التدريبية* 🎓✨
