{% extends "layout.html" %}

{% block styles %}
<style>
    .reference-table {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .reference-table-header {
        background-color: #4a6bff;
        color: white;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .reference-table-title {
        margin: 0;
        font-size: 1.5rem;
    }

    .reference-table-body {
        padding: 20px;
    }

    .table {
        margin-bottom: 0;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    .btn-add {
        background-color: #4a6bff;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        transition: all 0.3s;
    }

    .btn-add:hover {
        background-color: #3a5ae8;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    .btn-edit {
        background-color: #ffc107;
        color: #212529;
    }

    .btn-delete {
        background-color: #dc3545;
        color: white;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }

    .badge {
        font-size: 0.9em;
        padding: 5px 10px;
        border-radius: 10px;
    }

    .badge-primary { background-color: #4a6bff; }
    .badge-secondary { background-color: #6c757d; }
    .badge-success { background-color: #28a745; }
    .badge-danger { background-color: #dc3545; }
    .badge-warning { background-color: #ffc107; color: #212529; }
    .badge-info { background-color: #17a2b8; }
    .badge-light { background-color: #f8f9fa; color: #212529; }
    .badge-dark { background-color: #343a40; }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('reference_tables') }}">الجداول الترميزية</a></li>
            <li class="breadcrumb-item active" aria-current="page">أنواع المؤهلات</li>
        </ol>
    </nav>

    <div class="reference-table">
        <div class="reference-table-header">
            <h3 class="reference-table-title">أنواع المؤهلات</h3>
            <a href="{{ url_for('add_qualification_type') }}" class="btn btn-add">
                <i class="fas fa-plus-circle me-1"></i> إضافة نوع مؤهل جديد
            </a>
        </div>
        <div class="reference-table-body">
            {% if qualification_types %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>الوصف</th>
                            <th>المستوى</th>
                            <th>الرمز</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for qualification_type in qualification_types %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ qualification_type.name }}</td>
                            <td>{{ qualification_type.description or '-' }}</td>
                            <td>
                                {% if qualification_type.level == 'primary' %}
                                    <span class="badge badge-info">ابتدائي</span>
                                {% elif qualification_type.level == 'middle' %}
                                    <span class="badge badge-light">متوسط</span>
                                {% elif qualification_type.level == 'secondary' %}
                                    <span class="badge badge-secondary">ثانوي</span>
                                {% elif qualification_type.level == 'diploma' %}
                                    <span class="badge badge-warning">دبلوم</span>
                                {% elif qualification_type.level == 'bachelor' %}
                                    <span class="badge badge-primary">بكالوريوس</span>
                                {% elif qualification_type.level == 'master' %}
                                    <span class="badge badge-success">ماجستير</span>
                                {% elif qualification_type.level == 'phd' %}
                                    <span class="badge badge-danger">دكتوراه</span>
                                {% else %}
                                    <span class="badge badge-dark">أخرى</span>
                                {% endif %}
                            </td>
                            <td>{{ qualification_type.code or '-' }}</td>
                            <td>
                                <a href="{{ url_for('edit_qualification_type', qualification_type_id=qualification_type.id) }}" class="btn btn-edit btn-sm">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{{ url_for('delete_qualification_type', qualification_type_id=qualification_type.id) }}" class="btn btn-delete btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذا النوع؟');">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد أنواع مؤهلات مضافة حتى الآن.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
