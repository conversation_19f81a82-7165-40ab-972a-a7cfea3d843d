# 🌳 دليل النظام المتقدم لإدارة المستخدمين والأدوار

## 🎯 نظرة عامة

تم إنشاء نظام متقدم لإدارة المستخدمين والأدوار يعتمد على **شجرة النظام الكاملة** مع إمكانية التحكم الدقيق في الصلاحيات على مستوى كل شاشة وكل زر.

## 🏗️ مكونات النظام

### 1️⃣ **كارت عرض شجرة النظام**
- **الغرض:** عرض شجرة النظام الكاملة للاطلاع فقط
- **المحتوى:** جميع الشاشات والأزرار المتاحة في النظام
- **الاستخدام:** للمراجعة والفهم العام لهيكل النظام

### 2️⃣ **كارت إدارة الأدوار**
- **الغرض:** إنشاء وتعديل الأدوار مع تحديد الصلاحيات
- **المميزات:**
  - إدخال اسم الدور ووصفه
  - عرض شجرة النظام مع checkboxes للصلاحيات
  - تحديد صلاحيات دقيقة لكل شاشة وزر
  - حفظ الدور مع الصلاحيات المحددة

### 3️⃣ **كارت إدارة المستخدمين**
- **الغرض:** إضافة وتعديل المستخدمين
- **المميزات:**
  - إدخال بيانات المستخدم الكاملة
  - اختيار الدور من قائمة الأدوار المتاحة
  - تطبيق صلاحيات الدور عند تسجيل الدخول

## 🌳 شجرة النظام الكاملة

### الوحدات الرئيسية (8 وحدات):

#### 📊 **لوحة التحكم** (dashboard)
- عرض لوحة التحكم
- تصدير إحصائيات لوحة التحكم

#### 👥 **إدارة المستخدمين** (users_management)
- عرض المستخدمين
- إضافة مستخدم جديد
- تعديل بيانات المستخدم
- حذف المستخدم
- إعادة تعيين كلمة المرور
- تفعيل/إلغاء تفعيل المستخدم
- تصدير قائمة المستخدمين
- استيراد المستخدمين
- طباعة قائمة المستخدمين

#### 🛡️ **إدارة الأدوار والصلاحيات** (roles_management)
- عرض الأدوار
- إضافة دور جديد
- تعديل الدور
- حذف الدور
- تعيين الصلاحيات للدور
- تصدير الأدوار
- طباعة الأدوار

#### 🎓 **إدارة الدورات** (courses_management)
- عرض الدورات
- إضافة دورة جديدة
- تعديل الدورة
- حذف الدورة
- نسخ الدورة
- تصدير الدورات
- استيراد الدورات
- طباعة الدورات

##### 👥 **إدارة المشاركين** (course_participants)
- عرض المشاركين
- إضافة مشارك
- تعديل بيانات المشارك
- حذف المشارك
- تصدير المشاركين
- طباعة قائمة المشاركين
- إرسال الشهادات

##### 📅 **جدولة الدورات** (course_schedule)
- عرض الجدول
- إضافة موعد
- تعديل الموعد
- حذف الموعد
- تصدير الجدول
- طباعة الجدول

#### 📇 **إدارة بيانات الأشخاص** (persons_management)
- عرض بيانات الأشخاص
- إضافة شخص جديد
- تعديل بيانات الشخص
- حذف الشخص
- تصدير البيانات
- استيراد البيانات
- طباعة البيانات
- البحث المتقدم

#### 📊 **التقارير** (reports)
- عرض التقارير
- تصدير التقارير
- طباعة التقارير
- جدولة التقارير

##### 👥 **تقارير المستخدمين** (users_reports)
- عرض تقارير المستخدمين
- تصدير تقارير المستخدمين
- طباعة تقارير المستخدمين

##### 🎓 **تقارير الدورات** (courses_reports)
- عرض تقارير الدورات
- تصدير تقارير الدورات
- طباعة تقارير الدورات

##### 💰 **التقارير المالية** (financial_reports)
- عرض التقارير المالية
- تصدير التقارير المالية
- طباعة التقارير المالية

#### 📋 **الجداول الترميزية** (reference_tables)
- عرض الجداول الترميزية
- إضافة جدول ترميزي
- تعديل الجدول الترميزي
- حذف الجدول الترميزي
- تصدير الجداول
- طباعة الجداول

##### 🛤️ **مسارات التدريب** (course_paths)
- عرض المسارات
- إضافة مسار
- تعديل المسار
- حذف المسار

##### 📍 **المواقع** (locations)
- عرض المواقع
- إضافة موقع
- تعديل الموقع
- حذف الموقع

#### ⚙️ **إعدادات النظام** (system_settings)
- عرض الإعدادات
- تعديل الإعدادات
- إنشاء نسخة احتياطية
- استعادة نسخة احتياطية
- عرض سجلات النظام
- صيانة النظام

## 🚀 كيفية الاستخدام

### 1. **الوصول للنظام المتقدم:**
```
http://localhost:5000/admin/advanced-users
```

### 2. **تسجيل الدخول:**
```
البريد: <EMAIL>
كلمة المرور: admin123
```

### 3. **إنشاء دور جديد:**
1. اضغط على كارت "إدارة الأدوار"
2. اضغط "إضافة دور جديد"
3. أدخل اسم الدور ووصفه
4. حدد الصلاحيات من شجرة النظام
5. اضغط "حفظ الدور"

### 4. **إضافة مستخدم جديد:**
1. اضغط على كارت "إدارة المستخدمين"
2. اضغط "إضافة مستخدم جديد"
3. أدخل بيانات المستخدم
4. اختر الدور من القائمة
5. اضغط "حفظ المستخدم"

### 5. **عرض شجرة النظام:**
1. اضغط على كارت "عرض شجرة النظام"
2. ستظهر شجرة النظام الكاملة للمراجعة

## 📊 الإحصائيات

- **الوحدات الرئيسية:** 8 وحدات
- **الوحدات الفرعية:** 5 وحدات
- **إجمالي الصلاحيات:** 80 صلاحية
- **الأدوار الافتراضية:** 6 أدوار

## 🔧 الملفات المهمة

- `system_tree_manager.py` - مدير شجرة النظام
- `templates/admin/advanced_users_management.html` - الواجهة الرئيسية
- `upgrade_advanced_roles.py` - ترقية قاعدة البيانات
- `test_advanced_system.py` - اختبار النظام

## 🎯 المميزات الرئيسية

✅ **شجرة نظام شاملة** مع 80 صلاحية مختلفة
✅ **واجهة مستخدم متقدمة** مع 3 كروت رئيسية
✅ **نظام أدوار مرن** مع تحكم دقيق في الصلاحيات
✅ **إدارة مستخدمين متطورة** مع ربط الأدوار
✅ **APIs متكاملة** لجميع العمليات
✅ **تصميم responsive** يعمل على جميع الأجهزة
✅ **نظام اختبار شامل** للتأكد من سلامة العمل

## 🔗 الروابط المهمة

- **النظام المتقدم:** http://localhost:5000/admin/advanced-users
- **النظام العادي:** http://localhost:5000/admin/users
- **لوحة التحكم:** http://localhost:5000/dashboard
- **تسجيل الدخول:** http://localhost:5000/login

---

**🎉 النظام جاهز للاستخدام بالكامل!**
