import os
import sys
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///training_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# تعريف نموذج البيانات الشخصية
class PersonalData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    national_number = db.Column(db.String(20), nullable=True)
    nickname = db.Column(db.String(50), nullable=True)
    birth_date = db.Column(db.Date, nullable=True)
    age = db.Column(db.Integer, nullable=True)
    phone_yemen_mobile = db.Column(db.String(20), nullable=True)
    job_title = db.Column(db.String(100), nullable=True)
    work_place = db.Column(db.String(100), nullable=True)

    def __repr__(self):
        return f"PersonalData('{self.full_name}')"

def add_new_person():
    """
    إضافة شخص جديد إلى قاعدة البيانات
    """
    try:
        # إنشاء كائن PersonalData جديد
        new_person = PersonalData(
            user_id=1,  # افتراض أن معرف المستخدم هو 1
            full_name="محمد أحمد علي",
            national_number="1234567890",
            nickname="أبو أحمد",
            birth_date=datetime.strptime("1990-01-01", "%Y-%m-%d"),
            age=33,
            phone_yemen_mobile="777123456",
            job_title="مهندس برمجيات",
            work_place="شركة البرمجيات"
        )

        # التحقق من أن الكائن هو من نوع PersonalData
        print(f"نوع الكائن: {type(new_person)}")

        # إضافة الكائن إلى قاعدة البيانات
        db.session.add(new_person)
        db.session.commit()

        print(f"تم إضافة الشخص بنجاح: {new_person.full_name}, الرقم الوطني: {new_person.national_number}")
        return True
    except Exception as e:
        db.session.rollback()
        print(f"حدث خطأ أثناء إضافة الشخص: {str(e)}")
        return False

if __name__ == "__main__":
    with app.app_context():
        add_new_person()
