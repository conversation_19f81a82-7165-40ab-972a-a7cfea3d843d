#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ترقية قاعدة البيانات لنظام الأدوار المتقدم
"""

import sqlite3
import json
from datetime import datetime
from system_tree_manager import SystemTreeManager

def upgrade_database():
    """ترقية قاعدة البيانات"""
    
    print("🔄 ترقية قاعدة البيانات للنظام المتقدم...")
    
    conn = sqlite3.connect('training_system.db')
    cursor = conn.cursor()
    
    try:
        # إضافة عمود permissions_json إلى جدول role
        cursor.execute("PRAGMA table_info(role)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'permissions_json' not in columns:
            cursor.execute("ALTER TABLE role ADD COLUMN permissions_json TEXT")
            print("✅ تم إضافة عمود permissions_json")
        
        # تحديث الأدوار الموجودة
        cursor.execute("SELECT id, name FROM role WHERE is_active = 1")
        existing_roles = cursor.fetchall()
        
        for role_id, role_name in existing_roles:
            permissions_structure = SystemTreeManager.create_role_permissions_structure()
            
            # تطبيق صلاحيات حسب الدور
            if role_name == 'admin':
                # المدير له جميع الصلاحيات
                for module_key, module_data in permissions_structure.items():
                    for perm_key in module_data['permissions']:
                        permissions_structure[module_key]['permissions'][perm_key]['enabled'] = True
                    for sub_key, sub_data in module_data['sub_modules'].items():
                        for perm_key in sub_data['permissions']:
                            permissions_structure[module_key]['sub_modules'][sub_key]['permissions'][perm_key]['enabled'] = True
            
            elif role_name == 'manager':
                manager_permissions = [
                    'users_management.view', 'users_management.edit',
                    'courses_management.view', 'courses_management.add', 'courses_management.edit',
                    'reports.view', 'reports.export'
                ]
                for permission in manager_permissions:
                    parts = permission.split('.')
                    if len(parts) == 2:
                        module_key, perm_key = parts
                        if module_key in permissions_structure and perm_key in permissions_structure[module_key]['permissions']:
                            permissions_structure[module_key]['permissions'][perm_key]['enabled'] = True
            
            elif role_name == 'trainer':
                trainer_permissions = [
                    'courses_management.view', 'courses_management.edit',
                    'course_participants.view', 'course_participants.add', 'course_participants.edit',
                    'reports.view'
                ]
                for permission in trainer_permissions:
                    parts = permission.split('.')
                    if len(parts) == 2:
                        module_key, perm_key = parts
                        if module_key in permissions_structure and perm_key in permissions_structure[module_key]['permissions']:
                            permissions_structure[module_key]['permissions'][perm_key]['enabled'] = True
            
            elif role_name == 'viewer':
                viewer_permissions = [
                    'dashboard.view',
                    'courses_management.view',
                    'reports.view'
                ]
                for permission in viewer_permissions:
                    parts = permission.split('.')
                    if len(parts) == 2:
                        module_key, perm_key = parts
                        if module_key in permissions_structure and perm_key in permissions_structure[module_key]['permissions']:
                            permissions_structure[module_key]['permissions'][perm_key]['enabled'] = True
            
            permissions_json = SystemTreeManager.get_role_permissions_json(permissions_structure)
            
            cursor.execute("UPDATE role SET permissions_json = ? WHERE id = ?", (permissions_json, role_id))
            print(f"✅ تم تحديث الدور: {role_name}")
        
        # إنشاء أدوار جديدة
        new_roles = [
            {
                'name': 'hr_manager',
                'display_name': 'مدير الموارد البشرية',
                'description': 'مدير الموارد البشرية',
                'permissions': [
                    'users_management.view', 'users_management.add', 'users_management.edit',
                    'reports.view', 'reports.export'
                ]
            },
            {
                'name': 'training_coordinator',
                'display_name': 'منسق التدريب',
                'description': 'منسق التدريب',
                'permissions': [
                    'courses_management.view', 'courses_management.add', 'courses_management.edit',
                    'course_participants.view', 'course_participants.add'
                ]
            }
        ]
        
        for role_data in new_roles:
            cursor.execute("SELECT id FROM role WHERE name = ?", (role_data['name'],))
            if cursor.fetchone():
                continue
            
            permissions_structure = SystemTreeManager.create_role_permissions_structure()
            
            for permission in role_data['permissions']:
                parts = permission.split('.')
                if len(parts) == 2:
                    module_key, perm_key = parts
                    if module_key in permissions_structure and perm_key in permissions_structure[module_key]['permissions']:
                        permissions_structure[module_key]['permissions'][perm_key]['enabled'] = True
            
            permissions_json = SystemTreeManager.get_role_permissions_json(permissions_structure)
            
            cursor.execute("""
                INSERT INTO role (name, display_name, description, permissions_json, is_active, is_system_role, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                role_data['name'],
                role_data['display_name'],
                role_data['description'],
                permissions_json,
                1,
                0,
                datetime.now().isoformat()
            ))
            
            print(f"✅ تم إنشاء الدور: {role_data['display_name']}")
        
        conn.commit()
        print("✅ تمت الترقية بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == '__main__':
    upgrade_database()
