#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لصفحة name_analysis_course
"""

import requests
import json
import time

# إعدادات الاختبار
BASE_URL = "http://localhost:5001"
TEST_SESSION = requests.Session()

def test_login():
    """اختبار تسجيل الدخول"""
    print("🔐 اختبار تسجيل الدخول...")
    
    # الحصول على صفحة تسجيل الدخول
    response = TEST_SESSION.get(f"{BASE_URL}/login")
    if response.status_code != 200:
        print(f"❌ خطأ في الوصول لصفحة تسجيل الدخول: {response.status_code}")
        return False
    
    # تسجيل الدخول
    login_data = {
        'username': 'admin',
        'password': 'admin'
    }
    
    response = TEST_SESSION.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200 and 'dashboard' in response.url:
        print("✅ تم تسجيل الدخول بنجاح")
        return True
    else:
        print(f"❌ فشل تسجيل الدخول: {response.status_code}")
        return False

def test_api_courses():
    """اختبار API الدورات"""
    print("📚 اختبار API الدورات...")
    
    response = TEST_SESSION.get(f"{BASE_URL}/api/courses")
    
    if response.status_code == 200:
        try:
            courses = response.json()
            print(f"✅ تم جلب {len(courses)} دورة")
            
            if courses:
                print("📋 أول دورة:")
                first_course = courses[0]
                for key, value in first_course.items():
                    print(f"   {key}: {value}")
            
            return True, courses
        except json.JSONDecodeError:
            print("❌ خطأ في تحليل JSON")
            return False, []
    else:
        print(f"❌ خطأ في API الدورات: {response.status_code}")
        print(f"Response: {response.text}")
        return False, []

def test_name_analysis_page():
    """اختبار صفحة name_analysis"""
    print("🧠 اختبار صفحة name_analysis...")
    
    response = TEST_SESSION.get(f"{BASE_URL}/name_analysis")
    
    if response.status_code == 200:
        print("✅ تم الوصول لصفحة name_analysis بنجاح")
        
        # التحقق من وجود العناصر المهمة
        content = response.text
        checks = [
            ('بدء التحليل الذكي', 'زر التحليل الذكي'),
            ('اختر ملف Excel', 'منطقة رفع الملف'),
            ('form', 'النموذج'),
            ('progressContainer', 'مؤشر التقدم')
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}: موجود")
            else:
                print(f"   ❌ {description}: غير موجود")
        
        return True
    else:
        print(f"❌ خطأ في الوصول لصفحة name_analysis: {response.status_code}")
        return False

def test_name_analysis_course_page():
    """اختبار صفحة name_analysis_course"""
    print("🎯 اختبار صفحة name_analysis_course...")
    
    response = TEST_SESSION.get(f"{BASE_URL}/name_analysis_course")
    
    if response.status_code == 200:
        print("✅ تم الوصول لصفحة name_analysis_course بنجاح")
        
        # التحقق من وجود العناصر المهمة
        content = response.text
        checks = [
            ('التحليل الذكي للدورات', 'عنوان الصفحة'),
            ('اختر الدورة لإضافة المشاركين', 'قسم اختيار الدورة'),
            ('مركز التحكم الذكي', 'أدوات التحكم'),
            ('courses-list', 'قائمة الدورات'),
            ('results-display', 'منطقة عرض النتائج')
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}: موجود")
            else:
                print(f"   ❌ {description}: غير موجود")
        
        return True
    else:
        print(f"❌ خطأ في الوصول لصفحة name_analysis_course: {response.status_code}")
        print(f"Response: {response.text[:500]}...")
        return False

def test_course_participants_api():
    """اختبار API مشاركي الدورة"""
    print("👥 اختبار API مشاركي الدورة...")
    
    # جرب مع دورة رقم 1
    response = TEST_SESSION.get(f"{BASE_URL}/api/course/1/participants")
    
    if response.status_code == 200:
        try:
            participants = response.json()
            print(f"✅ تم جلب {len(participants)} مشارك للدورة 1")
            
            if participants:
                print("👤 أول مشارك:")
                first_participant = participants[0]
                for key, value in first_participant.items():
                    print(f"   {key}: {value}")
            
            return True
        except json.JSONDecodeError:
            print("❌ خطأ في تحليل JSON")
            return False
    else:
        print(f"❌ خطأ في API مشاركي الدورة: {response.status_code}")
        print(f"Response: {response.text}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء الاختبار الشامل لصفحة name_analysis_course")
    print("=" * 60)
    
    # اختبار تسجيل الدخول
    if not test_login():
        print("❌ فشل في تسجيل الدخول - توقف الاختبار")
        return
    
    print()
    
    # اختبار API الدورات
    api_success, courses = test_api_courses()
    print()
    
    # اختبار صفحة name_analysis
    test_name_analysis_page()
    print()
    
    # اختبار صفحة name_analysis_course
    test_name_analysis_course_page()
    print()
    
    # اختبار API مشاركي الدورة
    test_course_participants_api()
    print()
    
    print("=" * 60)
    print("🎉 انتهى الاختبار الشامل")
    
    # ملخص النتائج
    print("\n📊 ملخص النتائج:")
    print("✅ تسجيل الدخول: نجح")
    print(f"{'✅' if api_success else '❌'} API الدورات: {'نجح' if api_success else 'فشل'}")
    print("✅ صفحة name_analysis: تم اختبارها")
    print("✅ صفحة name_analysis_course: تم اختبارها")
    print("✅ API مشاركي الدورة: تم اختباره")

if __name__ == "__main__":
    run_all_tests()
