# 🔐 دليل تطبيق نظام إدارة الصلاحيات

## 📋 فهرس المحتويات
1. [مفهوم النظام](#مفهوم-النظام)
2. [كيفية تحديد الصلاحيات](#كيفية-تحديد-الصلاحيات)
3. [إخفاء/إظهار الشاشات](#إخفاءإظهار-الشاشات)
4. [إخفاء/إظهار الأزرار](#إخفاءإظهار-الأزرار)
5. [أمثلة عملية](#أمثلة-عملية)
6. [إضافة صلاحيات جديدة](#إضافة-صلاحيات-جديدة)

---

## 🎯 مفهوم النظام

### الصلاحيات (Permissions)
الصلاحيات هي **إذن للقيام بعمل معين** مثل:
- `users.view` = عرض المستخدمين
- `users.create` = إنشاء مستخدمين جدد
- `users.edit` = تعديل المستخدمين
- `users.delete` = حذف المستخدمين

### الأدوار (Roles)
الأدوار هي **مجموعة من الصلاحيات** مثل:
- **admin** = جميع الصلاحيات
- **manager** = صلاحيات إدارية محدودة
- **trainer** = صلاحيات المدربين فقط
- **viewer** = صلاحيات العرض فقط

---

## 🔧 كيفية تحديد الصلاحيات

### 1. في ملف `permissions_manager.py`:

```python
# تعريف الصلاحيات
PERMISSIONS = {
    'users.view': 'عرض المستخدمين',
    'users.create': 'إنشاء مستخدمين جدد',
    'users.edit': 'تعديل المستخدمين',
    'users.delete': 'حذف المستخدمين',
    # ... المزيد
}

# تعريف الأدوار وصلاحياتها
DEFAULT_ROLES = {
    'admin': {
        'permissions': list(PERMISSIONS.keys())  # جميع الصلاحيات
    },
    'manager': {
        'permissions': ['users.view', 'users.edit', 'courses.view']
    },
    'viewer': {
        'permissions': ['users.view', 'courses.view']
    }
}
```

### 2. في قاعدة البيانات:
- جدول `permission`: يحتوي على جميع الصلاحيات
- جدول `role`: يحتوي على الأدوار
- جدول `role_permission`: يربط الأدوار بالصلاحيات
- جدول `user_role`: يربط المستخدمين بالأدوار

---

## 🖥️ إخفاء/إظهار الشاشات

### 1. حماية الصفحات في `app.py`:

```python
from permissions_manager import require_permission, require_role

# حماية صفحة بصلاحية معينة
@app.route('/admin/users')
@login_required
@require_permission('users.view')  # يحتاج صلاحية عرض المستخدمين
def users_list():
    return render_template('users.html')

# حماية صفحة بدور معين
@app.route('/admin/settings')
@login_required
@require_role('admin')  # فقط المديرين
def admin_settings():
    return render_template('settings.html')
```

### 2. إخفاء أقسام في Templates:

```html
<!-- إخفاء قسم كامل بناءً على صلاحية -->
{% if current_user.has_permission('users.view') %}
<div class="users-section">
    <h3>إدارة المستخدمين</h3>
    <!-- محتوى إدارة المستخدمين -->
</div>
{% endif %}

<!-- إخفاء قسم بناءً على الدور -->
{% if current_user.role == 'admin' %}
<div class="admin-panel">
    <h3>لوحة المدير</h3>
    <!-- محتوى خاص بالمديرين -->
</div>
{% endif %}
```

### 3. إخفاء عناصر الشريط الجانبي:

```html
<nav class="sidebar">
    <ul class="nav">
        <!-- رابط متاح للجميع -->
        <li><a href="/dashboard">الصفحة الرئيسية</a></li>
        
        <!-- رابط يظهر فقط لمن لديه صلاحية -->
        {% if current_user.has_permission('users.view') %}
        <li><a href="/admin/users">إدارة المستخدمين</a></li>
        {% endif %}
        
        {% if current_user.has_permission('courses.view') %}
        <li><a href="/courses">إدارة الدورات</a></li>
        {% endif %}
        
        {% if current_user.role == 'admin' %}
        <li><a href="/admin/settings">إعدادات النظام</a></li>
        {% endif %}
    </ul>
</nav>
```

---

## 🔘 إخفاء/إظهار الأزرار

### 1. أزرار الإجراءات:

```html
<!-- أزرار تظهر حسب الصلاحيات -->
<div class="action-buttons">
    {% if current_user.has_permission('users.create') %}
    <button class="btn btn-primary" onclick="addUser()">
        <i class="fas fa-plus"></i> إضافة مستخدم
    </button>
    {% endif %}
    
    {% if current_user.has_permission('users.import') %}
    <button class="btn btn-info" onclick="importUsers()">
        <i class="fas fa-upload"></i> استيراد
    </button>
    {% endif %}
    
    {% if current_user.has_permission('users.export') %}
    <button class="btn btn-success" onclick="exportUsers()">
        <i class="fas fa-download"></i> تصدير
    </button>
    {% endif %}
</div>
```

### 2. أزرار في الجداول:

```html
<table class="table">
    <tbody>
        {% for user in users %}
        <tr>
            <td>{{ user.name }}</td>
            <td>{{ user.email }}</td>
            <td>
                <!-- أزرار الإجراءات -->
                <div class="btn-group">
                    {% if current_user.has_permission('users.view') %}
                    <button class="btn btn-sm btn-info" onclick="viewUser({{ user.id }})">
                        <i class="fas fa-eye"></i>
                    </button>
                    {% endif %}
                    
                    {% if current_user.has_permission('users.edit') %}
                    <button class="btn btn-sm btn-warning" onclick="editUser({{ user.id }})">
                        <i class="fas fa-edit"></i>
                    </button>
                    {% endif %}
                    
                    {% if current_user.has_permission('users.delete') %}
                    <button class="btn btn-sm btn-danger" onclick="deleteUser({{ user.id }})">
                        <i class="fas fa-trash"></i>
                    </button>
                    {% endif %}
                </div>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

### 3. فحص الصلاحيات في JavaScript:

```html
<script>
// تمرير صلاحيات المستخدم إلى JavaScript
var userPermissions = {{ current_user.get_permissions_list() | tojson }};
var userRole = "{{ current_user.role }}";

function hasPermission(permission) {
    return userPermissions.includes(permission);
}

function deleteUser(userId) {
    // التحقق من الصلاحية قبل التنفيذ
    if (!hasPermission('users.delete')) {
        alert('ليس لديك صلاحية لحذف المستخدمين');
        return;
    }
    
    if (confirm('هل أنت متأكد من الحذف؟')) {
        // تنفيذ عملية الحذف
        fetch('/admin/users/' + userId + '/delete', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('فشل في الحذف: ' + data.message);
            }
        });
    }
}

// إخفاء أزرار بناءً على الصلاحيات عند تحميل الصفحة
$(document).ready(function() {
    if (!hasPermission('users.create')) {
        $('.btn-add-user').hide();
    }
    
    if (!hasPermission('users.delete')) {
        $('.btn-delete').hide();
    }
    
    if (userRole !== 'admin') {
        $('.admin-only').hide();
    }
});
</script>
```

---

## 💡 أمثلة عملية

### مثال 1: صفحة إدارة المستخدمين

```python
# في app.py
@app.route('/admin/users')
@login_required
@require_permission('users.view')
def users_management():
    users = User.query.all()
    return render_template('admin/users.html', users=users)

@app.route('/admin/users/create', methods=['POST'])
@login_required
@require_permission('users.create')
def create_user():
    # كود إنشاء المستخدم
    pass

@app.route('/admin/users/<int:user_id>/delete', methods=['POST'])
@login_required
@require_permission('users.delete')
def delete_user(user_id):
    # كود حذف المستخدم
    pass
```

```html
<!-- في template -->
<div class="container">
    <h2>إدارة المستخدمين</h2>
    
    <!-- زر الإضافة -->
    {% if current_user.has_permission('users.create') %}
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
        إضافة مستخدم جديد
    </button>
    {% endif %}
    
    <!-- جدول المستخدمين -->
    <table class="table">
        <!-- ... -->
    </table>
</div>
```

### مثال 2: قائمة منسدلة حسب الصلاحيات

```html
<div class="dropdown">
    <button class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
        الإجراءات
    </button>
    <ul class="dropdown-menu">
        {% if current_user.has_permission('users.view') %}
        <li><a class="dropdown-item" href="#" onclick="viewUser()">عرض</a></li>
        {% endif %}
        
        {% if current_user.has_permission('users.edit') %}
        <li><a class="dropdown-item" href="#" onclick="editUser()">تعديل</a></li>
        {% endif %}
        
        {% if current_user.has_permission('users.reset_password') %}
        <li><a class="dropdown-item" href="#" onclick="resetPassword()">إعادة تعيين كلمة المرور</a></li>
        {% endif %}
        
        {% if current_user.has_permission('users.delete') %}
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item text-danger" href="#" onclick="deleteUser()">حذف</a></li>
        {% endif %}
    </ul>
</div>
```

---

## ➕ إضافة صلاحيات جديدة

### 1. أضف الصلاحية في `permissions_manager.py`:

```python
PERMISSIONS = {
    # ... الصلاحيات الموجودة
    'inventory.view': 'عرض المخزون',
    'inventory.create': 'إضافة عناصر للمخزون',
    'inventory.edit': 'تعديل المخزون',
    'inventory.delete': 'حذف من المخزون',
}
```

### 2. أضف الصلاحيات للأدوار المناسبة:

```python
DEFAULT_ROLES = {
    'admin': {
        'permissions': list(PERMISSIONS.keys())  # جميع الصلاحيات
    },
    'manager': {
        'permissions': [
            # ... الصلاحيات الموجودة
            'inventory.view',
            'inventory.create',
            'inventory.edit'
        ]
    }
}
```

### 3. استخدم الصلاحيات في الكود:

```python
@app.route('/inventory')
@login_required
@require_permission('inventory.view')
def inventory_list():
    return render_template('inventory.html')
```

### 4. استخدم الصلاحيات في Templates:

```html
{% if current_user.has_permission('inventory.view') %}
<li><a href="/inventory">إدارة المخزون</a></li>
{% endif %}
```

---

## 🎯 الخلاصة

نظام إدارة الصلاحيات يعمل على **3 مستويات**:

1. **مستوى الخادم (Backend)**: حماية الـ Routes بـ `@require_permission`
2. **مستوى العرض (Frontend)**: إخفاء/إظهار العناصر بـ `{% if current_user.has_permission() %}`
3. **مستوى JavaScript**: فحص الصلاحيات قبل تنفيذ العمليات

هذا يضمن **أمان شامل** على جميع المستويات ويمنع الوصول غير المصرح به.

---

## 🔗 ملفات مهمة للمراجعة

- `permissions_manager.py` - تعريف الصلاحيات والأدوار
- `app.py` - استخدام الصلاحيات في الـ Routes
- `templates/admin/users_management.html` - مثال عملي للاستخدام
- `permissions_demo_page.html` - صفحة توضيحية تفاعلية
