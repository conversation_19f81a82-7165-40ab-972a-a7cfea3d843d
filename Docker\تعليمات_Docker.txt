========================================
    نظام التدريب والتأهيل - Docker
========================================

🎯 نظام شامل لإدارة التدريب والتأهيل باستخدام Docker
📍 يعمل على المنفذ: 5000 (قابل للتغيير)
🔧 لا يحتاج إلى تثبيت Python أو أي برامج إضافية

========================================
📋 المتطلبات الأساسية:
========================================

1️⃣ Docker Desktop مثبت ومشغل
   - تحميل من: https://www.docker.com/products/docker-desktop
   - تأكد من تشغيل Docker Desktop قبل البدء

========================================
🚀 طرق التشغيل:
========================================

1️⃣ التشغيل السريع (المنفذ الافتراضي 5000):
   - انقر نقراً مزدوجاً على ملف "docker_start.bat"

2️⃣ التشغيل على منفذ مخصص:
   - انقر نقراً مزدوجاً على ملف "docker_custom_port.bat"
   - أدخل رقم المنفذ المطلوب

3️⃣ التشغيل اليدوي:
   - افتح Command Prompt أو PowerShell
   - انتقل إلى مجلد النظام
   - نفذ الأوامر التالية:

   # بناء الصورة
   docker build -t training_system .
   
   # تشغيل النظام على المنفذ 5000
   docker run -d -p 5000:5000 --name training_system training_system
   
   # تشغيل النظام على منفذ مخصص (مثال: 8080)
   docker run -d -p 8080:5000 --name training_system_8080 training_system

4️⃣ استخدام Docker Compose:
   docker-compose up -d

========================================
🔑 بيانات تسجيل الدخول:
========================================

الإيميل: <EMAIL>
كلمة المرور: admin123

========================================
🌐 الوصول إلى النظام:
========================================

بعد تشغيل النظام، افتح المتصفح وانتقل إلى:
- المنفذ الافتراضي: http://localhost:5000
- منفذ مخصص: http://localhost:[رقم_المنفذ]

========================================
📋 أوامر Docker المفيدة:
========================================

# عرض الحاويات المشغلة
docker ps

# عرض السجلات
docker logs training_system

# إيقاف النظام
docker stop training_system

# إعادة تشغيل النظام
docker restart training_system

# حذف الحاوية
docker rm training_system

# حذف الصورة
docker rmi training_system

# تشغيل عدة نسخ على منافذ مختلفة
docker run -d -p 5001:5000 --name training_system_5001 training_system
docker run -d -p 8080:5000 --name training_system_8080 training_system

========================================
💾 إدارة البيانات:
========================================

# النسخ الاحتياطي لقاعدة البيانات
docker cp training_system:/app/training_system.db ./backup.db

# استعادة قاعدة البيانات
docker cp ./backup.db training_system:/app/training_system.db
docker restart training_system

========================================
🔧 استكشاف الأخطاء:
========================================

❌ إذا ظهر خطأ "Docker Engine غير مشغل":
   1. تأكد من تشغيل Docker Desktop
   2. انتظر حتى يكتمل تشغيل Docker Desktop
   3. أعد تشغيل الأمر

❌ إذا كان المنفذ مستخدم:
   1. غير رقم المنفذ في الأمر
   2. أو أوقف البرنامج الذي يستخدم المنفذ

❌ إذا فشل بناء الصورة:
   1. تأكد من وجود جميع الملفات
   2. تحقق من اتصال الإنترنت
   3. أعد المحاولة

❌ إذا لم يفتح المتصفح:
   1. افتح المتصفح يدوياً
   2. انتقل إلى العنوان المحدد

========================================
✨ المميزات:
========================================

✅ يعمل على أي نظام تشغيل يدعم Docker
✅ عزل كامل عن النظام المضيف
✅ سهولة النشر والتوزيع
✅ إمكانية تشغيل عدة نسخ
✅ حفظ البيانات خارج الحاوية
✅ تحديثات سهلة
✅ أمان عالي

========================================
📞 الدعم الفني:
========================================

في حالة وجود أي مشاكل:
1. تحقق من سجلات Docker: docker logs training_system
2. تأكد من تشغيل Docker Desktop
3. تحقق من توفر المنفذ المطلوب
4. أعد بناء الصورة: docker build -t training_system . --no-cache

========================================
🔄 إصدار النظام: 2025.1 - Docker
📅 تاريخ الإصدار: يونيو 2025
========================================
