#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت إضافة البيانات الأساسية المطلوبة للنظام
"""

from app import app, db
from app import (
    Agency, Department, Governorate, Directorate, Village,
    CoursePath, CoursePathLevel, ForceClassification,
    MaritalStatus, BloodType, IssuingAuthority, QualificationType,
    Specialization, AssignmentType, MilitaryRank, InjuryType,
    InjuryCause, CourseType, TrainingCenterType, Location,
    TrainingCenter, ParticipantType, CardType, CourseLevel,
    CourseCategory
)

def add_governorates():
    """إضافة المحافظات اليمنية"""
    governorates = [
        "صنعاء", "عدن", "تعز", "الحديدة", "إب", "ذمار", "حضرموت",
        "المحويت", "حجة", "صعدة", "عمران", "البيضاء", "أبين",
        "شبوة", "المهرة", "لحج", "الجوف", "مأرب", "الضالع",
        "ريمة", "أرخبيل سقطرى"
    ]
    
    print("📍 إضافة المحافظات...")
    for gov_name in governorates:
        if not Governorate.query.filter_by(name=gov_name).first():
            gov = Governorate(name=gov_name)
            db.session.add(gov)
    
    db.session.commit()
    print(f"✅ تم إضافة {len(governorates)} محافظة")

def add_agencies():
    """إضافة الجهات الحكومية"""
    agencies = [
        "وزارة الداخلية",
        "وزارة الدفاع", 
        "الأمن المركزي",
        "الحرس الجمهوري",
        "قوات الأمن الخاصة",
        "الشرطة العسكرية",
        "أمن المنشآت",
        "الدفاع المدني",
        "خفر السواحل",
        "أمن الطرق",
        "الأمن السياسي",
        "الاستخبارات العسكرية"
    ]
    
    print("🏢 إضافة الجهات...")
    for agency_name in agencies:
        if not Agency.query.filter_by(name=agency_name).first():
            agency = Agency(name=agency_name)
            db.session.add(agency)
    
    db.session.commit()
    print(f"✅ تم إضافة {len(agencies)} جهة")

def add_training_centers():
    """إضافة المراكز التدريبية"""
    centers = [
        "المركز التدريبي الرئيسي - صنعاء",
        "مركز التدريب المتقدم - عدن", 
        "مركز التدريب التخصصي - تعز",
        "مركز التدريب الأمني - الحديدة",
        "مركز التدريب العسكري - مأرب",
        "مركز التدريب القيادي - إب",
        "مركز التدريب الفني - ذمار",
        "مركز التدريب المهني - حضرموت"
    ]
    
    print("🏫 إضافة المراكز التدريبية...")
    for center_name in centers:
        if not TrainingCenter.query.filter_by(name=center_name).first():
            center = TrainingCenter(name=center_name, capacity=100, is_ready=True)
            db.session.add(center)
    
    db.session.commit()
    print(f"✅ تم إضافة {len(centers)} مركز تدريبي")

def add_course_levels():
    """إضافة مستويات الدورات"""
    levels = [
        "مبتدئ",
        "متوسط", 
        "متقدم",
        "خبير",
        "تخصصي",
        "قيادي"
    ]
    
    print("📊 إضافة مستويات الدورات...")
    for level_name in levels:
        if not CourseLevel.query.filter_by(name=level_name).first():
            level = CourseLevel(name=level_name)
            db.session.add(level)
    
    db.session.commit()
    print(f"✅ تم إضافة {len(levels)} مستوى")

def add_course_categories():
    """إضافة فئات الدورات"""
    categories = [
        "أمنية",
        "عسكرية",
        "فنية", 
        "إدارية",
        "قيادية",
        "تخصصية",
        "تأهيلية",
        "تطويرية"
    ]
    
    print("📚 إضافة فئات الدورات...")
    for category_name in categories:
        if not CourseCategory.query.filter_by(name=category_name).first():
            category = CourseCategory(name=category_name)
            db.session.add(category)
    
    db.session.commit()
    print(f"✅ تم إضافة {len(categories)} فئة")

def add_participant_types():
    """إضافة أنواع المشاركين"""
    types = [
        "ضباط",
        "ضباط صف",
        "جنود",
        "مدنيين",
        "متدربين جدد",
        "متدربين متقدمين"
    ]
    
    print("👥 إضافة أنواع المشاركين...")
    for type_name in types:
        if not ParticipantType.query.filter_by(name=type_name).first():
            ptype = ParticipantType(name=type_name)
            db.session.add(ptype)
    
    db.session.commit()
    print(f"✅ تم إضافة {len(types)} نوع مشارك")

def add_marital_status():
    """إضافة الحالات الاجتماعية"""
    statuses = [
        "أعزب",
        "متزوج",
        "مطلق", 
        "أرمل"
    ]
    
    print("💑 إضافة الحالات الاجتماعية...")
    for status_name in statuses:
        if not MaritalStatus.query.filter_by(name=status_name).first():
            status = MaritalStatus(name=status_name)
            db.session.add(status)
    
    db.session.commit()
    print(f"✅ تم إضافة {len(statuses)} حالة اجتماعية")

def add_blood_types():
    """إضافة فصائل الدم"""
    blood_types = [
        "A+", "A-", "B+", "B-", 
        "AB+", "AB-", "O+", "O-"
    ]
    
    print("🩸 إضافة فصائل الدم...")
    for blood_type in blood_types:
        if not BloodType.query.filter_by(name=blood_type).first():
            bt = BloodType(name=blood_type)
            db.session.add(bt)
    
    db.session.commit()
    print(f"✅ تم إضافة {len(blood_types)} فصيلة دم")

def add_qualification_types():
    """إضافة أنواع المؤهلات"""
    qualifications = [
        "ابتدائي",
        "إعدادي",
        "ثانوي",
        "دبلوم",
        "بكالوريوس",
        "ماجستير",
        "دكتوراه"
    ]
    
    print("🎓 إضافة أنواع المؤهلات...")
    for qual_name in qualifications:
        if not QualificationType.query.filter_by(name=qual_name).first():
            qual = QualificationType(name=qual_name)
            db.session.add(qual)
    
    db.session.commit()
    print(f"✅ تم إضافة {len(qualifications)} نوع مؤهل")

def add_military_ranks():
    """إضافة الرتب العسكرية"""
    ranks = [
        # ضباط
        "ملازم ثاني", "ملازم أول", "نقيب", "رائد", "مقدم", "عقيد", "عميد", "لواء", "فريق", "فريق أول",
        # ضباط صف
        "عريف", "عريف أول", "رقيب", "رقيب أول", "مساعد", "مساعد أول",
        # جنود
        "جندي", "جندي أول"
    ]
    
    print("🎖️ إضافة الرتب العسكرية...")
    for rank_name in ranks:
        if not MilitaryRank.query.filter_by(name=rank_name).first():
            rank = MilitaryRank(name=rank_name)
            db.session.add(rank)
    
    db.session.commit()
    print(f"✅ تم إضافة {len(ranks)} رتبة عسكرية")

def main():
    """الدالة الرئيسية"""
    print("🌱 بدء إضافة البيانات الأساسية")
    print("=" * 60)
    
    with app.app_context():
        # إضافة البيانات الأساسية
        add_governorates()
        add_agencies()
        add_training_centers()
        add_course_levels()
        add_course_categories()
        add_participant_types()
        add_marital_status()
        add_blood_types()
        add_qualification_types()
        add_military_ranks()
        
        print("=" * 60)
        print("🎉 تم إضافة جميع البيانات الأساسية بنجاح!")

if __name__ == "__main__":
    main()
