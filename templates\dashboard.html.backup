{% extends "layout.html" %}

{% block title %}لوحة التحكم{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-pages.css') }}">
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title">
            <i class="fas fa-tachometer-alt icon-lg"></i>
            لوحة التحكم
        </div>
        <div class="page-subtitle">
            نظرة شاملة على إحصائيات النظام والأنشطة الحديثة
        </div>
    </div>

    <div class="content-wrapper">
        <!-- المحتوى الأصلي -->
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <h5>{{ current_user.username }}</h5>
                <p class="badge">{{ current_user.role }}</p>
            </div>

            <div class="sidebar-divider"></div>

            <div class="sidebar-section-title">
                <i class="fas fa-tachometer-alt me-2"></i>القائمة الرئيسية
            </div>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link active">
                <i class="fas fa-home"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>

            {% if current_user.role == 'admin' %}
            <div class="sidebar-divider"></div>

            <div class="sidebar-section-title">
                <i class="fas fa-users-cog me-2"></i>إدارة النظام
            </div>

            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('person_data.person_data_table') }}" class="sidebar-link">
                <i class="fas fa-table"></i> جدول بيانات الأشخاص
            </a>
            <a href="{{ url_for('reference_tables') }}" class="sidebar-link">
                <i class="fas fa-list-alt"></i> الجداول الترميزية
            </a>

            <div class="sidebar-divider"></div>

            <div class="sidebar-section-title">
                <i class="fas fa-chart-bar me-2"></i>التحليل والتقارير
            </div>

            <a href="http://localhost:5000/person_data/name_analysis" class="sidebar-link">
                <i class="fas fa-chart-pie"></i> تحليل البيانات
            </a>
            <a href="/reports/dashboard" class="sidebar-link">
                <i class="fas fa-chart-line"></i> التقارير التفاعلية
            </a>

            <div class="sidebar-divider"></div>

            <div class="sidebar-section-title">
                <i class="fas fa-tools me-2"></i>أدوات النظام
            </div>

            <a href="{{ url_for('backup') }}" class="sidebar-link">
                <i class="fas fa-database"></i> النسخ الاحتياطي
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
            {% endif %}
        </div>
    </div>

    <div class="col-md-9">
        <div class="main-container">
            <h2 class="page-title"><i class="fas fa-cogs"></i> لوحة التحكم</h2>



            <div class="row mb-5">
                <div class="col-md-3">
                    <div class="stat-card stat-primary">
                        <i class="fas fa-users stat-icon"></i>
                        <div class="stat-number">1,016</div>
                        <div class="stat-label">إجمالي الأشخاص</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-success">
                        <i class="fas fa-graduation-cap stat-icon"></i>
                        <div class="stat-number">201</div>
                        <div class="stat-label">الدورات المتاحة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-info">
                        <i class="fas fa-user-graduate stat-icon"></i>
                        <div class="stat-number">5,720</div>
                        <div class="stat-label">إجمالي المشاركين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card stat-warning">
                        <i class="fas fa-chart-line stat-icon"></i>
                        <div class="stat-number">95%</div>
                        <div class="stat-label">معدل النجاح</div>
                    </div>
                </div>
            </div>

        <!-- المربعات الملونة الكبيرة -->
        <div class="row mb-5">
            <h3 class="mb-4">إدارة النظام</h3>

            <div class="col-md-6 mb-4">
                <a href="{{ url_for('personal_data_list') }}" class="text-decoration-none">
                    <div class="category-card category-primary">
                        <div class="category-icon">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <h3 class="category-title">الملتحقين</h3>
                        <p class="category-description">إدارة بيانات المتدربين والملتحقين بالدورات</p>
                    </div>
                </a>
            </div>

            <div class="col-md-6 mb-4">
                <a href="{{ url_for('graduates') }}" class="text-decoration-none">
                    <div class="category-card category-success">
                        <div class="category-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3 class="category-title">الخريجين</h3>
                        <p class="category-description">عرض بيانات الخريجين من الدورات</p>
                    </div>
                </a>
            </div>

            {% if current_user.role == 'admin' %}
            <div class="col-md-6 mb-4">
                <a href="/reports/dashboard" class="text-decoration-none">
                    <div class="category-card category-info">
                        <div class="category-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="category-title">التقارير التفاعلية</h3>
                        <p class="category-description">تقارير بصرية تفاعلية مع رسوم بيانية متطورة</p>
                    </div>
                </a>
            </div>

            <div class="col-md-6 mb-4">
                <a href="{{ url_for('backup') }}" class="text-decoration-none">
                    <div class="category-card category-warning">
                        <div class="category-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h3 class="category-title">النسخ الاحتياطي</h3>
                        <p class="category-description">إنشاء وإدارة النسخ الاحتياطية مع عرض تاريخ الإنشاء</p>
                    </div>
                </a>
            </div>
            {% endif %}
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="dashboard-card mb-4">
                    <div class="dashboard-card-header">
                        <i class="fas fa-calendar-alt me-2"></i> الدورات القادمة
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>تطوير تطبيقات الويب</span>
                                <span class="badge bg-primary rounded-pill">غداً</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>أساسيات قواعد البيانات</span>
                                <span class="badge bg-primary rounded-pill">بعد 3 أيام</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>إدارة المشاريع الاحترافية</span>
                                <span class="badge bg-primary rounded-pill">بعد أسبوع</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="dashboard-card mb-4">
                    <div class="dashboard-card-header">
                        <i class="fas fa-bell me-2"></i> آخر الإشعارات
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success mb-2">
                            <small class="text-muted">منذ ساعتين</small>
                            <p class="mb-0">تم إضافة دورة جديدة: "تطوير تطبيقات الهاتف المحمول"</p>
                        </div>
                        <div class="alert alert-info mb-2">
                            <small class="text-muted">منذ 5 ساعات</small>
                            <p class="mb-0">تم تحديث جدول الدورات التدريبية للشهر القادم</p>
                        </div>
                        <div class="alert alert-warning mb-2">
                            <small class="text-muted">منذ يوم</small>
                            <p class="mb-0">تذكير: موعد تسليم المشروع النهائي بعد أسبوع</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div> <!-- إغلاق main-container -->
    </div>
</div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تفعيل القائمة المنسدلة للجداول الترميزية
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');

        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const dropdownMenu = this.nextElementSibling;
                dropdownMenu.classList.toggle('show');
            });
        });
    });
</script>
{% endblock %}