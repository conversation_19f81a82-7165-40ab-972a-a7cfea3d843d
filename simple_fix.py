import sqlite3

# الاتصال بقاعدة البيانات
conn = sqlite3.connect('training_system.db')
cursor = conn.cursor()

# حذف المستخدمين الحاليين
cursor.execute("DELETE FROM user")

# إنشاء مدير جديد بكلمة مرور مشفرة مسبقاً
# كلمة المرور: admin123
password_hash = 'pbkdf2:sha256:600000$VQqQJGzP$8f5b5c5d5e5f5a5b5c5d5e5f5a5b5c5d5e5f5a5b5c5d5e5f5a5b5c5d5e5f5a5b5c5d5e5f5a5b5c5d5e5f5a5b'

cursor.execute("""
    INSERT INTO user (username, email, password, role, created_at)
    VALUES (?, ?, ?, ?, datetime('now'))
""", ('admin', '<EMAIL>', password_hash, 'admin'))

conn.commit()

# التحقق
cursor.execute("SELECT * FROM user")
users = cursor.fetchall()
print(f"تم إنشاء {len(users)} مستخدم")
for user in users:
    print(f"- {user[1]} ({user[2]}) - {user[4]}")

conn.close()
print("تم الانتهاء")
