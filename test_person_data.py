#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف لاختبار إضافة بيانات الأشخاص
"""

import requests
import json

def test_person_data_endpoints():
    """اختبار نقاط النهاية لبيانات الأشخاص"""
    base_url = "http://localhost:5000"
    
    # اختبار الحصول على البيانات
    try:
        response = requests.get(f"{base_url}/person_data_json")
        print(f"GET /person_data_json: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"عدد السجلات: {len(data)}")
        else:
            print(f"خطأ: {response.text}")
    except Exception as e:
        print(f"خطأ في الاتصال: {str(e)}")
    
    # اختبار إضافة شخص جديد
    try:
        test_person = {
            "full_name": "اختبار الاسم",
            "nickname": "اختبار",
            "age": 30,
            "governorate": "صنعاء",
            "directorate": "الثورة",
            "uzla": "الحصبة",
            "village": "شارع الزبيري",
            "qualification": "بكالوريوس",
            "marital_status": "متزوج",
            "job": "مطور",
            "agency": "تقنية المعلومات",
            "work_place": "الشركة",
            "national_number": "12345678901",
            "military_number": "M123456",
            "phone": "777123456"
        }
        
        response = requests.post(
            f"{base_url}/person_data_add",
            json=test_person,
            headers={'Content-Type': 'application/json'}
        )
        print(f"POST /person_data_add: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"نتيجة الإضافة: {result}")
        else:
            print(f"خطأ: {response.text}")
    except Exception as e:
        print(f"خطأ في الاتصال: {str(e)}")

if __name__ == "__main__":
    test_person_data_endpoints()
