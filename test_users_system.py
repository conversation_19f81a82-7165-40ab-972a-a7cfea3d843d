#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لنظام إدارة المستخدمين والصلاحيات
Comprehensive Testing for Users and Permissions Management System
"""

import requests
import json
from datetime import datetime

# إعدادات الاختبار
BASE_URL = 'http://localhost:5000'
TEST_USERS = [
    {'email': '<EMAIL>', 'password': 'admin123', 'role': 'admin'},
    {'email': '<EMAIL>', 'password': 'manager123', 'role': 'manager'},
    {'email': '<EMAIL>', 'password': 'trainer123', 'role': 'trainer'},
    {'email': '<EMAIL>', 'password': 'data123', 'role': 'data_entry'},
    {'email': '<EMAIL>', 'password': 'viewer123', 'role': 'viewer'}
]

class UsersSystemTester:
    def __init__(self):
        self.session = requests.Session()
        self.logged_in_user = None
        
    def login(self, email, password):
        """تسجيل الدخول"""
        try:
            # الحصول على صفحة تسجيل الدخول أولاً
            response = self.session.get(f'{BASE_URL}/login')
            if response.status_code != 200:
                return False, f"خطأ في الوصول لصفحة تسجيل الدخول: {response.status_code}"
            
            # تسجيل الدخول
            login_data = {
                'email': email,
                'password': password
            }
            
            response = self.session.post(f'{BASE_URL}/login', data=login_data)
            
            if response.status_code == 200 and 'dashboard' in response.url:
                self.logged_in_user = email
                return True, "تم تسجيل الدخول بنجاح"
            else:
                return False, f"فشل تسجيل الدخول: {response.status_code}"
                
        except Exception as e:
            return False, f"خطأ في تسجيل الدخول: {str(e)}"
    
    def logout(self):
        """تسجيل الخروج"""
        try:
            response = self.session.get(f'{BASE_URL}/logout')
            self.logged_in_user = None
            return True, "تم تسجيل الخروج بنجاح"
        except Exception as e:
            return False, f"خطأ في تسجيل الخروج: {str(e)}"
    
    def test_users_page_access(self):
        """اختبار الوصول لصفحة إدارة المستخدمين"""
        try:
            response = self.session.get(f'{BASE_URL}/admin/users')
            
            if response.status_code == 200:
                return True, "تم الوصول لصفحة إدارة المستخدمين بنجاح"
            elif response.status_code == 403:
                return False, "ليس لديك صلاحية للوصول لصفحة إدارة المستخدمين"
            elif response.status_code == 302:
                return False, "تم إعادة التوجيه - قد تحتاج لتسجيل الدخول"
            else:
                return False, f"خطأ في الوصول للصفحة: {response.status_code}"
                
        except Exception as e:
            return False, f"خطأ في اختبار الوصول: {str(e)}"
    
    def test_create_user(self):
        """اختبار إنشاء مستخدم جديد"""
        try:
            new_user_data = {
                'username': f'test_user_{datetime.now().strftime("%H%M%S")}',
                'email': f'test_{datetime.now().strftime("%H%M%S")}@test.com',
                'password': 'test123',
                'confirm_password': 'test123',
                'role': 'viewer',
                'first_name': 'مستخدم',
                'last_name': 'اختبار',
                'department': 'قسم الاختبار',
                'position': 'مختبر',
                'is_active': True
            }
            
            response = self.session.post(
                f'{BASE_URL}/admin/users/create',
                json=new_user_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    return True, f"تم إنشاء المستخدم بنجاح: {new_user_data['username']}"
                else:
                    return False, f"فشل إنشاء المستخدم: {result.get('message', 'خطأ غير معروف')}"
            else:
                return False, f"خطأ في إنشاء المستخدم: {response.status_code}"
                
        except Exception as e:
            return False, f"خطأ في اختبار إنشاء المستخدم: {str(e)}"
    
    def test_reset_password(self, user_id=2):
        """اختبار إعادة تعيين كلمة المرور"""
        try:
            response = self.session.post(f'{BASE_URL}/admin/users/{user_id}/reset-password')
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    return True, f"تم إعادة تعيين كلمة المرور: {result.get('new_password', 'غير محدد')}"
                else:
                    return False, f"فشل إعادة تعيين كلمة المرور: {result.get('message', 'خطأ غير معروف')}"
            else:
                return False, f"خطأ في إعادة تعيين كلمة المرور: {response.status_code}"
                
        except Exception as e:
            return False, f"خطأ في اختبار إعادة تعيين كلمة المرور: {str(e)}"
    
    def test_toggle_user_status(self, user_id=9, status=False):
        """اختبار تغيير حالة المستخدم"""
        try:
            response = self.session.post(
                f'{BASE_URL}/admin/users/{user_id}/toggle-status',
                json={'status': status},
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    action = "تفعيل" if status else "إلغاء تفعيل"
                    return True, f"تم {action} المستخدم بنجاح"
                else:
                    return False, f"فشل تغيير حالة المستخدم: {result.get('message', 'خطأ غير معروف')}"
            else:
                return False, f"خطأ في تغيير حالة المستخدم: {response.status_code}"
                
        except Exception as e:
            return False, f"خطأ في اختبار تغيير حالة المستخدم: {str(e)}"
    
    def run_comprehensive_test(self):
        """تشغيل اختبار شامل للنظام"""
        print("🧪 بدء الاختبار الشامل لنظام إدارة المستخدمين والصلاحيات")
        print("=" * 70)
        
        test_results = []
        
        # اختبار تسجيل الدخول لكل مستخدم
        for user in TEST_USERS:
            print(f"\n👤 اختبار المستخدم: {user['email']} ({user['role']})")
            print("-" * 50)
            
            # تسجيل الدخول
            success, message = self.login(user['email'], user['password'])
            print(f"🔐 تسجيل الدخول: {'✅' if success else '❌'} {message}")
            test_results.append(('تسجيل الدخول', user['email'], success, message))
            
            if success:
                # اختبار الوصول لصفحة إدارة المستخدمين
                success, message = self.test_users_page_access()
                print(f"📋 الوصول لصفحة إدارة المستخدمين: {'✅' if success else '❌'} {message}")
                test_results.append(('الوصول لصفحة المستخدمين', user['email'], success, message))
                
                # اختبار إنشاء مستخدم (للمديرين فقط)
                if user['role'] in ['admin', 'manager']:
                    success, message = self.test_create_user()
                    print(f"➕ إنشاء مستخدم جديد: {'✅' if success else '❌'} {message}")
                    test_results.append(('إنشاء مستخدم', user['email'], success, message))
                    
                    # اختبار إعادة تعيين كلمة المرور
                    success, message = self.test_reset_password()
                    print(f"🔑 إعادة تعيين كلمة المرور: {'✅' if success else '❌'} {message}")
                    test_results.append(('إعادة تعيين كلمة المرور', user['email'], success, message))
                    
                    # اختبار تغيير حالة المستخدم
                    success, message = self.test_toggle_user_status()
                    print(f"🔄 تغيير حالة المستخدم: {'✅' if success else '❌'} {message}")
                    test_results.append(('تغيير حالة المستخدم', user['email'], success, message))
                
                # تسجيل الخروج
                success, message = self.logout()
                print(f"🚪 تسجيل الخروج: {'✅' if success else '❌'} {message}")
                test_results.append(('تسجيل الخروج', user['email'], success, message))
        
        # ملخص النتائج
        print(f"\n📊 ملخص نتائج الاختبار")
        print("=" * 70)
        
        total_tests = len(test_results)
        passed_tests = sum(1 for _, _, success, _ in test_results if success)
        failed_tests = total_tests - passed_tests
        
        print(f"إجمالي الاختبارات: {total_tests}")
        print(f"الاختبارات الناجحة: {passed_tests} ✅")
        print(f"الاختبارات الفاشلة: {failed_tests} ❌")
        print(f"معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        # عرض الاختبارات الفاشلة
        if failed_tests > 0:
            print(f"\n❌ الاختبارات الفاشلة:")
            for test_name, user_email, success, message in test_results:
                if not success:
                    print(f"   • {test_name} ({user_email}): {message}")
        
        print(f"\n🎉 انتهى الاختبار الشامل!")
        return test_results

def main():
    """الدالة الرئيسية"""
    tester = UsersSystemTester()
    
    try:
        # التحقق من تشغيل الخادم
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code != 200:
            print("❌ الخادم غير متاح. تأكد من تشغيل النظام أولاً.")
            return
    except requests.exceptions.RequestException:
        print("❌ لا يمكن الوصول للخادم. تأكد من تشغيل النظام على localhost:5000")
        return
    
    # تشغيل الاختبار الشامل
    results = tester.run_comprehensive_test()
    
    # حفظ النتائج في ملف
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"test_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump([{
            'test_name': test_name,
            'user_email': user_email,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        } for test_name, user_email, success, message in results], f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ نتائج الاختبار في: {results_file}")

if __name__ == '__main__':
    main()
