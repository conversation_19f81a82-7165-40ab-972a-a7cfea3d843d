('E:\\app\\TRINING\\build\\build_exe_5001\\PYZ-00.pyz',
 [('PIL',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'C:\\Python313\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python313\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_colorize', 'C:\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python313\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'C:\\Python313\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support', 'C:\\Python313\\Lib\\_ios_support.py', 'PYMODULE'),
  ('_opcode_metadata', 'C:\\Python313\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python313\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python313\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyrepl', 'C:\\Python313\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands', 'C:\\Python313\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console', 'C:\\Python313\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('_pyrepl.curses', 'C:\\Python313\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input', 'C:\\Python313\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'C:\\Python313\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.main', 'C:\\Python313\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl.pager', 'C:\\Python313\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('_pyrepl.reader', 'C:\\Python313\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl.readline', 'C:\\Python313\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace', 'C:\\Python313\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.types', 'C:\\Python313\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'C:\\Python313\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python313\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python313\\Lib\\_threading_local.py', 'PYMODULE'),
  ('arabic_reshaper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\arabic_reshaper\\__init__.py',
   'PYMODULE'),
  ('arabic_reshaper.__version__',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\arabic_reshaper\\__version__.py',
   'PYMODULE'),
  ('arabic_reshaper.arabic_reshaper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\arabic_reshaper\\arabic_reshaper.py',
   'PYMODULE'),
  ('arabic_reshaper.letters',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\arabic_reshaper\\letters.py',
   'PYMODULE'),
  ('arabic_reshaper.ligatures',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\arabic_reshaper\\ligatures.py',
   'PYMODULE'),
  ('arabic_reshaper.reshaper_config',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\arabic_reshaper\\reshaper_config.py',
   'PYMODULE'),
  ('argparse', 'C:\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'C:\\Python313\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\Python313\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'C:\\Python313\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'C:\\Python313\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'C:\\Python313\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'C:\\Python313\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'C:\\Python313\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'C:\\Python313\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'C:\\Python313\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\Python313\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python313\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'C:\\Python313\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'C:\\Python313\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'C:\\Python313\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'C:\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\Python313\\Lib\\bdb.py', 'PYMODULE'),
  ('bidi',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\bidi\\__init__.py',
   'PYMODULE'),
  ('bidi.algorithm',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\bidi\\algorithm.py',
   'PYMODULE'),
  ('bidi.mirror',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\bidi\\mirror.py',
   'PYMODULE'),
  ('bidi.wrapper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\bidi\\wrapper.py',
   'PYMODULE'),
  ('bisect', 'C:\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bz2', 'C:\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('click',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'C:\\Python313\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Python313\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python313\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Python313\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'C:\\Python313\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python313\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python313\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python313\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\Python313\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python313\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'C:\\Python313\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\Python313\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'C:\\Python313\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'C:\\Python313\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'C:\\Python313\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python313\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'C:\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Python313\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('dns',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\__init__.py',
   'PYMODULE'),
  ('dns._asyncbackend',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\_asyncbackend.py',
   'PYMODULE'),
  ('dns._asyncio_backend',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\_asyncio_backend.py',
   'PYMODULE'),
  ('dns._ddr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\_ddr.py',
   'PYMODULE'),
  ('dns._features',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\_features.py',
   'PYMODULE'),
  ('dns._immutable_ctx',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\_immutable_ctx.py',
   'PYMODULE'),
  ('dns._trio_backend',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\_trio_backend.py',
   'PYMODULE'),
  ('dns.asyncbackend',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\asyncbackend.py',
   'PYMODULE'),
  ('dns.asyncquery',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\asyncquery.py',
   'PYMODULE'),
  ('dns.asyncresolver',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\asyncresolver.py',
   'PYMODULE'),
  ('dns.dnssectypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\dnssectypes.py',
   'PYMODULE'),
  ('dns.edns',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\edns.py',
   'PYMODULE'),
  ('dns.entropy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\entropy.py',
   'PYMODULE'),
  ('dns.enum',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\enum.py',
   'PYMODULE'),
  ('dns.exception',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\exception.py',
   'PYMODULE'),
  ('dns.flags',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\flags.py',
   'PYMODULE'),
  ('dns.grange',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\grange.py',
   'PYMODULE'),
  ('dns.immutable',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\immutable.py',
   'PYMODULE'),
  ('dns.inet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\inet.py',
   'PYMODULE'),
  ('dns.ipv4',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\ipv4.py',
   'PYMODULE'),
  ('dns.ipv6',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\ipv6.py',
   'PYMODULE'),
  ('dns.message',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\message.py',
   'PYMODULE'),
  ('dns.name',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\name.py',
   'PYMODULE'),
  ('dns.nameserver',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\nameserver.py',
   'PYMODULE'),
  ('dns.node',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\node.py',
   'PYMODULE'),
  ('dns.opcode',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\opcode.py',
   'PYMODULE'),
  ('dns.query',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\query.py',
   'PYMODULE'),
  ('dns.quic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\quic\\__init__.py',
   'PYMODULE'),
  ('dns.quic._asyncio',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\quic\\_asyncio.py',
   'PYMODULE'),
  ('dns.quic._common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\quic\\_common.py',
   'PYMODULE'),
  ('dns.quic._sync',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\quic\\_sync.py',
   'PYMODULE'),
  ('dns.quic._trio',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\quic\\_trio.py',
   'PYMODULE'),
  ('dns.rcode',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rcode.py',
   'PYMODULE'),
  ('dns.rdata',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdata.py',
   'PYMODULE'),
  ('dns.rdataclass',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdataclass.py',
   'PYMODULE'),
  ('dns.rdataset',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdataset.py',
   'PYMODULE'),
  ('dns.rdatatype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdatatype.py',
   'PYMODULE'),
  ('dns.rdtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AFSDB',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\AFSDB.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AMTRELAY',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\AMTRELAY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AVC',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\AVC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CAA',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CAA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDNSKEY',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CDNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDS',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CDS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CERT',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CERT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CNAME',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CSYNC',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\CSYNC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DLV',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DLV.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNAME',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNSKEY',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DS',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\DS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI48',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\EUI48.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI64',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\EUI64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.GPOS',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\GPOS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HINFO',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\HINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HIP',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\HIP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ISDN',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\ISDN.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L32',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\L32.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L64',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\L64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LOC',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\LOC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LP',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\LP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.MX',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\MX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NID',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NID.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NINFO',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NS',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3PARAM',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3PARAM.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPENPGPKEY',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\OPENPGPKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPT',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\OPT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.PTR',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RESINFO',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RESINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RP',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RRSIG',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RRSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RT',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\RT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SMIMEA',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SMIMEA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SOA',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SOA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SPF',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SPF.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SSHFP',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\SSHFP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TKEY',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TLSA',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TLSA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TSIG',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TXT',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\TXT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.URI',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\URI.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.WALLET',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\WALLET.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.X25',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\X25.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ZONEMD',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\ANY\\ZONEMD.py',
   'PYMODULE'),
  ('dns.rdtypes.CH',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\CH\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.CH.A',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\CH\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.A',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.AAAA',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\AAAA.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.APL',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\APL.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.DHCID',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\DHCID.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.HTTPS',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\HTTPS.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.IPSECKEY',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\IPSECKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.KX',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\KX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NAPTR',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\NAPTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\NSAP.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP_PTR',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\NSAP_PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.PX',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\PX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SRV',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\SRV.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SVCB',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\SVCB.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.WKS',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\IN\\WKS.py',
   'PYMODULE'),
  ('dns.rdtypes.dnskeybase',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\dnskeybase.py',
   'PYMODULE'),
  ('dns.rdtypes.dsbase',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\dsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.euibase',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\euibase.py',
   'PYMODULE'),
  ('dns.rdtypes.mxbase',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\mxbase.py',
   'PYMODULE'),
  ('dns.rdtypes.nsbase',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\nsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.svcbbase',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\svcbbase.py',
   'PYMODULE'),
  ('dns.rdtypes.tlsabase',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\tlsabase.py',
   'PYMODULE'),
  ('dns.rdtypes.txtbase',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\txtbase.py',
   'PYMODULE'),
  ('dns.rdtypes.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rdtypes\\util.py',
   'PYMODULE'),
  ('dns.renderer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\renderer.py',
   'PYMODULE'),
  ('dns.resolver',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\resolver.py',
   'PYMODULE'),
  ('dns.reversename',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\reversename.py',
   'PYMODULE'),
  ('dns.rrset',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\rrset.py',
   'PYMODULE'),
  ('dns.serial',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\serial.py',
   'PYMODULE'),
  ('dns.set',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\set.py',
   'PYMODULE'),
  ('dns.tokenizer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\tokenizer.py',
   'PYMODULE'),
  ('dns.transaction',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\transaction.py',
   'PYMODULE'),
  ('dns.tsig',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\tsig.py',
   'PYMODULE'),
  ('dns.ttl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\ttl.py',
   'PYMODULE'),
  ('dns.update',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\update.py',
   'PYMODULE'),
  ('dns.version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('dns.win32util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\win32util.py',
   'PYMODULE'),
  ('dns.wire',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\wire.py',
   'PYMODULE'),
  ('dns.xfr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\xfr.py',
   'PYMODULE'),
  ('dns.zone',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\zone.py',
   'PYMODULE'),
  ('dns.zonefile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\zonefile.py',
   'PYMODULE'),
  ('dns.zonetypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dns\\zonetypes.py',
   'PYMODULE'),
  ('doctest', 'C:\\Python313\\Lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email', 'C:\\Python313\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'C:\\Python313\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'C:\\Python313\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python313\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'C:\\Python313\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python313\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python313\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python313\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'C:\\Python313\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'C:\\Python313\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'C:\\Python313\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python313\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python313\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'C:\\Python313\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python313\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email_validator',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\email_validator\\__init__.py',
   'PYMODULE'),
  ('email_validator.__main__',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\email_validator\\__main__.py',
   'PYMODULE'),
  ('email_validator.deliverability',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\email_validator\\deliverability.py',
   'PYMODULE'),
  ('email_validator.exceptions_types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\email_validator\\exceptions_types.py',
   'PYMODULE'),
  ('email_validator.rfc_constants',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\email_validator\\rfc_constants.py',
   'PYMODULE'),
  ('email_validator.syntax',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\email_validator\\syntax.py',
   'PYMODULE'),
  ('email_validator.validate_email',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\email_validator\\validate_email.py',
   'PYMODULE'),
  ('email_validator.version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\email_validator\\version.py',
   'PYMODULE'),
  ('et_xmlfile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput', 'C:\\Python313\\Lib\\fileinput.py', 'PYMODULE'),
  ('flask',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.__main__',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\__main__.py',
   'PYMODULE'),
  ('flask.app',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.sansio.app',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.views',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\views.py',
   'PYMODULE'),
  ('flask.wrappers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_login',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_login\\__init__.py',
   'PYMODULE'),
  ('flask_login.__about__',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_login\\__about__.py',
   'PYMODULE'),
  ('flask_login.config',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_login\\config.py',
   'PYMODULE'),
  ('flask_login.login_manager',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_login\\login_manager.py',
   'PYMODULE'),
  ('flask_login.mixins',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_login\\mixins.py',
   'PYMODULE'),
  ('flask_login.signals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_login\\signals.py',
   'PYMODULE'),
  ('flask_login.test_client',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_login\\test_client.py',
   'PYMODULE'),
  ('flask_login.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_login\\utils.py',
   'PYMODULE'),
  ('flask_sqlalchemy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('flask_sqlalchemy.cli',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\cli.py',
   'PYMODULE'),
  ('flask_sqlalchemy.extension',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\extension.py',
   'PYMODULE'),
  ('flask_sqlalchemy.model',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\model.py',
   'PYMODULE'),
  ('flask_sqlalchemy.pagination',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\pagination.py',
   'PYMODULE'),
  ('flask_sqlalchemy.query',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\query.py',
   'PYMODULE'),
  ('flask_sqlalchemy.record_queries',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\record_queries.py',
   'PYMODULE'),
  ('flask_sqlalchemy.session',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\session.py',
   'PYMODULE'),
  ('flask_sqlalchemy.table',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\table.py',
   'PYMODULE'),
  ('flask_sqlalchemy.track_modifications',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_sqlalchemy\\track_modifications.py',
   'PYMODULE'),
  ('flask_wtf',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_wtf\\__init__.py',
   'PYMODULE'),
  ('flask_wtf._compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_wtf\\_compat.py',
   'PYMODULE'),
  ('flask_wtf.csrf',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_wtf\\csrf.py',
   'PYMODULE'),
  ('flask_wtf.file',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_wtf\\file.py',
   'PYMODULE'),
  ('flask_wtf.form',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_wtf\\form.py',
   'PYMODULE'),
  ('flask_wtf.i18n',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_wtf\\i18n.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\__init__.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.fields',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\fields.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.validators',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\validators.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.widgets',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\flask_wtf\\recaptcha\\widgets.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip', 'C:\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python313\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'C:\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\Python313\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python313\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'C:\\Python313\\Lib\\http\\server.py', 'PYMODULE'),
  ('idna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'C:\\Python313\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python313\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Python313\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python313\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'C:\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'C:\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python313\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python313\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python313\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'C:\\Python313\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'C:\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python313\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.abc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\compat\\abc.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.product',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\compat\\product.py',
   'PYMODULE'),
  ('openpyxl.compat.singleton',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\compat\\singleton.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.descriptors.slots',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\slots.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.interface',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\interface.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.dataframe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\dataframe.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.inference',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\inference.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_watch',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_watch.py',
   'PYMODULE'),
  ('openpyxl.worksheet.controls',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\controls.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.custom',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.errors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\errors.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.ole',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\ole.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.picture',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\picture.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.smart_tag',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\smart_tag.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._hypothesis',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_testing\\_hypothesis.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\conftest.py',
   'PYMODULE'),
  ('pandas.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.sparse',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.sparse.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\sparse\\api.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tests',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.api.test_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\api\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.api.test_types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\api\\test_types.py',
   'PYMODULE'),
  ('pandas.tests.apply',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\apply\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.apply.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\apply\\common.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_apply',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_apply.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_apply_relabeling',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_apply_relabeling.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_transform',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_transform.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_invalid_arg',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\apply\\test_invalid_arg.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_numba',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\apply\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_apply',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_apply.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_apply_relabeling',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_apply_relabeling.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_transform',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_transform.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_str',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\apply\\test_str.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\common.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_array_ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_array_ops.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_categorical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_datetime64',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_datetime64.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_numeric',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_numeric.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_object',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_object.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_timedelta64',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_timedelta64.py',
   'PYMODULE'),
  ('pandas.tests.arrays',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_comparison',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_construction',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_function',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_logical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_logical.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_reduction',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_reduction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_repr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_algos',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_algos.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_analytics',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_analytics.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_map',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_missing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_operators',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_operators.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_replace',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_repr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_sorting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_subclass',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_take',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_warnings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_warnings.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_cumulative',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_reductions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_comparison',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_concat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_construction',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_contains',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_contains.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_function',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_repr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_to_numpy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_comparison',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_concat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_construction',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_function',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_reduction',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_reduction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_repr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_interval_pyarrow',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_interval_pyarrow.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_overlaps',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_overlaps.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_arrow_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_arrow_compat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_function',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked_shared',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\masked_shared.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_.test_numpy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_\\test_numpy.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_arrow_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_arrow_compat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_reductions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_accessor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_accessor.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_arithmetics',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_arithmetics.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_array.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_combine_concat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_combine_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_dtype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_dtype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_libsparse',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_libsparse.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_reductions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_unary',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_unary.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_concat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_string',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\test_string.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_string_arrow',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\test_string_arrow.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_array.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_datetimelike',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_datetimes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_datetimes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_ndarray_backed',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_ndarray_backed.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_timedeltas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\test_timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_cumulative',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_reductions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\base\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.base.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\base\\common.py',
   'PYMODULE'),
  ('pandas.tests.base.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\base\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.base.test_conversion',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\base\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.base.test_fillna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\base\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.base.test_misc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\base\\test_misc.py',
   'PYMODULE'),
  ('pandas.tests.base.test_transpose',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\base\\test_transpose.py',
   'PYMODULE'),
  ('pandas.tests.base.test_unique',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\base\\test_unique.py',
   'PYMODULE'),
  ('pandas.tests.base.test_value_counts',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\base\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.computation',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.computation.test_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\computation\\test_compat.py',
   'PYMODULE'),
  ('pandas.tests.computation.test_eval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\computation\\test_eval.py',
   'PYMODULE'),
  ('pandas.tests.config',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\config\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.config.test_config',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\config\\test_config.py',
   'PYMODULE'),
  ('pandas.tests.config.test_localization',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\config\\test_localization.py',
   'PYMODULE'),
  ('pandas.tests.construction',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\construction\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.construction.test_extract_array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\construction\\test_extract_array.py',
   'PYMODULE'),
  ('pandas.tests.copy_view',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_datetimeindex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_datetimeindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_periodindex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_periodindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_timedeltaindex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_timedeltaindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_array.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_chained_assignment_deprecation',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_chained_assignment_deprecation.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_clip',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_clip.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_core_functionalities',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_core_functionalities.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_functions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_functions.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_internals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_internals.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_interp_fillna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_interp_fillna.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_methods.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_replace',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_setitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_util.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\copy_view\\util.py',
   'PYMODULE'),
  ('pandas.tests.dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_can_hold_element',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_can_hold_element.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_from_scalar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_from_scalar.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_ndarray',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_ndarray.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_object_arr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_object_arr.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_dict_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_dict_compat.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_downcast',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_downcast.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_find_common_type',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_find_common_type.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_infer_datetimelike',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_infer_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_infer_dtype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_infer_dtype.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_maybe_box_native',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_maybe_box_native.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_promote',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_promote.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_concat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_generic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_generic.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_inference',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_inference.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_missing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.extension',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\array_with_attr\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr.array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\array_with_attr\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr.test_array_with_attr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\array_with_attr\\test_array_with_attr.py',
   'PYMODULE'),
  ('pandas.tests.extension.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.accumulate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\accumulate.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.casting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\casting.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\constructors.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.dim2',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\dim2.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.dtype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\dtype.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.getitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\getitem.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.groupby',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\groupby.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\index.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.interface',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\interface.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.io',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\io.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\methods.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.missing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\missing.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\ops.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.printing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\printing.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.reduce',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\reduce.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.reshaping',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\reshaping.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.setitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\base\\setitem.py',
   'PYMODULE'),
  ('pandas.tests.extension.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.extension.date',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\date\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.date.array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\date\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\decimal\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal.array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\decimal\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal.test_decimal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\decimal\\test_decimal.py',
   'PYMODULE'),
  ('pandas.tests.extension.json',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.json.array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\json\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.json.test_json',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\json\\test_json.py',
   'PYMODULE'),
  ('pandas.tests.extension.list',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\list\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.list.array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\list\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.list.test_list',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\list\\test_list.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_arrow',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\test_arrow.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_categorical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_datetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_extension',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\test_extension.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_masked',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\test_masked.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_numpy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\test_numpy.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_sparse',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\test_sparse.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_string',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\extension\\test_string.py',
   'PYMODULE'),
  ('pandas.tests.frame',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\common.py',
   'PYMODULE'),
  ('pandas.tests.frame.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\constructors\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors.test_from_dict',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\constructors\\test_from_dict.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors.test_from_records',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\constructors\\test_from_records.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_coercion',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_coercion.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_delitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_delitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_get',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_get.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_get_value',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_get_value.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_getitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_insert',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_mask',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_mask.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_set_value',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_set_value.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_setitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_take',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_where',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_where.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_xs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_xs.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_add_prefix_suffix',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_add_prefix_suffix.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_align',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_align.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_asfreq',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_asof',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_assign',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_assign.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_at_time',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_at_time.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_between_time',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_between_time.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_clip',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_clip.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_combine',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_combine.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_combine_first',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_combine_first.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_compare',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_compare.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_convert_dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_convert_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_copy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_count',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_count.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_cov_corr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_cov_corr.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_describe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_describe.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_diff',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_diff.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dot',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dot.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_drop',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_drop.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_drop_duplicates',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_droplevel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_droplevel.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dropna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dropna.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_duplicated',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_duplicated.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_equals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_explode',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_explode.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_fillna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_filter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_filter.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_first_and_last',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_first_and_last.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_first_valid_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_first_valid_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_get_numeric_data',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_get_numeric_data.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_head_tail',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_head_tail.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_infer_objects',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_infer_objects.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_info',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_info.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_interpolate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_interpolate.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_is_homogeneous_dtype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_is_homogeneous_dtype.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_isetitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_isetitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_isin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_isin.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_iterrows',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_iterrows.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_join',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_map',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_matmul',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_matmul.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_nlargest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_nlargest.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pct_change',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pct_change.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pipe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pipe.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pop',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pop.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_quantile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rank',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reindex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reindex_like',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reindex_like.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rename',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rename.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rename_axis',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rename_axis.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reorder_levels',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reorder_levels.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_replace',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reset_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reset_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_round',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sample',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sample.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_select_dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_select_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_set_axis',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_set_axis.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_set_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_set_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_shift',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_size',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_size.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sort_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sort_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sort_values',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_swapaxes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_swapaxes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_swaplevel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_swaplevel.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_csv',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_dict',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_dict.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_dict_of_blocks',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_dict_of_blocks.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_numpy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_period.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_records',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_records.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_timestamp',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_transpose',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_transpose.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_truncate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_truncate.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_tz_convert',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_tz_localize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_update',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_update.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_value_counts',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_values',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_values.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_alter_axes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_alter_axes.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_arrow_interface',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_arrow_interface.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_block_internals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_block_internals.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_cumulative',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_iteration',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_iteration.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_logical_ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_logical_ops.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_nonunique_indexes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_nonunique_indexes.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_npfuncs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_query_eval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_query_eval.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_reductions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_repr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_stack_unstack',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_stack_unstack.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_subclass',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_ufunc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_ufunc.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_unary',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_unary.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_validate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\frame\\test_validate.py',
   'PYMODULE'),
  ('pandas.tests.generic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\generic\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_duplicate_labels',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\generic\\test_duplicate_labels.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_finalize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\generic\\test_finalize.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_frame',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\generic\\test_frame.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_generic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\generic\\test_generic.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_label_or_level_utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\generic\\test_label_or_level_utils.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_series',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\generic\\test_series.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_to_xarray',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\generic\\test_to_xarray.py',
   'PYMODULE'),
  ('pandas.tests.groupby',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_aggregate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_aggregate.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_cython',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_cython.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_numba',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_other',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_other.py',
   'PYMODULE'),
  ('pandas.tests.groupby.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_corrwith',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_corrwith.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_describe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_describe.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_groupby_shift_diff',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_groupby_shift_diff.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_is_monotonic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_nlargest_nsmallest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_nlargest_nsmallest.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_nth',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_nth.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_quantile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_rank',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_sample',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_sample.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_size',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_size.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_skew',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_skew.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_value_counts',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_all_methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_all_methods.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_apply',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_apply.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_apply_mutate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_apply_mutate.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_bin_groupby',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_bin_groupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_categorical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_counting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_counting.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_cumulative',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_filters',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_filters.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby_dropna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby_dropna.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby_subclass',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby_subclass.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_grouping',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_grouping.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_index_as_string',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_index_as_string.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_libgroupby',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_libgroupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_missing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_numba',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_numeric_only',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_numeric_only.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_pipe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_pipe.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_raises',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_raises.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_reductions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_timegrouper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\test_timegrouper.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\transform\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform.test_numba',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\transform\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform.test_transform',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\groupby\\transform\\test_transform.py',
   'PYMODULE'),
  ('pandas.tests.indexes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_pickle',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_reshape',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_reshape.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_setops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_where',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_where.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_append',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_category',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_category.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_equals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_fillna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_map',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_reindex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_setops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_drop_duplicates',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_equals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_is_monotonic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_nat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_nat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_sort_values',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_value_counts',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_asof',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_delete',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_delete.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_factorize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_fillna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_insert',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_isocalendar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_isocalendar.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_map',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_normalize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_repeat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_resolution',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_round',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_shift',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_snap',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_snap.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_frame',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_frame.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_julian_date',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_julian_date.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_period.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_pydatetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_pydatetime.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_series',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_series.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_tz_convert',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_tz_localize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_unique',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_unique.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_date_range',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_date_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_datetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_freq_attr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_iter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_iter.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_join',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_npfuncs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_partial_slicing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_partial_slicing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_pickle',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_reindex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_scalar_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_setops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_timezones',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_equals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval_range',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval_tree',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval_tree.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_join',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_pickle',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_setops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_analytics',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_analytics.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_conversion',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_copy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_drop',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_drop.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_duplicates',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_equivalence',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_equivalence.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_get_level_values',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_get_level_values.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_get_set',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_get_set.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_integrity',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_integrity.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_isin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_isin.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_join',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_lexsort',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_lexsort.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_missing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_monotonic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_names',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_names.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_partial_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_partial_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_pickle',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_reindex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_reshape',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_reshape.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_setops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_sorting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_take',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_join',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_numeric',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_numeric.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_setops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\object\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\object\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\object\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_asfreq',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_factorize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_fillna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_insert',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_is_full',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_is_full.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_repeat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_shift',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_to_timestamp',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_to_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_freq_attr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_join',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_monotonic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_partial_slicing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_partial_slicing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_period_range',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_period_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_pickle',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_resolution',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_scalar_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_searchsorted',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_setops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_tools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_tools.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_join',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_range',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_setops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.string',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\string\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.string.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\string\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.string.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\string\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_any_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_any_index.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_base.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_datetimelike',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_engines',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_engines.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_frozen',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_frozen.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_index_new',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_index_new.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_numpy_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_numpy_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_old_base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_old_base.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_setops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_subclass',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_factorize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_fillna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_insert',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_repeat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_shift',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_delete',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_delete.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_freq_attr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_join',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_pickle',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_scalar_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_searchsorted',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_setops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_timedelta',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_timedelta_range',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_timedelta_range.py',
   'PYMODULE'),
  ('pandas.tests.indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\common.py',
   'PYMODULE'),
  ('pandas.tests.indexing.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval.test_interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval.test_interval_new',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\interval\\test_interval_new.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_chaining_and_caching',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_chaining_and_caching.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_datetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_getitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_iloc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_iloc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_indexing_slow',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_indexing_slow.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_loc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_loc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_multiindex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_multiindex.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_partial',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_partial.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_setitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_slice',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_slice.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_sorted',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_sorted.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_at',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_at.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_categorical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_chaining_and_caching',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_chaining_and_caching.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_check_indexer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_check_indexer.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_coercion',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_coercion.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_datetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_floats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_floats.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_iat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_iat.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_iloc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_iloc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_indexers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_indexers.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_loc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_loc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_na_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_na_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_partial',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_partial.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_scalar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\indexing\\test_scalar.py',
   'PYMODULE'),
  ('pandas.tests.interchange',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\interchange\\test_impl.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_spec_conformance',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\interchange\\test_spec_conformance.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\interchange\\test_utils.py',
   'PYMODULE'),
  ('pandas.tests.internals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\internals\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_internals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\internals\\test_internals.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_managers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\internals\\test_managers.py',
   'PYMODULE'),
  ('pandas.tests.io',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.excel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_odf',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_odf.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_odswriter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_odswriter.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_openpyxl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_openpyxl.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_readers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_readers.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_style',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_style.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_writers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_writers.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_xlrd',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_xlrd.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_xlsxwriter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.tests.io.formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_bar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_bar.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_exceptions.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_format',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_format.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_highlight',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_highlight.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_html',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_html.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_matplotlib',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_matplotlib.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_non_unique',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_non_unique.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_style',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_style.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_to_latex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_to_latex.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_to_string',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_to_string.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_tooltip',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_tooltip.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_console',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_console.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_css',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_css.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_eng_formatting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_eng_formatting.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_format',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_format.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_ipython_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_ipython_compat.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_printing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_printing.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_csv',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_excel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_excel.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_html',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_html.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_latex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_latex.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_markdown',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_markdown.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_string',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_string.py',
   'PYMODULE'),
  ('pandas.tests.io.generate_legacy_storage_files',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\generate_legacy_storage_files.py',
   'PYMODULE'),
  ('pandas.tests.io.json',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.json.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\json\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_compression',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_deprecated_kwargs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_deprecated_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_json_table_schema',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_json_table_schema.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_json_table_schema_ext_dtype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_json_table_schema_ext_dtype.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_normalize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_pandas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_pandas.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_readlines',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_readlines.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_ujson',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_ujson.py',
   'PYMODULE'),
  ('pandas.tests.io.parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_chunksize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_chunksize.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_common_basic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_common_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_data_list',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_data_list.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_decimal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_decimal.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_file_buffer_url',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_file_buffer_url.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_float',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_float.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_inf',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_inf.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_ints',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_ints.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_iterator',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_iterator.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_read_errors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_read_errors.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_verbose',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_verbose.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_categorical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_dtypes_basic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_dtypes_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_empty',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_empty.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_c_parser_only',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_c_parser_only.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_comment',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_comment.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_compression',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_concatenate_chunks',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_concatenate_chunks.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_converters',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_converters.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_dialect',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_dialect.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_encoding',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_encoding.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_header',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_header.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_index_col',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_index_col.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_mangle_dupes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_mangle_dupes.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_multi_thread',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_multi_thread.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_na_values',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_na_values.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_network',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_network.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_parse_dates',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_parse_dates.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_python_parser_only',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_python_parser_only.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_quoting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_quoting.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_read_fwf',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_read_fwf.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_skiprows',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_skiprows.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_textreader',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_textreader.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_unsupported',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_unsupported.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_upcast',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_upcast.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_parse_dates',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_parse_dates.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_strings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_strings.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_usecols_basic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_usecols_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\common.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_append',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_categorical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_compat.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_complex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_complex.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_errors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_errors.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_file_handling',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_file_handling.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_keys',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_keys.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_put',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_put.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_pytables_missing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_pytables_missing.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_read',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_read.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_retain_attributes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_retain_attributes.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_round_trip',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_round_trip.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_select',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_select.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_store',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_store.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_subclass',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_time_series',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_time_series.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_timezones',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.io.sas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_byteswap',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_byteswap.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_sas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_sas.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_sas7bdat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_sas7bdat.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_xport',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_xport.py',
   'PYMODULE'),
  ('pandas.tests.io.test_clipboard',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_clipboard.py',
   'PYMODULE'),
  ('pandas.tests.io.test_common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.io.test_compression',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.test_feather',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_feather.py',
   'PYMODULE'),
  ('pandas.tests.io.test_fsspec',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_fsspec.py',
   'PYMODULE'),
  ('pandas.tests.io.test_gbq',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_gbq.py',
   'PYMODULE'),
  ('pandas.tests.io.test_gcs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_gcs.py',
   'PYMODULE'),
  ('pandas.tests.io.test_html',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_html.py',
   'PYMODULE'),
  ('pandas.tests.io.test_http_headers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_http_headers.py',
   'PYMODULE'),
  ('pandas.tests.io.test_orc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_orc.py',
   'PYMODULE'),
  ('pandas.tests.io.test_parquet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_parquet.py',
   'PYMODULE'),
  ('pandas.tests.io.test_pickle',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.io.test_s3',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_s3.py',
   'PYMODULE'),
  ('pandas.tests.io.test_spss',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_spss.py',
   'PYMODULE'),
  ('pandas.tests.io.test_sql',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_sql.py',
   'PYMODULE'),
  ('pandas.tests.io.test_stata',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\test_stata.py',
   'PYMODULE'),
  ('pandas.tests.io.xml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\xml\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\xml\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_to_xml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_to_xml.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_xml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_xml.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_xml_dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_xml_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.libs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\libs\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_hashtable',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\libs\\test_hashtable.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_join',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\libs\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_lib',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\libs\\test_lib.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_libalgos',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\libs\\test_libalgos.py',
   'PYMODULE'),
  ('pandas.tests.plotting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.plotting.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\common.py',
   'PYMODULE'),
  ('pandas.tests.plotting.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_color',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_color.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_groupby',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_groupby.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_legend',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_legend.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_subplots',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_subplots.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_hist_box_by',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_hist_box_by.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_backend',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_backend.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_boxplot_method',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_boxplot_method.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_converter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_converter.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_datetimelike',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_groupby',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_hist_method',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_hist_method.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_misc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_misc.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_series',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_series.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_style',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\plotting\\test_style.py',
   'PYMODULE'),
  ('pandas.tests.reductions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reductions\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reductions.test_reductions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reductions\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.reductions.test_stat_reductions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reductions\\test_stat_reductions.py',
   'PYMODULE'),
  ('pandas.tests.resample',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\resample\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.resample.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\resample\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\resample\\test_base.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_datetime_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\resample\\test_datetime_index.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_period_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\resample\\test_period_index.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_resample_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\resample\\test_resample_api.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_resampler_grouper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\resample\\test_resampler_grouper.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_time_grouper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\resample\\test_time_grouper.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_timedelta',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\resample\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.reshape',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_append',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_append_common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_append_common.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_categorical',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_concat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_dataframe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_dataframe.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_datetimes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_datetimes.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_empty',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_empty.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_invalid',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_invalid.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_series',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_series.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_sort',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_sort.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_join',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_asof',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_asof.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_cross',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_cross.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_index_as_string',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_index_as_string.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_ordered',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_ordered.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_multi',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_multi.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_crosstab',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_crosstab.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_cut',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_cut.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_from_dummies',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_from_dummies.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_get_dummies',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_get_dummies.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_melt',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_melt.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_pivot',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_pivot.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_pivot_multilevel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_pivot_multilevel.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_qcut',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_qcut.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_union_categoricals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_union_categoricals.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\reshape\\test_util.py',
   'PYMODULE'),
  ('pandas.tests.scalar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_contains',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_contains.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_interval',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_overlaps',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_overlaps.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_asfreq',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.scalar.test_na_scalar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\test_na_scalar.py',
   'PYMODULE'),
  ('pandas.tests.scalar.test_nat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\test_nat.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods.test_as_unit',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods\\test_as_unit.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods.test_round',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_timedelta',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_as_unit',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_as_unit.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_normalize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_replace',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_round',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_timestamp_method',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_timestamp_method.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_to_julian_date',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_to_julian_date.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_to_pydatetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_to_pydatetime.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_tz_convert',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_tz_localize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_comparisons',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_comparisons.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_timestamp',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_timezones',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.series',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_cat_accessor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_cat_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_dt_accessor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_dt_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_list_accessor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_list_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_sparse_accessor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_sparse_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_str_accessor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_str_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_struct_accessor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_struct_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_datetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_delitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_delitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_get',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_get.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_getitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_indexing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_mask',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_mask.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_set_value',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_set_value.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_setitem',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_take',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_where',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_where.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_xs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_xs.py',
   'PYMODULE'),
  ('pandas.tests.series.methods',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_add_prefix_suffix',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_add_prefix_suffix.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_align',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_align.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_argsort',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_argsort.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_asof',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_astype',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_autocorr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_autocorr.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_between',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_between.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_case_when',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_case_when.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_clip',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_clip.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_combine',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_combine.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_combine_first',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_combine_first.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_compare',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_compare.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_convert_dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_convert_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_copy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_count',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_count.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_cov_corr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_cov_corr.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_describe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_describe.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_diff',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_diff.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_drop',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_drop.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_drop_duplicates',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_dropna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_dropna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_duplicated',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_duplicated.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_equals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_explode',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_explode.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_fillna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_get_numeric_data',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_get_numeric_data.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_head_tail',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_head_tail.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_infer_objects',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_infer_objects.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_info',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_info.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_interpolate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_interpolate.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_is_monotonic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_is_unique',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_is_unique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_isin',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_isin.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_isna',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_isna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_item',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_item.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_map',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_matmul',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_matmul.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_nlargest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_nlargest.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_nunique',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_nunique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_pct_change',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_pct_change.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_pop',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_pop.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_quantile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rank',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reindex',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reindex_like',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reindex_like.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rename',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rename.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rename_axis',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rename_axis.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_repeat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_replace',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reset_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reset_index.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_round',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_searchsorted',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_set_name',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_set_name.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_size',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_size.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_sort_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_sort_index.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_sort_values',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_csv',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_dict',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_dict.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_frame',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_frame.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_numpy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_tolist',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_tolist.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_truncate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_truncate.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_tz_localize',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_unique',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_unique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_unstack',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_unstack.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_update',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_update.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_value_counts',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_values',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_values.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_view',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_view.py',
   'PYMODULE'),
  ('pandas.tests.series.test_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.series.test_arithmetic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.series.test_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.series.test_cumulative',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.series.test_formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.series.test_iteration',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_iteration.py',
   'PYMODULE'),
  ('pandas.tests.series.test_logical_ops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_logical_ops.py',
   'PYMODULE'),
  ('pandas.tests.series.test_missing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.series.test_npfuncs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.series.test_reductions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.series.test_subclass',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.series.test_ufunc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_ufunc.py',
   'PYMODULE'),
  ('pandas.tests.series.test_unary',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_unary.py',
   'PYMODULE'),
  ('pandas.tests.series.test_validate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\series\\test_validate.py',
   'PYMODULE'),
  ('pandas.tests.strings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.strings.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\strings\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\strings\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_case_justify',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\strings\\test_case_justify.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_cat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\strings\\test_cat.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_extract',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\strings\\test_extract.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_find_replace',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\strings\\test_find_replace.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_get_dummies',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\strings\\test_get_dummies.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_split_partition',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\strings\\test_split_partition.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_string_array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\strings\\test_string_array.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_strings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\strings\\test_strings.py',
   'PYMODULE'),
  ('pandas.tests.test_aggregation',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_aggregation.py',
   'PYMODULE'),
  ('pandas.tests.test_algos',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_algos.py',
   'PYMODULE'),
  ('pandas.tests.test_common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.test_downstream',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_downstream.py',
   'PYMODULE'),
  ('pandas.tests.test_errors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_errors.py',
   'PYMODULE'),
  ('pandas.tests.test_expressions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_expressions.py',
   'PYMODULE'),
  ('pandas.tests.test_flags',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_flags.py',
   'PYMODULE'),
  ('pandas.tests.test_multilevel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_multilevel.py',
   'PYMODULE'),
  ('pandas.tests.test_nanops',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_nanops.py',
   'PYMODULE'),
  ('pandas.tests.test_optional_dependency',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_optional_dependency.py',
   'PYMODULE'),
  ('pandas.tests.test_register_accessor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_register_accessor.py',
   'PYMODULE'),
  ('pandas.tests.test_sorting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.test_take',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.tools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_datetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_numeric',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_numeric.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_time',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_time.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_timedelta',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.tseries',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_freq_code',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_freq_code.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_frequencies',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_frequencies.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_inference',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_inference.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_calendar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_calendar.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_federal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_federal.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_holiday',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_holiday.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_observance',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_observance.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\common.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_day',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_day.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_hour',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_hour.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_month',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_quarter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_quarter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_year',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_year.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_day',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_day.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_hour',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_hour.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_month',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_dst',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_dst.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_easter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_easter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_fiscal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_fiscal.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_index',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_month',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_offsets',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_offsets.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_offsets_properties',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_offsets_properties.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_quarter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_quarter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_ticks',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_ticks.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_week',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_week.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_year',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_year.py',
   'PYMODULE'),
  ('pandas.tests.tslibs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_array_to_datetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_array_to_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_ccalendar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_ccalendar.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_conversion',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_fields',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_fields.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_libfrequencies',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_libfrequencies.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_liboffsets',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_liboffsets.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_np_datetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_np_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_npy_units',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_npy_units.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_parse_iso8601',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_parse_iso8601.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_parsing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_parsing.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_period',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_resolution',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_strptime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_strptime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_timedeltas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_timezones',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_to_offset',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_to_offset.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_tzconversion',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_tzconversion.py',
   'PYMODULE'),
  ('pandas.tests.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.util.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_almost_equal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_almost_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_attr_equal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_attr_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_categorical_equal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_categorical_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_extension_array_equal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_extension_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_frame_equal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_frame_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_index_equal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_index_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_interval_array_equal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_interval_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_numpy_array_equal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_numpy_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_produces_warning',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_produces_warning.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_series_equal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_series_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate_kwarg',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate_kwarg.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate_nonkeyword_arguments',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate_nonkeyword_arguments.py',
   'PYMODULE'),
  ('pandas.tests.util.test_doc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_doc.py',
   'PYMODULE'),
  ('pandas.tests.util.test_hashing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_hashing.py',
   'PYMODULE'),
  ('pandas.tests.util.test_numba',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.util.test_rewrite_warning',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_rewrite_warning.py',
   'PYMODULE'),
  ('pandas.tests.util.test_shares_memory',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_shares_memory.py',
   'PYMODULE'),
  ('pandas.tests.util.test_show_versions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_show_versions.py',
   'PYMODULE'),
  ('pandas.tests.util.test_util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_util.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_args',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_args.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_args_and_kwargs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_args_and_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_inclusive',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_inclusive.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_kwargs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.window',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.window.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.window.moments',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\moments\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.conftest',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\moments\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_ewm',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_ewm.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_expanding',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_expanding.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_rolling',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_rolling.py',
   'PYMODULE'),
  ('pandas.tests.window.test_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.window.test_apply',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_apply.py',
   'PYMODULE'),
  ('pandas.tests.window.test_base_indexer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_base_indexer.py',
   'PYMODULE'),
  ('pandas.tests.window.test_cython_aggregations',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_cython_aggregations.py',
   'PYMODULE'),
  ('pandas.tests.window.test_dtypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.window.test_ewm',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_ewm.py',
   'PYMODULE'),
  ('pandas.tests.window.test_expanding',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_expanding.py',
   'PYMODULE'),
  ('pandas.tests.window.test_groupby',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.window.test_numba',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.window.test_online',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_online.py',
   'PYMODULE'),
  ('pandas.tests.window.test_pairwise',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_pairwise.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_functions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_functions.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_quantile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_quantile.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_skew_kurt',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_skew_kurt.py',
   'PYMODULE'),
  ('pandas.tests.window.test_timeseries_window',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_timeseries_window.py',
   'PYMODULE'),
  ('pandas.tests.window.test_win_type',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tests\\window\\test_win_type.py',
   'PYMODULE'),
  ('pandas.tseries',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._doctools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\util\\_doctools.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._test_decorators',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\util\\_test_decorators.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Python313\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'C:\\Python313\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'C:\\Python313\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pdb', 'C:\\Python313\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools', 'C:\\Python313\\Lib\\pickletools.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Python313\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python313\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'C:\\Python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'C:\\Python313\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'C:\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter', 'C:\\Python313\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'C:\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Python313\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Python313\\Lib\\site.py', 'PYMODULE'),
  ('six', 'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('socket', 'C:\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python313\\Lib\\socketserver.py', 'PYMODULE'),
  ('sqlalchemy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.aioodbc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.asyncio',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadb',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.vector',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\vector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('sqlite3', 'C:\\Python313\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.__main__', 'C:\\Python313\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'C:\\Python313\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'C:\\Python313\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'C:\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Python313\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python313\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python313\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Python313\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'C:\\Python313\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'C:\\Python313\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'C:\\Python313\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'C:\\Python313\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tqdm',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.cli',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE'),
  ('tqdm.gui',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE'),
  ('tqdm.notebook',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.std',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tqdm\\std.py',
   'PYMODULE'),
  ('tqdm.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('tqdm.version',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tracemalloc', 'C:\\Python313\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'C:\\Python313\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('tzdata',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('unittest', 'C:\\Python313\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python313\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'C:\\Python313\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\Python313\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'C:\\Python313\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'C:\\Python313\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'C:\\Python313\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\Python313\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'C:\\Python313\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\Python313\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'C:\\Python313\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'C:\\Python313\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python313\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python313\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python313\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python313\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('uuid', 'C:\\Python313\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python313\\Lib\\webbrowser.py', 'PYMODULE'),
  ('werkzeug',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('wtforms',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\__init__.py',
   'PYMODULE'),
  ('wtforms.csrf',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\csrf\\__init__.py',
   'PYMODULE'),
  ('wtforms.csrf.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\csrf\\core.py',
   'PYMODULE'),
  ('wtforms.csrf.session',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\csrf\\session.py',
   'PYMODULE'),
  ('wtforms.fields',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\fields\\__init__.py',
   'PYMODULE'),
  ('wtforms.fields.choices',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\fields\\choices.py',
   'PYMODULE'),
  ('wtforms.fields.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\fields\\core.py',
   'PYMODULE'),
  ('wtforms.fields.datetime',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\fields\\datetime.py',
   'PYMODULE'),
  ('wtforms.fields.form',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\fields\\form.py',
   'PYMODULE'),
  ('wtforms.fields.list',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\fields\\list.py',
   'PYMODULE'),
  ('wtforms.fields.numeric',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\fields\\numeric.py',
   'PYMODULE'),
  ('wtforms.fields.simple',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\fields\\simple.py',
   'PYMODULE'),
  ('wtforms.form',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\form.py',
   'PYMODULE'),
  ('wtforms.i18n',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\i18n.py',
   'PYMODULE'),
  ('wtforms.meta',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\meta.py',
   'PYMODULE'),
  ('wtforms.utils',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\utils.py',
   'PYMODULE'),
  ('wtforms.validators',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\validators.py',
   'PYMODULE'),
  ('wtforms.widgets',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\widgets\\__init__.py',
   'PYMODULE'),
  ('wtforms.widgets.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\wtforms\\widgets\\core.py',
   'PYMODULE'),
  ('xlsxwriter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE'),
  ('xlsxwriter.app',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE'),
  ('xlsxwriter.chart',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE'),
  ('xlsxwriter.chart_area',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE'),
  ('xlsxwriter.chart_bar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_column',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE'),
  ('xlsxwriter.chart_doughnut',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE'),
  ('xlsxwriter.chart_line',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE'),
  ('xlsxwriter.chart_pie',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE'),
  ('xlsxwriter.chart_radar',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_scatter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE'),
  ('xlsxwriter.chart_stock',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE'),
  ('xlsxwriter.chartsheet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE'),
  ('xlsxwriter.comments',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE'),
  ('xlsxwriter.contenttypes',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE'),
  ('xlsxwriter.core',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE'),
  ('xlsxwriter.custom',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE'),
  ('xlsxwriter.drawing',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE'),
  ('xlsxwriter.exceptions',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE'),
  ('xlsxwriter.feature_property_bag',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE'),
  ('xlsxwriter.format',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE'),
  ('xlsxwriter.image',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE'),
  ('xlsxwriter.metadata',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE'),
  ('xlsxwriter.packager',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE'),
  ('xlsxwriter.relationships',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_rel',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_structure',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_types',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE'),
  ('xlsxwriter.shape',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE'),
  ('xlsxwriter.sharedstrings',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE'),
  ('xlsxwriter.styles',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE'),
  ('xlsxwriter.table',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE'),
  ('xlsxwriter.theme',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE'),
  ('xlsxwriter.url',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE'),
  ('xlsxwriter.utility',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE'),
  ('xlsxwriter.vml',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE'),
  ('xlsxwriter.workbook',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE'),
  ('xlsxwriter.worksheet',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE'),
  ('xlsxwriter.xmlwriter',
   'e:\\app\\TRINING\\.venv\\Lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE'),
  ('xml', 'C:\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'C:\\Python313\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'C:\\Python313\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'C:\\Python313\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'C:\\Python313\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'C:\\Python313\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\Python313\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'C:\\Python313\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python313\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python313\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Python313\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python313\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python313\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Python313\\Lib\\zipimport.py', 'PYMODULE'),
  ('zoneinfo', 'C:\\Python313\\Lib\\zoneinfo\\__init__.py', 'PYMODULE'),
  ('zoneinfo._common', 'C:\\Python313\\Lib\\zoneinfo\\_common.py', 'PYMODULE'),
  ('zoneinfo._tzpath', 'C:\\Python313\\Lib\\zoneinfo\\_tzpath.py', 'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Python313\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
