
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), netrc (delayed, conditional), getpass (delayed, optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named 'collections.abc' - imported by traceback (top-level), typing (top-level), inspect (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), http.client (top-level), werkzeug.wrappers.request (top-level), werkzeug.datastructures.accept (top-level), werkzeug.datastructures.structures (top-level), markupsafe (top-level), typing_extensions (top-level), asyncio.base_events (top-level), asyncio.coroutines (top-level), werkzeug.datastructures.cache_control (top-level), werkzeug.datastructures.mixins (top-level), werkzeug.datastructures.auth (top-level), werkzeug.datastructures.csp (top-level), werkzeug.datastructures.etag (top-level), werkzeug.datastructures.file_storage (top-level), werkzeug.datastructures.headers (top-level), werkzeug.datastructures.range (top-level), werkzeug.middleware.shared_data (top-level), flask.app (top-level), click.core (top-level), click.types (top-level), click._compat (top-level), click._winconsole (top-level), click.exceptions (top-level), click.utils (top-level), click.shell_completion (top-level), click.formatting (top-level), click.parser (top-level), click._textwrap (top-level), click.termui (top-level), click._termui_impl (top-level), flask.cli (top-level), blinker.base (top-level), blinker._utilities (top-level), flask.typing (top-level), flask.sessions (top-level), itsdangerous.serializer (top-level), itsdangerous.signer (top-level), itsdangerous.timed (top-level), click.testing (top-level), setuptools (top-level), setuptools._distutils.filelist (top-level), setuptools._distutils.util (top-level), setuptools._vendor.jaraco.functools (top-level), setuptools._vendor.more_itertools.more (top-level), setuptools._vendor.more_itertools.recipes (top-level), setuptools._distutils._modified (top-level), setuptools._distutils.compat (top-level), setuptools._distutils.spawn (top-level), setuptools._distutils.compilers.C.base (top-level), setuptools._distutils.fancy_getopt (top-level), setuptools._reqs (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools._distutils.command.bdist (top-level), setuptools._distutils.core (top-level), setuptools._distutils.cmd (top-level), setuptools._distutils.dist (top-level), configparser (top-level), setuptools._distutils.extension (top-level), setuptools.config.setupcfg (top-level), setuptools.config.expand (top-level), setuptools.config.pyprojecttoml (top-level), setuptools.config._apply_pyprojecttoml (top-level), tomllib._parser (top-level), setuptools._vendor.tomli._parser (top-level), setuptools.command.egg_info (top-level), setuptools._distutils.command.build (top-level), setuptools._distutils.command.sdist (top-level), setuptools.glob (top-level), setuptools.command._requirestxt (top-level), setuptools.command.bdist_wheel (top-level), setuptools._vendor.wheel.cli.convert (top-level), setuptools._vendor.wheel.cli.tags (top-level), sqlalchemy.util.compat (conditional), sqlalchemy.util.typing (top-level), sqlalchemy.sql.coercions (top-level), sqlalchemy.sql.traversals (top-level), sqlalchemy.sql.compiler (top-level), sqlalchemy.sql.dml (top-level), sqlalchemy.sql.sqltypes (top-level), sqlalchemy.engine.row (top-level), sqlalchemy.sql.lambdas (top-level), sqlalchemy.engine.url (top-level), sqlalchemy.orm.query (top-level), dns.immutable (top-level), numpy._typing._array_like (top-level), numpy._typing._nested_sequence (conditional), numpy._typing._shape (top-level), numpy._typing._dtype_like (top-level), numpy.lib._function_base_impl (top-level), numpy.lib._npyio_impl (top-level), numpy.random._common (top-level), numpy.random._generator (top-level), numpy.random.bit_generator (top-level), numpy.random.mtrand (top-level), numpy.polynomial._polybase (top-level), pandas._typing (top-level), pytz.lazy (optional), pandas.util._exceptions (conditional), pandas._config.config (conditional), pandas.util.version (top-level), pandas.core.dtypes.inference (conditional), pandas.util._validators (top-level), pandas.core.construction (top-level), pandas.core.common (top-level), pandas.util._decorators (conditional), pandas.core.frame (top-level), pandas.core.dtypes.concat (conditional), pandas.core.sorting (conditional), pandas.core.indexes.category (conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.masked (conditional), pandas.core.apply (conditional), pandas.core.base (conditional), pandas.core.indexing (conditional), pandas.core.internals.blocks (conditional), pandas.core.arrays.numeric (conditional), pandas.core.arrays.timedeltas (conditional), pandas.io.formats.format (top-level), pandas.core.indexes.range (top-level), pandas.core.tools.timedeltas (conditional), pandas.core.indexes.datetimelike (conditional), pandas.core.reshape.concat (conditional), pandas.io.common (top-level), pandas.io.formats.printing (top-level), pandas.core.indexes.multi (top-level), pandas.io.formats.html (conditional), pandas.io.formats.string (conditional), pandas.io.formats.csvs (top-level), pandas.io.formats.style_render (top-level), pandas.core.interchange.dataframe_protocol (conditional), pandas.core.window.rolling (conditional), pandas.core.series (top-level), pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (conditional), pandas.core.methods.selectn (top-level), pandas.core.strings.accessor (conditional), pandas.core.tools.datetimes (conditional), pandas.io.formats.info (conditional), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.core.groupby.grouper (conditional), pandas.core.groupby.ops (conditional), pandas.io._util (conditional), pandas.io.json._normalize (conditional), pandas.io.parsers.base_parser (conditional), pandas.io.parsers.c_parser_wrapper (conditional), pandas.io.parsers.python_parser (top-level), pandas.io.parsers.readers (conditional), pandas.io.json._json (conditional), pandas.io.stata (conditional), pandas.io.formats.style (conditional), pandas.io.formats.excel (top-level), pandas.io.formats.css (conditional), pandas.io.excel._base (top-level), pandas.io.excel._util (top-level), xml.etree.ElementTree (top-level), PIL.Image (top-level), PIL._typing (top-level), PIL.TiffImagePlugin (top-level), PIL.ImageOps (top-level), PIL.ImagePalette (top-level), PIL.ImageFilter (top-level), PIL.PngImagePlugin (top-level), pandas.core.arrays.interval (conditional), pandas.core.indexes.interval (conditional), pandas.core.arrays.period (conditional), pandas.core.indexes.period (conditional), pandas.core.internals.managers (top-level), pandas.core.internals.ops (conditional), pandas.core.internals.array_manager (conditional), pandas.core.internals.construction (conditional), pandas.core.methods.describe (conditional), pandas.core.generic (conditional), pandas.core.computation.parsing (conditional), pandas.compat.pickle_compat (conditional), pandas.core.computation.ops (conditional), pandas.core.computation.align (conditional), pandas.io.pytables (conditional), pandas.io.sql (conditional), sqlite3.dbapi2 (top-level), pandas.core.groupby.groupby (top-level), pandas.core.strings.base (conditional), pandas.core.strings.object_array (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.groupby.base (conditional), pandas.core.groupby.indexing (top-level), pandas.core.resample (conditional), pandas.core.groupby.generic (conditional), pandas.core.reshape.merge (top-level), pandas.core.arrays.arrow.array (conditional), pandas.core.arrays.datetimelike (conditional), pandas.core.arrays.datetimes (conditional), pandas.core.indexes.datetimes (conditional), pandas.core.arrays._mixins (conditional), pandas.core.arrays.categorical (conditional), pandas.core.reshape.melt (conditional), pandas.core.interchange.dataframe (conditional), pandas.io.feather_format (conditional), pandas.io.xml (conditional), pandas.core.reshape.pivot (top-level), pandas.core.arrays.base (conditional), pandas.core.internals.concat (conditional), pandas.core.indexes.base (conditional), pandas.core.arrays.numpy_ (conditional), pandas.core.dtypes.cast (conditional), pandas.core.arrays.arrow.accessors (conditional), pandas.core.dtypes.dtypes (conditional), pandas.core.util.hashing (conditional), pandas.core.reshape.encoding (top-level), pandas._config.localization (conditional), pandas._testing.contexts (conditional), pandas._testing._warnings (conditional), pandas.io.html (conditional), pandas.io.sas.sasreader (conditional), pandas.io.spss (conditional), pandas.conftest (conditional), pandas.tests.dtypes.test_inference (top-level), pandas.tests.extension.date.array (conditional), pandas.tests.extension.json.array (conditional), pandas.tests.frame.constructors.test_from_records (top-level), pandas.tests.frame.test_constructors (top-level), pandas.tests.io.json.test_readlines (top-level), pandas.tests.io.parser.test_python_parser_only (conditional), pandas.tests.io.pytables.common (top-level), pandas.tests.io.test_html (top-level), sqlalchemy.dialects.postgresql.psycopg2 (top-level), pandas.tests.libs.test_hashtable (top-level), pandas.tests.plotting.common (conditional), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.converter (conditional), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.groupby (conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.tests.reshape.concat.test_concat (top-level), pandas.tests.series.test_constructors (top-level), pandas.tests.test_register_accessor (top-level), pandas.util._doctools (conditional), PIL.Jpeg2KImagePlugin (top-level), PIL.IptcImagePlugin (top-level), setuptools._distutils.command.build_ext (top-level), _pyrepl.types (top-level), _pyrepl.readline (top-level), setuptools._distutils.compilers.C.msvc (top-level), sqlalchemy.ext.baked (top-level)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), _pyrepl.unix_console (delayed, optional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), tqdm.utils (delayed, optional), _pyrepl.unix_console (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level), _pyrepl.pager (delayed, optional), werkzeug._reloader (delayed, optional), click._termui_impl (conditional), tqdm.utils (delayed, optional), _pyrepl.unix_console (top-level), _pyrepl.fancy_termios (top-level), _pyrepl.unix_eventqueue (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named multiprocessing.Value - imported by multiprocessing (top-level), werkzeug.debug (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional), sqlalchemy.util.langhelpers (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level), _pyrepl.curses (optional)
missing module named readline - imported by code (delayed, conditional, optional), flask.cli (delayed, conditional, optional), rlcompleter (optional), cmd (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), sqlite3.__main__ (delayed, conditional, optional)
missing module named _typeshed - imported by werkzeug._internal (conditional), click.testing (conditional), setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), numpy.random.bit_generator (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional), tqdm.cli (delayed, conditional, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pyimod02_importers - imported by E:\app\TRINING\.venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named wmi - imported by dns.win32util (conditional)
missing module named pythoncom - imported by dns.win32util (conditional)
missing module named httpx - imported by dns._trio_backend (conditional), dns.query (conditional), dns.asyncquery (conditional), dns._asyncio_backend (conditional)
missing module named 'httpcore._backends' - imported by dns._trio_backend (conditional), dns.query (conditional), dns._asyncio_backend (conditional)
missing module named 'aioquic.quic' - imported by dns.quic._asyncio (top-level), dns.quic._common (top-level), dns.quic._sync (top-level), dns.quic._trio (top-level)
missing module named trio - imported by dns._trio_backend (top-level), dns.quic (conditional), dns.quic._trio (top-level)
missing module named 'aioquic.h3' - imported by dns.quic._common (top-level)
missing module named aioquic - imported by dns.quic (conditional)
missing module named sniffio - imported by dns.asyncbackend (delayed, optional)
missing module named httpcore - imported by dns._trio_backend (conditional), dns._asyncio_backend (conditional)
missing module named anyio - imported by dns._asyncio_backend (conditional)
missing module named 'trio.socket' - imported by dns._trio_backend (top-level)
missing module named pysqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed)
missing module named sqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, optional)
missing module named fontTools - imported by arabic_reshaper.reshaper_config (optional)
missing module named 'defusedxml.ElementTree' - imported by openpyxl.xml.functions (conditional)
missing module named 'lxml.etree' - imported by openpyxl.xml.functions (conditional), pandas.io.xml (delayed), pandas.io.formats.xml (delayed), pandas.io.html (delayed)
missing module named defusedxml - imported by openpyxl.xml (delayed, optional), PIL.Image (optional)
missing module named lxml - imported by openpyxl.xml (delayed, optional), pandas.io.xml (conditional)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named PIL._avif - imported by PIL (optional), PIL.AvifImagePlugin (optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._array_utils_impl (top-level), numpy (conditional), numpy.fft._helper (top-level), numpy.fft._pocketfft (top-level)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._function_base_impl (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy.lib._utils_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longlong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named pytest - imported by pandas._testing._io (delayed), pandas._testing (delayed), pandas.conftest (top-level), pandas.util._test_decorators (top-level), pandas.tests.api.test_api (top-level), pandas.tests.apply.test_frame_apply (top-level), pandas.tests.apply.test_frame_apply_relabeling (top-level), pandas.tests.apply.test_frame_transform (top-level), pandas.tests.apply.test_invalid_arg (top-level), pandas.tests.apply.test_numba (top-level), pandas.tests.apply.test_series_apply (top-level), pandas.tests.apply.test_series_transform (top-level), pandas.tests.apply.test_str (top-level), pandas.tests.arithmetic.common (top-level), pandas.tests.arithmetic.conftest (top-level), pandas.tests.arithmetic.test_array_ops (top-level), pandas.tests.arithmetic.test_datetime64 (top-level), pandas.tests.arithmetic.test_interval (top-level), pandas.tests.arithmetic.test_numeric (top-level), pandas.tests.arithmetic.test_object (top-level), pandas.tests.arithmetic.test_period (top-level), pandas.tests.arithmetic.test_timedelta64 (top-level), pandas.tests.arrays.boolean.test_arithmetic (top-level), pandas.tests.arrays.boolean.test_astype (top-level), pandas.tests.arrays.boolean.test_comparison (top-level), pandas.tests.arrays.masked_shared (top-level), pandas.tests.extension.base.accumulate (top-level), pandas.tests.extension.base.casting (top-level), pandas.tests.extension.base.constructors (top-level), pandas.tests.extension.base.dim2 (top-level), pandas.tests.extension.base.dtype (top-level), pandas.tests.extension.base.getitem (top-level), pandas.tests.extension.base.groupby (top-level), pandas.tests.extension.base.interface (top-level), pandas.tests.extension.base.io (top-level), pandas.tests.extension.base.methods (top-level), pandas.tests.extension.base.missing (top-level), pandas.tests.extension.base.ops (top-level), pandas.tests.extension.base.printing (top-level), pandas.tests.extension.base.reduce (top-level), pandas.tests.extension.base.reshaping (top-level), pandas.tests.extension.base.setitem (top-level), pandas.tests.arrays.boolean.test_construction (top-level), pandas.tests.arrays.boolean.test_function (top-level), pandas.tests.arrays.boolean.test_indexing (top-level), pandas.tests.arrays.boolean.test_logical (top-level), pandas.tests.arrays.boolean.test_reduction (top-level), pandas.tests.arrays.categorical.test_algos (top-level), pandas.tests.arrays.categorical.test_analytics (top-level), pandas.tests.arrays.categorical.test_api (top-level), pandas.tests.arrays.categorical.test_astype (top-level), pandas.tests.arrays.categorical.test_constructors (top-level), pandas.tests.arrays.categorical.test_dtypes (top-level), pandas.tests.arrays.categorical.test_indexing (top-level), pandas.tests.arrays.categorical.test_map (top-level), pandas.tests.arrays.categorical.test_missing (top-level), pandas.tests.arrays.categorical.test_operators (top-level), pandas.tests.arrays.categorical.test_replace (top-level), pandas.tests.arrays.categorical.test_repr (top-level), pandas.tests.arrays.categorical.test_sorting (top-level), pandas.tests.arrays.categorical.test_take (top-level), pandas.tests.arrays.categorical.test_warnings (top-level), pandas.tests.arrays.datetimes.test_constructors (top-level), pandas.tests.arrays.datetimes.test_cumulative (top-level), pandas.tests.arrays.datetimes.test_reductions (top-level), pandas.tests.arrays.floating.conftest (top-level), pandas.tests.arrays.floating.test_arithmetic (top-level), pandas.tests.arrays.floating.test_astype (top-level), pandas.tests.arrays.floating.test_comparison (top-level), pandas.tests.arrays.floating.test_concat (top-level), pandas.tests.arrays.floating.test_construction (top-level), pandas.tests.arrays.floating.test_function (top-level), pandas.tests.arrays.floating.test_repr (top-level), pandas.tests.arrays.floating.test_to_numpy (top-level), pandas.tests.arrays.integer.conftest (top-level), pandas.tests.arrays.integer.test_arithmetic (top-level), pandas.tests.arrays.integer.test_comparison (top-level), pandas.tests.arrays.integer.test_concat (top-level), pandas.tests.arrays.integer.test_construction (top-level), pandas.tests.arrays.integer.test_dtypes (top-level), pandas.tests.arrays.integer.test_function (top-level), pandas.tests.arrays.integer.test_reduction (top-level), pandas.tests.arrays.integer.test_repr (top-level), pandas.tests.arrays.interval.test_astype (top-level), pandas.tests.arrays.interval.test_interval (top-level), pandas.tests.arrays.interval.test_interval_pyarrow (top-level), pandas.tests.arrays.interval.test_overlaps (top-level), pandas.tests.arrays.masked.test_arithmetic (top-level), pandas.tests.arrays.masked.test_arrow_compat (top-level), pandas.tests.arrays.masked.test_function (top-level), pandas.tests.arrays.masked.test_indexing (top-level), pandas.tests.arrays.numpy_.test_numpy (top-level), pandas.tests.arrays.period.test_arrow_compat (top-level), pandas.tests.arrays.period.test_astype (top-level), pandas.tests.arrays.period.test_constructors (top-level), pandas.tests.arrays.period.test_reductions (top-level), pandas.tests.arrays.sparse.test_accessor (top-level), pandas.tests.arrays.sparse.test_arithmetics (top-level), pandas.tests.arrays.sparse.test_array (top-level), pandas.tests.arrays.sparse.test_astype (top-level), pandas.tests.arrays.sparse.test_combine_concat (top-level), pandas.tests.arrays.sparse.test_constructors (top-level), pandas.tests.arrays.sparse.test_dtype (top-level), pandas.tests.arrays.sparse.test_indexing (top-level), pandas.tests.arrays.sparse.test_libsparse (top-level), pandas.tests.arrays.sparse.test_reductions (top-level), pandas.tests.arrays.sparse.test_unary (top-level), pandas.tests.arrays.string_.test_concat (top-level), pandas.tests.arrays.string_.test_string (top-level), pandas.tests.arrays.string_.test_string_arrow (top-level), pandas.tests.arrays.test_array (top-level), pandas.tests.arrays.test_datetimelike (top-level), pandas.tests.arrays.test_datetimes (top-level), pandas.tests.arrays.test_period (top-level), pandas.tests.arrays.test_timedeltas (top-level), pandas.tests.arrays.timedeltas.test_constructors (top-level), pandas.tests.arrays.timedeltas.test_cumulative (top-level), pandas.tests.arrays.timedeltas.test_reductions (top-level), pandas.tests.base.test_constructors (top-level), pandas.tests.base.test_conversion (top-level), pandas.tests.base.test_fillna (top-level), pandas.tests.base.test_misc (top-level), pandas.tests.base.test_transpose (top-level), pandas.tests.base.test_unique (top-level), pandas.tests.base.test_value_counts (top-level), pandas.tests.computation.test_compat (top-level), pandas.tests.computation.test_eval (top-level), pandas.tests.config.test_config (top-level), pandas.tests.config.test_localization (top-level), pandas.tests.copy_view.index.test_datetimeindex (top-level), pandas.tests.copy_view.index.test_index (top-level), pandas.tests.copy_view.index.test_periodindex (top-level), pandas.tests.copy_view.index.test_timedeltaindex (top-level), pandas.tests.copy_view.test_array (top-level), pandas.tests.copy_view.test_astype (top-level), pandas.tests.copy_view.test_chained_assignment_deprecation (top-level), pandas.tests.copy_view.test_constructors (top-level), pandas.tests.copy_view.test_core_functionalities (top-level), pandas.tests.copy_view.test_functions (top-level), pandas.tests.copy_view.test_indexing (top-level), pandas.tests.copy_view.test_internals (top-level), pandas.tests.copy_view.test_interp_fillna (top-level), pandas.tests.copy_view.test_methods (top-level), pandas.tests.copy_view.test_replace (top-level), pandas.tests.dtypes.cast.test_construct_from_scalar (top-level), pandas.tests.dtypes.cast.test_construct_ndarray (top-level), pandas.tests.dtypes.cast.test_construct_object_arr (top-level), pandas.tests.dtypes.cast.test_downcast (top-level), pandas.tests.dtypes.cast.test_find_common_type (top-level), pandas.tests.dtypes.cast.test_infer_datetimelike (top-level), pandas.tests.dtypes.cast.test_infer_dtype (top-level), pandas.tests.dtypes.cast.test_maybe_box_native (top-level), pandas.tests.dtypes.cast.test_promote (top-level), pandas.tests.dtypes.test_common (top-level), pandas.tests.dtypes.test_concat (top-level), pandas.tests.dtypes.test_dtypes (top-level), pandas.tests.dtypes.test_generic (top-level), pandas.tests.dtypes.test_inference (top-level), pandas.tests.dtypes.test_missing (top-level), pandas.tests.extension.conftest (top-level), pandas.tests.extension.decimal.test_decimal (top-level), pandas.tests.extension.json.test_json (top-level), pandas.tests.extension.list.test_list (top-level), pandas.tests.extension.test_arrow (top-level), pandas.tests.extension.test_categorical (top-level), pandas.tests.extension.test_common (top-level), pandas.tests.extension.test_datetime (top-level), pandas.tests.extension.test_extension (top-level), pandas.tests.extension.test_interval (top-level), pandas.tests.extension.test_masked (top-level), pandas.tests.extension.test_numpy (top-level), pandas.tests.extension.test_period (top-level), pandas.tests.extension.test_sparse (top-level), pandas.tests.extension.test_string (top-level), pandas.tests.frame.conftest (top-level), pandas.tests.frame.constructors.test_from_dict (top-level), pandas.tests.frame.constructors.test_from_records (top-level), pandas.tests.frame.indexing.test_coercion (top-level), pandas.tests.frame.indexing.test_delitem (top-level), pandas.tests.frame.indexing.test_get (top-level), pandas.tests.frame.indexing.test_get_value (top-level), pandas.tests.frame.indexing.test_getitem (top-level), pandas.tests.frame.indexing.test_indexing (top-level), pandas.tests.frame.indexing.test_insert (top-level), pandas.tests.frame.indexing.test_setitem (top-level), pandas.tests.frame.indexing.test_take (top-level), pandas.tests.frame.indexing.test_where (top-level), pandas.tests.frame.indexing.test_xs (top-level), pandas.tests.frame.methods.test_add_prefix_suffix (top-level), pandas.tests.frame.methods.test_align (top-level), pandas.tests.frame.methods.test_asfreq (top-level), pandas.tests.frame.methods.test_asof (top-level), pandas.tests.frame.methods.test_assign (top-level), pandas.tests.frame.methods.test_astype (top-level), pandas.tests.frame.methods.test_at_time (top-level), pandas.tests.frame.methods.test_between_time (top-level), pandas.tests.frame.methods.test_clip (top-level), pandas.tests.frame.methods.test_combine (top-level), pandas.tests.frame.methods.test_combine_first (top-level), pandas.tests.frame.methods.test_compare (top-level), pandas.tests.frame.methods.test_convert_dtypes (top-level), pandas.tests.frame.methods.test_copy (top-level), pandas.tests.frame.methods.test_cov_corr (top-level), pandas.tests.frame.methods.test_describe (top-level), pandas.tests.frame.methods.test_diff (top-level), pandas.tests.frame.methods.test_dot (top-level), pandas.tests.frame.methods.test_drop (top-level), pandas.tests.frame.methods.test_drop_duplicates (top-level), pandas.tests.frame.methods.test_droplevel (top-level), pandas.tests.frame.methods.test_dropna (top-level), pandas.tests.frame.methods.test_dtypes (top-level), pandas.tests.frame.methods.test_duplicated (top-level), pandas.tests.frame.methods.test_explode (top-level), pandas.tests.frame.methods.test_fillna (top-level), pandas.tests.frame.methods.test_filter (top-level), pandas.tests.frame.methods.test_first_and_last (top-level), pandas.tests.frame.methods.test_first_valid_index (top-level), pandas.tests.frame.methods.test_info (top-level), pandas.tests.frame.methods.test_interpolate (top-level), pandas.tests.frame.methods.test_is_homogeneous_dtype (top-level), pandas.tests.frame.methods.test_isetitem (top-level), pandas.tests.frame.methods.test_isin (top-level), pandas.tests.frame.methods.test_join (top-level), pandas.tests.frame.methods.test_map (top-level), pandas.tests.frame.methods.test_matmul (top-level), pandas.tests.frame.methods.test_nlargest (top-level), pandas.tests.frame.methods.test_pct_change (top-level), pandas.tests.frame.methods.test_pipe (top-level), pandas.tests.frame.methods.test_quantile (top-level), pandas.tests.frame.methods.test_rank (top-level), pandas.tests.frame.methods.test_reindex (top-level), pandas.tests.frame.methods.test_reindex_like (top-level), pandas.tests.frame.methods.test_rename (top-level), pandas.tests.frame.methods.test_rename_axis (top-level), pandas.tests.frame.methods.test_reorder_levels (top-level), pandas.tests.frame.methods.test_replace (top-level), pandas.tests.frame.methods.test_reset_index (top-level), pandas.tests.frame.methods.test_round (top-level), pandas.tests.frame.methods.test_sample (top-level), pandas.tests.frame.methods.test_select_dtypes (top-level), pandas.tests.frame.methods.test_set_axis (top-level), pandas.tests.frame.methods.test_set_index (top-level), pandas.tests.frame.methods.test_shift (top-level), pandas.tests.frame.methods.test_size (top-level), pandas.tests.frame.methods.test_sort_index (top-level), pandas.tests.frame.methods.test_sort_values (top-level), pandas.tests.frame.methods.test_swapaxes (top-level), pandas.tests.frame.methods.test_swaplevel (top-level), pandas.tests.frame.methods.test_to_csv (top-level), pandas.tests.frame.methods.test_to_dict (top-level), pandas.tests.frame.methods.test_to_dict_of_blocks (top-level), pandas.tests.frame.methods.test_to_numpy (top-level), pandas.tests.frame.methods.test_to_period (top-level), pandas.tests.frame.methods.test_to_records (top-level), pandas.tests.frame.methods.test_to_timestamp (top-level), pandas.tests.frame.methods.test_transpose (top-level), pandas.tests.frame.methods.test_truncate (top-level), pandas.tests.frame.methods.test_tz_convert (top-level), pandas.tests.frame.methods.test_tz_localize (top-level), pandas.tests.frame.methods.test_update (top-level), pandas.tests.frame.methods.test_value_counts (top-level), pandas.tests.frame.methods.test_values (top-level), pandas.tests.frame.test_api (top-level), pandas.tests.frame.test_arithmetic (top-level), pandas.tests.frame.test_arrow_interface (top-level), pandas.tests.frame.test_block_internals (top-level), pandas.tests.frame.test_constructors (top-level), pandas.tests.frame.test_cumulative (top-level), pandas.tests.frame.test_iteration (top-level), pandas.tests.frame.test_logical_ops (top-level), pandas.tests.frame.test_nonunique_indexes (top-level), pandas.tests.frame.test_query_eval (top-level), pandas.tests.frame.test_reductions (top-level), pandas.tests.frame.test_repr (top-level), pandas.tests.frame.test_stack_unstack (top-level), pandas.tests.frame.test_subclass (top-level), pandas.tests.frame.test_ufunc (top-level), pandas.tests.frame.test_unary (top-level), pandas.tests.frame.test_validate (top-level), pandas.tests.generic.test_duplicate_labels (top-level), pandas.tests.generic.test_finalize (top-level), pandas.tests.generic.test_frame (top-level), pandas.tests.generic.test_generic (top-level), pandas.tests.generic.test_label_or_level_utils (top-level), pandas.tests.generic.test_series (top-level), pandas.tests.generic.test_to_xarray (top-level), pandas.tests.groupby.aggregate.test_aggregate (top-level), pandas.tests.groupby.aggregate.test_cython (top-level), pandas.tests.groupby.aggregate.test_numba (top-level), pandas.tests.groupby.aggregate.test_other (top-level), pandas.tests.groupby.conftest (top-level), pandas.tests.groupby.methods.test_describe (top-level), pandas.tests.groupby.methods.test_groupby_shift_diff (top-level), pandas.tests.groupby.methods.test_is_monotonic (top-level), pandas.tests.groupby.methods.test_nlargest_nsmallest (top-level), pandas.tests.groupby.methods.test_nth (top-level), pandas.tests.groupby.methods.test_quantile (top-level), pandas.tests.groupby.methods.test_rank (top-level), pandas.tests.groupby.methods.test_sample (top-level), pandas.tests.groupby.methods.test_size (top-level), pandas.tests.groupby.methods.test_value_counts (top-level), pandas.tests.groupby.test_all_methods (top-level), pandas.tests.groupby.test_api (top-level), pandas.tests.groupby.test_apply (top-level), pandas.tests.groupby.test_bin_groupby (top-level), pandas.tests.groupby.test_categorical (top-level), pandas.tests.groupby.test_counting (top-level), pandas.tests.groupby.test_cumulative (top-level), pandas.tests.groupby.test_filters (top-level), pandas.tests.groupby.test_groupby (top-level), pandas.tests.groupby.test_groupby_dropna (top-level), pandas.tests.groupby.test_groupby_subclass (top-level), pandas.tests.groupby.test_grouping (top-level), pandas.tests.groupby.test_index_as_string (top-level), pandas.tests.groupby.test_indexing (top-level), pandas.tests.groupby.test_libgroupby (top-level), pandas.tests.groupby.test_missing (top-level), pandas.tests.groupby.test_numba (top-level), pandas.tests.groupby.test_numeric_only (top-level), pandas.tests.groupby.test_raises (top-level), pandas.tests.groupby.test_reductions (top-level), pandas.tests.groupby.test_timegrouper (top-level), pandas.tests.groupby.transform.test_numba (top-level), pandas.tests.groupby.transform.test_transform (top-level), pandas.tests.indexes.base_class.test_constructors (top-level), pandas.tests.indexes.base_class.test_formats (top-level), pandas.tests.indexes.base_class.test_indexing (top-level), pandas.tests.indexes.base_class.test_reshape (top-level), pandas.tests.indexes.base_class.test_setops (top-level), pandas.tests.indexes.categorical.test_append (top-level), pandas.tests.indexes.categorical.test_astype (top-level), pandas.tests.indexes.categorical.test_category (top-level), pandas.tests.indexes.categorical.test_constructors (top-level), pandas.tests.indexes.categorical.test_equals (top-level), pandas.tests.indexes.categorical.test_fillna (top-level), pandas.tests.indexes.categorical.test_formats (top-level), pandas.tests.indexes.categorical.test_indexing (top-level), pandas.tests.indexes.categorical.test_map (top-level), pandas.tests.indexes.categorical.test_reindex (top-level), pandas.tests.indexes.categorical.test_setops (top-level), pandas.tests.indexes.conftest (top-level), pandas.tests.indexes.datetimelike_.test_drop_duplicates (top-level), pandas.tests.indexes.datetimelike_.test_equals (top-level), pandas.tests.indexes.datetimelike_.test_indexing (top-level), pandas.tests.indexes.datetimelike_.test_nat (top-level), pandas.tests.indexes.datetimelike_.test_sort_values (top-level), pandas.tests.indexes.datetimes.methods.test_astype (top-level), pandas.tests.indexes.datetimes.methods.test_delete (top-level), pandas.tests.indexes.datetimes.methods.test_factorize (top-level), pandas.tests.indexes.datetimes.methods.test_fillna (top-level), pandas.tests.indexes.datetimes.methods.test_insert (top-level), pandas.tests.indexes.datetimes.methods.test_map (top-level), pandas.tests.indexes.datetimes.methods.test_normalize (top-level), pandas.tests.indexes.datetimes.methods.test_repeat (top-level), pandas.tests.indexes.datetimes.methods.test_resolution (top-level), pandas.tests.indexes.datetimes.methods.test_round (top-level), pandas.tests.indexes.datetimes.methods.test_shift (top-level), pandas.tests.indexes.datetimes.methods.test_snap (top-level), pandas.tests.indexes.datetimes.methods.test_to_period (top-level), pandas.tests.indexes.datetimes.test_timezones (top-level), pandas.tests.indexes.datetimes.methods.test_tz_convert (top-level), pandas.tests.indexes.datetimes.methods.test_tz_localize (top-level), pandas.tests.indexes.datetimes.test_arithmetic (top-level), pandas.tests.indexes.datetimes.test_constructors (top-level), pandas.tests.indexes.datetimes.test_date_range (top-level), pandas.tests.indexes.datetimes.test_datetime (top-level), pandas.tests.indexes.datetimes.test_formats (top-level), pandas.tests.indexes.datetimes.test_freq_attr (top-level), pandas.tests.indexes.datetimes.test_indexing (top-level), pandas.tests.indexes.datetimes.test_iter (top-level), pandas.tests.indexes.datetimes.test_join (top-level), pandas.tests.indexes.datetimes.test_ops (top-level), pandas.tests.indexes.datetimes.test_partial_slicing (top-level), pandas.tests.indexes.datetimes.test_pickle (top-level), pandas.tests.indexes.datetimes.test_scalar_compat (top-level), pandas.tests.indexes.datetimes.test_setops (top-level), pandas.tests.indexes.interval.test_astype (top-level), pandas.tests.indexes.interval.test_constructors (top-level), pandas.tests.indexes.interval.test_formats (top-level), pandas.tests.indexes.interval.test_indexing (top-level), pandas.tests.indexes.interval.test_interval (top-level), pandas.tests.indexes.interval.test_interval_range (top-level), pandas.tests.indexes.interval.test_interval_tree (top-level), pandas.tests.indexes.interval.test_join (top-level), pandas.tests.indexes.interval.test_pickle (top-level), pandas.tests.indexes.interval.test_setops (top-level), pandas.tests.indexes.multi.conftest (top-level), pandas.tests.indexes.multi.test_analytics (top-level), pandas.tests.indexes.multi.test_astype (top-level), pandas.tests.indexes.multi.test_compat (top-level), pandas.tests.indexes.multi.test_constructors (top-level), pandas.tests.indexes.multi.test_conversion (top-level), pandas.tests.indexes.multi.test_copy (top-level), pandas.tests.indexes.multi.test_drop (top-level), pandas.tests.indexes.multi.test_duplicates (top-level), pandas.tests.indexes.multi.test_equivalence (top-level), pandas.tests.indexes.multi.test_formats (top-level), pandas.tests.indexes.multi.test_get_set (top-level), pandas.tests.indexes.multi.test_indexing (top-level), pandas.tests.indexes.multi.test_integrity (top-level), pandas.tests.indexes.multi.test_isin (top-level), pandas.tests.indexes.multi.test_join (top-level), pandas.tests.indexes.multi.test_missing (top-level), pandas.tests.indexes.multi.test_monotonic (top-level), pandas.tests.indexes.multi.test_names (top-level), pandas.tests.indexes.multi.test_partial_indexing (top-level), pandas.tests.indexes.multi.test_pickle (top-level), pandas.tests.indexes.multi.test_reindex (top-level), pandas.tests.indexes.multi.test_reshape (top-level), pandas.tests.indexes.multi.test_setops (top-level), pandas.tests.indexes.multi.test_sorting (top-level), pandas.tests.indexes.multi.test_take (top-level), pandas.tests.indexes.numeric.test_astype (top-level), pandas.tests.indexes.numeric.test_indexing (top-level), pandas.tests.indexes.numeric.test_join (top-level), pandas.tests.indexes.numeric.test_numeric (top-level), pandas.tests.indexes.numeric.test_setops (top-level), pandas.tests.indexes.object.test_astype (top-level), pandas.tests.indexes.object.test_indexing (top-level), pandas.tests.indexes.period.methods.test_asfreq (top-level), pandas.tests.indexes.period.methods.test_astype (top-level), pandas.tests.indexes.period.methods.test_insert (top-level), pandas.tests.indexes.period.methods.test_is_full (top-level), pandas.tests.indexes.period.methods.test_repeat (top-level), pandas.tests.indexes.period.methods.test_shift (top-level), pandas.tests.indexes.period.methods.test_to_timestamp (top-level), pandas.tests.indexes.period.test_constructors (top-level), pandas.tests.indexes.period.test_formats (top-level), pandas.tests.indexes.period.test_freq_attr (top-level), pandas.tests.indexes.period.test_indexing (top-level), pandas.tests.indexes.period.test_join (top-level), pandas.tests.indexes.period.test_partial_slicing (top-level), pandas.tests.indexes.period.test_period (top-level), pandas.tests.indexes.period.test_period_range (top-level), pandas.tests.indexes.period.test_pickle (top-level), pandas.tests.indexes.period.test_resolution (top-level), pandas.tests.indexes.period.test_scalar_compat (top-level), pandas.tests.indexes.period.test_searchsorted (top-level), pandas.tests.indexes.period.test_setops (top-level), pandas.tests.indexes.period.test_tools (top-level), pandas.tests.indexes.ranges.test_constructors (top-level), pandas.tests.indexes.ranges.test_indexing (top-level), pandas.tests.indexes.ranges.test_range (top-level), pandas.tests.indexes.ranges.test_setops (top-level), pandas.tests.indexes.string.test_indexing (top-level), pandas.tests.indexes.test_any_index (top-level), pandas.tests.indexes.test_base (top-level), pandas.tests.indexes.test_common (top-level), pandas.tests.indexes.test_datetimelike (top-level), pandas.tests.indexes.test_engines (top-level), pandas.tests.indexes.test_frozen (top-level), pandas.tests.indexes.test_index_new (top-level), pandas.tests.indexes.test_indexing (top-level), pandas.tests.indexes.test_numpy_compat (top-level), pandas.tests.indexes.test_old_base (top-level), pandas.tests.indexes.test_setops (top-level), pandas.tests.indexes.timedeltas.methods.test_astype (top-level), pandas.tests.indexes.timedeltas.methods.test_insert (top-level), pandas.tests.indexes.timedeltas.methods.test_shift (top-level), pandas.tests.indexes.timedeltas.test_constructors (top-level), pandas.tests.indexes.timedeltas.test_formats (top-level), pandas.tests.indexes.timedeltas.test_freq_attr (top-level), pandas.tests.indexes.timedeltas.test_indexing (top-level), pandas.tests.indexes.timedeltas.test_scalar_compat (top-level), pandas.tests.indexes.timedeltas.test_searchsorted (top-level), pandas.tests.indexes.timedeltas.test_setops (top-level), pandas.tests.indexes.timedeltas.test_timedelta (top-level), pandas.tests.indexes.timedeltas.test_timedelta_range (top-level), pandas.tests.indexing.conftest (top-level), pandas.tests.indexing.interval.test_interval (top-level), pandas.tests.indexing.interval.test_interval_new (top-level), pandas.tests.indexing.multiindex.test_chaining_and_caching (top-level), pandas.tests.indexing.multiindex.test_getitem (top-level), pandas.tests.indexing.multiindex.test_iloc (top-level), pandas.tests.indexing.multiindex.test_indexing_slow (top-level), pandas.tests.indexing.multiindex.test_loc (top-level), pandas.tests.indexing.multiindex.test_multiindex (top-level), pandas.tests.indexing.multiindex.test_partial (top-level), pandas.tests.indexing.multiindex.test_setitem (top-level), pandas.tests.indexing.multiindex.test_slice (top-level), pandas.tests.indexing.multiindex.test_sorted (top-level), pandas.tests.indexing.test_at (top-level), pandas.tests.indexing.test_categorical (top-level), pandas.tests.indexing.test_chaining_and_caching (top-level), pandas.tests.indexing.test_check_indexer (top-level), pandas.tests.indexing.test_coercion (top-level), pandas.tests.indexing.test_datetime (top-level), pandas.tests.indexing.test_floats (top-level), pandas.tests.indexing.test_iloc (top-level), pandas.tests.indexing.test_indexers (top-level), pandas.tests.indexing.test_indexing (top-level), pandas.tests.indexing.test_loc (top-level), pandas.tests.indexing.test_na_indexing (top-level), pandas.tests.indexing.test_partial (top-level), pandas.tests.indexing.test_scalar (top-level), pandas.tests.interchange.test_impl (top-level), pandas.tests.interchange.test_spec_conformance (top-level), pandas.tests.interchange.test_utils (top-level), pandas.tests.internals.test_api (top-level), pandas.tests.internals.test_internals (top-level), pandas.tests.internals.test_managers (top-level), pandas.tests.io.conftest (top-level), pandas.tests.io.excel.test_odf (top-level), pandas.tests.io.excel.test_odswriter (top-level), pandas.tests.io.excel.test_openpyxl (top-level), pandas.tests.io.excel.test_readers (top-level), pandas.tests.io.excel.test_style (top-level), pandas.tests.io.excel.test_writers (top-level), pandas.tests.io.excel.test_xlrd (top-level), pandas.tests.io.excel.test_xlsxwriter (top-level), pandas.tests.io.formats.style.test_bar (top-level), pandas.tests.io.formats.style.test_exceptions (top-level), pandas.tests.io.formats.style.test_format (top-level), pandas.tests.io.formats.style.test_highlight (top-level), pandas.tests.io.formats.style.test_html (top-level), pandas.tests.io.formats.style.test_matplotlib (top-level), pandas.tests.io.formats.style.test_non_unique (top-level), pandas.tests.io.formats.style.test_style (top-level), pandas.tests.io.formats.style.test_to_latex (top-level), pandas.tests.io.formats.style.test_to_string (top-level), pandas.tests.io.formats.style.test_tooltip (top-level), pandas.tests.io.formats.test_console (top-level), pandas.tests.io.formats.test_css (top-level), pandas.tests.io.formats.test_eng_formatting (top-level), pandas.tests.io.formats.test_format (top-level), pandas.tests.io.formats.test_to_csv (top-level), pandas.tests.io.formats.test_to_excel (top-level), pandas.tests.io.formats.test_to_html (top-level), pandas.tests.io.formats.test_to_latex (top-level), pandas.tests.io.formats.test_to_markdown (top-level), pandas.tests.io.formats.test_to_string (top-level), pandas.tests.io.json.conftest (top-level), pandas.tests.io.json.test_compression (top-level), pandas.tests.io.json.test_json_table_schema (top-level), pandas.tests.io.json.test_json_table_schema_ext_dtype (top-level), pandas.tests.io.json.test_normalize (top-level), pandas.tests.io.json.test_pandas (top-level), pandas.tests.io.json.test_readlines (top-level), pandas.tests.io.json.test_ujson (top-level), pandas.tests.io.parser.common.test_chunksize (top-level), pandas.tests.io.parser.common.test_common_basic (top-level), pandas.tests.io.parser.common.test_data_list (top-level), pandas.tests.io.parser.common.test_decimal (top-level), pandas.tests.io.parser.common.test_file_buffer_url (top-level), pandas.tests.io.parser.common.test_float (top-level), pandas.tests.io.parser.common.test_index (top-level), pandas.tests.io.parser.common.test_inf (top-level), pandas.tests.io.parser.common.test_ints (top-level), pandas.tests.io.parser.common.test_iterator (top-level), pandas.tests.io.parser.common.test_read_errors (top-level), pandas.tests.io.parser.common.test_verbose (top-level), pandas.tests.io.parser.conftest (top-level), pandas.tests.io.parser.dtypes.test_categorical (top-level), pandas.tests.io.parser.dtypes.test_dtypes_basic (top-level), pandas.tests.io.parser.dtypes.test_empty (top-level), pandas.tests.io.parser.test_c_parser_only (top-level), pandas.tests.io.parser.test_comment (top-level), pandas.tests.io.parser.test_compression (top-level), pandas.tests.io.parser.test_concatenate_chunks (top-level), pandas.tests.io.parser.test_converters (top-level), pandas.tests.io.parser.test_dialect (top-level), pandas.tests.io.parser.test_encoding (top-level), pandas.tests.io.parser.test_header (top-level), pandas.tests.io.parser.test_index_col (top-level), pandas.tests.io.parser.test_mangle_dupes (top-level), pandas.tests.io.parser.test_multi_thread (top-level), pandas.tests.io.parser.test_na_values (top-level), pandas.tests.io.parser.test_network (top-level), pandas.tests.io.parser.test_parse_dates (top-level), pandas.tests.io.parser.test_python_parser_only (top-level), pandas.tests.io.parser.test_quoting (top-level), pandas.tests.io.parser.test_read_fwf (top-level), pandas.tests.io.parser.test_skiprows (top-level), pandas.tests.io.parser.test_textreader (top-level), pandas.tests.io.parser.test_unsupported (top-level), pandas.tests.io.parser.test_upcast (top-level), pandas.tests.io.parser.usecols.test_parse_dates (top-level), pandas.tests.io.parser.usecols.test_strings (top-level), pandas.tests.io.parser.usecols.test_usecols_basic (top-level), pandas.tests.io.pytables.common (top-level), pandas.tests.io.pytables.conftest (top-level), pandas.tests.io.pytables.test_append (top-level), pandas.tests.io.pytables.test_categorical (top-level), pandas.tests.io.pytables.test_compat (top-level), pandas.tests.io.pytables.test_complex (top-level), pandas.tests.io.pytables.test_errors (top-level), pandas.tests.io.pytables.test_file_handling (top-level), pandas.tests.io.pytables.test_keys (top-level), pandas.tests.io.pytables.test_put (top-level), pandas.tests.io.pytables.test_pytables_missing (top-level), pandas.tests.io.pytables.test_read (top-level), pandas.tests.io.pytables.test_retain_attributes (top-level), pandas.tests.io.pytables.test_round_trip (top-level), pandas.tests.io.pytables.test_select (top-level), pandas.tests.io.pytables.test_store (top-level), pandas.tests.io.pytables.test_subclass (top-level), pandas.tests.io.pytables.test_time_series (top-level), pandas.tests.io.pytables.test_timezones (top-level), pandas.tests.io.sas.test_byteswap (top-level), pandas.tests.io.sas.test_sas (top-level), pandas.tests.io.sas.test_sas7bdat (top-level), pandas.tests.io.sas.test_xport (top-level), pandas.tests.io.test_clipboard (top-level), pandas.tests.io.test_common (top-level), pandas.tests.io.test_compression (top-level), pandas.tests.io.test_feather (top-level), pandas.tests.io.test_fsspec (top-level), pandas.tests.io.test_gcs (top-level), pandas.tests.io.test_html (top-level), pandas.tests.io.test_http_headers (top-level), pandas.tests.io.test_orc (top-level), pandas.tests.io.test_parquet (top-level), pandas.tests.io.test_pickle (top-level), pandas.tests.io.test_s3 (top-level), pandas.tests.io.test_spss (top-level), pandas.tests.io.test_sql (top-level), pandas.tests.io.test_stata (top-level), pandas.tests.io.xml.conftest (top-level), pandas.tests.io.xml.test_to_xml (top-level), pandas.tests.io.xml.test_xml (top-level), pandas.tests.io.xml.test_xml_dtypes (top-level), pandas.tests.libs.test_hashtable (top-level), pandas.tests.libs.test_join (top-level), pandas.tests.libs.test_lib (top-level), pandas.tests.plotting.conftest (top-level), pandas.tests.plotting.frame.test_frame (top-level), pandas.tests.plotting.frame.test_frame_color (top-level), pandas.tests.plotting.frame.test_frame_groupby (top-level), pandas.tests.plotting.frame.test_frame_legend (top-level), pandas.tests.plotting.frame.test_frame_subplots (top-level), pandas.tests.plotting.frame.test_hist_box_by (top-level), pandas.tests.plotting.test_backend (top-level), pandas.tests.plotting.test_boxplot_method (top-level), pandas.tests.plotting.test_common (top-level), pandas.tests.plotting.test_converter (top-level), pandas.tests.plotting.test_datetimelike (top-level), pandas.tests.plotting.test_groupby (top-level), pandas.tests.plotting.test_hist_method (top-level), pandas.tests.plotting.test_misc (top-level), pandas.tests.plotting.test_series (top-level), pandas.tests.plotting.test_style (top-level), pandas.tests.reductions.test_reductions (top-level), pandas.tests.reductions.test_stat_reductions (top-level), pandas.tests.resample.conftest (top-level), pandas.tests.resample.test_base (top-level), pandas.tests.resample.test_datetime_index (top-level), pandas.tests.resample.test_period_index (top-level), pandas.tests.resample.test_resample_api (top-level), pandas.tests.resample.test_resampler_grouper (top-level), pandas.tests.resample.test_time_grouper (top-level), pandas.tests.resample.test_timedelta (top-level), pandas.tests.reshape.concat.conftest (top-level), pandas.tests.reshape.concat.test_append (top-level), pandas.tests.reshape.concat.test_append_common (top-level), pandas.tests.reshape.concat.test_concat (top-level), pandas.tests.reshape.concat.test_dataframe (top-level), pandas.tests.reshape.concat.test_datetimes (top-level), pandas.tests.reshape.concat.test_empty (top-level), pandas.tests.reshape.concat.test_index (top-level), pandas.tests.reshape.concat.test_invalid (top-level), pandas.tests.reshape.concat.test_series (top-level), pandas.tests.reshape.concat.test_sort (top-level), pandas.tests.reshape.merge.test_join (top-level), pandas.tests.reshape.merge.test_merge (top-level), pandas.tests.reshape.merge.test_merge_asof (top-level), pandas.tests.reshape.merge.test_merge_cross (top-level), pandas.tests.reshape.merge.test_merge_index_as_string (top-level), pandas.tests.reshape.merge.test_merge_ordered (top-level), pandas.tests.reshape.merge.test_multi (top-level), pandas.tests.reshape.test_crosstab (top-level), pandas.tests.reshape.test_cut (top-level), pandas.tests.reshape.test_from_dummies (top-level), pandas.tests.reshape.test_get_dummies (top-level), pandas.tests.reshape.test_melt (top-level), pandas.tests.reshape.test_pivot (top-level), pandas.tests.reshape.test_pivot_multilevel (top-level), pandas.tests.reshape.test_qcut (top-level), pandas.tests.reshape.test_union_categoricals (top-level), pandas.tests.reshape.test_util (top-level), pandas.tests.scalar.interval.test_arithmetic (top-level), pandas.tests.scalar.interval.test_constructors (top-level), pandas.tests.scalar.interval.test_contains (top-level), pandas.tests.scalar.interval.test_interval (top-level), pandas.tests.scalar.interval.test_overlaps (top-level), pandas.tests.scalar.period.test_arithmetic (top-level), pandas.tests.scalar.period.test_asfreq (top-level), pandas.tests.scalar.period.test_period (top-level), pandas.tests.scalar.test_na_scalar (top-level), pandas.tests.scalar.test_nat (top-level), pandas.tests.scalar.timedelta.methods.test_as_unit (top-level), pandas.tests.scalar.timedelta.methods.test_round (top-level), pandas.tests.scalar.timedelta.test_arithmetic (top-level), pandas.tests.scalar.timedelta.test_constructors (top-level), pandas.tests.scalar.timedelta.test_formats (top-level), pandas.tests.scalar.timedelta.test_timedelta (top-level), pandas.tests.scalar.timestamp.methods.test_as_unit (top-level), pandas.tests.scalar.timestamp.methods.test_normalize (top-level), pandas.tests.scalar.timestamp.methods.test_replace (top-level), pandas.tests.scalar.timestamp.methods.test_round (top-level), pandas.tests.scalar.timestamp.methods.test_tz_convert (top-level), pandas.tests.scalar.timestamp.methods.test_tz_localize (top-level), pandas.tests.scalar.timestamp.test_arithmetic (top-level), pandas.tests.scalar.timestamp.test_comparisons (top-level), pandas.tests.scalar.timestamp.test_constructors (top-level), pandas.tests.scalar.timestamp.test_formats (top-level), pandas.tests.scalar.timestamp.test_timestamp (top-level), pandas.tests.series.accessors.test_cat_accessor (top-level), pandas.tests.series.accessors.test_dt_accessor (top-level), pandas.tests.series.accessors.test_list_accessor (top-level), pandas.tests.series.accessors.test_str_accessor (top-level), pandas.tests.series.accessors.test_struct_accessor (top-level), pandas.tests.series.indexing.test_datetime (top-level), pandas.tests.series.indexing.test_delitem (top-level), pandas.tests.series.indexing.test_get (top-level), pandas.tests.series.indexing.test_getitem (top-level), pandas.tests.series.indexing.test_indexing (top-level), pandas.tests.series.indexing.test_mask (top-level), pandas.tests.series.indexing.test_setitem (top-level), pandas.tests.series.indexing.test_take (top-level), pandas.tests.series.indexing.test_where (top-level), pandas.tests.series.indexing.test_xs (top-level), pandas.tests.series.methods.test_add_prefix_suffix (top-level), pandas.tests.series.methods.test_align (top-level), pandas.tests.series.methods.test_argsort (top-level), pandas.tests.series.methods.test_asof (top-level), pandas.tests.series.methods.test_astype (top-level), pandas.tests.series.methods.test_between (top-level), pandas.tests.series.methods.test_case_when (top-level), pandas.tests.series.methods.test_clip (top-level), pandas.tests.series.methods.test_compare (top-level), pandas.tests.series.methods.test_convert_dtypes (top-level), pandas.tests.series.methods.test_copy (top-level), pandas.tests.series.methods.test_cov_corr (top-level), pandas.tests.series.methods.test_describe (top-level), pandas.tests.series.methods.test_diff (top-level), pandas.tests.series.methods.test_drop (top-level), pandas.tests.series.methods.test_drop_duplicates (top-level), pandas.tests.series.methods.test_dropna (top-level), pandas.tests.series.methods.test_duplicated (top-level), pandas.tests.series.methods.test_equals (top-level), pandas.tests.series.methods.test_explode (top-level), pandas.tests.series.methods.test_fillna (top-level), pandas.tests.series.methods.test_info (top-level), pandas.tests.series.methods.test_interpolate (top-level), pandas.tests.series.methods.test_is_unique (top-level), pandas.tests.series.methods.test_isin (top-level), pandas.tests.series.methods.test_item (top-level), pandas.tests.series.methods.test_map (top-level), pandas.tests.series.methods.test_matmul (top-level), pandas.tests.series.methods.test_nlargest (top-level), pandas.tests.series.methods.test_pct_change (top-level), pandas.tests.series.methods.test_quantile (top-level), pandas.tests.series.methods.test_rank (top-level), pandas.tests.series.methods.test_reindex (top-level), pandas.tests.series.methods.test_rename (top-level), pandas.tests.series.methods.test_rename_axis (top-level), pandas.tests.series.methods.test_repeat (top-level), pandas.tests.series.methods.test_replace (top-level), pandas.tests.series.methods.test_reset_index (top-level), pandas.tests.series.methods.test_round (top-level), pandas.tests.series.methods.test_searchsorted (top-level), pandas.tests.series.methods.test_size (top-level), pandas.tests.series.methods.test_sort_index (top-level), pandas.tests.series.methods.test_sort_values (top-level), pandas.tests.series.methods.test_to_csv (top-level), pandas.tests.series.methods.test_to_dict (top-level), pandas.tests.series.methods.test_to_frame (top-level), pandas.tests.series.methods.test_to_numpy (top-level), pandas.tests.series.methods.test_tolist (top-level), pandas.tests.series.methods.test_truncate (top-level), pandas.tests.series.methods.test_tz_localize (top-level), pandas.tests.series.methods.test_unstack (top-level), pandas.tests.series.methods.test_update (top-level), pandas.tests.series.methods.test_value_counts (top-level), pandas.tests.series.methods.test_values (top-level), pandas.tests.series.methods.test_view (top-level), pandas.tests.series.test_api (top-level), pandas.tests.series.test_arithmetic (top-level), pandas.tests.series.test_constructors (top-level), pandas.tests.series.test_cumulative (top-level), pandas.tests.series.test_formats (top-level), pandas.tests.series.test_logical_ops (top-level), pandas.tests.series.test_missing (top-level), pandas.tests.series.test_npfuncs (top-level), pandas.tests.series.test_reductions (top-level), pandas.tests.series.test_subclass (top-level), pandas.tests.series.test_ufunc (top-level), pandas.tests.series.test_unary (top-level), pandas.tests.series.test_validate (top-level), pandas.tests.strings.conftest (top-level), pandas.tests.strings.test_api (top-level), pandas.tests.strings.test_case_justify (top-level), pandas.tests.strings.test_cat (top-level), pandas.tests.strings.test_extract (top-level), pandas.tests.strings.test_find_replace (top-level), pandas.tests.strings.test_split_partition (top-level), pandas.tests.strings.test_string_array (top-level), pandas.tests.strings.test_strings (top-level), pandas.tests.test_aggregation (top-level), pandas.tests.test_algos (top-level), pandas.tests.test_common (top-level), pandas.tests.test_downstream (top-level), pandas.tests.test_errors (top-level), pandas.tests.test_expressions (top-level), pandas.tests.test_flags (top-level), pandas.tests.test_multilevel (top-level), pandas.tests.test_nanops (top-level), pandas.tests.test_optional_dependency (top-level), pandas.tests.test_register_accessor (top-level), pandas.tests.test_sorting (top-level), pandas.tests.test_take (top-level), pandas.tests.tools.test_to_datetime (top-level), pandas.tests.tools.test_to_numeric (top-level), pandas.tests.tools.test_to_time (top-level), pandas.tests.tools.test_to_timedelta (top-level), pandas.tests.tseries.frequencies.test_freq_code (top-level), pandas.tests.tseries.frequencies.test_frequencies (top-level), pandas.tests.tseries.frequencies.test_inference (top-level), pandas.tests.tseries.holiday.test_calendar (top-level), pandas.tests.tseries.holiday.test_holiday (top-level), pandas.tests.tseries.holiday.test_observance (top-level), pandas.tests.tseries.offsets.test_business_day (top-level), pandas.tests.tseries.offsets.test_business_hour (top-level), pandas.tests.tseries.offsets.test_business_month (top-level), pandas.tests.tseries.offsets.test_business_quarter (top-level), pandas.tests.tseries.offsets.test_business_year (top-level), pandas.tests.tseries.offsets.test_common (top-level), pandas.tests.tseries.offsets.test_custom_business_day (top-level), pandas.tests.tseries.offsets.test_custom_business_hour (top-level), pandas.tests.tseries.offsets.test_custom_business_month (top-level), pandas.tests.tseries.offsets.test_dst (top-level), pandas.tests.tseries.offsets.test_easter (top-level), pandas.tests.tseries.offsets.test_fiscal (top-level), pandas.tests.tseries.offsets.test_index (top-level), pandas.tests.tseries.offsets.test_month (top-level), pandas.tests.tseries.offsets.test_offsets (top-level), pandas.tests.tseries.offsets.test_offsets_properties (top-level), pandas.tests.tseries.offsets.test_quarter (top-level), pandas.tests.tseries.offsets.test_ticks (top-level), pandas.tests.tseries.offsets.test_week (top-level), pandas.tests.tseries.offsets.test_year (top-level), pandas.tests.tslibs.test_array_to_datetime (top-level), pandas.tests.tslibs.test_ccalendar (top-level), pandas.tests.tslibs.test_conversion (top-level), pandas.tests.tslibs.test_fields (top-level), pandas.tests.tslibs.test_libfrequencies (top-level), pandas.tests.tslibs.test_liboffsets (top-level), pandas.tests.tslibs.test_np_datetime (top-level), pandas.tests.tslibs.test_parse_iso8601 (top-level), pandas.tests.tslibs.test_parsing (top-level), pandas.tests.tslibs.test_period (top-level), pandas.tests.tslibs.test_resolution (top-level), pandas.tests.tslibs.test_strptime (top-level), pandas.tests.tslibs.test_timedeltas (top-level), pandas.tests.tslibs.test_timezones (top-level), pandas.tests.tslibs.test_to_offset (top-level), pandas.tests.tslibs.test_tzconversion (top-level), pandas.tests.util.conftest (top-level), pandas.tests.util.test_assert_almost_equal (top-level), pandas.tests.util.test_assert_attr_equal (top-level), pandas.tests.util.test_assert_categorical_equal (top-level), pandas.tests.util.test_assert_extension_array_equal (top-level), pandas.tests.util.test_assert_frame_equal (top-level), pandas.tests.util.test_assert_index_equal (top-level), pandas.tests.util.test_assert_interval_array_equal (top-level), pandas.tests.util.test_assert_numpy_array_equal (top-level), pandas.tests.util.test_assert_produces_warning (top-level), pandas.tests.util.test_assert_series_equal (top-level), pandas.tests.util.test_deprecate (top-level), pandas.tests.util.test_deprecate_kwarg (top-level), pandas.tests.util.test_hashing (top-level), pandas.tests.util.test_numba (top-level), pandas.tests.util.test_rewrite_warning (top-level), pandas.tests.util.test_util (top-level), pandas.tests.util.test_validate_args (top-level), pandas.tests.util.test_validate_args_and_kwargs (top-level), pandas.tests.util.test_validate_inclusive (top-level), pandas.tests.util.test_validate_kwargs (top-level), pandas.tests.window.conftest (top-level), pandas.tests.window.moments.conftest (top-level), pandas.tests.window.moments.test_moments_consistency_ewm (top-level), pandas.tests.window.moments.test_moments_consistency_expanding (top-level), pandas.tests.window.moments.test_moments_consistency_rolling (top-level), pandas.tests.window.test_api (top-level), pandas.tests.window.test_apply (top-level), pandas.tests.window.test_base_indexer (top-level), pandas.tests.window.test_cython_aggregations (top-level), pandas.tests.window.test_dtypes (top-level), pandas.tests.window.test_ewm (top-level), pandas.tests.window.test_expanding (top-level), pandas.tests.window.test_groupby (top-level), pandas.tests.window.test_numba (top-level), pandas.tests.window.test_online (top-level), pandas.tests.window.test_pairwise (top-level), pandas.tests.window.test_rolling (top-level), pandas.tests.window.test_rolling_functions (top-level), pandas.tests.window.test_rolling_quantile (top-level), pandas.tests.window.test_rolling_skew_kurt (top-level), pandas.tests.window.test_timeseries_window (top-level), pandas.tests.window.test_win_type (top-level)
missing module named 'matplotlib.pyplot' - imported by pandas.io.formats.style (optional), tqdm.gui (delayed), pandas.tests.plotting.common (delayed), pandas.plotting._matplotlib.tools (delayed), pandas.plotting._matplotlib.style (delayed), pandas.plotting._matplotlib.misc (delayed), pandas.plotting._matplotlib.core (delayed), pandas.plotting._matplotlib.boxplot (delayed), pandas.plotting._matplotlib.hist (delayed), pandas.plotting._matplotlib (delayed), pandas.tests.plotting.frame.test_frame (delayed), pandas.tests.plotting.test_boxplot_method (delayed), pandas.tests.plotting.test_datetimelike (delayed), pandas.tests.plotting.test_hist_method (delayed), pandas.tests.plotting.test_style (delayed), pandas.util._doctools (delayed)
excluded module named matplotlib - imported by pandas.io.formats.style (optional), tqdm.gui (delayed), pandas.tests.io.formats.style.test_matplotlib (top-level), pandas.tests.plotting.common (delayed), pandas.plotting._matplotlib.boxplot (top-level), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (top-level), pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.timeseries (delayed), pandas.tests.plotting.frame.test_frame (delayed), pandas.tests.plotting.frame.test_frame_color (delayed), pandas.tests.plotting.test_misc (delayed), pandas.tests.plotting.test_series (delayed), pandas.tests.plotting.test_style (delayed), pandas.util._doctools (delayed)
missing module named pandas.util.hash_pandas_object - imported by pandas.util (top-level), pandas.tests.util.test_hashing (top-level)
missing module named pandas.util.hash_array - imported by pandas.util (top-level), pandas.tests.util.test_hashing (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named StringIO - imported by six (conditional)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), pandas.core._numba.extensions (top-level), pandas.tests.groupby.aggregate.test_numba (delayed, conditional), pandas.tests.groupby.transform.test_numba (delayed, conditional), pandas.tests.window.test_numba (delayed, conditional)
missing module named pyarrow - imported by pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.masked (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.interchange.utils (delayed, conditional), pandas.core.strings.accessor (delayed, conditional), pandas.io._util (conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.methods.describe (delayed, conditional), pandas.io.sql (delayed, conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.interchange.buffer (conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.dtypes.cast (delayed, conditional), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays.arrow.accessors (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas.core.reshape.encoding (delayed, conditional), pandas._testing (conditional), pandas.conftest (optional), pandas.tests.arrays.string_.test_string (delayed, conditional), pandas.tests.indexes.test_base (delayed), pandas.tests.io.excel.test_readers (delayed, conditional), pandas.tests.io.parser.conftest (delayed, conditional), pandas.tests.io.test_feather (delayed), pandas.tests.io.test_html (delayed, conditional), pandas.tests.io.test_orc (top-level), pandas.tests.io.test_parquet (delayed, optional), pandas.tests.reshape.test_get_dummies (optional), pandas.tests.util.test_shares_memory (delayed)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named hypothesis - imported by pandas._testing._hypothesis (top-level), pandas.conftest (top-level), pandas.tests.frame.indexing.test_where (top-level), pandas.tests.indexes.ranges.test_setops (top-level), pandas.tests.io.sas.test_byteswap (top-level), pandas.tests.scalar.timedelta.methods.test_round (top-level), pandas.tests.scalar.timedelta.test_timedelta (top-level), pandas.tests.scalar.timestamp.methods.test_round (top-level), pandas.tests.scalar.timestamp.test_timestamp (top-level), pandas.tests.tseries.offsets.test_offsets_properties (top-level), pandas.tests.tseries.offsets.test_ticks (top-level), pandas.tests.tslibs.test_ccalendar (top-level), pandas.tests.tslibs.test_parsing (top-level)
missing module named xarray - imported by pandas.tests.generic.test_to_xarray (delayed), pandas.tests.test_downstream (delayed, conditional)
missing module named dask - imported by pandas.tests.test_downstream (delayed, conditional)
missing module named sklearn - imported by pandas.tests.test_downstream (delayed)
missing module named 'IPython.core' - imported by dotenv.ipython (top-level), pandas.io.formats.printing (delayed, conditional), pandas.conftest (delayed), pandas.tests.arrays.categorical.test_warnings (delayed), pandas.tests.frame.test_api (delayed), pandas.tests.indexes.test_base (delayed), pandas.tests.resample.test_resampler_grouper (delayed)
missing module named 'matplotlib.colors' - imported by pandas.plotting._misc (conditional), pandas.io.formats.style (conditional), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.core (delayed), pandas.tests.plotting.test_style (delayed)
missing module named 'scipy.stats' - imported by pandas.core.nanops (delayed, conditional), pandas.tests.groupby.test_reductions (delayed), pandas.plotting._matplotlib.misc (delayed, conditional), pandas.plotting._matplotlib.hist (delayed)
missing module named 'matplotlib.figure' - imported by pandas.plotting._misc (conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.core (conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named 'matplotlib.axes' - imported by pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas._testing.asserters (delayed), pandas.tests.plotting.common (delayed, conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.timeseries (conditional), pandas.plotting._matplotlib.core (delayed, conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named 'matplotlib.lines' - imported by pandas.tests.plotting.common (delayed), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.boxplot (conditional), pandas.tests.plotting.frame.test_frame_legend (delayed)
missing module named 'matplotlib.ticker' - imported by pandas.tests.plotting.common (delayed), pandas.plotting._matplotlib.converter (top-level), pandas.plotting._matplotlib.core (delayed)
missing module named 'matplotlib.axis' - imported by pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.converter (conditional), pandas.plotting._matplotlib.core (conditional)
missing module named 'matplotlib.artist' - imported by pandas._testing.asserters (delayed), pandas.plotting._matplotlib.boxplot (top-level), pandas.plotting._matplotlib.core (conditional)
missing module named 'matplotlib.units' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.transforms' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.dates' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.table' - imported by pandas.plotting._misc (conditional), pandas.plotting._matplotlib.tools (top-level)
missing module named 'matplotlib.text' - imported by pandas.tests.plotting.test_misc (delayed)
missing module named 'matplotlib.patches' - imported by pandas.tests.plotting.frame.test_frame (delayed), pandas.tests.plotting.test_hist_method (delayed)
missing module named pylab - imported by pandas.tests.plotting.test_hist_method (delayed)
missing module named 'matplotlib.collections' - imported by pandas.tests.plotting.common (delayed), pandas.tests.plotting.frame.test_frame_color (delayed), pandas.tests.plotting.frame.test_frame_legend (delayed)
missing module named cycler - imported by pandas.tests.plotting.frame.test_frame_color (delayed)
missing module named 'mpl_toolkits.axes_grid1' - imported by pandas.tests.plotting.frame.test_frame (delayed)
missing module named mpl_toolkits - imported by pandas.tests.plotting.frame.test_frame (delayed)
missing module named 'mysql.connector' - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed, conditional, optional)
missing module named mysql - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed)
missing module named asyncmy - imported by sqlalchemy.dialects.mysql.asyncmy (delayed)
missing module named pymysql - imported by sqlalchemy.dialects.mysql.aiomysql (delayed)
missing module named psycopg2 - imported by sqlalchemy.dialects.postgresql.psycopg2 (delayed), sqlalchemy (top-level)
missing module named 'psycopg.pq' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed)
missing module named 'psycopg.types' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named 'psycopg.adapt' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named psycopg - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named asyncpg - imported by sqlalchemy.dialects.postgresql.asyncpg (delayed)
missing module named adbc_driver_sqlite - imported by pandas.tests.io.test_sql (delayed)
missing module named adbc_driver_manager - imported by pandas.tests.io.test_sql (delayed)
missing module named adbc_driver_postgresql - imported by pandas.tests.io.test_sql (delayed)
missing module named 'botocore.response' - imported by pandas.tests.io.test_s3 (delayed)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed), pandas.tests.io.test_parquet (delayed)
missing module named 'pyarrow.dataset' - imported by pandas.tests.io.test_parquet (delayed)
missing module named fastparquet - imported by pandas.tests.io.test_parquet (delayed, optional)
missing module named fsspec - imported by pandas.io.orc (conditional), pandas.tests.io.test_fsspec (delayed), pandas.tests.io.test_gcs (delayed), pandas.tests.io.test_http_headers (delayed)
missing module named 'fsspec.registry' - imported by pandas.tests.io.test_fsspec (delayed)
missing module named 'fsspec.implementations' - imported by pandas.tests.io.test_fsspec (delayed)
missing module named 'py.path' - imported by pandas.tests.io.pytables.test_read (delayed), pandas.tests.io.sas.test_sas7bdat (delayed), pandas.tests.io.test_common (optional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional), pandas.tests.io.parser.test_network (delayed)
missing module named 'xlrd.biffh' - imported by pandas.tests.io.excel.test_xlrd (delayed)
missing module named py - imported by pandas.tests.io.excel.test_readers (delayed)
missing module named s3fs - imported by pandas.tests.io.excel.test_readers (delayed)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional), pandas.tests.io.excel.test_readers (delayed, conditional)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed, conditional), pandas.io.excel._base (delayed, conditional), pandas.tests.io.excel.test_readers (delayed, conditional)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed), pandas.tests.io.excel.test_odswriter (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed), pandas.tests.io.excel.test_odswriter (delayed)
missing module named boto3 - imported by pandas.tests.io.conftest (delayed)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.arrays.arrow.accessors (conditional), pandas.tests.arrays.string_.test_string (delayed), pandas.tests.interchange.test_impl (delayed)
missing module named 'pyarrow.interchange' - imported by pandas.tests.interchange.test_impl (delayed)
missing module named 'scipy.sparse' - imported by pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (delayed, conditional), pandas.core.arrays.sparse.accessor (delayed), pandas.tests.dtypes.test_common (delayed, conditional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed), pandas.tests.computation.test_eval (delayed, conditional, optional)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'pyarrow.fs' - imported by pandas.io.orc (conditional)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named bs4 - imported by pandas.io.html (delayed)
missing module named google - imported by pandas.io.gbq (conditional)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional)
missing module named IPython - imported by dotenv.ipython (top-level), pandas.io.formats.printing (delayed)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named scipy - imported by pandas.core.dtypes.common (delayed, conditional, optional), pandas.core.missing (delayed)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named 'traitlets.config' - imported by pandas.conftest (delayed)
missing module named 'hypothesis.extra' - imported by pandas._testing._hypothesis (top-level)
missing module named babel - imported by wtforms.fields.numeric (delayed, optional), flask_wtf.i18n (top-level)
missing module named flask_babel - imported by flask_wtf.i18n (top-level)
missing module named '_typeshed.wsgi' - imported by werkzeug.exceptions (conditional), werkzeug.http (conditional), werkzeug.wsgi (conditional), werkzeug.utils (conditional), werkzeug.wrappers.response (conditional), werkzeug.test (conditional), werkzeug.datastructures.headers (conditional), werkzeug.formparser (conditional), werkzeug.wrappers.request (conditional), werkzeug.serving (conditional), werkzeug.debug (conditional), werkzeug.middleware.shared_data (conditional), werkzeug.local (conditional), werkzeug.routing.exceptions (conditional), werkzeug.routing.map (conditional), flask.typing (conditional), flask.ctx (conditional), flask.testing (conditional), flask.cli (conditional), flask.app (conditional)
missing module named 'watchdog.observers' - imported by werkzeug._reloader (delayed)
missing module named 'watchdog.events' - imported by werkzeug._reloader (delayed)
missing module named watchdog - imported by werkzeug._reloader (delayed)
missing module named 'cryptography.hazmat' - imported by werkzeug.serving (delayed, optional)
missing module named 'cryptography.x509' - imported by werkzeug.serving (delayed, conditional, optional)
missing module named cryptography - imported by werkzeug.serving (delayed, conditional, optional), flask.cli (delayed, conditional, optional)
missing module named asgiref - imported by flask.app (delayed, optional)
missing module named pdfkit - imported by E:\app\TRINING\exe_build\app_exe.py (delayed, optional)
missing module named reports_generator - imported by E:\app\TRINING\exe_build\app_exe.py (top-level)
missing module named person_data_routes - imported by E:\app\TRINING\exe_build\app_exe.py (top-level)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional)
missing module named setuptools_scm - imported by tqdm.version (optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named oracledb - imported by sqlalchemy.dialects.oracle.oracledb (delayed, conditional)
missing module named cx_Oracle - imported by sqlalchemy.dialects.oracle.cx_oracle (delayed)
missing module named backup_utils - imported by E:\app\TRINING\exe_build\app_exe.py (top-level)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named charset_normalizer - imported by numpy.f2py.crackfortran (optional)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level)
missing module named yaml - imported by numpy.__config__ (delayed)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
