@echo off
chcp 65001 > nul
title Training System - Docker

echo.
echo ========================================
echo    Training System - Docker
echo ========================================
echo.

echo 🔍 التحقق من وجود Docker...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker غير مثبت على النظام
    echo يرجى تثبيت Docker Desktop من: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo 🔍 التحقق من تشغيل Docker Engine...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Engine غير مشغل
    echo يرجى تشغيل Docker Desktop أولاً
    echo محاولة تشغيل Docker Desktop...
    start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    echo انتظار تشغيل Docker Desktop...
    timeout /t 30 /nobreak > nul

    docker info >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ فشل في تشغيل Docker Desktop
        echo يرجى تشغيل Docker Desktop يدوياً ثم إعادة تشغيل هذا الملف
        pause
        exit /b 1
    )
)

echo ✅ Docker متاح
echo.

echo 🐳 بناء وتشغيل النظام...
docker-compose up -d --build

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ تم تشغيل النظام بنجاح!
    echo ========================================
    echo.
    echo 🌐 الوصول إلى النظام:
    echo    http://localhost:5000
    echo.
    echo 🔑 بيانات تسجيل الدخول:
    echo    الإيميل: <EMAIL>
    echo    كلمة المرور: admin123
    echo.
    echo 📋 أوامر مفيدة:
    echo    عرض السجلات: docker-compose logs -f
    echo    إيقاف النظام: docker-compose down
    echo    إعادة التشغيل: docker-compose restart
    echo.
    echo ⏰ سيتم فتح المتصفح خلال 5 ثوان...
    timeout /t 5 /nobreak > nul
    start http://localhost:5000
) else (
    echo.
    echo ❌ فشل في تشغيل النظام
    echo يرجى التحقق من السجلات: docker-compose logs
)

echo.
pause
