#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, User
from werkzeug.security import generate_password_hash

def create_admin_user():
    """إنشاء مستخدم مدير جديد"""
    with app.app_context():
        try:
            # حذف المدير الحالي إذا كان موجوداً
            existing_admin = User.query.filter_by(role='admin').first()
            if existing_admin:
                print(f"حذف المدير الحالي: {existing_admin.email}")
                db.session.delete(existing_admin)
                db.session.commit()
            
            # إنشاء مدير جديد
            admin = User(
                username='admin',
                email='<EMAIL>',
                password=generate_password_hash('admin123'),
                role='admin'
            )
            
            db.session.add(admin)
            db.session.commit()
            
            print("✅ تم إنشاء المدير بنجاح!")
            print(f"البريد الإلكتروني: {admin.email}")
            print(f"كلمة المرور: admin123")
            print(f"الدور: {admin.role}")
            
            # التحقق من إنشاء المدير
            check_admin = User.query.filter_by(email='<EMAIL>').first()
            if check_admin:
                print("✅ تم التحقق من وجود المدير في قاعدة البيانات")
                
                # اختبار كلمة المرور
                from werkzeug.security import check_password_hash
                if check_password_hash(check_admin.password, 'admin123'):
                    print("✅ كلمة المرور صحيحة")
                else:
                    print("❌ كلمة المرور غير صحيحة")
            else:
                print("❌ فشل في إنشاء المدير")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء المدير: {str(e)}")
            db.session.rollback()

if __name__ == '__main__':
    create_admin_user()
