#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def create_correct_excel():
    """إنشاء ملف Excel بالترتيب الصحيح للأعمدة"""
    
    print("=== إنشاء ملف Excel بالترتيب الصحيح ===")
    
    # البيانات بالترتيب الصحيح حسب ما هو مطلوب
    correct_data = {
        'الاسم الشخصي': [
            'علي صالح محمد الحميري', 
            'فاطمة أحمد علي المقطري', 
            'محمد حسن يحيى الزبيدي',
            'أحمد محمد علي السنباني',
            'زينب حسين أحمد الشامي'
        ],
        'الاسم المستعار': [
            'أبو محمد', 
            'أم أحمد', 
            'أبو علي',
            'أبو سالم',
            'أم زياد'
        ],
        'العمر': [30, 25, 35, 28, 32],
        'المحافظة': [
            'صنعاء', 
            'تعز', 
            'الحديدة',
            'إب',
            'ذمار'
        ],
        'المديرية': [
            'الثورة', 
            'صالة', 
            'الحوك',
            'إب',
            'ذمار'
        ],
        'العزلة': [
            'شعوب', 
            'الجند', 
            'الزهرة',
            'النادرة',
            'عنس'
        ],
        'الحي/القرية': [
            'حي السبعين', 
            'حي الجمهورية', 
            'حي الثورة',
            'حي الصافية',
            'حي الوحدة'
        ],
        'المؤهل العلمي': [
            'بكالوريوس', 
            'ثانوية', 
            'دبلوم',
            'ماجستير',
            'بكالوريوس'
        ],
        'الحالة الاجتماعية': [
            'متزوج', 
            'عزباء', 
            'متزوج',
            'متزوج',
            'متزوجة'
        ],
        'العمل': [
            'مهندس', 
            'معلمة', 
            'طبيب',
            'محاسب',
            'ممرضة'
        ],
        'الإدارة': [
            'الهندسة', 
            'التعليم', 
            'الصحة',
            'المالية',
            'الصحة'
        ],
        'مكان العمل': [
            'وزارة الأشغال', 
            'مدرسة الأمل', 
            'مستشفى الثورة',
            'وزارة المالية',
            'مستشفى الكويت'
        ],
        'الرقم الوطني': [
            '12345678', 
            '87654321', 
            '11223344',
            '55667788',
            '99887766'
        ],
        'الرقم العسكري': [
            'M123456', 
            'M654321', 
            'M112233',
            'M556677',
            'M998877'
        ],
        'رقم التلفون': [
            '777123456', 
            '733987654', 
            '770112233',
            '715556677',
            '712998877'
        ]
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(correct_data)
    
    print(f"عدد الصفوف: {len(df)}")
    print(f"عدد الأعمدة: {len(df.columns)}")
    print("\n=== ترتيب الأعمدة الصحيح ===")
    for i, col in enumerate(df.columns):
        print(f"{i+1:2d}. '{col}'")
    
    # حفظ في ملف Excel
    excel_file = 'test_import_correct.xlsx'
    df.to_excel(excel_file, index=False)
    print(f"\n✅ تم إنشاء ملف Excel: {excel_file}")
    
    print("\n=== عينة من البيانات ===")
    print(df.head())
    
    print(f"\n📁 استخدم الملف: {excel_file} للاستيراد في التطبيق")
    print("🔍 تأكد من أن ترتيب الأعمدة في ملفك مطابق للترتيب أعلاه")

if __name__ == "__main__":
    create_correct_excel()
