{% extends "layout.html" %}

{% block styles %}
<style>
    .form-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }

    .form-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: bold;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4a6bff;
        box-shadow: 0 0 0 0.25rem rgba(74, 107, 255, 0.25);
    }

    .form-text {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .btn-submit {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
    }

    .btn-cancel {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-cancel:hover {
        transform: translateY(-2px);
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .sidebar {
        background-color: #343a40;
        color: white;
        min-height: calc(100vh - 56px);
        padding-top: 20px;
    }

    .sidebar-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 10px 15px;
        display: block;
        text-decoration: none;
        transition: all 0.3s;
        border-radius: 5px;
        margin: 5px 10px;
    }

    .sidebar-link:hover, .sidebar-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .sidebar-link i {
        margin-left: 10px;
        width: 20px;
        text-align: center;
    }

    .sidebar-dropdown-menu {
        display: none;
        padding-right: 20px;
    }

    .sidebar-dropdown-menu.show {
        display: block;
    }

    .dropdown-toggle::after {
        display: inline-block;
        margin-right: 5px;
        vertical-align: middle;
        content: "";
        border-top: 0.3em solid;
        border-left: 0.3em solid transparent;
        border-right: 0.3em solid transparent;
    }

    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
    }

    .form-section-title {
        font-weight: bold;
        margin-bottom: 15px;
        color: #4a6bff;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 10px;
    }

    .form-check-input {
        margin-left: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_list') }}" class="sidebar-link active">
                <i class="fas fa-id-card"></i> البيانات الشخصية
            </a>
            <div class="sidebar-dropdown">
                <a href="#" class="sidebar-link dropdown-toggle">
                    <i class="fas fa-table"></i> الجداول الترميزية
                </a>
                <div class="sidebar-dropdown-menu">
                    <a href="{{ url_for('governorates') }}" class="sidebar-link">
                        <i class="fas fa-map-marker-alt"></i> المحافظات
                    </a>
                    <a href="{{ url_for('directorates') }}" class="sidebar-link">
                        <i class="fas fa-map"></i> المديريات
                    </a>
                    <!-- يمكن إضافة المزيد من الروابط للجداول الترميزية الأخرى هنا -->
                </div>
            </div>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <h2 class="mb-4">إضافة بيانات شخصية جديدة</h2>

        <div class="form-card">
            <div class="form-header">
                <h4><i class="fas fa-plus-circle me-2"></i> إضافة بيانات شخصية</h4>
                <p class="text-muted">أدخل البيانات الشخصية للفرد</p>
            </div>

            <form method="POST">
                {{ form.hidden_tag() }}

                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-user me-2"></i> البيانات الأساسية
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.full_name.label(class="form-label") }}
                                {% if form.full_name.errors %}
                                    {{ form.full_name(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.full_name.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.full_name(class="form-control", placeholder="أدخل الاسم الكامل") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.nickname.label(class="form-label") }}
                                {% if form.nickname.errors %}
                                    {{ form.nickname(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.nickname.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.nickname(class="form-control", placeholder="أدخل الاسم المستعار (اختياري)") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.triple_number.label(class="form-label") }}
                                {% if form.triple_number.errors %}
                                    {{ form.triple_number(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.triple_number.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.triple_number(class="form-control", placeholder="أدخل الرقم الثلاثي") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.national_number.label(class="form-label") }}
                                {% if form.national_number.errors %}
                                    {{ form.national_number(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.national_number.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.national_number(class="form-control", placeholder="أدخل الرقم الوطني") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.birth_date.label(class="form-label") }}
                                {% if form.birth_date.errors %}
                                    {{ form.birth_date(class="form-control is-invalid", type="date") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.birth_date.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.birth_date(class="form-control", type="date") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.age.label(class="form-label") }}
                                {% if form.age.errors %}
                                    {{ form.age(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.age.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.age(class="form-control", placeholder="أدخل العمر") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.issue_date.label(class="form-label") }}
                                {% if form.issue_date.errors %}
                                    {{ form.issue_date(class="form-control is-invalid", type="date") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.issue_date.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.issue_date(class="form-control", type="date") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.issuing_authority_id.label(class="form-label") }}
                                {% if form.issuing_authority_id.errors %}
                                    {{ form.issuing_authority_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.issuing_authority_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.issuing_authority_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.children_count.label(class="form-label") }}
                                {% if form.children_count.errors %}
                                    {{ form.children_count(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.children_count.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.children_count(class="form-control", placeholder="أدخل عدد الأولاد") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.marital_status_id.label(class="form-label") }}
                                {% if form.marital_status_id.errors %}
                                    {{ form.marital_status_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.marital_status_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.marital_status_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-home me-2"></i> بيانات السكن
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.residence_governorate_id.label(class="form-label") }}
                                {% if form.residence_governorate_id.errors %}
                                    {{ form.residence_governorate_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.residence_governorate_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.residence_governorate_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.residence_directorate_id.label(class="form-label") }}
                                {% if form.residence_directorate_id.errors %}
                                    {{ form.residence_directorate_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.residence_directorate_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.residence_directorate_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.residence_village_id.label(class="form-label") }}
                                {% if form.residence_village_id.errors %}
                                    {{ form.residence_village_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.residence_village_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.residence_village_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.residence_house.label(class="form-label") }}
                                {% if form.residence_house.errors %}
                                    {{ form.residence_house(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.residence_house.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.residence_house(class="form-control", placeholder="أدخل تفاصيل المنزل") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.residence_type.label(class="form-label") }}
                                {% if form.residence_type.errors %}
                                    {{ form.residence_type(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.residence_type.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.residence_type(class="form-control", placeholder="أدخل نوع السكن") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.phone_yemen_mobile.label(class="form-label") }}
                                {% if form.phone_yemen_mobile.errors %}
                                    {{ form.phone_yemen_mobile(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.phone_yemen_mobile.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.phone_yemen_mobile(class="form-control", placeholder="أدخل رقم يمن موبايل") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.phone_you.label(class="form-label") }}
                                {% if form.phone_you.errors %}
                                    {{ form.phone_you(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.phone_you.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.phone_you(class="form-control", placeholder="أدخل رقم YOU") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.phone_sayyon.label(class="form-label") }}
                                {% if form.phone_sayyon.errors %}
                                    {{ form.phone_sayyon(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.phone_sayyon.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.phone_sayyon(class="form-control", placeholder="أدخل رقم سيئون") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.phone_landline.label(class="form-label") }}
                                {% if form.phone_landline.errors %}
                                    {{ form.phone_landline(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.phone_landline.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.phone_landline(class="form-control", placeholder="أدخل رقم الهاتف الثابت") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-graduation-cap me-2"></i> المؤهلات العلمية
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.qualification_type_id.label(class="form-label") }}
                                {% if form.qualification_type_id.errors %}
                                    {{ form.qualification_type_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.qualification_type_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.qualification_type_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.specialization_id.label(class="form-label") }}
                                {% if form.specialization_id.errors %}
                                    {{ form.specialization_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.specialization_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.specialization_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.qualification_place.label(class="form-label") }}
                                {% if form.qualification_place.errors %}
                                    {{ form.qualification_place(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.qualification_place.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.qualification_place(class="form-control", placeholder="أدخل مكان الحصول على المؤهل") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.qualification_date.label(class="form-label") }}
                                {% if form.qualification_date.errors %}
                                    {{ form.qualification_date(class="form-control is-invalid", type="date") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.qualification_date.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.qualification_date(class="form-control", type="date") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-briefcase me-2"></i> العمل الحالي
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.work_place_id.label(class="form-label") }}
                                {% if form.work_place_id.errors %}
                                    {{ form.work_place_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.work_place_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.work_place_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.job_title.label(class="form-label") }}
                                {% if form.job_title.errors %}
                                    {{ form.job_title(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.job_title.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.job_title(class="form-control", placeholder="أدخل المسمى الوظيفي") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.work_governorate_id.label(class="form-label") }}
                                {% if form.work_governorate_id.errors %}
                                    {{ form.work_governorate_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.work_governorate_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.work_governorate_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.work_directorate_id.label(class="form-label") }}
                                {% if form.work_directorate_id.errors %}
                                    {{ form.work_directorate_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.work_directorate_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.work_directorate_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.work_village_id.label(class="form-label") }}
                                {% if form.work_village_id.errors %}
                                    {{ form.work_village_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.work_village_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.work_village_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.work_unit.label(class="form-label") }}
                                {% if form.work_unit.errors %}
                                    {{ form.work_unit(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.work_unit.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.work_unit(class="form-control", placeholder="أدخل الوحدة") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.assignment_type_id.label(class="form-label") }}
                                {% if form.assignment_type_id.errors %}
                                    {{ form.assignment_type_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.assignment_type_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.assignment_type_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.assignment_date.label(class="form-label") }}
                                {% if form.assignment_date.errors %}
                                    {{ form.assignment_date(class="form-control is-invalid", type="date") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.assignment_date.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.assignment_date(class="form-control", type="date") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                {{ form.assignment_authority_id.label(class="form-label") }}
                                {% if form.assignment_authority_id.errors %}
                                    {{ form.assignment_authority_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.assignment_authority_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.assignment_authority_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-shield-alt me-2"></i> بيانات عسكرية
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.military_number.label(class="form-label") }}
                                {% if form.military_number.errors %}
                                    {{ form.military_number(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.military_number.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.military_number(class="form-control", placeholder="أدخل الرقم العسكري") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.blood_type_id.label(class="form-label") }}
                                {% if form.blood_type_id.errors %}
                                    {{ form.blood_type_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.blood_type_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.blood_type_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                <div class="form-check mt-4">
                                    {{ form.is_fighter(class="form-check-input") }}
                                    {{ form.is_fighter.label(class="form-check-label") }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.weapon_ownership.label(class="form-label") }}
                                {% if form.weapon_ownership.errors %}
                                    {{ form.weapon_ownership(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.weapon_ownership.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.weapon_ownership(class="form-control", placeholder="أدخل ملكية السلاح") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.military_rank_id.label(class="form-label") }}
                                {% if form.military_rank_id.errors %}
                                    {{ form.military_rank_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.military_rank_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.military_rank_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.rank_date.label(class="form-label") }}
                                {% if form.rank_date.errors %}
                                    {{ form.rank_date(class="form-control is-invalid", type="date") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.rank_date.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.rank_date(class="form-control", type="date") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-heartbeat me-2"></i> بيانات صحية
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                {{ form.health_status.label(class="form-label") }}
                                {% if form.health_status.errors %}
                                    {{ form.health_status(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.health_status.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.health_status(class="form-control", placeholder="أدخل الحالة الصحية") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.injury_type_id.label(class="form-label") }}
                                {% if form.injury_type_id.errors %}
                                    {{ form.injury_type_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.injury_type_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.injury_type_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.injury_cause_id.label(class="form-label") }}
                                {% if form.injury_cause_id.errors %}
                                    {{ form.injury_cause_id(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.injury_cause_id.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.injury_cause_id(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.injury_place.label(class="form-label") }}
                                {% if form.injury_place.errors %}
                                    {{ form.injury_place(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.injury_place.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.injury_place(class="form-control", placeholder="أدخل مكان الإصابة") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.injury_date.label(class="form-label") }}
                                {% if form.injury_date.errors %}
                                    {{ form.injury_date(class="form-control is-invalid", type="date") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.injury_date.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.injury_date(class="form-control", type="date") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.injury_body_location.label(class="form-label") }}
                                {% if form.injury_body_location.errors %}
                                    {{ form.injury_body_location(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.injury_body_location.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.injury_body_location(class="form-control", placeholder="أدخل مكان الإصابة في الجسم") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.injury_authority.label(class="form-label") }}
                                {% if form.injury_authority.errors %}
                                    {{ form.injury_authority(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.injury_authority.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.injury_authority(class="form-control", placeholder="أدخل الجهة التابع لها أثناء الإصابة") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check mt-4">
                                    {{ form.injury_disability(class="form-check-input") }}
                                    {{ form.injury_disability.label(class="form-check-label") }}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check mt-4">
                                    {{ form.injury_hinders_work(class="form-check-input") }}
                                    {{ form.injury_hinders_work.label(class="form-check-label") }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="form-section-title">
                        <i class="fas fa-sticky-note me-2"></i> ملاحظات
                    </div>

                    <div class="form-group">
                        {{ form.notes.label(class="form-label") }}
                        {% if form.notes.errors %}
                            {{ form.notes(class="form-control is-invalid", rows=5) }}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.notes(class="form-control", rows=5, placeholder="أدخل أي ملاحظات إضافية") }}
                        {% endif %}
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ url_for('personal_data_list') }}" class="btn btn-secondary btn-cancel">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                    {{ form.submit(class="btn btn-primary btn-submit") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تفعيل القائمة المنسدلة للجداول الترميزية
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');

        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const dropdownMenu = this.nextElementSibling;
                dropdownMenu.classList.toggle('show');
            });
        });

        // تحديث المديريات عند تغيير المحافظة (للسكن)
        const residenceGovernorateSelect = document.getElementById('residence_governorate_id');
        const residenceDirectorateSelect = document.getElementById('residence_directorate_id');
        const residenceVillageSelect = document.getElementById('residence_village_id');

        if (residenceGovernorateSelect && residenceDirectorateSelect) {
            residenceGovernorateSelect.addEventListener('change', function() {
                const governorateId = this.value;

                // إفراغ قائمة المديريات والقرى
                residenceDirectorateSelect.innerHTML = '<option value="0">اختر المديرية</option>';
                residenceVillageSelect.innerHTML = '<option value="0">اختر الحي/القرية</option>';

                if (governorateId > 0) {
                    // جلب المديريات التابعة للمحافظة المختارة
                    fetch(`/api/directorates/${governorateId}`)
                        .then(response => response.json())
                        .then(data => {
                            data.forEach(directorate => {
                                const option = document.createElement('option');
                                option.value = directorate.id;
                                option.textContent = directorate.name;
                                residenceDirectorateSelect.appendChild(option);
                            });
                        });
                }
            });
        }

        // تحديث القرى/الأحياء عند تغيير المديرية (للسكن)
        if (residenceDirectorateSelect && residenceVillageSelect) {
            residenceDirectorateSelect.addEventListener('change', function() {
                const directorateId = this.value;

                // إفراغ قائمة القرى
                residenceVillageSelect.innerHTML = '<option value="0">اختر الحي/القرية</option>';

                if (directorateId > 0) {
                    // جلب القرى/الأحياء التابعة للمديرية المختارة
                    fetch(`/api/villages/${directorateId}`)
                        .then(response => response.json())
                        .then(data => {
                            data.forEach(village => {
                                const option = document.createElement('option');
                                option.value = village.id;
                                option.textContent = village.name;
                                residenceVillageSelect.appendChild(option);
                            });
                        });
                }
            });
        }

        // تحديث المديريات عند تغيير المحافظة (للعمل)
        const workGovernorateSelect = document.getElementById('work_governorate_id');
        const workDirectorateSelect = document.getElementById('work_directorate_id');
        const workVillageSelect = document.getElementById('work_village_id');

        if (workGovernorateSelect && workDirectorateSelect) {
            workGovernorateSelect.addEventListener('change', function() {
                const governorateId = this.value;

                // إفراغ قائمة المديريات والقرى
                workDirectorateSelect.innerHTML = '<option value="0">اختر المديرية</option>';
                workVillageSelect.innerHTML = '<option value="0">اختر الحي/القرية</option>';

                if (governorateId > 0) {
                    // جلب المديريات التابعة للمحافظة المختارة
                    fetch(`/api/directorates/${governorateId}`)
                        .then(response => response.json())
                        .then(data => {
                            data.forEach(directorate => {
                                const option = document.createElement('option');
                                option.value = directorate.id;
                                option.textContent = directorate.name;
                                workDirectorateSelect.appendChild(option);
                            });
                        });
                }
            });
        }

        // تحديث القرى/الأحياء عند تغيير المديرية (للعمل)
        if (workDirectorateSelect && workVillageSelect) {
            workDirectorateSelect.addEventListener('change', function() {
                const directorateId = this.value;

                // إفراغ قائمة القرى
                workVillageSelect.innerHTML = '<option value="0">اختر الحي/القرية</option>';

                if (directorateId > 0) {
                    // جلب القرى/الأحياء التابعة للمديرية المختارة
                    fetch(`/api/villages/${directorateId}`)
                        .then(response => response.json())
                        .then(data => {
                            data.forEach(village => {
                                const option = document.createElement('option');
                                option.value = village.id;
                                option.textContent = village.name;
                                workVillageSelect.appendChild(option);
                            });
                        });
                }
            });
        }
    });
</script>
{% endblock %}
