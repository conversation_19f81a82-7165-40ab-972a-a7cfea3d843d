# نظام التدريب والتأهيل - Docker Configuration
# Training System - Docker Configuration

FROM python:3.13-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=app.py
ENV FLASK_ENV=production
ENV PORT=5000

# تعيين مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملف المتطلبات
COPY requirements.txt .

# تثبيت المكتبات Python
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY . .

# إنشاء مجلد قاعدة البيانات
RUN mkdir -p /app/data

# تعيين الصلاحيات
RUN chmod +x /app

# فتح المنفذ الافتراضي 5000 (قابل للتغيير عبر متغير PORT)
EXPOSE 5000

# إنشاء مستخدم غير جذر لتشغيل التطبيق
RUN useradd --create-home --shell /bin/bash app && chown -R app:app /app
USER app

# تشغيل التطبيق على المنفذ المحدد في متغير PORT
CMD ["python", "-c", "import os; from app import app; app.run(host='0.0.0.0', port=int(os.environ.get('PORT', 5000)), debug=False)"]
