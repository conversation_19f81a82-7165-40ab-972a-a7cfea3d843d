#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
الاختبار النهائي الشامل
"""

import requests
import re
import sqlite3
from datetime import datetime

def final_test():
    print("🚀 الاختبار النهائي الشامل")
    print("="*60)
    
    # 1. فحص قاعدة البيانات
    print("1️⃣ فحص قاعدة البيانات...")
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM user")
        user_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM user WHERE is_active = 1")
        active_users = cursor.fetchone()[0]
        
        print(f"   ✅ إجمالي المستخدمين: {user_count}")
        print(f"   ✅ المستخدمين النشطين: {active_users}")
        
        conn.close()
        db_ok = True
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
        db_ok = False
    
    # 2. اختبار تسجيل الدخول
    print("\n2️⃣ اختبار تسجيل الدخول...")
    
    test_users = [
        ('<EMAIL>', 'admin123', 'المدير الأساسي'),
        ('<EMAIL>', 'ceo123', 'المدير التنفيذي'),
        ('<EMAIL>', 'hr123', 'مدير الموارد البشرية'),
        ('<EMAIL>', 'tech123', 'مدرب التقنية'),
        ('<EMAIL>', 'audit123', 'المراجع الداخلي')
    ]
    
    successful_logins = 0
    
    for email, password, description in test_users:
        try:
            session = requests.Session()
            
            # الحصول على CSRF token
            login_page = session.get('http://localhost:5000/login')
            csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
            csrf_token = csrf_match.group(1) if csrf_match else None
            
            login_data = {
                'email': email,
                'password': password
            }
            
            if csrf_token:
                login_data['csrf_token'] = csrf_token
            
            response = session.post('http://localhost:5000/login', data=login_data)
            
            if 'dashboard' in response.url:
                print(f"   ✅ {description}: نجح")
                successful_logins += 1
                
                # اختبار صفحة إدارة المستخدمين
                users_page = session.get('http://localhost:5000/admin/users')
                if users_page.status_code == 200:
                    print(f"      ✅ يمكن الوصول لإدارة المستخدمين")
                
                session.get('http://localhost:5000/logout')
            else:
                print(f"   ❌ {description}: فشل")
                
        except Exception as e:
            print(f"   ❌ {description}: خطأ - {e}")
    
    print(f"\n   📊 نجح {successful_logins} من {len(test_users)} مستخدمين")
    
    # 3. اختبار وظائف إدارة المستخدمين
    print("\n3️⃣ اختبار وظائف إدارة المستخدمين...")
    
    session = requests.Session()
    
    # تسجيل الدخول كمدير
    login_page = session.get('http://localhost:5000/login')
    csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
    csrf_token = csrf_match.group(1) if csrf_match else None
    
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    if csrf_token:
        login_data['csrf_token'] = csrf_token
    
    login_response = session.post('http://localhost:5000/login', data=login_data)
    
    if 'dashboard' in login_response.url:
        print("   ✅ تم تسجيل الدخول كمدير")
        
        # اختبار إنشاء مستخدم
        timestamp = datetime.now().strftime("%H%M%S")
        new_user_data = {
            'username': f'final_test_{timestamp}',
            'email': f'final_test_{timestamp}@test.com',
            'password': 'test123',
            'role': 'viewer',
            'first_name': 'اختبار',
            'last_name': 'نهائي',
            'is_active': True
        }
        
        create_response = session.post(
            'http://localhost:5000/admin/users/create',
            json=new_user_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if create_response.status_code == 200:
            result = create_response.json()
            if result.get('success'):
                print("   ✅ إنشاء المستخدم نجح")
                functions_ok = True
            else:
                print(f"   ❌ إنشاء المستخدم فشل: {result.get('message')}")
                functions_ok = False
        else:
            print(f"   ❌ خطأ في إنشاء المستخدم: {create_response.status_code}")
            functions_ok = False
    else:
        print("   ❌ فشل تسجيل الدخول كمدير")
        functions_ok = False
    
    # 4. اختبار واجهة المستخدم
    print("\n4️⃣ اختبار واجهة المستخدم...")
    
    users_page = session.get('http://localhost:5000/admin/users')
    
    if users_page.status_code == 200:
        ui_components = [
            'إدارة المستخدمين',
            'إجمالي المستخدمين',
            'user-row',
            'addUserModal',
            'btn-primary'
        ]
        
        components_found = 0
        for component in ui_components:
            if component in users_page.text:
                components_found += 1
        
        print(f"   ✅ مكونات واجهة المستخدم: {components_found}/{len(ui_components)}")
        ui_ok = components_found >= 4
    else:
        print("   ❌ لا يمكن الوصول لصفحة إدارة المستخدمين")
        ui_ok = False
    
    # النتيجة النهائية
    print("\n" + "="*60)
    print("📋 النتيجة النهائية")
    print("="*60)
    
    print(f"🔍 قاعدة البيانات: {'✅ سليمة' if db_ok else '❌ مشاكل'}")
    print(f"🔐 تسجيل الدخول: ✅ {successful_logins}/{len(test_users)} نجح")
    print(f"⚙️ وظائف الإدارة: {'✅ تعمل' if functions_ok else '❌ مشاكل'}")
    print(f"🎨 واجهة المستخدم: {'✅ مكتملة' if ui_ok else '❌ ناقصة'}")
    
    # حساب النتيجة الإجمالية
    total_tests = 4
    passed_tests = sum([db_ok, successful_logins >= 4, functions_ok, ui_ok])
    
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"\n🏆 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 النظام ممتاز ومكتمل!")
    elif success_rate >= 75:
        print("👍 النظام جيد جداً")
    elif success_rate >= 50:
        print("👌 النظام جيد مع بعض التحسينات")
    else:
        print("⚠️ النظام يحتاج إصلاحات")
    
    print("\n🔗 معلومات النظام:")
    print("   الصفحة الرئيسية: http://localhost:5000")
    print("   تسجيل الدخول: http://localhost:5000/login")
    print("   إدارة المستخدمين: http://localhost:5000/admin/users")
    
    print("\n👤 بيانات تسجيل الدخول:")
    print("   المدير العام: <EMAIL> / admin123")
    print("   المدير التنفيذي: <EMAIL> / ceo123")
    print("   مدير الموارد البشرية: <EMAIL> / hr123")
    
    return success_rate >= 75

if __name__ == '__main__':
    try:
        # التحقق من تشغيل الخادم
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code != 200:
            print("❌ الخادم غير متاح")
            exit(1)
    except:
        print("❌ لا يمكن الوصول للخادم على localhost:5000")
        exit(1)
    
    success = final_test()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة التقرير أعلاه.")
