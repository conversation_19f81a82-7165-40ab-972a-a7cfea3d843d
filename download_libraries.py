#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنزيل جميع المكتبات الخارجية وتحويلها إلى ملفات محلية
Download all external libraries and convert them to local files
"""

import os
import requests
import sys
from pathlib import Path

def download_file(url, local_path):
    """تنزيل ملف من URL وحفظه محلياً"""
    try:
        print(f"📥 تنزيل: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # إنشاء المجلد إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        with open(local_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ تم حفظ: {local_path}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنزيل {url}: {e}")
        return False

def main():
    """تنزيل جميع المكتبات المطلوبة"""
    
    # قائمة المكتبات المطلوبة
    libraries = {
        # Bootstrap
        'static/libs/bootstrap/bootstrap.rtl.min.css': 'https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.rtl.min.css',
        'static/libs/bootstrap/bootstrap.bundle.min.js': 'https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js',
        
        # Font Awesome
        'static/libs/fontawesome/all.min.css': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css',
        'static/libs/fontawesome/webfonts/fa-solid-900.woff2': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-solid-900.woff2',
        'static/libs/fontawesome/webfonts/fa-regular-400.woff2': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-regular-400.woff2',
        'static/libs/fontawesome/webfonts/fa-brands-400.woff2': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-brands-400.woff2',
        
        # jQuery
        'static/libs/jquery/jquery-3.6.0.min.js': 'https://code.jquery.com/jquery-3.6.0.min.js',
        
        # Chart.js
        'static/libs/chartjs/chart.min.js': 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
        
        # DataTables
        'static/libs/datatables/dataTables.bootstrap5.min.css': 'https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css',
        'static/libs/datatables/jquery.dataTables.min.js': 'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js',
        'static/libs/datatables/dataTables.bootstrap5.min.js': 'https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js',
        'static/libs/datatables/responsive.bootstrap5.min.css': 'https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css',
        'static/libs/datatables/dataTables.responsive.min.js': 'https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js',
        'static/libs/datatables/responsive.bootstrap5.min.js': 'https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.min.js',
        'static/libs/datatables/buttons.bootstrap5.min.css': 'https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css',
        'static/libs/datatables/dataTables.buttons.min.js': 'https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js',
        'static/libs/datatables/buttons.bootstrap5.min.js': 'https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js',
        'static/libs/datatables/buttons.html5.min.js': 'https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js',
        'static/libs/datatables/buttons.print.min.js': 'https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js',
        
        # Select2
        'static/libs/select2/select2.min.css': 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css',
        'static/libs/select2/select2.min.js': 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js',
        'static/libs/select2/select2-bootstrap-5-theme.min.css': 'https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css',
        
        # DevExtreme
        'static/libs/devextreme/dx.light.css': 'https://cdnjs.cloudflare.com/ajax/libs/devextreme/24.2.3/css/dx.light.min.css',
        'static/libs/devextreme/dx.all.js': 'https://cdnjs.cloudflare.com/ajax/libs/devextreme/24.2.3/js/dx.all.js',
        
        # Other Libraries
        'static/libs/other/jspdf.umd.min.js': 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js',
        'static/libs/other/xlsx.full.min.js': 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js',
        'static/libs/other/jszip.min.js': 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js',
    }
    
    print("🚀 بدء تنزيل المكتبات...")
    print("=" * 60)
    
    success_count = 0
    total_count = len(libraries)
    
    for local_path, url in libraries.items():
        if download_file(url, local_path):
            success_count += 1
        print("-" * 40)
    
    print("=" * 60)
    print(f"✅ تم تنزيل {success_count} من {total_count} ملف بنجاح")
    
    if success_count == total_count:
        print("🎉 تم تنزيل جميع المكتبات بنجاح!")
        return True
    else:
        print(f"⚠️ فشل في تنزيل {total_count - success_count} ملف")
        return False

if __name__ == "__main__":
    try:
        if main():
            print("\n✅ العملية مكتملة بنجاح!")
        else:
            print("\n❌ حدثت بعض الأخطاء أثناء التنزيل")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        sys.exit(1)
