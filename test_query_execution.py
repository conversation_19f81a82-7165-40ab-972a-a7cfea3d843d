#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تنفيذ الاستعلام المطلوب واختبار النتائج
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, CourseParticipant, PersonData

def test_query_execution():
    """
    تنفيذ الاستعلام المطلوب واختبار النتائج
    """
    with app.app_context():
        try:
            course_id = 1
            print(f"🔍 تنفيذ الاستعلام للدورة رقم: {course_id}")
            print("=" * 60)
            
            # الخطوة 1: جلب المشاركين المضافين مسبقاً
            print("📋 الخطوة 1: جلب المشاركين المضافين مسبقاً")
            existing_participants = CourseParticipant.query.filter_by(course_id=course_id).all()
            existing_participant_ids = [p.personal_data_id for p in existing_participants]
            
            print(f"   - عدد المشاركين المضافين: {len(existing_participants)}")
            print(f"   - معرفات المضافين: {existing_participant_ids}")
            
            # عرض تفاصيل المضافين
            for i, participant in enumerate(existing_participants, 1):
                person = PersonData.query.get(participant.personal_data_id)
                person_name = person.full_name if person else "غير موجود"
                print(f"     {i}. ID: {participant.personal_data_id} - {person_name}")
            
            print("\n" + "-" * 60)
            
            # الخطوة 2: جلب الأشخاص المتاحين للإضافة
            print("📋 الخطوة 2: جلب الأشخاص المتاحين للإضافة")
            
            if existing_participant_ids:
                available_people = PersonData.query.filter(~PersonData.id.in_(existing_participant_ids)).all()
            else:
                available_people = PersonData.query.all()
            
            print(f"   - عدد الأشخاص المتاحين: {len(available_people)}")
            
            # عرض أول 10 أشخاص متاحين
            print("   - أول 10 أشخاص متاحين:")
            for i, person in enumerate(available_people[:10], 1):
                print(f"     {i}. ID: {person.id} - {person.full_name}")
            
            if len(available_people) > 10:
                print(f"     ... و {len(available_people) - 10} شخص آخر")
            
            print("\n" + "-" * 60)
            
            # الخطوة 3: إنشاء خيارات القائمة المنسدلة
            print("📋 الخطوة 3: إنشاء خيارات القائمة المنسدلة")
            choices = [(0, 'اختر مشارك')] + [(p.id, p.full_name) for p in available_people]
            
            print(f"   - عدد الخيارات الإجمالي: {len(choices)}")
            print("   - أول 10 خيارات:")
            for i, choice in enumerate(choices[:10], 1):
                print(f"     {i}. ({choice[0]}, '{choice[1]}')")
            
            if len(choices) > 10:
                print(f"     ... و {len(choices) - 10} خيار آخر")
            
            print("\n" + "=" * 60)
            
            # الخطوة 4: التحقق من صحة المنطق
            print("📊 التحقق من صحة المنطق:")
            total_people = PersonData.query.count()
            expected_available = total_people - len(existing_participants)
            actual_available = len(available_people)
            
            print(f"   - إجمالي الأشخاص في قاعدة البيانات: {total_people}")
            print(f"   - المشاركين المضافين مسبقاً: {len(existing_participants)}")
            print(f"   - المتوقع أن يكون متاح: {expected_available}")
            print(f"   - الفعلي المتاح: {actual_available}")
            
            if expected_available == actual_available:
                print("   ✅ المنطق صحيح!")
                return True
            else:
                print("   ❌ خطأ في المنطق!")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("🧪 تنفيذ الاستعلام المطلوب...")
    success = test_query_execution()
    if success:
        print("\n✅ تم تنفيذ الاستعلام بنجاح!")
    else:
        print("\n❌ فشل في تنفيذ الاستعلام!")
