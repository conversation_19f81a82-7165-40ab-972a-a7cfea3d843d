{% extends "layout.html" %}
{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">نتائج استيراد البيانات الشخصية</h4>
                </div>
                <div class="card-body">
                    {% if 'import_progress' in session %}
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">حالة الاستيراد: <span id="import-status" class="badge bg-primary">{{ session.import_progress.status }}</span></h5>
                            <div id="spinner-container" class="d-flex align-items-center" {% if session.import_progress.status != 'جاري الاستيراد...' %}style="display: none !important;"{% endif %}>
                                <div class="spinner-border text-primary me-2" role="status">
                                    <span class="visually-hidden">جاري الاستيراد...</span>
                                </div>
                                <span class="text-primary">جاري الاستيراد...</span>
                            </div>
                        </div>

                        <!-- شريط التقدم الرئيسي - تصميم محسن -->
                        <div class="progress mb-3" style="height: 30px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); background-color: #f0f0f0;">
                            {% set progress = (session.import_progress.processed / session.import_progress.total * 100) if session.import_progress.total > 0 else 0 %}
                            <div id="main-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar"
                                style="width: {{ progress }}%; font-size: 16px; font-weight: bold; transition: width 0.5s ease;"
                                aria-valuenow="{{ progress }}" aria-valuemin="0" aria-valuemax="100">
                                <span id="progress-text">{{ session.import_progress.processed }} / {{ session.import_progress.total }} ({{ progress|round(1) }}%)</span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <!-- شريط التقدم للسجلات الناجحة -->
                                <div class="progress mb-2" style="height: 24px; border-radius: 8px; background-color: #e9f5e9;">
                                    {% set success_progress = (session.import_progress.success / session.import_progress.total * 100) if session.import_progress.total > 0 else 0 %}
                                    <div id="success-progress-bar" class="progress-bar bg-success" role="progressbar"
                                        style="width: {{ success_progress }}%; font-weight: bold; transition: width 0.5s ease;"
                                        aria-valuenow="{{ success_progress }}" aria-valuemin="0" aria-valuemax="100">
                                        <span id="success-text">السجلات الناجحة ({{ session.import_progress.success }})</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- شريط التقدم للسجلات الفاشلة -->
                                <div class="progress mb-2" style="height: 24px; border-radius: 8px; background-color: #f9e9e9;">
                                    {% set error_progress = (session.import_progress.error / session.import_progress.total * 100) if session.import_progress.total > 0 else 0 %}
                                    <div id="error-progress-bar" class="progress-bar bg-danger" role="progressbar"
                                        style="width: {{ error_progress }}%; font-weight: bold; transition: width 0.5s ease;"
                                        aria-valuenow="{{ error_progress }}" aria-valuemin="0" aria-valuemax="100">
                                        <span id="error-text">السجلات الفاشلة ({{ session.import_progress.error }})</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="alert alert-info" id="import-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="import-message">
                                {% if session.import_progress.status == 'جاري الاستيراد...' %}
                                    جاري استيراد البيانات، يرجى الانتظار...
                                {% elif session.import_progress.status == 'تم الاستيراد بنجاح' %}
                                    تم الانتهاء من استيراد البيانات بنجاح.
                                {% elif session.import_progress.status == 'فشل الاستيراد' %}
                                    حدث خطأ أثناء استيراد البيانات.
                                {% else %}
                                    جاري تحضير عملية الاستيراد...
                                {% endif %}
                            </span>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="alert alert-success">
                                    <h5>تم استيراد {{ session.import_progress.success }} سجل بنجاح</h5>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-danger">
                                    <h5>فشل استيراد {{ session.import_progress.error }} سجل</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if 'import_results' in session %}
                    <div class="mt-4">
                        <ul class="nav nav-tabs" id="importTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="success-tab" data-bs-toggle="tab" data-bs-target="#success" type="button" role="tab" aria-controls="success" aria-selected="true">
                                    السجلات الناجحة ({{ session.import_results.success }})
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="error-tab" data-bs-toggle="tab" data-bs-target="#error" type="button" role="tab" aria-controls="error" aria-selected="false">
                                    السجلات الفاشلة ({{ session.import_results.error }})
                                </button>
                            </li>
                        </ul>
                        <div class="tab-content" id="importTabsContent">
                            <div class="tab-pane fade show active" id="success" role="tabpanel" aria-labelledby="success-tab">
                                <div class="table-responsive mt-3">
                                    <table class="table table-striped table-bordered">
                                        <thead class="table-success">
                                            <tr>
                                                <th>#</th>
                                                <th>الاسم</th>
                                                <th>الرقم الوطني</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for item in session.import_results.imported_data %}
                                            <tr>
                                                <td>{{ loop.index }}</td>
                                                <td>{{ item.name }}</td>
                                                <td>{{ item.national_id }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="error" role="tabpanel" aria-labelledby="error-tab">
                                <div class="table-responsive mt-3">
                                    <table class="table table-striped table-bordered">
                                        <thead class="table-danger">
                                            <tr>
                                                <th>الصف</th>
                                                <th>الخطأ</th>
                                                <th>البيانات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for item in session.import_results.error_data %}
                                            <tr>
                                                <td>{{ item.row }}</td>
                                                <td>{{ item.error }}</td>
                                                <td>{{ item.data }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="mt-4">
                        <a href="{{ url_for('personal_data_list') }}" class="btn btn-primary">
                            <i class="fas fa-list me-1"></i> العودة إلى قائمة البيانات الشخصية
                        </a>
                        <a href="{{ url_for('add_personal_data_new') }}" class="btn btn-success">
                            <i class="fas fa-file-import me-1"></i> استيراد ملف جديد
                        </a>
                        <button id="refresh-btn" class="btn btn-info">
                            <i class="fas fa-sync-alt me-1"></i> تحديث الصفحة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تحديث التقدم كل ثانية - نسخة محسنة
    $(document).ready(function() {
        // تهيئة متغيرات عالمية
        var isImporting = {% if 'import_progress' in session and session.import_progress.status == 'جاري الاستيراد...' %}true{% else %}false{% endif %};
        var progressInterval;
        var retryCount = 0;
        var maxRetries = 5;

        // دالة لتحديث شريط التقدم
        function updateProgressBar(selector, value, total, text) {
            var percent = (total > 0) ? (value / total * 100) : 0;
            $(selector).css('width', percent + '%');
            $(selector).attr('aria-valuenow', percent);
            $(selector).find('span').text(text);
            return percent;
        }

        // دالة لتحديث حالة الاستيراد
        function updateImportStatus(data) {
            // تحديث شريط التقدم الرئيسي
            var mainProgress = updateProgressBar(
                '#main-progress-bar',
                data.processed,
                data.total,
                data.processed + ' / ' + data.total + ' (' + (data.processed / data.total * 100).toFixed(1) + '%)'
            );

            // تحديث شريط التقدم للسجلات الناجحة
            updateProgressBar(
                '#success-progress-bar',
                data.success,
                data.total,
                'السجلات الناجحة (' + data.success + ')'
            );

            // تحديث شريط التقدم للسجلات الفاشلة
            updateProgressBar(
                '#error-progress-bar',
                data.error,
                data.total,
                'السجلات الفاشلة (' + data.error + ')'
            );

            // تحديث العدادات
            $('.alert-success h5').text('تم استيراد ' + data.success + ' سجل بنجاح');
            $('.alert-danger h5').text('فشل استيراد ' + data.error + ' سجل');

            // تحديث حالة الاستيراد
            $('#import-status').text(data.status);

            // تحديث رسالة الاستيراد
            if (data.status === 'جاري الاستيراد...') {
                $('#import-message').text('جاري استيراد البيانات، يرجى الانتظار...');
                $('#spinner-container').show();
            } else if (data.status === 'تم الاستيراد بنجاح') {
                $('#import-message').text('تم الانتهاء من استيراد البيانات بنجاح.');
                $('#spinner-container').hide();
                $('#import-info').removeClass('alert-info').addClass('alert-success');
            } else if (data.status === 'فشل الاستيراد') {
                $('#import-message').text('حدث خطأ أثناء استيراد البيانات.');
                $('#spinner-container').hide();
                $('#import-info').removeClass('alert-info').addClass('alert-danger');
            }

            // إذا انتهت العملية، أوقف التحديث
            if (data.status !== 'جاري الاستيراد...') {
                isImporting = false;
                clearInterval(progressInterval);

                // إعادة تحميل الصفحة بعد 3 ثوان لعرض النتائج النهائية
                setTimeout(function() {
                    location.reload();
                }, 3000);
            }
        }

        // دالة لجلب تقدم الاستيراد
        function fetchImportProgress() {
            $.ajax({
                url: "{{ url_for('get_import_progress') }}",
                type: "GET",
                dataType: "json",
                cache: false,
                success: function(data) {
                    // إعادة ضبط عداد المحاولات
                    retryCount = 0;

                    // تحديث واجهة المستخدم
                    updateImportStatus(data);
                },
                error: function(xhr, status, error) {
                    console.error("خطأ في جلب تقدم الاستيراد:", error);

                    // زيادة عداد المحاولات
                    retryCount++;

                    // إذا تجاوزنا الحد الأقصى للمحاولات، أوقف التحديث
                    if (retryCount >= maxRetries) {
                        console.error("تم تجاوز الحد الأقصى لمحاولات الاتصال");
                        clearInterval(progressInterval);
                        $('#import-message').text('فشل الاتصال بالخادم. يرجى تحديث الصفحة.');
                        $('#import-info').removeClass('alert-info').addClass('alert-warning');
                    }
                }
            });
        }

        // بدء تحديث التقدم إذا كانت العملية جارية
        if (isImporting) {
            // جلب التقدم فوراً
            fetchImportProgress();

            // ثم تحديث كل ثانية
            progressInterval = setInterval(fetchImportProgress, 1000);
        }

        // إضافة زر لتحديث الصفحة يدوياً
        $('#refresh-btn').on('click', function() {
            location.reload();
        });
    });
</script>
{% endblock %}
