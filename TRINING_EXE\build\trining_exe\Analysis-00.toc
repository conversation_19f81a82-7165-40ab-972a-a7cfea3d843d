(['E:\\app\\TRINING\\TRINING_EXE\\app.py'],
 ['E:\\app\\TRINING\\TRINING_EXE'],
 ['flask'],
 [('C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('database\\training_system.db',
   'E:\\app\\TRINING\\TRINING_EXE\\database\\training_system.db',
   'DATA'),
  ('static\\css\\dashboard-style.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\dashboard-style.css',
   'DATA'),
  ('static\\css\\dashboard.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\dashboard.css',
   'DATA'),
  ('static\\css\\login.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\login.css',
   'DATA'),
  ('static\\css\\main.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\main.css',
   'DATA'),
  ('static\\css\\reports.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\reports.css',
   'DATA'),
  ('static\\css\\smart-search.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\smart-search.css',
   'DATA'),
  ('static\\img\\README.md',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\README.md',
   'DATA'),
  ('static\\img\\default_course.jpg',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\default_course.jpg',
   'DATA'),
  ('static\\img\\training-bg.jpg',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\training-bg.jpg',
   'DATA'),
  ('static\\img\\user-avatar.png',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\user-avatar.png',
   'DATA'),
  ('static\\js\\main.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\js\\main.js',
   'DATA'),
  ('static\\js\\smart-search.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\js\\smart-search.js',
   'DATA'),
  ('static\\libs\\bootstrap\\bootstrap.bundle.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\bootstrap\\bootstrap.bundle.min.js',
   'DATA'),
  ('static\\libs\\bootstrap\\bootstrap.rtl.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\bootstrap\\bootstrap.rtl.min.css',
   'DATA'),
  ('static\\libs\\chartjs\\chart.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\chartjs\\chart.min.js',
   'DATA'),
  ('static\\libs\\datatables\\buttons.bootstrap5.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.bootstrap5.min.css',
   'DATA'),
  ('static\\libs\\datatables\\buttons.bootstrap5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.bootstrap5.min.js',
   'DATA'),
  ('static\\libs\\datatables\\buttons.html5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.html5.min.js',
   'DATA'),
  ('static\\libs\\datatables\\buttons.print.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.print.min.js',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.bootstrap5.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.bootstrap5.min.css',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.bootstrap5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.bootstrap5.min.js',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.buttons.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.buttons.min.js',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.responsive.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.responsive.min.js',
   'DATA'),
  ('static\\libs\\datatables\\jquery.dataTables.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\jquery.dataTables.min.js',
   'DATA'),
  ('static\\libs\\datatables\\responsive.bootstrap5.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\responsive.bootstrap5.min.css',
   'DATA'),
  ('static\\libs\\datatables\\responsive.bootstrap5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\responsive.bootstrap5.min.js',
   'DATA'),
  ('static\\libs\\devextreme\\dx.light.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\devextreme\\dx.light.css',
   'DATA'),
  ('static\\libs\\fontawesome\\all.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\all.min.css',
   'DATA'),
  ('static\\libs\\fontawesome\\webfonts\\fa-brands-400.woff2',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\webfonts\\fa-brands-400.woff2',
   'DATA'),
  ('static\\libs\\fontawesome\\webfonts\\fa-regular-400.woff2',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\webfonts\\fa-regular-400.woff2',
   'DATA'),
  ('static\\libs\\fontawesome\\webfonts\\fa-solid-900.woff2',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\webfonts\\fa-solid-900.woff2',
   'DATA'),
  ('static\\libs\\jquery\\jquery-3.6.0.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\jquery\\jquery-3.6.0.min.js',
   'DATA'),
  ('static\\libs\\libs_config.py',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\libs_config.py',
   'DATA'),
  ('static\\libs\\other\\jspdf.umd.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\other\\jspdf.umd.min.js',
   'DATA'),
  ('static\\libs\\other\\jszip.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\other\\jszip.min.js',
   'DATA'),
  ('static\\libs\\other\\xlsx.full.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\other\\xlsx.full.min.js',
   'DATA'),
  ('static\\libs\\select2\\select2-bootstrap-5-theme.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\select2\\select2-bootstrap-5-theme.min.css',
   'DATA'),
  ('static\\libs\\select2\\select2.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\select2\\select2.min.css',
   'DATA'),
  ('static\\libs\\select2\\select2.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\select2\\select2.min.js',
   'DATA'),
  ('static\\participants_complete_sample.xlsx',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\participants_complete_sample.xlsx',
   'DATA'),
  ('static\\participants_sample.xlsx',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\participants_sample.xlsx',
   'DATA'),
  ('static\\uploads\\courses\\ibb2.bmp',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\courses\\ibb2.bmp',
   'DATA'),
  ('static\\uploads\\courses\\images.jpeg',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\courses\\images.jpeg',
   'DATA'),
  ('static\\uploads\\materials\\1\\22-_720P_HD.mp4',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\materials\\1\\22-_720P_HD.mp4',
   'DATA'),
  ('static\\uploads\\materials\\1\\46_ERP720P_HD.mp4',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\materials\\1\\46_ERP720P_HD.mp4',
   'DATA'),
  ('static\\uploads\\materials\\1\\720P_HD.mp4',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\materials\\1\\720P_HD.mp4',
   'DATA'),
  ('static\\welcome.html',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\welcome.html',
   'DATA'),
  ('static\\قالب_بيانات_الأشخاص_نموذج.xlsx',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\قالب_بيانات_الأشخاص_نموذج.xlsx',
   'DATA'),
  ('templates\\add_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\add_course.html',
   'DATA'),
  ('templates\\add_course_participant.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\add_course_participant.html',
   'DATA'),
  ('templates\\add_material.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\add_material.html',
   'DATA'),
  ('templates\\backup\\backup.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\backup\\backup.html',
   'DATA'),
  ('templates\\backup\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\backup\\index.html',
   'DATA'),
  ('templates\\batches.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\batches.html',
   'DATA'),
  ('templates\\batches\\add_batch.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\batches\\add_batch.html',
   'DATA'),
  ('templates\\batches\\edit_batch.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\batches\\edit_batch.html',
   'DATA'),
  ('templates\\course_details.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_details.html',
   'DATA'),
  ('templates\\course_import_detailed_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_import_detailed_results.html',
   'DATA'),
  ('templates\\course_import_participants.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_import_participants.html',
   'DATA'),
  ('templates\\course_import_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_import_results.html',
   'DATA'),
  ('templates\\course_participants.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_participants.html',
   'DATA'),
  ('templates\\course_participants_with_evaluation.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_participants_with_evaluation.html',
   'DATA'),
  ('templates\\course_reports.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_reports.html',
   'DATA'),
  ('templates\\course_schedule.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_schedule.html',
   'DATA'),
  ('templates\\course_smart_analysis.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_smart_analysis.html',
   'DATA'),
  ('templates\\courses.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\courses.html',
   'DATA'),
  ('templates\\dashboard.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\dashboard.html',
   'DATA'),
  ('templates\\dynamic_evaluation_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\dynamic_evaluation_form.html',
   'DATA'),
  ('templates\\edit_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_course.html',
   'DATA'),
  ('templates\\edit_course_participant.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_course_participant.html',
   'DATA'),
  ('templates\\edit_material.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_material.html',
   'DATA'),
  ('templates\\edit_schedule_item.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_schedule_item.html',
   'DATA'),
  ('templates\\evaluation_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\evaluation_form.html',
   'DATA'),
  ('templates\\evaluation_print.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\evaluation_print.html',
   'DATA'),
  ('templates\\graduates.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\graduates.html',
   'DATA'),
  ('templates\\hello.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\hello.html',
   'DATA'),
  ('templates\\home.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\home.html',
   'DATA'),
  ('templates\\home_new.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\home_new.html',
   'DATA'),
  ('templates\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\index.html',
   'DATA'),
  ('templates\\layout.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\layout.html',
   'DATA'),
  ('templates\\layout_local.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\layout_local.html',
   'DATA'),
  ('templates\\login.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\login.html',
   'DATA'),
  ('templates\\manage_course_participants.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\manage_course_participants.html',
   'DATA'),
  ('templates\\monthly_course_report.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\monthly_course_report.html',
   'DATA'),
  ('templates\\name_analysis_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\name_analysis_course.html',
   'DATA'),
  ('templates\\person_data.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data.html',
   'DATA'),
  ('templates\\person_data\\add_correction.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\add_correction.html',
   'DATA'),
  ('templates\\person_data\\duplicate_names.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\duplicate_names.html',
   'DATA'),
  ('templates\\person_data\\excel_management.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\excel_management.html',
   'DATA'),
  ('templates\\person_data\\manage_corrections.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\manage_corrections.html',
   'DATA'),
  ('templates\\person_data\\name_analysis.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_backup_before_enhancement.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_backup_before_enhancement.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results_backup.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results_backup.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results_backup_before_charts.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results_backup_before_charts.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results_backup_before_font.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results_backup_before_font.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_evaluation_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_evaluation_results.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_evaluation_results_backup_before_charts.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_evaluation_results_backup_before_charts.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_results.html',
   'DATA'),
  ('templates\\person_data\\test_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\test_form.html',
   'DATA'),
  ('templates\\person_data_dashboard.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data_dashboard.html',
   'DATA'),
  ('templates\\person_data_table.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data_table.html',
   'DATA'),
  ('templates\\person_data_table_new.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data_table_new.html',
   'DATA'),
  ('templates\\personal_data\\add.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\add.html',
   'DATA'),
  ('templates\\personal_data\\add_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\add_course.html',
   'DATA'),
  ('templates\\personal_data\\advanced_excel_view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\advanced_excel_view.html',
   'DATA'),
  ('templates\\personal_data\\compare_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\compare_results.html',
   'DATA'),
  ('templates\\personal_data\\devextreme_view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\devextreme_view.html',
   'DATA'),
  ('templates\\personal_data\\excel_devextreme.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\excel_devextreme.html',
   'DATA'),
  ('templates\\personal_data\\excel_view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\excel_view.html',
   'DATA'),
  ('templates\\personal_data\\existing_records.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\existing_records.html',
   'DATA'),
  ('templates\\personal_data\\import_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\import_results.html',
   'DATA'),
  ('templates\\personal_data\\list.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\list.html',
   'DATA'),
  ('templates\\personal_data\\new_add.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\new_add.html',
   'DATA'),
  ('templates\\personal_data\\view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\view.html',
   'DATA'),
  ('templates\\reference_tables\\add_card_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_card_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_category.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_category.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_level.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_path.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_path.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_path_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_path_level.html',
   'DATA'),
  ('templates\\reference_tables\\add_department.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_department.html',
   'DATA'),
  ('templates\\reference_tables\\add_directorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_directorate.html',
   'DATA'),
  ('templates\\reference_tables\\add_force_classification.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_force_classification.html',
   'DATA'),
  ('templates\\reference_tables\\add_governorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_governorate.html',
   'DATA'),
  ('templates\\reference_tables\\add_injury_cause.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_injury_cause.html',
   'DATA'),
  ('templates\\reference_tables\\add_injury_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_injury_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_location.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_location.html',
   'DATA'),
  ('templates\\reference_tables\\add_military_rank.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_military_rank.html',
   'DATA'),
  ('templates\\reference_tables\\add_participant_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_participant_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_qualification_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_qualification_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_specialization.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_specialization.html',
   'DATA'),
  ('templates\\reference_tables\\add_training_center.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_training_center.html',
   'DATA'),
  ('templates\\reference_tables\\add_training_center_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_training_center_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_village.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_village.html',
   'DATA'),
  ('templates\\reference_tables\\agencies.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\agencies.html',
   'DATA'),
  ('templates\\reference_tables\\agencies_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\agencies_tree.html',
   'DATA'),
  ('templates\\reference_tables\\blood_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\blood_types.html',
   'DATA'),
  ('templates\\reference_tables\\card_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\card_types.html',
   'DATA'),
  ('templates\\reference_tables\\course_categories.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_categories.html',
   'DATA'),
  ('templates\\reference_tables\\course_levels.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_levels.html',
   'DATA'),
  ('templates\\reference_tables\\course_path_levels.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_path_levels.html',
   'DATA'),
  ('templates\\reference_tables\\course_paths.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_paths.html',
   'DATA'),
  ('templates\\reference_tables\\course_paths_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_paths_tree.html',
   'DATA'),
  ('templates\\reference_tables\\departments.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\departments.html',
   'DATA'),
  ('templates\\reference_tables\\departments_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\departments_tree.html',
   'DATA'),
  ('templates\\reference_tables\\directorates.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\directorates.html',
   'DATA'),
  ('templates\\reference_tables\\edit_agency.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_agency.html',
   'DATA'),
  ('templates\\reference_tables\\edit_blood_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_blood_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_category.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_category.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_level.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_path.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_path.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_path_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_path_level.html',
   'DATA'),
  ('templates\\reference_tables\\edit_department.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_department.html',
   'DATA'),
  ('templates\\reference_tables\\edit_directorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_directorate.html',
   'DATA'),
  ('templates\\reference_tables\\edit_force_classification.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_force_classification.html',
   'DATA'),
  ('templates\\reference_tables\\edit_governorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_governorate.html',
   'DATA'),
  ('templates\\reference_tables\\edit_injury_cause.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_injury_cause.html',
   'DATA'),
  ('templates\\reference_tables\\edit_injury_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_injury_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_location.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_location.html',
   'DATA'),
  ('templates\\reference_tables\\edit_marital_status.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_marital_status.html',
   'DATA'),
  ('templates\\reference_tables\\edit_military_rank.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_military_rank.html',
   'DATA'),
  ('templates\\reference_tables\\edit_participant_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_participant_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_qualification_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_qualification_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_specialization.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_specialization.html',
   'DATA'),
  ('templates\\reference_tables\\edit_training_center.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_training_center.html',
   'DATA'),
  ('templates\\reference_tables\\edit_training_center_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_training_center_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_village.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_village.html',
   'DATA'),
  ('templates\\reference_tables\\force_classifications.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\force_classifications.html',
   'DATA'),
  ('templates\\reference_tables\\governorates.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\governorates.html',
   'DATA'),
  ('templates\\reference_tables\\governorates_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\governorates_tree.html',
   'DATA'),
  ('templates\\reference_tables\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\index.html',
   'DATA'),
  ('templates\\reference_tables\\injury_causes.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\injury_causes.html',
   'DATA'),
  ('templates\\reference_tables\\injury_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\injury_types.html',
   'DATA'),
  ('templates\\reference_tables\\locations.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\locations.html',
   'DATA'),
  ('templates\\reference_tables\\marital_statuses.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\marital_statuses.html',
   'DATA'),
  ('templates\\reference_tables\\military_ranks.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\military_ranks.html',
   'DATA'),
  ('templates\\reference_tables\\participant_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\participant_types.html',
   'DATA'),
  ('templates\\reference_tables\\qualification_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\qualification_types.html',
   'DATA'),
  ('templates\\reference_tables\\specializations.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\specializations.html',
   'DATA'),
  ('templates\\reference_tables\\training_center_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\training_center_types.html',
   'DATA'),
  ('templates\\reference_tables\\training_centers.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\training_centers.html',
   'DATA'),
  ('templates\\reference_tables\\villages.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\villages.html',
   'DATA'),
  ('templates\\reports.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reports.html',
   'DATA'),
  ('templates\\reports_dashboard.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reports_dashboard.html',
   'DATA'),
  ('templates\\search_courses.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\search_courses.html',
   'DATA'),
  ('templates\\search_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\search_results.html',
   'DATA'),
  ('templates\\simple_data\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_data\\index.html',
   'DATA'),
  ('templates\\simple_data_basic.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_data_basic.html',
   'DATA'),
  ('templates\\simple_evaluation_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_evaluation_form.html',
   'DATA'),
  ('templates\\simple_person_list.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_person_list.html',
   'DATA'),
  ('templates\\simple_test.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_test.html',
   'DATA'),
  ('templates\\trainers.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\trainers.html',
   'DATA'),
  ('templates\\users.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\users.html',
   'DATA'),
  ('templates\\video_player.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\video_player.html',
   'DATA'),
  ('templates\\welcome.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome.html',
   'DATA'),
  ('templates\\welcome_direct.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_direct.html',
   'DATA'),
  ('templates\\welcome_final.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_final.html',
   'DATA'),
  ('templates\\welcome_new.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_new.html',
   'DATA'),
  ('templates\\welcome_simple.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_simple.html',
   'DATA')],
 '3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('app', 'E:\\app\\TRINING\\TRINING_EXE\\app.py', 'PYSOURCE')],
 [('pkgutil', 'C:\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\Python313\\Lib\\zipimport.py', 'PYMODULE'),
  ('struct', 'C:\\Python313\\Lib\\struct.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('typing', 'C:\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.resources',
   'C:\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'C:\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'C:\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('argparse', 'C:\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'C:\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'C:\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('statistics', 'C:\\Python313\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python313\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python313\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'C:\\Python313\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'C:\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python313\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python313\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('threading', 'C:\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python313\\Lib\\_threading_local.py', 'PYMODULE'),
  ('string', 'C:\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'C:\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('shutil', 'C:\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'C:\\Python313\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'C:\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python313\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'C:\\Python313\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'C:\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'C:\\Python313\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python313\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'C:\\Python313\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python313\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'C:\\Python313\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python313\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python313\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'C:\\Python313\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'C:\\Python313\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python313\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python313\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python313\\Lib\\email\\utils.py', 'PYMODULE'),
  ('socket', 'C:\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('email._parseaddr', 'C:\\Python313\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'C:\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python313\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('quopri', 'C:\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('importlib.abc', 'C:\\Python313\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python313\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('email', 'C:\\Python313\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python313\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python313\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('json', 'C:\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python313\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python313\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python313\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('pathlib', 'C:\\Python313\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'C:\\Python313\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('glob', 'C:\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('pathlib._abc', 'C:\\Python313\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python313\\Lib\\contextlib.py', 'PYMODULE'),
  ('__future__', 'C:\\Python313\\Lib\\__future__.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('inspect', 'C:\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'C:\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata', 'C:\\Python313\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('ast', 'C:\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python313\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib', 'C:\\Python313\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python313\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'C:\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python313\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'C:\\Python313\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\Python313\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'C:\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python313\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python313\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'C:\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python313\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python313\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'C:\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'C:\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python313\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python313\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'C:\\Python313\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python313\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'C:\\Python313\\Lib\\http\\client.py', 'PYMODULE'),
  ('hmac', 'C:\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'C:\\Python313\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.util', 'C:\\Python313\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python313\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian', 'C:\\Python313\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'C:\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'C:\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock', 'C:\\Python313\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'C:\\Python313\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals', 'C:\\Python313\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'C:\\Python313\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\Python313\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\Python313\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\Python313\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'C:\\Python313\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python313\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'C:\\Python313\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result', 'C:\\Python313\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.util', 'C:\\Python313\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('asyncio', 'C:\\Python313\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\Python313\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'C:\\Python313\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\Python313\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.runners', 'C:\\Python313\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'C:\\Python313\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('asyncio.trsock', 'C:\\Python313\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts', 'C:\\Python313\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python313\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.queues', 'C:\\Python313\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', 'C:\\Python313\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'C:\\Python313\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', 'C:\\Python313\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures', 'C:\\Python313\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\Python313\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig', 'C:\\Python313\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python313\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform', 'C:\\Python313\\Lib\\platform.py', 'PYMODULE'),
  ('_ios_support', 'C:\\Python313\\Lib\\_ios_support.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'C:\\Python313\\Lib\\site.py', 'PYMODULE'),
  ('_pyrepl.main', 'C:\\Python313\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl', 'C:\\Python313\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl.curses', 'C:\\Python313\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('curses', 'C:\\Python313\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'C:\\Python313\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input', 'C:\\Python313\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'C:\\Python313\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.types', 'C:\\Python313\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.commands', 'C:\\Python313\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.pager', 'C:\\Python313\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('tty', 'C:\\Python313\\Lib\\tty.py', 'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader', 'C:\\Python313\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'C:\\Python313\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_colorize', 'C:\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_pyrepl.console', 'C:\\Python313\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('code', 'C:\\Python313\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python313\\Lib\\codeop.py', 'PYMODULE'),
  ('_pyrepl.trace', 'C:\\Python313\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'C:\\Python313\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('_pyrepl.readline', 'C:\\Python313\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('rlcompleter', 'C:\\Python313\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python313\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'C:\\Python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python313\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'C:\\Python313\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'C:\\Python313\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python313\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'C:\\Python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python313\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data', 'C:\\Python313\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python313\\Lib\\configparser.py', 'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'C:\\Python313\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'C:\\Python313\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._types', 'C:\\Python313\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'C:\\Python313\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('tracemalloc', 'C:\\Python313\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python313\\Lib\\stringprep.py', 'PYMODULE'),
  ('datetime', 'C:\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python313\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('werkzeug.security',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.test',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('uuid', 'C:\\Python313\\Lib\\uuid.py', 'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('flask_login',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_login\\__init__.py',
   'PYMODULE'),
  ('flask_login.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_login\\utils.py',
   'PYMODULE'),
  ('werkzeug.local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('flask_login.test_client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_login\\test_client.py',
   'PYMODULE'),
  ('flask.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.app',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.blueprints',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.wrappers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.templating',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('flask.signals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('blinker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('blinker._utilities',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('flask.scaffold',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.json.provider',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.ctx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('flask.sessions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.json.tag',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('itsdangerous',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('flask.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('click.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('flask_login.signals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_login\\signals.py',
   'PYMODULE'),
  ('flask_login.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_login\\mixins.py',
   'PYMODULE'),
  ('flask_login.login_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_login\\login_manager.py',
   'PYMODULE'),
  ('flask_login.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_login\\config.py',
   'PYMODULE'),
  ('flask_login.__about__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_login\\__about__.py',
   'PYMODULE'),
  ('flask',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('sqlite3', 'C:\\Python313\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'C:\\Python313\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.__main__', 'C:\\Python313\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'C:\\Python313\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE')],
 [('python313.dll', 'C:\\Python313\\python313.dll', 'BINARY'),
  ('_decimal.pyd', 'C:\\Python313\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python313\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python313\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python313\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python313\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python313\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python313\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python313\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'C:\\Python313\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Python313\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python313\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\Python313\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Python313\\DLLs\\libffi-8.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python313\\VCRUNTIME140_1.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Python313\\DLLs\\sqlite3.dll', 'BINARY')],
 [],
 [],
 [('database\\training_system.db',
   'E:\\app\\TRINING\\TRINING_EXE\\database\\training_system.db',
   'DATA'),
  ('static\\css\\dashboard-style.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\dashboard-style.css',
   'DATA'),
  ('static\\css\\dashboard.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\dashboard.css',
   'DATA'),
  ('static\\css\\login.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\login.css',
   'DATA'),
  ('static\\css\\main.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\main.css',
   'DATA'),
  ('static\\css\\reports.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\reports.css',
   'DATA'),
  ('static\\css\\smart-search.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\smart-search.css',
   'DATA'),
  ('static\\img\\README.md',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\README.md',
   'DATA'),
  ('static\\img\\default_course.jpg',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\default_course.jpg',
   'DATA'),
  ('static\\img\\training-bg.jpg',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\training-bg.jpg',
   'DATA'),
  ('static\\img\\user-avatar.png',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\user-avatar.png',
   'DATA'),
  ('static\\js\\main.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\js\\main.js',
   'DATA'),
  ('static\\js\\smart-search.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\js\\smart-search.js',
   'DATA'),
  ('static\\libs\\bootstrap\\bootstrap.bundle.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\bootstrap\\bootstrap.bundle.min.js',
   'DATA'),
  ('static\\libs\\bootstrap\\bootstrap.rtl.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\bootstrap\\bootstrap.rtl.min.css',
   'DATA'),
  ('static\\libs\\chartjs\\chart.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\chartjs\\chart.min.js',
   'DATA'),
  ('static\\libs\\datatables\\buttons.bootstrap5.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.bootstrap5.min.css',
   'DATA'),
  ('static\\libs\\datatables\\buttons.bootstrap5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.bootstrap5.min.js',
   'DATA'),
  ('static\\libs\\datatables\\buttons.html5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.html5.min.js',
   'DATA'),
  ('static\\libs\\datatables\\buttons.print.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.print.min.js',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.bootstrap5.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.bootstrap5.min.css',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.bootstrap5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.bootstrap5.min.js',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.buttons.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.buttons.min.js',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.responsive.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.responsive.min.js',
   'DATA'),
  ('static\\libs\\datatables\\jquery.dataTables.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\jquery.dataTables.min.js',
   'DATA'),
  ('static\\libs\\datatables\\responsive.bootstrap5.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\responsive.bootstrap5.min.css',
   'DATA'),
  ('static\\libs\\datatables\\responsive.bootstrap5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\responsive.bootstrap5.min.js',
   'DATA'),
  ('static\\libs\\devextreme\\dx.light.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\devextreme\\dx.light.css',
   'DATA'),
  ('static\\libs\\fontawesome\\all.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\all.min.css',
   'DATA'),
  ('static\\libs\\fontawesome\\webfonts\\fa-brands-400.woff2',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\webfonts\\fa-brands-400.woff2',
   'DATA'),
  ('static\\libs\\fontawesome\\webfonts\\fa-regular-400.woff2',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\webfonts\\fa-regular-400.woff2',
   'DATA'),
  ('static\\libs\\fontawesome\\webfonts\\fa-solid-900.woff2',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\webfonts\\fa-solid-900.woff2',
   'DATA'),
  ('static\\libs\\jquery\\jquery-3.6.0.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\jquery\\jquery-3.6.0.min.js',
   'DATA'),
  ('static\\libs\\libs_config.py',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\libs_config.py',
   'DATA'),
  ('static\\libs\\other\\jspdf.umd.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\other\\jspdf.umd.min.js',
   'DATA'),
  ('static\\libs\\other\\jszip.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\other\\jszip.min.js',
   'DATA'),
  ('static\\libs\\other\\xlsx.full.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\other\\xlsx.full.min.js',
   'DATA'),
  ('static\\libs\\select2\\select2-bootstrap-5-theme.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\select2\\select2-bootstrap-5-theme.min.css',
   'DATA'),
  ('static\\libs\\select2\\select2.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\select2\\select2.min.css',
   'DATA'),
  ('static\\libs\\select2\\select2.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\select2\\select2.min.js',
   'DATA'),
  ('static\\participants_complete_sample.xlsx',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\participants_complete_sample.xlsx',
   'DATA'),
  ('static\\participants_sample.xlsx',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\participants_sample.xlsx',
   'DATA'),
  ('static\\uploads\\courses\\ibb2.bmp',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\courses\\ibb2.bmp',
   'DATA'),
  ('static\\uploads\\courses\\images.jpeg',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\courses\\images.jpeg',
   'DATA'),
  ('static\\uploads\\materials\\1\\22-_720P_HD.mp4',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\materials\\1\\22-_720P_HD.mp4',
   'DATA'),
  ('static\\uploads\\materials\\1\\46_ERP720P_HD.mp4',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\materials\\1\\46_ERP720P_HD.mp4',
   'DATA'),
  ('static\\uploads\\materials\\1\\720P_HD.mp4',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\materials\\1\\720P_HD.mp4',
   'DATA'),
  ('static\\welcome.html',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\welcome.html',
   'DATA'),
  ('static\\قالب_بيانات_الأشخاص_نموذج.xlsx',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\قالب_بيانات_الأشخاص_نموذج.xlsx',
   'DATA'),
  ('templates\\add_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\add_course.html',
   'DATA'),
  ('templates\\add_course_participant.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\add_course_participant.html',
   'DATA'),
  ('templates\\add_material.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\add_material.html',
   'DATA'),
  ('templates\\backup\\backup.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\backup\\backup.html',
   'DATA'),
  ('templates\\backup\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\backup\\index.html',
   'DATA'),
  ('templates\\batches.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\batches.html',
   'DATA'),
  ('templates\\batches\\add_batch.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\batches\\add_batch.html',
   'DATA'),
  ('templates\\batches\\edit_batch.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\batches\\edit_batch.html',
   'DATA'),
  ('templates\\course_details.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_details.html',
   'DATA'),
  ('templates\\course_import_detailed_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_import_detailed_results.html',
   'DATA'),
  ('templates\\course_import_participants.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_import_participants.html',
   'DATA'),
  ('templates\\course_import_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_import_results.html',
   'DATA'),
  ('templates\\course_participants.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_participants.html',
   'DATA'),
  ('templates\\course_participants_with_evaluation.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_participants_with_evaluation.html',
   'DATA'),
  ('templates\\course_reports.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_reports.html',
   'DATA'),
  ('templates\\course_schedule.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_schedule.html',
   'DATA'),
  ('templates\\course_smart_analysis.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_smart_analysis.html',
   'DATA'),
  ('templates\\courses.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\courses.html',
   'DATA'),
  ('templates\\dashboard.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\dashboard.html',
   'DATA'),
  ('templates\\dynamic_evaluation_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\dynamic_evaluation_form.html',
   'DATA'),
  ('templates\\edit_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_course.html',
   'DATA'),
  ('templates\\edit_course_participant.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_course_participant.html',
   'DATA'),
  ('templates\\edit_material.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_material.html',
   'DATA'),
  ('templates\\edit_schedule_item.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_schedule_item.html',
   'DATA'),
  ('templates\\evaluation_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\evaluation_form.html',
   'DATA'),
  ('templates\\evaluation_print.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\evaluation_print.html',
   'DATA'),
  ('templates\\graduates.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\graduates.html',
   'DATA'),
  ('templates\\hello.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\hello.html',
   'DATA'),
  ('templates\\home.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\home.html',
   'DATA'),
  ('templates\\home_new.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\home_new.html',
   'DATA'),
  ('templates\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\index.html',
   'DATA'),
  ('templates\\layout.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\layout.html',
   'DATA'),
  ('templates\\layout_local.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\layout_local.html',
   'DATA'),
  ('templates\\login.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\login.html',
   'DATA'),
  ('templates\\manage_course_participants.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\manage_course_participants.html',
   'DATA'),
  ('templates\\monthly_course_report.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\monthly_course_report.html',
   'DATA'),
  ('templates\\name_analysis_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\name_analysis_course.html',
   'DATA'),
  ('templates\\person_data.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data.html',
   'DATA'),
  ('templates\\person_data\\add_correction.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\add_correction.html',
   'DATA'),
  ('templates\\person_data\\duplicate_names.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\duplicate_names.html',
   'DATA'),
  ('templates\\person_data\\excel_management.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\excel_management.html',
   'DATA'),
  ('templates\\person_data\\manage_corrections.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\manage_corrections.html',
   'DATA'),
  ('templates\\person_data\\name_analysis.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_backup_before_enhancement.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_backup_before_enhancement.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results_backup.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results_backup.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results_backup_before_charts.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results_backup_before_charts.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results_backup_before_font.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results_backup_before_font.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_evaluation_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_evaluation_results.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_evaluation_results_backup_before_charts.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_evaluation_results_backup_before_charts.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_results.html',
   'DATA'),
  ('templates\\person_data\\test_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\test_form.html',
   'DATA'),
  ('templates\\person_data_dashboard.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data_dashboard.html',
   'DATA'),
  ('templates\\person_data_table.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data_table.html',
   'DATA'),
  ('templates\\person_data_table_new.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data_table_new.html',
   'DATA'),
  ('templates\\personal_data\\add.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\add.html',
   'DATA'),
  ('templates\\personal_data\\add_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\add_course.html',
   'DATA'),
  ('templates\\personal_data\\advanced_excel_view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\advanced_excel_view.html',
   'DATA'),
  ('templates\\personal_data\\compare_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\compare_results.html',
   'DATA'),
  ('templates\\personal_data\\devextreme_view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\devextreme_view.html',
   'DATA'),
  ('templates\\personal_data\\excel_devextreme.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\excel_devextreme.html',
   'DATA'),
  ('templates\\personal_data\\excel_view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\excel_view.html',
   'DATA'),
  ('templates\\personal_data\\existing_records.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\existing_records.html',
   'DATA'),
  ('templates\\personal_data\\import_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\import_results.html',
   'DATA'),
  ('templates\\personal_data\\list.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\list.html',
   'DATA'),
  ('templates\\personal_data\\new_add.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\new_add.html',
   'DATA'),
  ('templates\\personal_data\\view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\view.html',
   'DATA'),
  ('templates\\reference_tables\\add_card_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_card_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_category.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_category.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_level.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_path.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_path.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_path_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_path_level.html',
   'DATA'),
  ('templates\\reference_tables\\add_department.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_department.html',
   'DATA'),
  ('templates\\reference_tables\\add_directorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_directorate.html',
   'DATA'),
  ('templates\\reference_tables\\add_force_classification.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_force_classification.html',
   'DATA'),
  ('templates\\reference_tables\\add_governorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_governorate.html',
   'DATA'),
  ('templates\\reference_tables\\add_injury_cause.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_injury_cause.html',
   'DATA'),
  ('templates\\reference_tables\\add_injury_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_injury_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_location.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_location.html',
   'DATA'),
  ('templates\\reference_tables\\add_military_rank.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_military_rank.html',
   'DATA'),
  ('templates\\reference_tables\\add_participant_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_participant_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_qualification_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_qualification_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_specialization.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_specialization.html',
   'DATA'),
  ('templates\\reference_tables\\add_training_center.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_training_center.html',
   'DATA'),
  ('templates\\reference_tables\\add_training_center_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_training_center_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_village.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_village.html',
   'DATA'),
  ('templates\\reference_tables\\agencies.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\agencies.html',
   'DATA'),
  ('templates\\reference_tables\\agencies_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\agencies_tree.html',
   'DATA'),
  ('templates\\reference_tables\\blood_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\blood_types.html',
   'DATA'),
  ('templates\\reference_tables\\card_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\card_types.html',
   'DATA'),
  ('templates\\reference_tables\\course_categories.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_categories.html',
   'DATA'),
  ('templates\\reference_tables\\course_levels.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_levels.html',
   'DATA'),
  ('templates\\reference_tables\\course_path_levels.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_path_levels.html',
   'DATA'),
  ('templates\\reference_tables\\course_paths.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_paths.html',
   'DATA'),
  ('templates\\reference_tables\\course_paths_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_paths_tree.html',
   'DATA'),
  ('templates\\reference_tables\\departments.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\departments.html',
   'DATA'),
  ('templates\\reference_tables\\departments_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\departments_tree.html',
   'DATA'),
  ('templates\\reference_tables\\directorates.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\directorates.html',
   'DATA'),
  ('templates\\reference_tables\\edit_agency.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_agency.html',
   'DATA'),
  ('templates\\reference_tables\\edit_blood_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_blood_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_category.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_category.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_level.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_path.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_path.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_path_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_path_level.html',
   'DATA'),
  ('templates\\reference_tables\\edit_department.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_department.html',
   'DATA'),
  ('templates\\reference_tables\\edit_directorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_directorate.html',
   'DATA'),
  ('templates\\reference_tables\\edit_force_classification.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_force_classification.html',
   'DATA'),
  ('templates\\reference_tables\\edit_governorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_governorate.html',
   'DATA'),
  ('templates\\reference_tables\\edit_injury_cause.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_injury_cause.html',
   'DATA'),
  ('templates\\reference_tables\\edit_injury_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_injury_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_location.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_location.html',
   'DATA'),
  ('templates\\reference_tables\\edit_marital_status.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_marital_status.html',
   'DATA'),
  ('templates\\reference_tables\\edit_military_rank.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_military_rank.html',
   'DATA'),
  ('templates\\reference_tables\\edit_participant_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_participant_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_qualification_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_qualification_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_specialization.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_specialization.html',
   'DATA'),
  ('templates\\reference_tables\\edit_training_center.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_training_center.html',
   'DATA'),
  ('templates\\reference_tables\\edit_training_center_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_training_center_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_village.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_village.html',
   'DATA'),
  ('templates\\reference_tables\\force_classifications.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\force_classifications.html',
   'DATA'),
  ('templates\\reference_tables\\governorates.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\governorates.html',
   'DATA'),
  ('templates\\reference_tables\\governorates_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\governorates_tree.html',
   'DATA'),
  ('templates\\reference_tables\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\index.html',
   'DATA'),
  ('templates\\reference_tables\\injury_causes.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\injury_causes.html',
   'DATA'),
  ('templates\\reference_tables\\injury_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\injury_types.html',
   'DATA'),
  ('templates\\reference_tables\\locations.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\locations.html',
   'DATA'),
  ('templates\\reference_tables\\marital_statuses.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\marital_statuses.html',
   'DATA'),
  ('templates\\reference_tables\\military_ranks.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\military_ranks.html',
   'DATA'),
  ('templates\\reference_tables\\participant_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\participant_types.html',
   'DATA'),
  ('templates\\reference_tables\\qualification_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\qualification_types.html',
   'DATA'),
  ('templates\\reference_tables\\specializations.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\specializations.html',
   'DATA'),
  ('templates\\reference_tables\\training_center_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\training_center_types.html',
   'DATA'),
  ('templates\\reference_tables\\training_centers.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\training_centers.html',
   'DATA'),
  ('templates\\reference_tables\\villages.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\villages.html',
   'DATA'),
  ('templates\\reports.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reports.html',
   'DATA'),
  ('templates\\reports_dashboard.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reports_dashboard.html',
   'DATA'),
  ('templates\\search_courses.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\search_courses.html',
   'DATA'),
  ('templates\\search_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\search_results.html',
   'DATA'),
  ('templates\\simple_data\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_data\\index.html',
   'DATA'),
  ('templates\\simple_data_basic.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_data_basic.html',
   'DATA'),
  ('templates\\simple_evaluation_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_evaluation_form.html',
   'DATA'),
  ('templates\\simple_person_list.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_person_list.html',
   'DATA'),
  ('templates\\simple_test.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_test.html',
   'DATA'),
  ('templates\\trainers.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\trainers.html',
   'DATA'),
  ('templates\\users.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\users.html',
   'DATA'),
  ('templates\\video_player.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\video_player.html',
   'DATA'),
  ('templates\\welcome.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome.html',
   'DATA'),
  ('templates\\welcome_direct.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_direct.html',
   'DATA'),
  ('templates\\welcome_final.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_final.html',
   'DATA'),
  ('templates\\welcome_new.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_new.html',
   'DATA'),
  ('templates\\welcome_simple.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_simple.html',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('flask-2.3.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA'),
  ('flask-2.3.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-2.3.3.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'DATA'),
  ('flask-2.3.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('base_library.zip',
   'E:\\app\\TRINING\\TRINING_EXE\\build\\trining_exe\\base_library.zip',
   'DATA')],
 [('weakref', 'C:\\Python313\\Lib\\weakref.py', 'PYMODULE'),
  ('functools', 'C:\\Python313\\Lib\\functools.py', 'PYMODULE'),
  ('keyword', 'C:\\Python313\\Lib\\keyword.py', 'PYMODULE'),
  ('genericpath', 'C:\\Python313\\Lib\\genericpath.py', 'PYMODULE'),
  ('locale', 'C:\\Python313\\Lib\\locale.py', 'PYMODULE'),
  ('_collections_abc', 'C:\\Python313\\Lib\\_collections_abc.py', 'PYMODULE'),
  ('warnings', 'C:\\Python313\\Lib\\warnings.py', 'PYMODULE'),
  ('enum', 'C:\\Python313\\Lib\\enum.py', 'PYMODULE'),
  ('sre_parse', 'C:\\Python313\\Lib\\sre_parse.py', 'PYMODULE'),
  ('reprlib', 'C:\\Python313\\Lib\\reprlib.py', 'PYMODULE'),
  ('io', 'C:\\Python313\\Lib\\io.py', 'PYMODULE'),
  ('operator', 'C:\\Python313\\Lib\\operator.py', 'PYMODULE'),
  ('abc', 'C:\\Python313\\Lib\\abc.py', 'PYMODULE'),
  ('sre_compile', 'C:\\Python313\\Lib\\sre_compile.py', 'PYMODULE'),
  ('heapq', 'C:\\Python313\\Lib\\heapq.py', 'PYMODULE'),
  ('re._parser', 'C:\\Python313\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'C:\\Python313\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'C:\\Python313\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'C:\\Python313\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'C:\\Python313\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('sre_constants', 'C:\\Python313\\Lib\\sre_constants.py', 'PYMODULE'),
  ('linecache', 'C:\\Python313\\Lib\\linecache.py', 'PYMODULE'),
  ('collections', 'C:\\Python313\\Lib\\collections\\__init__.py', 'PYMODULE'),
  ('types', 'C:\\Python313\\Lib\\types.py', 'PYMODULE'),
  ('codecs', 'C:\\Python313\\Lib\\codecs.py', 'PYMODULE'),
  ('ntpath', 'C:\\Python313\\Lib\\ntpath.py', 'PYMODULE'),
  ('_weakrefset', 'C:\\Python313\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('traceback', 'C:\\Python313\\Lib\\traceback.py', 'PYMODULE'),
  ('stat', 'C:\\Python313\\Lib\\stat.py', 'PYMODULE'),
  ('posixpath', 'C:\\Python313\\Lib\\posixpath.py', 'PYMODULE'),
  ('copyreg', 'C:\\Python313\\Lib\\copyreg.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Python313\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Python313\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Python313\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'C:\\Python313\\Lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'C:\\Python313\\Lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Python313\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Python313\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'C:\\Python313\\Lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Python313\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Python313\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'C:\\Python313\\Lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Python313\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Python313\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Python313\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Python313\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Python313\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Python313\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'C:\\Python313\\Lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Python313\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Python313\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Python313\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Python313\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos', 'C:\\Python313\\Lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'C:\\Python313\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'C:\\Python313\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Python313\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Python313\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Python313\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Python313\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Python313\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Python313\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Python313\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Python313\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Python313\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Python313\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Python313\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048', 'C:\\Python313\\Lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'C:\\Python313\\Lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'C:\\Python313\\Lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'C:\\Python313\\Lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'C:\\Python313\\Lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Python313\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Python313\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Python313\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Python313\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Python313\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Python313\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Python313\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Python313\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Python313\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Python313\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Python313\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Python313\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Python313\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Python313\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Python313\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Python313\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'C:\\Python313\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'C:\\Python313\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Python313\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Python313\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'C:\\Python313\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'C:\\Python313\\Lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Python313\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr', 'C:\\Python313\\Lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'C:\\Python313\\Lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Python313\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Python313\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'C:\\Python313\\Lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'C:\\Python313\\Lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'C:\\Python313\\Lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'C:\\Python313\\Lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'C:\\Python313\\Lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'C:\\Python313\\Lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'C:\\Python313\\Lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'C:\\Python313\\Lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'C:\\Python313\\Lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'C:\\Python313\\Lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'C:\\Python313\\Lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'C:\\Python313\\Lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'C:\\Python313\\Lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'C:\\Python313\\Lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'C:\\Python313\\Lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'C:\\Python313\\Lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'C:\\Python313\\Lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'C:\\Python313\\Lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'C:\\Python313\\Lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'C:\\Python313\\Lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'C:\\Python313\\Lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'C:\\Python313\\Lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'C:\\Python313\\Lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'C:\\Python313\\Lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'C:\\Python313\\Lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'C:\\Python313\\Lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'C:\\Python313\\Lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'C:\\Python313\\Lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'C:\\Python313\\Lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'C:\\Python313\\Lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'C:\\Python313\\Lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'C:\\Python313\\Lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'C:\\Python313\\Lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'C:\\Python313\\Lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'C:\\Python313\\Lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'C:\\Python313\\Lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'C:\\Python313\\Lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'C:\\Python313\\Lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'C:\\Python313\\Lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'C:\\Python313\\Lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap',
   'C:\\Python313\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Python313\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Python313\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'C:\\Python313\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Python313\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'C:\\Python313\\Lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases',
   'C:\\Python313\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'C:\\Python313\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('os', 'C:\\Python313\\Lib\\os.py', 'PYMODULE')])
