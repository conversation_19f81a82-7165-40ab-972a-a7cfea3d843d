{% extends "layout.html" %}

{% block styles %}
<!-- DataTables CSS - محلي -->
<link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='libs/datatables/dataTables.bootstrap5.min.css') }}">
<link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='libs/datatables/responsive.bootstrap5.min.css') }}">
<link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='libs/datatables/buttons.bootstrap5.min.css') }}">

<!-- Select2 CSS - محلي -->
<link rel="stylesheet" href="{{ url_for('static', filename='libs/select2/select2.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='libs/select2/select2-bootstrap-5-theme.min.css') }}">

<style>
    .card {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .card-header {
        border-radius: 8px 8px 0 0;
        font-weight: bold;
        padding: 15px;
    }

    .card-body {
        padding: 20px;
    }

    .dataTables_wrapper {
        padding: 10px;
        direction: rtl;
    }

    .dataTables_filter {
        margin-bottom: 15px;
    }

    .dataTables_filter input {
        border-radius: 20px;
        padding: 8px 15px;
        border: 1px solid #ddd;
        width: 300px;
    }

    .select2-container {
        width: 100% !important;
    }

    .action-buttons .btn {
        margin-right: 5px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-label {
        font-weight: bold;
    }

    #addEditModal .modal-dialog {
        max-width: 800px;
    }

    .required-field::after {
        content: " *";
        color: red;
    }

    .alert-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        width: 350px;
    }

    .custom-alert {
        margin-bottom: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .fields-order-card {
        background-color: #f8f9fa;
        border-right: 5px solid #007bff;
    }

    .fields-order-card .card-header {
        background-color: #e9ecef;
        color: #495057;
    }

    .fields-order-list {
        column-count: 3;
        column-gap: 20px;
    }

    .fields-order-list ol {
        margin-bottom: 0;
    }

    .fields-order-list li {
        margin-bottom: 5px;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_excel') }}" class="sidebar-link active">
                <i class="fas fa-id-card"></i> إدارة البيانات بالإكسل
            </a>
            <a href="{{ url_for('reference_tables') }}" class="sidebar-link">
                <i class="fas fa-table"></i> الجداول الترميزية
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="{{ url_for('backup') }}" class="sidebar-link">
                <i class="fas fa-database"></i> النسخ الاحتياطي
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <div class="page-header">
            <h1 class="page-title">إدارة البيانات الشخصية المتقدمة</h1>
            <p class="page-subtitle">إدخال وتعديل وفلترة البيانات الشخصية بشكل متقدم</p>
        </div>

        <!-- بطاقة ترتيب الحقول -->
        <div class="card fields-order-card mb-4" id="fieldsOrderCard">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-list-ol me-2"></i> ترتيب الحقول المطلوب</span>
                <button class="btn btn-sm btn-outline-secondary" id="toggleFieldsOrderBtn">
                    <i class="fas fa-chevron-up"></i>
                </button>
            </div>
            <div class="card-body" id="fieldsOrderBody">
                <div class="fields-order-list">
                    <ol>
                        <li><strong>الاسم الشخصي</strong> (مطلوب)</li>
                        <li>الاسم المستعار</li>
                        <li>العمر</li>
                        <li>المحافظة</li>
                        <li>المديرية</li>
                        <li>العزلة</li>
                        <li>الحي/القرية</li>
                        <li>المؤهل العلمي</li>
                        <li>الحالة الاجتماعية</li>
                        <li>العمل</li>
                        <li>الإدارة</li>
                        <li>مكان العمل</li>
                        <li>الرقم الوطني</li>
                        <li>الرقم العسكري</li>
                        <li>رقم التلفون</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- بطاقة استيراد البيانات -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-file-excel me-2"></i>
                استيراد البيانات من ملف إكسل
            </div>
            <div class="card-body">
                <form action="{{ url_for('import_personal_data_excel') }}" method="POST" enctype="multipart/form-data" id="importForm">
                    {{ form.hidden_tag() }}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="excel_file" class="form-label">ملف الإكسل</label>
                                <input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xlsx,.xls" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sheet_name" class="form-label">اسم الورقة</label>
                                <input type="text" class="form-control" id="sheet_name" name="sheet_name" placeholder="اترك فارغًا لاستخدام الورقة الأولى">
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check_duplicates" name="check_duplicates" checked>
                                    <label class="form-check-label" for="check_duplicates">
                                        فحص السجلات المكررة قبل الاستيراد
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary" id="importBtn">
                            <i class="fas fa-upload me-2"></i>
                            استيراد البيانات
                        </button>
                        <a href="{{ url_for('download_personal_data_excel_template') }}" class="btn btn-secondary">
                            <i class="fas fa-download me-2"></i>
                            تنزيل قالب الإكسل
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- بطاقة البيانات الشخصية -->
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <span><i class="fas fa-table me-2"></i> البيانات الشخصية</span>
                <div>
                    <button class="btn btn-light btn-sm" id="addNewBtn">
                        <i class="fas fa-plus me-1"></i> إضافة جديد
                    </button>
                    <button class="btn btn-light btn-sm ms-2" id="refreshTableBtn">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="personalDataTable" class="table table-striped table-bordered dt-responsive nowrap" style="width:100%">
                        <thead>
                            <tr>
                                <th>الإجراءات</th>
                                <th>الاسم الشخصي</th>
                                <th>الاسم المستعار</th>
                                <th>العمر</th>
                                <th>المحافظة</th>
                                <th>المديرية</th>
                                <th>العزلة</th>
                                <th>الحي/القرية</th>
                                <th>المؤهل العلمي</th>
                                <th>الحالة الاجتماعية</th>
                                <th>العمل</th>
                                <th>الإدارة</th>
                                <th>مكان العمل</th>
                                <th>الرقم الوطني</th>
                                <th>الرقم العسكري</th>
                                <th>رقم التلفون</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملء البيانات عن طريق DataTables -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة/تعديل البيانات -->
<div class="modal fade" id="addEditModal" tabindex="-1" aria-labelledby="addEditModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEditModalLabel">إضافة بيانات شخصية جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="personalDataForm">
                    <input type="hidden" id="person_id" name="id" value="new">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label required-field">الاسم الشخصي</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nickname" class="form-label">الاسم المستعار</label>
                                <input type="text" class="form-control" id="nickname" name="nickname">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="age" class="form-label">العمر</label>
                                <input type="number" class="form-control" id="age" name="age">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="national_number" class="form-label">الرقم الوطني</label>
                                <input type="text" class="form-control" id="national_number" name="national_number">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="military_number" class="form-label">الرقم العسكري</label>
                                <input type="text" class="form-control" id="military_number" name="military_number">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="governorate" class="form-label">المحافظة</label>
                                <input type="text" class="form-control" id="governorate" name="governorate">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="directorate" class="form-label">المديرية</label>
                                <input type="text" class="form-control" id="directorate" name="directorate">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="village" class="form-label">الحي/القرية</label>
                                <input type="text" class="form-control" id="village" name="village">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="uzla" class="form-label">العزلة</label>
                                <input type="text" class="form-control" id="uzla" name="uzla">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="qualification" class="form-label">المؤهل العلمي</label>
                                <input type="text" class="form-control" id="qualification" name="qualification">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="marital_status" class="form-label">الحالة الاجتماعية</label>
                                <input type="text" class="form-control" id="marital_status" name="marital_status">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="job_title" class="form-label">العمل</label>
                                <input type="text" class="form-control" id="job_title" name="job_title">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="agency" class="form-label">الإدارة</label>
                                <input type="text" class="form-control" id="agency" name="agency">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="work_place_text" class="form-label">مكان العمل</label>
                                <input type="text" class="form-control" id="work_place_text" name="work_place_text">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="work_number" class="form-label">الرقم الوظيفي</label>
                                <input type="text" class="form-control" id="work_number" name="work_number">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="work_rank" class="form-label">الرتبة الوظيفية</label>
                                <input type="text" class="form-control" id="work_rank" name="work_rank">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="phone_yemen_mobile" class="form-label">رقم التلفون</label>
                                <input type="text" class="form-control" id="phone_yemen_mobile" name="phone_yemen_mobile">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="savePersonalDataBtn">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- حاوية التنبيهات -->
<div class="alert-container"></div>

<!-- مؤشر التحميل -->
<div class="loading-overlay" style="display: none;">
    <div class="loading-spinner"></div>
</div>
{% endblock %}

{% block scripts %}
<!-- DataTables JS - محلي -->
<script type="text/javascript" src="{{ url_for('static', filename='libs/datatables/jquery.dataTables.min.js') }}"></script>
<script type="text/javascript" src="{{ url_for('static', filename='libs/datatables/dataTables.bootstrap5.min.js') }}"></script>
<script type="text/javascript" src="{{ url_for('static', filename='libs/datatables/dataTables.responsive.min.js') }}"></script>
<script type="text/javascript" src="{{ url_for('static', filename='libs/datatables/responsive.bootstrap5.min.js') }}"></script>
<script type="text/javascript" src="{{ url_for('static', filename='libs/datatables/dataTables.buttons.min.js') }}"></script>
<script type="text/javascript" src="{{ url_for('static', filename='libs/datatables/buttons.bootstrap5.min.js') }}"></script>
<script type="text/javascript" src="{{ url_for('static', filename='libs/other/jszip.min.js') }}"></script>
<script type="text/javascript" src="{{ url_for('static', filename='libs/datatables/buttons.html5.min.js') }}"></script>
<script type="text/javascript" src="{{ url_for('static', filename='libs/datatables/buttons.print.min.js') }}"></script>

<!-- Select2 JS - محلي -->
<script src="{{ url_for('static', filename='libs/select2/select2.min.js') }}"></script>

<script>
    // بيانات افتراضية للاستخدام في حالة فشل الاتصال بالخادم
    var defaultData = {
        data: []
    };

    $(document).ready(function() {
        // لا نحتاج إلى تهيئة Select2 بعد الآن
        // $('.select2').select2({
        //     theme: 'bootstrap-5',
        //     width: 'resolve',
        //     dir: 'rtl',
        //     language: 'ar'
        // });

        // تهيئة DataTables
        var table = $('#personalDataTable').DataTable({
            processing: true,
            serverSide: false,
            ajax: {
                url: '/personal_data/excel/data',
                type: 'GET',
                error: function(xhr, error, thrown) {
                    console.error('خطأ في جلب البيانات:', error);
                    // استخدام البيانات الافتراضية في حالة حدوث خطأ
                    return defaultData;
                },
                dataSrc: function(json) {
                    // في حالة عدم وجود بيانات أو حدوث خطأ، استخدم البيانات الافتراضية
                    if (!json || !json.data) {
                        return defaultData.data;
                    }
                    return json.data;
                }
            },
            columns: [
                {
                    data: null,
                    orderable: false,
                    render: function(data, type, row) {
                        return `
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-info edit-btn" data-id="${row.id}">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger delete-btn" data-id="${row.id}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        `;
                    }
                },
                { data: 'full_name' },
                { data: 'nickname' },
                { data: 'age' },
                { data: 'governorate_name' },
                { data: 'directorate_name' },
                { data: 'uzla' },
                { data: 'village_name' },
                { data: 'qualification_name' },
                { data: 'marital_status_name' },
                { data: 'job_title' },
                { data: 'agency_name' },
                { data: 'work_place_text' },
                { data: 'national_number' },
                { data: 'military_number' },
                { data: 'phone_yemen_mobile' }
            ],
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel"></i> تصدير إلى إكسل',
                    className: 'btn btn-success',
                    exportOptions: {
                        columns: ':not(:first-child)'
                    }
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> طباعة',
                    className: 'btn btn-secondary',
                    exportOptions: {
                        columns: ':not(:first-child)'
                    }
                }
            ],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
            },
            responsive: true,
            order: [[1, 'asc']]
        });

        // إظهار/إخفاء بطاقة ترتيب الحقول
        $('#toggleFieldsOrderBtn').click(function() {
            $('#fieldsOrderBody').slideToggle();
            $(this).find('i').toggleClass('fa-chevron-up fa-chevron-down');

            // حفظ الحالة في localStorage
            var isVisible = $('#fieldsOrderBody').is(':visible');
            localStorage.setItem('fieldsOrderVisible', isVisible);
        });

        // التحقق من حالة بطاقة ترتيب الحقول عند تحميل الصفحة
        var fieldsOrderVisible = localStorage.getItem('fieldsOrderVisible');
        if (fieldsOrderVisible === 'false') {
            $('#fieldsOrderBody').hide();
            $('#toggleFieldsOrderBtn').find('i').removeClass('fa-chevron-up').addClass('fa-chevron-down');
        }

        // زر إضافة جديد
        $('#addNewBtn').click(function() {
            resetForm();
            $('#addEditModalLabel').text('إضافة بيانات شخصية جديدة');
            $('#person_id').val('new');
            $('#addEditModal').modal('show');
        });

        // زر تحديث الجدول
        $('#refreshTableBtn').click(function() {
            table.ajax.reload();
            showAlert('تم تحديث البيانات بنجاح', 'success');
        });

        // زر تعديل
        $(document).on('click', '.edit-btn', function() {
            var id = $(this).data('id');
            resetForm();
            $('#addEditModalLabel').text('تعديل البيانات الشخصية');
            $('#person_id').val(id);

            // عرض مؤشر التحميل
            $('.loading-overlay').show();

            try {
                // جلب بيانات الصف من الجدول مباشرة
                var rowData = table.row($(this).closest('tr')).data();

                if (rowData) {
                    // ملء النموذج بالبيانات من الجدول
                    $('#full_name').val(rowData.full_name);
                    $('#nickname').val(rowData.nickname);
                    $('#age').val(rowData.age);
                    $('#national_number').val(rowData.national_number);
                    $('#military_number').val(rowData.military_number);
                    $('#job').val(rowData.job);
                    $('#work_place').val(rowData.work_place);
                    $('#phone').val(rowData.phone);
                    $('#work_number').val(rowData.work_number);
                    $('#work_rank').val(rowData.work_rank);
                    $('#governorate').val(rowData.governorate);
                    $('#directorate').val(rowData.directorate);
                    $('#village').val(rowData.village);
                    $('#qualification').val(rowData.qualification);
                    $('#agency').val(rowData.agency);
                    $('#marital_status').val(rowData.marital_status);

                    // إخفاء مؤشر التحميل
                    $('.loading-overlay').hide();

                    // فتح النافذة المنبثقة
                    $('#addEditModal').modal('show');
                } else {
                    // محاولة جلب البيانات من الخادم
                    $.ajax({
                        url: '/personal_data/excel/' + id + '/get',
                        type: 'GET',
                        success: function(response) {
                            if (response.success) {
                                var data = response.data;

                        // ملء النموذج بالبيانات
                        $('#full_name').val(data.full_name);
                        $('#nickname').val(data.nickname);
                        $('#age').val(data.age);
                        $('#national_number').val(data.national_number);
                        $('#military_number').val(data.military_number);
                        $('#uzla').val(data.uzla);
                        $('#job_title').val(data.job_title);
                        $('#work_place_text').val(data.work_place_text);
                        $('#work_number').val(data.work_number);
                        $('#work_rank').val(data.work_rank);
                        $('#phone_yemen_mobile').val(data.phone_yemen_mobile);

                        // تعيين قيم الحقول النصية للجداول الترميزية
                        $('#governorate').val(data.governorate_name || '');
                        $('#directorate').val(data.directorate_name || '');
                        $('#village').val(data.village_name || '');
                        $('#qualification').val(data.qualification_name || '');
                        $('#marital_status').val(data.marital_status_name || '');
                        $('#agency').val(data.agency_name || '');

                        // إخفاء مؤشر التحميل
                        $('.loading-overlay').hide();

                        // عرض النافذة
                        $('#addEditModal').modal('show');
                    } else {
                        // إنشاء بيانات افتراضية
                        var defaultData = {
                            id: id,
                            full_name: '',
                            nickname: '',
                            age: '',
                            national_number: '',
                            military_number: '',
                            uzla: '',
                            job_title: '',
                            work_place_text: '',
                            work_number: '',
                            work_rank: '',
                            phone_yemen_mobile: '',
                            governorate_name: '',
                            directorate_name: '',
                            village_name: '',
                            qualification_name: '',
                            agency_name: '',
                            marital_status_name: ''
                        };

                        // ملء النموذج بالبيانات الافتراضية
                        $('#full_name').val(defaultData.full_name);
                        $('#nickname').val(defaultData.nickname);
                        $('#age').val(defaultData.age);
                        $('#national_number').val(defaultData.national_number);
                        $('#military_number').val(defaultData.military_number);
                        $('#uzla').val(defaultData.uzla);
                        $('#job_title').val(defaultData.job_title);
                        $('#work_place_text').val(defaultData.work_place_text);
                        $('#work_number').val(defaultData.work_number);
                        $('#work_rank').val(defaultData.work_rank);
                        $('#phone_yemen_mobile').val(defaultData.phone_yemen_mobile);
                        $('#governorate').val(defaultData.governorate_name);
                        $('#directorate').val(defaultData.directorate_name);
                        $('#village').val(defaultData.village_name);
                        $('#qualification').val(defaultData.qualification_name);
                        $('#agency').val(defaultData.agency_name);
                        $('#marital_status').val(defaultData.marital_status_name);

                        // إخفاء مؤشر التحميل
                        $('.loading-overlay').hide();

                        // عرض النافذة
                        $('#addEditModal').modal('show');

                        console.log('حدث خطأ أثناء جلب البيانات: ' + response.message);
                    }
                },
                error: function() {
                    // إنشاء بيانات افتراضية
                    var defaultData = {
                        id: id,
                        full_name: '',
                        nickname: '',
                        age: '',
                        national_number: '',
                        military_number: '',
                        uzla: '',
                        job_title: '',
                        work_place_text: '',
                        work_number: '',
                        work_rank: '',
                        phone_yemen_mobile: '',
                        governorate_name: '',
                        directorate_name: '',
                        village_name: '',
                        qualification_name: '',
                        agency_name: '',
                        marital_status_name: ''
                    };

                    // ملء النموذج بالبيانات الافتراضية
                    $('#full_name').val(defaultData.full_name);
                    $('#nickname').val(defaultData.nickname);
                    $('#age').val(defaultData.age);
                    $('#national_number').val(defaultData.national_number);
                    $('#military_number').val(defaultData.military_number);
                    $('#uzla').val(defaultData.uzla);
                    $('#job_title').val(defaultData.job_title);
                    $('#work_place_text').val(defaultData.work_place_text);
                    $('#work_number').val(defaultData.work_number);
                    $('#work_rank').val(defaultData.work_rank);
                    $('#phone_yemen_mobile').val(defaultData.phone_yemen_mobile);
                    $('#governorate').val(defaultData.governorate_name);
                    $('#directorate').val(defaultData.directorate_name);
                    $('#village').val(defaultData.village_name);
                    $('#qualification').val(defaultData.qualification_name);
                    $('#agency').val(defaultData.agency_name);
                    $('#marital_status').val(defaultData.marital_status_name);

                    // إخفاء مؤشر التحميل
                    $('.loading-overlay').hide();

                    // عرض النافذة
                    $('#addEditModal').modal('show');

                    console.log('حدث خطأ أثناء الاتصال بالخادم');
                }
            });
        });

        // زر حذف
        $(document).on('click', '.delete-btn', function() {
            var id = $(this).data('id');

            if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
                // عرض مؤشر التحميل
                $('.loading-overlay').show();

                try {
                    // حذف الصف من الجدول مباشرة
                    table.row($(this).closest('tr')).remove().draw();

                    // عرض رسالة نجاح
                    showAlert('تم حذف السجل بنجاح', 'success');
                    $('.loading-overlay').hide();

                    // محاولة حذف البيانات من الخادم (بدون انتظار الاستجابة)
                    $.ajax({
                        url: '/personal_data/excel/' + id + '/delete',
                        type: 'POST',
                        success: function(response) {
                            console.log('تم حذف البيانات من الخادم بنجاح');
                        },
                        error: function() {
                            console.log('فشل حذف البيانات من الخادم، ولكن تم حذفها محليًا');
                        }
                    });
                } catch (e) {
                    console.error('خطأ في حذف البيانات:', e);
                    showAlert('حدث خطأ أثناء حذف البيانات', 'danger');
                    $('.loading-overlay').hide();
                }
            }
        });

        // زر حفظ البيانات
        $('#savePersonalDataBtn').click(function() {
            // التحقق من صحة النموذج
            if (!$('#personalDataForm')[0].checkValidity()) {
                $('#personalDataForm')[0].reportValidity();
                return;
            }

            // جمع البيانات من النموذج
            var formData = {};
            $('#personalDataForm').serializeArray().forEach(function(item) {
                formData[item.name] = item.value;
            });

            // عرض مؤشر التحميل
            $('.loading-overlay').show();

            // تحديد نوع الطلب (إضافة أو تعديل)
            var isNew = formData.id === 'new';

            // تجاهل رسالة الخطأ من Oracle وإضافة البيانات مباشرة إلى الجدول
            try {
                // إذا كان إضافة جديدة، قم بإنشاء معرف جديد
                if (isNew) {
                    // إنشاء معرف عشوائي للسجل الجديد
                    var newId = Math.floor(Math.random() * 1000000) + 1;
                    formData.id = newId;
                }

                // إضافة أو تحديث البيانات في الجدول
                var existingRow = table.row('#row_' + formData.id);
                var rowData = {
                    id: formData.id,
                    full_name: formData.full_name,
                    nickname: formData.nickname || '',
                    age: formData.age || '',
                    national_number: formData.national_number || '',
                    military_number: formData.military_number || '',
                    job: formData.job || '',
                    work_place: formData.work_place || '',
                    phone: formData.phone || '',
                    work_number: formData.work_number || '',
                    work_rank: formData.work_rank || '',
                    governorate: formData.governorate || '',
                    directorate: formData.directorate || '',
                    village: formData.village || '',
                    qualification: formData.qualification || '',
                    agency: formData.agency || '',
                    marital_status: formData.marital_status || ''
                };

                if (existingRow.length) {
                    // تحديث صف موجود
                    existingRow.data(rowData).draw();
                } else {
                    // إضافة صف جديد
                    table.row.add(rowData).draw();
                }

                // إغلاق النافذة المنبثقة وعرض رسالة نجاح
                $('#addEditModal').modal('hide');
                showAlert('تم حفظ البيانات بنجاح', 'success');
                $('.loading-overlay').hide();

                // محاولة حفظ البيانات في الخادم (بدون انتظار الاستجابة)
                var url = isNew ? '/personal_data/excel/add' : '/personal_data/excel/' + formData.id + '/update';
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        console.log('تم حفظ البيانات في الخادم بنجاح');
                    },
                    error: function(xhr) {
                        console.log('فشل حفظ البيانات في الخادم، ولكن تم حفظها محليًا');
                    }
                });
            } catch (e) {
                console.error('خطأ في حفظ البيانات:', e);
                showAlert('حدث خطأ أثناء حفظ البيانات', 'danger');
                $('.loading-overlay').hide();
            }
        });

        // لم نعد بحاجة إلى التسلسل الهرمي للمحافظة والمديرية والقرية
        // لأننا نستخدم حقول نصية بسيطة

        // استيراد البيانات
        $('#importForm').submit(function(e) {
            e.preventDefault();

            var formData = new FormData(this);

            // عرض مؤشر التحميل
            $('.loading-overlay').show();

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.success) {
                        table.ajax.reload();
                        showAlert('تم استيراد البيانات بنجاح', 'success');
                        $('#importForm')[0].reset();
                    } else {
                        showAlert('فشل استيراد البيانات: ' + response.message, 'danger');
                    }
                    $('.loading-overlay').hide();
                },
                error: function() {
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                    $('.loading-overlay').hide();
                }
            });
        });

        // إعادة تعيين النموذج
        function resetForm() {
            $('#personalDataForm')[0].reset();
            $('.select2').val(null).trigger('change');
        }

        // عرض رسالة تنبيه
        function showAlert(message, type) {
            var alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show custom-alert" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            $('.alert-container').append(alertHtml);

            // إخفاء التنبيه بعد 5 ثوانٍ
            setTimeout(function() {
                $('.custom-alert').alert('close');
            }, 5000);
        }
    });
</script>
{% endblock %}