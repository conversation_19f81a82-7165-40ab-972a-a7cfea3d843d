#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة بيانات سريعة للتقارير
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, Course, PersonData, CourseParticipant
from datetime import datetime, timedelta
import random

def add_quick_data():
    """إضافة بيانات سريعة"""
    
    with app.app_context():
        print("🚀 إضافة بيانات سريعة...")
        
        # إنشاء مستخدم افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(username='admin', email='<EMAIL>', role='admin')
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي")
        
        # حذف البيانات الموجودة
        CourseParticipant.query.delete()
        Course.query.delete()
        PersonData.query.delete()
        db.session.commit()
        
        # إنشاء دورات بسيطة
        courses = [
            {
                'title': 'دورة القيادة العسكرية',
                'course_number': 'MOD-001',
                'description': 'دورة تدريبية في القيادة العسكرية',
                'category': 'عسكرية',
                'level': 'متقدم',
                'agency_id': 1,
                'center_id': 1,
                'participants': 25
            },
            {
                'title': 'دورة الأمن السيبراني',
                'course_number': 'MOI-001',
                'description': 'دورة تدريبية في الأمن السيبراني',
                'category': 'تقنية',
                'level': 'متوسط',
                'agency_id': 2,
                'center_id': 2,
                'participants': 20
            },
            {
                'title': 'دورة طرق التدريس',
                'course_number': 'MOE-001',
                'description': 'دورة تدريبية في طرق التدريس الحديثة',
                'category': 'تعليمية',
                'level': 'أساسي',
                'agency_id': 3,
                'center_id': 3,
                'participants': 30
            },
            {
                'title': 'دورة الطوارئ الطبية',
                'course_number': 'MOH-001',
                'description': 'دورة تدريبية في الطوارئ الطبية',
                'category': 'طبية',
                'level': 'متوسط',
                'agency_id': 4,
                'center_id': 4,
                'participants': 18
            },
            {
                'title': 'دورة المحاسبة الحكومية',
                'course_number': 'MOF-001',
                'description': 'دورة تدريبية في المحاسبة الحكومية',
                'category': 'إدارية',
                'level': 'متقدم',
                'agency_id': 5,
                'center_id': 1,
                'participants': 22
            }
        ]
        
        # أسماء عربية
        names = [
            'أحمد محمد علي الزهراني', 'محمد عبدالله أحمد الغامدي', 'علي سعد محمد القحطاني',
            'عبدالرحمن خالد علي العتيبي', 'سعد عبدالعزيز محمد الدوسري', 'خالد أحمد سعد الشهري',
            'عبدالله محمد علي الحربي', 'فهد سعد أحمد المطيري', 'عبدالعزيز علي محمد العنزي',
            'محمد سعد عبدالله الرشيد', 'أحمد علي خالد السبيعي', 'سعد محمد عبدالرحمن الخالدي',
            'علي أحمد عبدالله الصالح', 'عبدالرحمن سعد محمد البقمي', 'خالد عبدالعزيز أحمد الفهد',
            'محمد علي سعد الناصر', 'أحمد عبدالله خالد الراشد', 'سعد علي محمد العبدالله',
            'عبدالعزيز أحمد سعد الملك', 'علي محمد عبدالرحمن الأمير', 'فهد خالد أحمد الوزير',
            'عبدالله سعد علي الشيخ', 'محمد عبدالعزيز خالد الدكتور', 'أحمد علي عبدالرحمن المهندس',
            'سعد محمد أحمد الأستاذ', 'علي عبدالله سعد الكابتن', 'خالد أحمد محمد الرائد',
            'عبدالرحمن علي سعد المقدم', 'محمد خالد أحمد العقيد', 'أحمد سعد علي العميد'
        ]
        
        created_courses = 0
        total_participants = 0
        
        for course_data in courses:
            print(f"📚 إنشاء دورة: {course_data['title']}")
            
            # إنشاء الدورة
            course = Course(
                title=course_data['title'],
                course_number=course_data['course_number'],
                description=course_data['description'],
                category=course_data['category'],
                level=course_data['level'],
                start_date=datetime.now() - timedelta(days=random.randint(30, 180)),
                end_date=datetime.now() + timedelta(days=random.randint(30, 90)),
                start_date_hijri='1445/05/15',
                end_date_hijri='1445/06/15',
                duration_days=30,
                trainer_id=1,
                agency_id=course_data['agency_id'],
                center_id=course_data['center_id'],
                total_participants=course_data['participants']
            )
            
            db.session.add(course)
            db.session.flush()
            
            # إنشاء المشاركين
            for i in range(course_data['participants']):
                name = names[i % len(names)]
                
                # إنشاء بيانات شخصية
                person = PersonData(
                    full_name=f"{name} - {course_data['course_number']}-{i+1}",
                    national_number=f"{random.randint(100000000, 999999999)}",
                    phone=f"05{random.randint(10000000, 99999999)}",
                    governorate=random.choice(['صنعاء', 'عدن', 'تعز', 'الحديدة', 'حضرموت']),
                    directorate=random.choice(['المديرية الأولى', 'المديرية الثانية', 'المديرية الثالثة']),
                    village=random.choice(['الحي الأول', 'الحي الثاني', 'الحي الثالث'])
                )
                
                db.session.add(person)
                db.session.flush()
                
                # إنشاء مشاركة
                participation = CourseParticipant(
                    course_id=course.id,
                    personal_data_id=person.id,
                    status=random.choice(['مسجل', 'مكتمل', 'منسحب']),
                    entry_date=course.start_date
                )
                
                db.session.add(participation)
            
            created_courses += 1
            total_participants += course_data['participants']
            print(f"   ✅ تم إنشاء {course_data['participants']} مشارك")
        
        # حفظ التغييرات
        db.session.commit()
        
        print(f"\n🎉 تم إنشاء {created_courses} دورة بنجاح!")
        print(f"👥 إجمالي المشاركين: {total_participants}")

if __name__ == "__main__":
    add_quick_data()
