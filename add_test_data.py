from app import app, db, PersonalData, User
from datetime import datetime

# إنشاء بيانات تجريبية
with app.app_context():
    try:
        # البحث عن مستخدم موجود
        user = User.query.first()
        if not user:
            print("لا يوجد مستخدمين في النظام")
            exit()

        # إنشاء بيانات أشخاص تجريبية بحقول أساسية فقط
        test_people = [
            {
                'full_name': 'أحمد محمد علي',
                'national_number': '1234567890',
                'military_number': 'M001'
            },
            {
                'full_name': 'فاطمة أحمد سالم',
                'national_number': '1234567891',
                'military_number': 'M002'
            },
            {
                'full_name': 'محمد عبدالله حسن',
                'national_number': '1234567892',
                'military_number': 'M003'
            }
        ]

        for person_data in test_people:
            try:
                # إنشاء شخص جديد مباشرة
                person = PersonalData(
                    user_id=user.id,
                    full_name=person_data['full_name'],
                    national_number=person_data['national_number'],
                    military_number=person_data['military_number']
                )
                db.session.add(person)
                print(f"تم إضافة: {person_data['full_name']}")
            except Exception as e:
                print(f"خطأ في إضافة {person_data['full_name']}: {str(e)}")
                db.session.rollback()

        db.session.commit()
        print("تم إنشاء البيانات التجريبية بنجاح!")

    except Exception as e:
        print(f"خطأ عام: {str(e)}")
        db.session.rollback()
