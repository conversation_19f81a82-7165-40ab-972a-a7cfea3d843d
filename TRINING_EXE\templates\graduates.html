{% extends "layout.html" %}

{% block styles %}
<style>
    .data-card {
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        border: none;
        margin-bottom: 20px;
    }
    
    .data-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
    
    .data-card-header {
        background: linear-gradient(135deg, #20c997 0%, #0ca678 100%);
        color: white;
        padding: 15px 20px;
        font-weight: bold;
        border-radius: 15px 15px 0 0;
    }
    
    .data-table {
        width: 100%;
    }
    
    .data-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    
    .data-table th, .data-table td {
        padding: 12px 15px;
        text-align: right;
    }
    
    .data-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    
    .data-table tr:hover {
        background-color: #e9ecef;
    }
    
    .btn-view {
        background-color: #28a745;
        color: white;
    }
    
    .btn-sm {
        padding: 5px 10px;
        font-size: 0.875rem;
        border-radius: 5px;
    }
    
    .search-box {
        margin-bottom: 20px;
    }
    
    .search-box .form-control {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
    }
    
    .search-box .btn {
        border-radius: 10px;
        padding: 12px;
    }
    
    .certificate-badge {
        background-color: #20c997;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 0.8rem;
        margin-right: 5px;
    }
    
    .stats-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .stats-icon {
        font-size: 2rem;
        margin-bottom: 10px;
        color: #20c997;
    }
    
    .stats-number {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stats-label {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>الخريجين</h2>
        <a href="{{ url_for('home') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى الصفحة الرئيسية
        </a>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <div class="stats-number">{{ graduates|length }}</div>
                <div class="stats-label">إجمالي الخريجين</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-certificate"></i>
                </div>
                <div class="stats-number">{{ '85' }}</div>
                <div class="stats-label">الشهادات الممنوحة</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-number">{{ '320' }}</div>
                <div class="stats-label">ساعات التدريب</div>
            </div>
        </div>
    </div>
    
    <div class="search-box">
        <div class="input-group">
            <input type="text" class="form-control" id="searchInput" placeholder="بحث عن خريج...">
            <button class="btn btn-primary" type="button">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
    
    <div class="data-card">
        <div class="data-card-header">
            <i class="fas fa-user-graduate me-2"></i> قائمة الخريجين
        </div>
        <div class="card-body">
            {% if graduates %}
            <div class="table-responsive">
                <table class="data-table" id="graduatesTable">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>اسم المتدرب</th>
                            <th>الدورة</th>
                            <th>تاريخ الإكمال</th>
                            <th>الشهادة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for graduate in graduates %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ graduate.personal_data.full_name }}</td>
                            <td>{{ graduate.course.title }}</td>
                            <td>{{ graduate.exit_date.strftime('%Y-%m-%d') if graduate.exit_date else 'غير محدد' }}</td>
                            <td>
                                <span class="certificate-badge">
                                    <i class="fas fa-certificate me-1"></i> تم المنح
                                </span>
                            </td>
                            <td>
                                <a href="{{ url_for('view_personal_data', personal_data_id=graduate.personal_data.id) }}" class="btn btn-sm btn-view">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا يوجد خريجين مسجلين حتى الآن.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // وظيفة البحث في الجدول
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const table = document.getElementById('graduatesTable');
        
        if (searchInput && table) {
            searchInput.addEventListener('keyup', function() {
                const searchText = searchInput.value.toLowerCase();
                const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
                
                for (let i = 0; i < rows.length; i++) {
                    const cells = rows[i].getElementsByTagName('td');
                    let found = false;
                    
                    for (let j = 0; j < cells.length; j++) {
                        const cellText = cells[j].textContent || cells[j].innerText;
                        
                        if (cellText.toLowerCase().indexOf(searchText) > -1) {
                            found = true;
                            break;
                        }
                    }
                    
                    if (found) {
                        rows[i].style.display = '';
                    } else {
                        rows[i].style.display = 'none';
                    }
                }
            });
        }
    });
</script>
{% endblock %}
