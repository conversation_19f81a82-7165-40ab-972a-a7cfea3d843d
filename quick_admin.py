#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User
from werkzeug.security import generate_password_hash

print("🔧 إنشاء مدير جديد...")

with app.app_context():
    try:
        # حذف جميع المديرين الحاليين
        existing_admins = User.query.filter_by(role='admin').all()
        for admin in existing_admins:
            print(f"حذف المدير الحالي: {admin.email}")
            db.session.delete(admin)
        
        # إنشاء مدير جديد
        admin = User(
            username='admin',
            email='<EMAIL>',
            password=generate_password_hash('admin123'),
            role='admin'
        )
        
        db.session.add(admin)
        db.session.commit()
        
        print("✅ تم إنشاء المدير بنجاح!")
        print("📧 البريد الإلكتروني: <EMAIL>")
        print("🔑 كلمة المرور: admin123")
        
        # التحقق من إنشاء المدير
        check_admin = User.query.filter_by(email='<EMAIL>').first()
        if check_admin:
            print("✅ تم التحقق من وجود المدير في قاعدة البيانات")
            print(f"ID: {check_admin.id}")
            print(f"اسم المستخدم: {check_admin.username}")
            print(f"البريد: {check_admin.email}")
            print(f"الدور: {check_admin.role}")
            
            # اختبار كلمة المرور
            from werkzeug.security import check_password_hash
            if check_password_hash(check_admin.password, 'admin123'):
                print("✅ كلمة المرور صحيحة")
            else:
                print("❌ كلمة المرور غير صحيحة")
        else:
            print("❌ فشل في إنشاء المدير")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المدير: {str(e)}")
        db.session.rollback()

print("\n🌐 يمكنك الآن تسجيل الدخول على:")
print("http://127.0.0.1:5000/login")
print("البريد: <EMAIL>")
print("كلمة المرور: admin123")
