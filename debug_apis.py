#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_apis():
    print("🔍 اختبار APIs...")

    session = requests.Session()

    # تسجيل الدخول أولاً
    print("1. تسجيل الدخول...")
    import re

    login_page = session.get('http://localhost:5000/login')
    csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
    csrf_token = csrf_match.group(1) if csrf_match else None

    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }

    if csrf_token:
        login_data['csrf_token'] = csrf_token

    login_response = session.post('http://localhost:5000/login', data=login_data)

    if 'dashboard' in login_response.url:
        print("   ✅ تم تسجيل الدخول")
    else:
        print("   ❌ فشل تسجيل الدخول")
        return

    # اختبار API شجرة النظام
    print("2. اختبار API شجرة النظام...")
    try:
        response = session.get('http://localhost:5000/api/system-tree')
        print(f'   Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'   Tree modules: {len(data)}')
            if data:
                print(f'   First module: {list(data.keys())[0]}')
                print(f'   Sample permissions: {len(data[list(data.keys())[0]].get("permissions", {}))}')
        else:
            print(f'   Error: {response.text[:200]}')
    except Exception as e:
        print(f'   Exception: {e}')

    print()

    # اختبار API الأدوار
    print("3. اختبار API الأدوار...")
    try:
        response = session.get('http://localhost:5000/api/roles')
        print(f'   Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'   Roles count: {len(data)}')
            for role in data:
                print(f'      - {role.get("display_name", role.get("name"))}')
        else:
            print(f'   Error: {response.text[:200]}')
    except Exception as e:
        print(f'   Exception: {e}')

if __name__ == '__main__':
    test_apis()
