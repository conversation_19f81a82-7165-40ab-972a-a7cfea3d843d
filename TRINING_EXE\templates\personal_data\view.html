{% extends "layout.html" %}

{% block styles %}
<style>
    .data-card {
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        border: none;
        margin-bottom: 20px;
    }
    
    .data-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
    
    .data-card-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 15px 20px;
        font-weight: bold;
        border-radius: 15px 15px 0 0;
    }
    
    .data-section {
        margin-bottom: 20px;
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .data-section:last-child {
        border-bottom: none;
    }
    
    .data-section-title {
        font-weight: bold;
        margin-bottom: 15px;
        color: #4a6bff;
    }
    
    .data-row {
        display: flex;
        margin-bottom: 10px;
    }
    
    .data-label {
        font-weight: bold;
        width: 30%;
        color: #6c757d;
    }
    
    .data-value {
        width: 70%;
    }
    
    .btn-edit {
        background-color: #ffc107;
        color: white;
    }
    
    .btn-delete {
        background-color: #dc3545;
        color: white;
    }
    
    .btn-add-course {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 10px 20px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-add-course:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
        color: white;
    }
    
    .btn-back {
        background-color: #6c757d;
        color: white;
    }
    
    .courses-table {
        width: 100%;
        margin-top: 15px;
    }
    
    .courses-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    
    .courses-table th, .courses-table td {
        padding: 12px 15px;
        text-align: right;
    }
    
    .courses-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    
    .courses-table tr:hover {
        background-color: #e9ecef;
    }
    
    .sidebar {
        background-color: #343a40;
        color: white;
        min-height: calc(100vh - 56px);
        padding-top: 20px;
    }
    
    .sidebar-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 10px 15px;
        display: block;
        text-decoration: none;
        transition: all 0.3s;
        border-radius: 5px;
        margin: 5px 10px;
    }
    
    .sidebar-link:hover, .sidebar-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar-link i {
        margin-left: 10px;
        width: 20px;
        text-align: center;
    }
    
    .sidebar-dropdown-menu {
        display: none;
        padding-right: 20px;
    }
    
    .sidebar-dropdown-menu.show {
        display: block;
    }
    
    .dropdown-toggle::after {
        display: inline-block;
        margin-right: 5px;
        vertical-align: middle;
        content: "";
        border-top: 0.3em solid;
        border-left: 0.3em solid transparent;
        border-right: 0.3em solid transparent;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_list') }}" class="sidebar-link active">
                <i class="fas fa-id-card"></i> البيانات الشخصية
            </a>
            <div class="sidebar-dropdown">
                <a href="#" class="sidebar-link dropdown-toggle">
                    <i class="fas fa-table"></i> الجداول الترميزية
                </a>
                <div class="sidebar-dropdown-menu">
                    <a href="{{ url_for('governorates') }}" class="sidebar-link">
                        <i class="fas fa-map-marker-alt"></i> المحافظات
                    </a>
                    <a href="{{ url_for('directorates') }}" class="sidebar-link">
                        <i class="fas fa-map"></i> المديريات
                    </a>
                    <!-- يمكن إضافة المزيد من الروابط للجداول الترميزية الأخرى هنا -->
                </div>
            </div>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </div>
    </div>
    
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>{{ personal_data.full_name }}</h2>
            <div>
                <a href="{{ url_for('personal_data_list') }}" class="btn btn-back">
                    <i class="fas fa-arrow-right me-1"></i> العودة
                </a>
                <a href="{{ url_for('edit_personal_data', personal_data_id=personal_data.id) }}" class="btn btn-edit">
                    <i class="fas fa-edit me-1"></i> تعديل
                </a>
                <a href="{{ url_for('delete_personal_data', personal_data_id=personal_data.id) }}" class="btn btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذه البيانات؟');">
                    <i class="fas fa-trash-alt me-1"></i> حذف
                </a>
            </div>
        </div>
        
        <div class="data-card">
            <div class="data-card-header">
                <i class="fas fa-id-card me-2"></i> البيانات الشخصية
            </div>
            <div class="card-body">
                <div class="data-section">
                    <div class="data-section-title">
                        <i class="fas fa-user me-2"></i> البيانات الأساسية
                    </div>
                    <div class="data-row">
                        <div class="data-label">الاسم:</div>
                        <div class="data-value">{{ personal_data.full_name }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">الاسم المستعار:</div>
                        <div class="data-value">{{ personal_data.nickname or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">الرقم الثلاثي:</div>
                        <div class="data-value">{{ personal_data.triple_number or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">الرقم الوطني:</div>
                        <div class="data-value">{{ personal_data.national_number or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">تاريخ الميلاد:</div>
                        <div class="data-value">{{ personal_data.birth_date.strftime('%Y-%m-%d') if personal_data.birth_date else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">العمر:</div>
                        <div class="data-value">{{ personal_data.age or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">تاريخ الإصدار:</div>
                        <div class="data-value">{{ personal_data.issue_date.strftime('%Y-%m-%d') if personal_data.issue_date else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">جهة الإصدار:</div>
                        <div class="data-value">{{ personal_data.issuing_authority.name if personal_data.issuing_authority else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">عدد الأولاد:</div>
                        <div class="data-value">{{ personal_data.children_count or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">الحالة الاجتماعية:</div>
                        <div class="data-value">{{ personal_data.marital_status.name if personal_data.marital_status else 'غير متوفر' }}</div>
                    </div>
                </div>
                
                <div class="data-section">
                    <div class="data-section-title">
                        <i class="fas fa-home me-2"></i> بيانات السكن
                    </div>
                    <div class="data-row">
                        <div class="data-label">المحافظة:</div>
                        <div class="data-value">{{ personal_data.residence_governorate.name if personal_data.residence_governorate else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">المديرية:</div>
                        <div class="data-value">{{ personal_data.residence_directorate.name if personal_data.residence_directorate else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">الحي/القرية:</div>
                        <div class="data-value">{{ personal_data.residence_village.name if personal_data.residence_village else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">المنزل:</div>
                        <div class="data-value">{{ personal_data.residence_house or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">نوع السكن:</div>
                        <div class="data-value">{{ personal_data.residence_type or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">يمن موبايل:</div>
                        <div class="data-value">{{ personal_data.phone_yemen_mobile or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">YOU:</div>
                        <div class="data-value">{{ personal_data.phone_you or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">سيئون:</div>
                        <div class="data-value">{{ personal_data.phone_sayyon or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">هاتف ثابت:</div>
                        <div class="data-value">{{ personal_data.phone_landline or 'غير متوفر' }}</div>
                    </div>
                </div>
                
                <div class="data-section">
                    <div class="data-section-title">
                        <i class="fas fa-graduation-cap me-2"></i> المؤهلات العلمية
                    </div>
                    <div class="data-row">
                        <div class="data-label">نوع المؤهل:</div>
                        <div class="data-value">{{ personal_data.qualification_type.name if personal_data.qualification_type else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">التخصص:</div>
                        <div class="data-value">{{ personal_data.specialization.name if personal_data.specialization else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">مكان الحصول عليه:</div>
                        <div class="data-value">{{ personal_data.qualification_place or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">تاريخ الحصول عليه:</div>
                        <div class="data-value">{{ personal_data.qualification_date.strftime('%Y-%m-%d') if personal_data.qualification_date else 'غير متوفر' }}</div>
                    </div>
                </div>
                
                <div class="data-section">
                    <div class="data-section-title">
                        <i class="fas fa-briefcase me-2"></i> العمل الحالي
                    </div>
                    <div class="data-row">
                        <div class="data-label">جهة العمل:</div>
                        <div class="data-value">{{ personal_data.work_place.name if personal_data.work_place else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">مسمى العمل:</div>
                        <div class="data-value">{{ personal_data.job_title or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">المحافظة:</div>
                        <div class="data-value">{{ personal_data.work_governorate.name if personal_data.work_governorate else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">المديرية:</div>
                        <div class="data-value">{{ personal_data.work_directorate.name if personal_data.work_directorate else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">الحي/القرية:</div>
                        <div class="data-value">{{ personal_data.work_village.name if personal_data.work_village else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">الوحدة:</div>
                        <div class="data-value">{{ personal_data.work_unit or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">نوع التكليف بالعمل:</div>
                        <div class="data-value">{{ personal_data.assignment_type.name if personal_data.assignment_type else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">تاريخ القرار أو التكليف:</div>
                        <div class="data-value">{{ personal_data.assignment_date.strftime('%Y-%m-%d') if personal_data.assignment_date else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">جهة إصدار القرار أو التكليف:</div>
                        <div class="data-value">{{ personal_data.assignment_authority.name if personal_data.assignment_authority else 'غير متوفر' }}</div>
                    </div>
                </div>
                
                <div class="data-section">
                    <div class="data-section-title">
                        <i class="fas fa-shield-alt me-2"></i> بيانات عسكرية
                    </div>
                    <div class="data-row">
                        <div class="data-label">الرقم العسكري:</div>
                        <div class="data-value">{{ personal_data.military_number or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">فصيلة الدم:</div>
                        <div class="data-value">{{ personal_data.blood_type.name if personal_data.blood_type else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">مقاتل سلاح:</div>
                        <div class="data-value">{{ 'نعم' if personal_data.is_fighter else 'لا' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">ملكية السلاح:</div>
                        <div class="data-value">{{ personal_data.weapon_ownership or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">الرتبة:</div>
                        <div class="data-value">{{ personal_data.military_rank.name if personal_data.military_rank else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">تاريخ الحصول عليها:</div>
                        <div class="data-value">{{ personal_data.rank_date.strftime('%Y-%m-%d') if personal_data.rank_date else 'غير متوفر' }}</div>
                    </div>
                </div>
                
                <div class="data-section">
                    <div class="data-section-title">
                        <i class="fas fa-heartbeat me-2"></i> بيانات صحية
                    </div>
                    <div class="data-row">
                        <div class="data-label">الحالة الصحية:</div>
                        <div class="data-value">{{ personal_data.health_status or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">نوع الإصابة:</div>
                        <div class="data-value">{{ personal_data.injury_type.name if personal_data.injury_type else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">سبب الإصابة:</div>
                        <div class="data-value">{{ personal_data.injury_cause.name if personal_data.injury_cause else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">مكان الإصابة:</div>
                        <div class="data-value">{{ personal_data.injury_place or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">تاريخ الإصابة:</div>
                        <div class="data-value">{{ personal_data.injury_date.strftime('%Y-%m-%d') if personal_data.injury_date else 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">مكان الإصابة في الجسم:</div>
                        <div class="data-value">{{ personal_data.injury_body_location or 'غير متوفر' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">عجز أثناء الإصابة:</div>
                        <div class="data-value">{{ 'نعم' if personal_data.injury_disability else 'لا' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">تعيق عن العمل الحالي:</div>
                        <div class="data-value">{{ 'نعم' if personal_data.injury_hinders_work else 'لا' }}</div>
                    </div>
                    <div class="data-row">
                        <div class="data-label">الجهة التابع لها أثناء الإصابة:</div>
                        <div class="data-value">{{ personal_data.injury_authority or 'غير متوفر' }}</div>
                    </div>
                </div>
                
                <div class="data-section">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="data-section-title">
                            <i class="fas fa-certificate me-2"></i> الدورات السابقة
                        </div>
                        <a href="{{ url_for('add_previous_course', personal_data_id=personal_data.id) }}" class="btn btn-add-course">
                            <i class="fas fa-plus-circle me-1"></i> إضافة دورة
                        </a>
                    </div>
                    
                    {% if previous_courses %}
                    <div class="table-responsive">
                        <table class="courses-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم الدورة</th>
                                    <th>نوع الدورة</th>
                                    <th>مكان الدورة</th>
                                    <th>تاريخ الدورة</th>
                                    <th>مدة الدورة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for course in previous_courses %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ course.course_name }}</td>
                                    <td>{{ course.course_type.name }}</td>
                                    <td>{{ course.course_place or 'غير متوفر' }}</td>
                                    <td>{{ course.course_date.strftime('%Y-%m-%d') if course.course_date else 'غير متوفر' }}</td>
                                    <td>{{ course.course_duration or 'غير متوفر' }}</td>
                                    <td>
                                        <a href="{{ url_for('delete_previous_course', previous_course_id=course.id) }}" class="btn btn-sm btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذه الدورة؟');">
                                            <i class="fas fa-trash-alt"></i> حذف
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i> لا توجد دورات سابقة مسجلة.
                    </div>
                    {% endif %}
                </div>
                
                <div class="data-section">
                    <div class="data-section-title">
                        <i class="fas fa-sticky-note me-2"></i> ملاحظات
                    </div>
                    <div class="data-row">
                        <div class="data-value">{{ personal_data.notes or 'لا توجد ملاحظات' }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تفعيل القائمة المنسدلة للجداول الترميزية
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
        
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const dropdownMenu = this.nextElementSibling;
                dropdownMenu.classList.toggle('show');
            });
        });
    });
</script>
{% endblock %}
