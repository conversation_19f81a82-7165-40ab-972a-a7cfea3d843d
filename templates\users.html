{% extends "layout.html" %}

{% block styles %}
<style>
    .user-card {
        margin-bottom: 20px;
    }

    .user-header {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 25px;
        text-align: center;
        border-radius: 25px 25px 0 0;
    }

    .user-card .user-avatar {
        width: 80px;
        height: 80px;
        margin: 0 auto 10px;
        border: 3px solid white;
    }

    .user-role {
        display: inline-block;
        padding: 8px 15px;
        border-radius: 25px;
        font-size: 0.8rem;
        font-weight: bold;
        background-color: rgba(255, 255, 255, 0.2);
        margin-top: 8px;
        backdrop-filter: blur(10px);
    }

    .user-body {
        padding: 25px;
        position: relative;
        z-index: 2;
    }

    .user-info {
        margin-bottom: 15px;
    }

    .user-info-label {
        font-weight: 600;
        color: #1e40af;
        margin-bottom: 5px;
    }

    .user-actions {
        display: flex;
        justify-content: space-between;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        padding-top: 15px;
    }

    .add-user-btn {
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            <a href="{{ url_for('users') }}" class="sidebar-link active">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <div class="main-container">
            <h2 class="page-title">
                <i class="fas fa-users"></i>
                إدارة المستخدمين
            </h2>

        <div class="filter-section">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="role" class="form-label">الدور</label>
                        <select class="form-select" id="role">
                            <option value="">جميع الأدوار</option>
                            <option value="admin">مدير</option>
                            <option value="trainer">مدرب</option>
                            <option value="trainee">متدرب</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" class="form-control" id="search" placeholder="اسم المستخدم أو البريد الإلكتروني">
                    </div>
                </div>
            </div>
        </div>

        <div class="text-end add-user-btn">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-user-plus me-1"></i> إضافة مستخدم جديد
            </button>
        </div>

        <div class="row">
            {% if trainers %}
                {% for trainer in trainers %}
                <div class="col-md-4">
                    <div class="card user-card">
                        <div class="user-header">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <h5>{{ trainer.username }}</h5>
                            <span class="user-role">مدرب</span>
                        </div>
                        <div class="user-body">
                            <div class="user-info">
                                <p class="user-info-label">البريد الإلكتروني:</p>
                                <p>{{ trainer.email }}</p>
                            </div>
                            <div class="user-info">
                                <p class="user-info-label">تاريخ التسجيل:</p>
                                <p>{{ trainer.created_at.strftime('%Y-%m-%d') }}</p>
                            </div>
                            <div class="user-info">
                                <p class="user-info-label">عدد الدورات:</p>
                                <p>{{ trainer.courses_created|length }}</p>
                            </div>
                            <div class="user-actions">
                                <button class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit me-1"></i> تعديل
                                </button>
                                <button class="btn btn-sm btn-outline-danger">
                                    <i class="fas fa-trash-alt me-1"></i> حذف
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا يوجد مدربين مسجلين حتى الآن.
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- ترقيم الصفحات -->
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item">
                    <a class="page-link" href="#">التالي</a>
                </li>
            </ul>
        </nav>
        </div> <!-- إغلاق main-container -->
    </div>
</div>

<!-- Modal إضافة مستخدم جديد -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="userRole" class="form-label">الدور</label>
                        <select class="form-select" id="userRole" required>
                            <option value="">اختر الدور</option>
                            <option value="admin">مدير</option>
                            <option value="trainer">مدرب</option>
                            <option value="trainee">متدرب</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">إضافة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
