#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح بسيط للمراكز والجهات
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, TrainingCenter, Agency, Course
import random

def fix_centers():
    """إصلاح المراكز والجهات"""
    
    with app.app_context():
        print("🔧 إصلاح المراكز والجهات...")
        
        # إنشاء جهات أساسية
        agencies_data = [
            'وزارة الدفاع',
            'وزارة الداخلية', 
            'وزارة التربية',
            'وزارة الصحة',
            'وزارة المالية'
        ]
        
        for agency_name in agencies_data:
            existing = Agency.query.filter_by(name=agency_name).first()
            if not existing:
                agency = Agency(name=agency_name, code=agency_name[:3])
                db.session.add(agency)
                print(f"✅ أضيفت جهة: {agency_name}")
        
        db.session.commit()
        
        # إنشاء مراكز أساسية
        centers_data = [
            'مركز التدريب المركزي',
            'مركز التدريب المتقدم',
            'مركز التدريب التخصصي',
            'مركز التدريب العام'
        ]
        
        agencies = Agency.query.all()
        
        for center_name in centers_data:
            existing = TrainingCenter.query.filter_by(name=center_name).first()
            if not existing:
                agency = random.choice(agencies)
                center = TrainingCenter(
                    name=center_name,
                    agency_id=agency.id
                )
                db.session.add(center)
                print(f"✅ أضيف مركز: {center_name}")
        
        db.session.commit()
        
        # ربط الدورات بالمراكز
        centers = TrainingCenter.query.all()
        courses = Course.query.all()
        
        for course in courses:
            if not course.center_id:
                center = random.choice(centers)
                course.center_id = center.id
                course.agency_id = center.agency_id
                print(f"✅ ربطت دورة {course.title} بمركز {center.name}")
        
        db.session.commit()
        
        print("🎉 تم الإصلاح بنجاح!")
        
        # طباعة النتائج
        print(f"📊 النتائج:")
        print(f"   - الجهات: {Agency.query.count()}")
        print(f"   - المراكز: {TrainingCenter.query.count()}")
        print(f"   - الدورات المربوطة: {Course.query.filter(Course.center_id.isnot(None)).count()}")

if __name__ == "__main__":
    fix_centers()
