# 🚀 نظام إدارة التدريب - دليل التشغيل السريع

## 🎯 **طرق التشغيل (اختر الأسهل لك)**

### **1. التشغيل الفوري (الأسرع)**
```cmd
python GO.py
```

### **2. التشغيل المبسط مع الإعداد**
```cmd
python SIMPLE_START.py
```

### **3. التشغيل باستخدام ملفات .bat**
```cmd
# للإعداد
SETUP.bat

# للتشغيل
RUN.bat
```

### **4. التشغيل باستخدام PowerShell**
```powershell
# في PowerShell
.\setup_system.ps1    # للإعداد
.\quick_start.ps1     # للتشغيل
```

### **5. التشغيل الموحد**
```powershell
.\start_system.ps1 -Setup    # إعداد
.\start_system.ps1 -Quick    # تشغيل سريع
```

---

## 🔧 **إذا واجهت مشاكل**

### **مشكلة: Python غير مثبت**
- حمل Python من: https://python.org
- تأكد من اختيار "Add to PATH" أثناء التثبيت

### **مشكلة: المكتبات غير مثبتة**
```cmd
pip install flask flask-sqlalchemy flask-login flask-wtf pandas openpyxl xlsxwriter arabic-reshaper python-bidi tqdm email-validator
```

### **مشكلة: البيئة الافتراضية**
```cmd
python -m venv .venv
.venv\Scripts\activate
pip install -r requirements.txt
```

---

## 🌐 **الوصول للنظام**

بعد التشغيل:
- افتح المتصفح
- اذهب إلى: `http://localhost:5000`
- المستخدم الافتراضي: `admin`
- كلمة المرور: `admin`

---

## 🎓 **المميزات الرئيسية**

### **النظام الذكي لتحليل الأسماء**
- تحليل البيانات الشخصية
- تحليل كشوف الدورات
- تحليل التقييمات مع تمييز ذكي

### **إدارة شاملة**
- إدارة الدورات والمشاركين
- نظام التقييمات المتطور
- تقارير Excel مفصلة
- نظام النسخ الاحتياطي التلقائي

---

## 🚀 **ابدأ الآن!**

```cmd
python GO.py
```

**النظام سيكون متاحاً على: http://localhost:5000** 🌐
