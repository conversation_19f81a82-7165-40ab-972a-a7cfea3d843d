{% extends "layout.html" %}

{% block styles %}
<!-- Chart.js CSS - محلي (إذا كان متوفراً) -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/reports.css') }}">
<style>
    .reports-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .reports-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        animation: float 20s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .reports-title {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 15px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .date-filter-section {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e3f2fd;
    }

    .filter-card {
        background: linear-gradient(135deg, #f8f9ff, #e3f2fd);
        border-radius: 15px;
        padding: 25px;
        border: 2px solid #667eea;
        transition: all 0.3s ease;
    }

    .filter-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
    }

    .date-input {
        border: 2px solid #e0e0e0;
        border-radius: 12px;
        padding: 12px 20px;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .date-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }

    .btn-generate {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        padding: 15px 40px;
        border-radius: 25px;
        color: white;
        font-weight: bold;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-generate:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e3f2fd;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
    }

    .stat-label {
        color: #666;
        font-size: 1.1rem;
        font-weight: 500;
    }

    .charts-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }

    .chart-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e3f2fd;
    }

    .chart-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }

    .detailed-tables {
        display: grid;
        gap: 30px;
    }

    .table-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e3f2fd;
        overflow: hidden;
    }

    .table-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 20px;
        margin: -30px -30px 20px -30px;
        font-size: 1.3rem;
        font-weight: bold;
        text-align: center;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }

    .data-table th {
        background: linear-gradient(135deg, #f8f9ff, #e3f2fd);
        color: #333;
        font-weight: bold;
        padding: 15px 12px;
        text-align: center;
        border: 1px solid #ddd;
        font-size: 0.95rem;
    }

    .data-table td {
        padding: 12px;
        text-align: center;
        border: 1px solid #ddd;
        transition: background-color 0.3s ease;
    }

    .data-table tbody tr:hover {
        background-color: #f8f9ff;
    }

    .data-table tbody tr:nth-child(even) {
        background-color: #fafbff;
    }

    .export-section {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-top: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e3f2fd;
        text-align: center;
    }

    .export-buttons {
        display: flex;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .btn-export {
        padding: 15px 30px;
        border-radius: 25px;
        border: none;
        font-weight: bold;
        font-size: 1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
    }

    .btn-export.pdf {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
    }

    .btn-export.excel {
        background: linear-gradient(135deg, #00b894, #00a085);
        color: white;
    }

    .btn-export.print {
        background: linear-gradient(135deg, #6c5ce7, #5f3dc4);
        color: white;
    }

    .btn-export:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .visual-separator {
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 2px;
        margin: 30px 0;
    }

    @media (max-width: 768px) {
        .charts-section {
            grid-template-columns: 1fr;
        }

        .reports-title {
            font-size: 2rem;
        }

        .export-buttons {
            flex-direction: column;
            align-items: center;
        }
    }

    @media print {
        .export-section, .date-filter-section {
            display: none;
        }

        .chart-card, .table-card {
            break-inside: avoid;
            box-shadow: none;
            border: 1px solid #ddd;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Header -->
    <div class="reports-header">
        <div class="reports-title">
            <i class="fas fa-chart-line me-3"></i>تقارير الدورات والمراكز التفاعلية
        </div>
        <div class="fs-5">
            نظام تقارير متطور مع تحليلات بصرية وإحصائيات شاملة
        </div>
    </div>

    <!-- Date Filter Section -->
    <div class="date-filter-section">
        <div class="filter-card">
            <h4 class="text-center mb-4">
                <i class="fas fa-calendar-alt me-2"></i>تحديد فترة التقرير
            </h4>
            <form id="reportForm" method="POST">
                <div class="row align-items-end">
                    <div class="col-md-4">
                        <label class="form-label fw-bold">من تاريخ:</label>
                        <input type="date" name="start_date" class="form-control date-input" required>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label fw-bold">إلى تاريخ:</label>
                        <input type="date" name="end_date" class="form-control date-input" required>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-generate w-100">
                            <i class="fas fa-magic me-2"></i>إنشاء التقرير
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Results Section (Hidden by default) -->
    <div id="resultsSection" style="display: none;">
        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="stat-number" id="totalCourses">0</div>
                <div class="stat-label">إجمالي الدورات</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number" id="totalParticipants">0</div>
                <div class="stat-label">إجمالي المشاركين</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stat-number" id="totalCenters">0</div>
                <div class="stat-label">عدد المراكز</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-medal"></i>
                </div>
                <div class="stat-number" id="totalGraduates">0</div>
                <div class="stat-label">إجمالي الخريجين</div>
            </div>
        </div>

        <div class="visual-separator"></div>

        <!-- Charts Section -->
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-title">
                    <i class="fas fa-chart-pie me-2"></i>توزيع الدورات حسب المستوى
                </div>
                <canvas id="levelChart" width="400" height="300"></canvas>
            </div>

            <div class="chart-card">
                <div class="chart-title">
                    <i class="fas fa-chart-bar me-2"></i>توزيع المشاركين حسب المراكز
                </div>
                <canvas id="centerChart" width="400" height="300"></canvas>
            </div>
        </div>

        <div class="visual-separator"></div>

        <!-- Detailed Tables -->
        <div class="detailed-tables">
            <!-- Courses by Level Table -->
            <div class="table-card">
                <div class="table-header">
                    <i class="fas fa-layer-group me-2"></i>تفصيل الدورات حسب المستوى والمراكز
                </div>
                <div class="table-responsive">
                    <table class="data-table" id="levelTable">
                        <thead>
                            <tr>
                                <th rowspan="2">المركز</th>
                                <th colspan="3">المستوى الأول</th>
                                <th colspan="3">المستوى الثاني</th>
                                <th colspan="3">المستوى الثالث</th>
                                <th rowspan="2">الإجمالي</th>
                            </tr>
                            <tr>
                                <th>عدد الدورات</th>
                                <th>المشاركين</th>
                                <th>الخريجين</th>
                                <th>عدد الدورات</th>
                                <th>المشاركين</th>
                                <th>الخريجين</th>
                                <th>عدد الدورات</th>
                                <th>المشاركين</th>
                                <th>الخريجين</th>
                            </tr>
                        </thead>
                        <tbody id="levelTableBody">
                            <!-- Data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Participants Details Table -->
            <div class="table-card">
                <div class="table-header">
                    <i class="fas fa-users me-2"></i>تفاصيل المشاركين والدورات
                </div>
                <div class="table-responsive">
                    <table class="data-table" id="participantsTable">
                        <thead>
                            <tr>
                                <th>م</th>
                                <th>الجهة</th>
                                <th>اسم المركز أو المعسكر</th>
                                <th>اسم مدير الدورة</th>
                                <th>نوع المستوى</th>
                                <th>مدة الدورة</th>
                                <th>عدد المشاركين</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody id="participantsTableBody">
                            <!-- Data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Export Section -->
        <div class="export-section">
            <h4 class="mb-4">
                <i class="fas fa-download me-2"></i>تصدير وطباعة التقرير
            </h4>
            <div class="export-buttons">
                <button class="btn-export pdf" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i>
                    تصدير PDF
                </button>
                <button class="btn-export excel" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i>
                    تصدير Excel
                </button>
                <button class="btn-export print" onclick="printReport()">
                    <i class="fas fa-print"></i>
                    طباعة التقرير
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js - محلي -->
<script src="{{ url_for('static', filename='libs/chartjs/chart.min.js') }}"></script>
<!-- jsPDF - محلي -->
<script src="{{ url_for('static', filename='libs/other/jspdf.umd.min.js') }}"></script>
<!-- XLSX - محلي -->
<script src="{{ url_for('static', filename='libs/other/xlsx.full.min.js') }}"></script>

<script>
let levelChart, centerChart;

// تحديث الأرقام بتأثير العد التصاعدي
function animateNumber(element, finalNumber) {
    let currentNumber = 0;
    const increment = finalNumber / 50;
    const timer = setInterval(() => {
        currentNumber += increment;
        if (currentNumber >= finalNumber) {
            element.textContent = finalNumber;
            clearInterval(timer);
        } else {
            element.textContent = Math.floor(currentNumber);
        }
    }, 30);
}

// إنشاء الرسوم البيانية
function createCharts(data) {
    // رسم بياني دائري للمستويات
    const levelCtx = document.getElementById('levelChart').getContext('2d');
    if (levelChart) levelChart.destroy();

    levelChart = new Chart(levelCtx, {
        type: 'doughnut',
        data: {
            labels: ['المستوى الأول', 'المستوى الثاني', 'المستوى الثالث'],
            datasets: [{
                data: [data.level1, data.level2, data.level3],
                backgroundColor: [
                    'rgba(102, 126, 234, 0.8)',
                    'rgba(118, 75, 162, 0.8)',
                    'rgba(255, 107, 107, 0.8)'
                ],
                borderColor: [
                    'rgba(102, 126, 234, 1)',
                    'rgba(118, 75, 162, 1)',
                    'rgba(255, 107, 107, 1)'
                ],
                borderWidth: 3,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        font: {
                            size: 14,
                            family: 'Arial'
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                duration: 2000
            }
        }
    });

    // رسم بياني عمودي للمراكز
    const centerCtx = document.getElementById('centerChart').getContext('2d');
    if (centerChart) centerChart.destroy();

    centerChart = new Chart(centerCtx, {
        type: 'bar',
        data: {
            labels: data.centerNames,
            datasets: [{
                label: 'عدد المشاركين',
                data: data.centerParticipants,
                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                borderColor: 'rgba(102, 126, 234, 1)',
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(102, 126, 234, 1)',
                    borderWidth: 1
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

// معالجة إرسال النموذج
document.getElementById('reportForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // إظهار شاشة التحميل
    document.getElementById('loadingOverlay').style.display = 'flex';

    const formData = new FormData(this);

    fetch('/generate_report', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // إخفاء شاشة التحميل
        document.getElementById('loadingOverlay').style.display = 'none';

        if (data.success) {
            // إظهار قسم النتائج
            document.getElementById('resultsSection').style.display = 'block';

            // تحديث الإحصائيات مع التأثير البصري
            animateNumber(document.getElementById('totalCourses'), data.totalCourses);
            animateNumber(document.getElementById('totalParticipants'), data.totalParticipants);
            animateNumber(document.getElementById('totalCenters'), data.totalCenters);
            animateNumber(document.getElementById('totalGraduates'), data.totalGraduates);

            // إنشاء الرسوم البيانية
            createCharts(data.chartData);

            // تحديث الجداول
            updateTables(data.tableData);

            // التمرير إلى النتائج
            document.getElementById('resultsSection').scrollIntoView({
                behavior: 'smooth'
            });
        } else {
            alert('حدث خطأ في إنشاء التقرير: ' + data.message);
        }
    })
    .catch(error => {
        document.getElementById('loadingOverlay').style.display = 'none';
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال بالخادم');
    });
});

// تحديث الجداول
function updateTables(tableData) {
    // تحديث جدول المستويات
    const levelTableBody = document.getElementById('levelTableBody');
    levelTableBody.innerHTML = '';

    tableData.levelData.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td class="fw-bold">${row.center}</td>
            <td>${row.level1_courses}</td>
            <td>${row.level1_participants}</td>
            <td>${row.level1_graduates}</td>
            <td>${row.level2_courses}</td>
            <td>${row.level2_participants}</td>
            <td>${row.level2_graduates}</td>
            <td>${row.level3_courses}</td>
            <td>${row.level3_participants}</td>
            <td>${row.level3_graduates}</td>
            <td class="fw-bold">${row.total}</td>
        `;
        levelTableBody.appendChild(tr);
    });

    // تحديث جدول المشاركين
    const participantsTableBody = document.getElementById('participantsTableBody');
    participantsTableBody.innerHTML = '';

    tableData.participantsData.forEach((row, index) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${index + 1}</td>
            <td>${row.agency}</td>
            <td>${row.center}</td>
            <td>${row.director}</td>
            <td>${row.level}</td>
            <td>${row.duration}</td>
            <td>${row.participants}</td>
            <td>${row.notes}</td>
        `;
        participantsTableBody.appendChild(tr);
    });
}

// تصدير إلى PDF
function exportToPDF() {
    window.print();
}

// تصدير إلى Excel
function exportToExcel() {
    const wb = XLSX.utils.book_new();

    // تصدير جدول المستويات
    const levelTable = document.getElementById('levelTable');
    const levelWS = XLSX.utils.table_to_sheet(levelTable);
    XLSX.utils.book_append_sheet(wb, levelWS, 'تفصيل المستويات');

    // تصدير جدول المشاركين
    const participantsTable = document.getElementById('participantsTable');
    const participantsWS = XLSX.utils.table_to_sheet(participantsTable);
    XLSX.utils.book_append_sheet(wb, participantsWS, 'تفاصيل المشاركين');

    // حفظ الملف
    const today = new Date().toISOString().split('T')[0];
    XLSX.writeFile(wb, `تقرير_الدورات_${today}.xlsx`);
}

// طباعة التقرير
function printReport() {
    window.print();
}

// تحديد التاريخ الافتراضي (آخر شهر)
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

    document.querySelector('input[name="end_date"]').value = today.toISOString().split('T')[0];
    document.querySelector('input[name="start_date"]').value = lastMonth.toISOString().split('T')[0];
});
</script>
{% endblock %}
