{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<!-- تضمين ملف CSS للبحث الذكي -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/smart-search.css') }}">
<style>
    .participant-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        transition: all 0.3s;
    }

    .participant-card:hover {
        background: #e3f2fd;
        border-color: #2196f3;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
    }

    .search-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        color: white;
    }

    .search-input {
        border: none;
        border-radius: 25px;
        padding: 12px 20px;
        font-size: 16px;
        width: 100%;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .search-results {
        background: white;
        border-radius: 10px;
        max-height: 300px;
        overflow-y: auto;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        display: none;
        position: absolute;
        width: 100%;
        z-index: 1000;
    }

    .search-result-item {
        padding: 12px 20px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        transition: background 0.2s;
        color: #333;
        font-size: 14px;
    }

    .search-result-item:hover {
        background: #f0f8ff;
        color: #000;
    }

    .search-result-item strong {
        color: #2c3e50;
        font-weight: 600;
    }

    .search-result-item small {
        color: #6c757d;
        font-size: 12px;
    }

    .action-buttons {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .btn-smart {
        background: linear-gradient(45deg, #4CAF50, #45a049);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
        margin: 5px;
    }

    .btn-smart:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        color: white;
    }

    .btn-import {
        background: linear-gradient(45deg, #FF9800, #F57C00);
    }

    .btn-import:hover {
        box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4);
    }

    .btn-danger-smart {
        background: linear-gradient(45deg, #f44336, #d32f2f);
    }

    .btn-danger-smart:hover {
        box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
    }

    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .modal-content {
        border-radius: 15px;
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }

    .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        border: none;
    }

    .analysis-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
    }

    .analysis-item {
        background: white;
        border-radius: 8px;
        padding: 10px;
        margin: 5px 0;
        border-left: 4px solid #007bff;
    }

    .existing-item {
        border-left-color: #28a745;
    }

    .new-item {
        border-left-color: #ffc107;
    }

    .duplicate-item {
        border-left-color: #dc3545;
    }

    .invalid-item {
        border-left-color: #6c757d;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }

    .participant-badge {
        background: linear-gradient(45deg, #2196F3, #21CBF3);
        color: white;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        margin-left: 10px;
    }

    .search-position {
        position: relative;
    }

    /* 📥 تحسينات زر تحميل النموذج */
    .btn-info {
        background: linear-gradient(135deg, #17a2b8, #138496);
        border: none;
        color: white;
        transition: all 0.3s ease;
        font-weight: 600;
    }

    .btn-info:hover {
        background: linear-gradient(135deg, #138496, #117a8b);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        color: white;
    }

    .btn-info:active {
        transform: translateY(0);
    }

    /* تحسينات الأزرار العامة */
    .action-buttons .btn {
        padding: 12px 20px;
        font-weight: 600;
        border-radius: 8px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }

    .action-buttons .btn i {
        font-size: 1.1rem;
    }

    .action-buttons .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="sidebar rounded">
                <div class="text-center mb-4">
                    <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                    <h5 class="mt-2">{{ current_user.username }}</h5>
                    <p class="badge bg-primary">{{ current_user.role }}</p>
                </div>
                <hr>
                <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a>
                <a href="{{ url_for('courses') }}" class="sidebar-link">
                    <i class="fas fa-graduation-cap"></i> الدورات التدريبية
                </a>
                <a href="{{ url_for('course_details', course_id=course.id) }}" class="sidebar-link active">
                    <i class="fas fa-info-circle"></i> تفاصيل الدورة
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-users me-2"></i>مركز إدارة المشاركين الذكي</h2>
                    <p class="text-muted">دورة: {{ course.title }}</p>
                </div>
                <a href="{{ url_for('course_details', course_id=course.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للدورة
                </a>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-number" id="current-count">{{ current_participants|length }}</div>
                        <div>المشاركين الحاليين</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-number">{{ all_people|length }}</div>
                        <div>إجمالي الأشخاص في القاعدة</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-number">{{ course.duration_days }}</div>
                        <div>مدة الدورة (أيام)</div>
                    </div>
                </div>
            </div>

            <!-- Smart Search System -->
            <div class="search-section">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-search me-2"></i>🚀 البحث الذكي والسريع</h5>
                    </div>
                    <div class="card-body">
                        <div class="smart-search-wrapper">
                            <div class="smart-search-container">
                                <div class="position-relative">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" id="smart-search-input" class="smart-search-input"
                                           placeholder="ابحث بالاسم أو الرقم الوطني أو العسكري... (يدعم البحث المرن والأخطاء الإملائية)">
                                </div>
                                <div id="search-results" class="search-results-container"></div>
                            </div>

                            <!-- Selected Persons Container -->
                            <div id="selected-persons" class="selected-persons-container mt-3" style="display: none;">
                                <div class="no-selection">
                                    <i class="fas fa-users text-muted"></i>
                                    <p class="text-muted">لم يتم اختيار أي أشخاص بعد</p>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <button class="btn btn-success w-100" onclick="addAllSelectedPersons()" id="add-selected-btn" disabled>
                                        <i class="fas fa-plus-circle me-2"></i>إضافة المختارين للدورة
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-outline-secondary w-100" onclick="clearAllSelected()">
                                        <i class="fas fa-trash me-2"></i>مسح الاختيارات
                                    </button>
                                </div>
                            </div>

                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    نصائح: يمكنك البحث بالاسم الجزئي أو حتى مع وجود أخطاء إملائية. النظام يدعم البحث في 200,000+ اسم بسرعة فائقة!
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ url_for('import_participants_page', course_id=course.id) }}"
                           class="btn btn-smart btn-import w-100">
                            <i class="fas fa-brain me-2"></i>🚀 استيراد ذكي
                        </a>
                        <small class="text-muted d-block mt-1 text-center">تحليل ذكي + تصنيف تلقائي</small>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-smart w-100" onclick="openImportModal()"
                                style="background: linear-gradient(45deg, #6c757d, #5a6268);">
                            <i class="fas fa-file-excel me-2"></i>استيراد تقليدي
                        </button>
                        <small class="text-muted d-block mt-1 text-center">استيراد Excel عادي</small>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-info w-100" onclick="downloadSampleExcel()"
                                title="الذهاب لقاعدة البيانات لإدارة الأشخاص وتحميل نموذج Excel"
                                data-bs-toggle="tooltip" data-bs-placement="top">
                            <i class="fas fa-database me-2"></i>قاعدة البيانات
                        </button>
                        <small class="text-muted d-block mt-1 text-center">إدارة الأشخاص</small>
                    </div>
                </div>

                <!-- معلومات الاستيراد الذكي -->
                <div class="alert alert-info mt-3" role="alert">
                    <h6 class="alert-heading">
                        <i class="fas fa-magic me-2"></i>ميزات الاستيراد الذكي الجديد:
                    </h6>
                    <div class="row">
                        <div class="col-md-4">
                            <small>
                                <i class="fas fa-user-plus text-success"></i> <strong>تصنيف تلقائي:</strong><br>
                                جدد، موجودين، مكررين
                            </small>
                        </div>
                        <div class="col-md-4">
                            <small>
                                <i class="fas fa-spell-check text-primary"></i> <strong>تصحيح الأسماء:</strong><br>
                                تصحيح تلقائي للأخطاء الإملائية
                            </small>
                        </div>
                        <div class="col-md-4">
                            <small>
                                <i class="fas fa-mouse-pointer text-warning"></i> <strong>إضافة مرنة:</strong><br>
                                اختيار ما تريد إضافته
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Participants -->
            <div class="card participants-section">
                <div class="card-header">
                    <h5><i class="fas fa-users me-2"></i>المشاركين الحاليين في الدورة</h5>
                </div>
                <div class="card-body" id="participants-list">
                    {% if current_participants %}
                        {% for participant in current_participants %}
                        <div class="participant-card" data-participant-id="{{ participant.id }}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ participant.full_name }}</h6>
                                    <small class="text-muted">
                                        الرقم الوطني: {{ participant.national_number }}
                                        {% if participant.military_number %}
                                        | الرقم العسكري: {{ participant.military_number }}
                                        {% endif %}
                                    </small>
                                    {% if participant.military_rank %}
                                    <span class="participant-badge">{{ participant.military_rank.name }}</span>
                                    {% endif %}
                                </div>
                                <button class="btn btn-danger-smart btn-sm"
                                        onclick="removeParticipant({{ participant.id }}, '{{ participant.full_name }}')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <p>لا يوجد مشاركين في الدورة حتى الآن</p>
                            <p>استخدم البحث الذكي أو استيراد Excel لإضافة مشاركين</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Excel Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-file-excel me-2"></i>استيراد المشاركين من Excel</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="upload-section">
                    <div class="mb-3">
                        <label class="form-label">اختر ملف Excel</label>
                        <input type="file" class="form-control" id="excel-file" accept=".xlsx,.xls">
                        <small class="form-text text-muted">
                            يجب أن يحتوي الملف على الأعمدة المطلوبة بنفس ترتيب قالب بيانات الأشخاص
                            <br><a href="/person_data_table" class="text-primary" target="_blank">
                                <i class="fas fa-database me-1"></i>الذهاب لقاعدة البيانات لتحميل النموذج
                            </a>
                        </small>
                    </div>
                    <button class="btn btn-smart" onclick="analyzeExcel()">
                        <i class="fas fa-search me-2"></i>تحليل الملف
                    </button>
                </div>

                <div id="analysis-section" style="display: none;">
                    <h6>نتائج التحليل:</h6>
                    <div id="analysis-results"></div>
                    <div class="mt-3">
                        <button class="btn btn-smart" onclick="processImport()">
                            <i class="fas fa-check me-2"></i>تأكيد الإضافة
                        </button>
                        <button class="btn btn-secondary" onclick="resetImport()">
                            <i class="fas fa-redo me-2"></i>إعادة تحليل
                        </button>
                    </div>
                </div>

                <div class="loading-spinner" id="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري المعالجة...</span>
                    </div>
                    <p class="mt-2">جاري معالجة البيانات...</p>
                </div>
            </div>
        </div>
    </div>
</div>




{% endblock %}

{% block scripts %}
<!-- تضمين ملف JavaScript للبحث الذكي -->
<script src="{{ url_for('static', filename='js/smart-search.js') }}"></script>
<script>
let analysisData = null;

// تهيئة نظام البحث الذكي
document.addEventListener('DOMContentLoaded', function() {
    // إنشاء مثيل البحث الذكي مع إعدادات قوية
    window.smartSearch = new SmartPersonSearch({
        searchInputId: 'smart-search-input',
        resultsContainerId: 'search-results',
        selectedContainerId: 'selected-persons',
        enableStats: true,
        enableHistory: true,
        maxResults: 100,  // عرض 100 نتيجة!
        threshold: 25,    // عتبة منخفضة جداً
        debounceDelay: 150, // استجابة سريعة
        minQueryLength: 1   // البحث من حرف واحد
    });

    // مستمعي الأحداث المخصصة
    document.addEventListener('smartSearch:personAdded', function(e) {
        const person = e.detail.personData;
        console.log('تم إضافة شخص للاختيار:', person.name);
        updateAddButton();
        showSelectedPersonsContainer();
    });

    document.addEventListener('smartSearch:personRemoved', function(e) {
        const person = e.detail.personData;
        console.log('تم إزالة شخص من الاختيار:', person.name);
        updateAddButton();
        hideSelectedPersonsIfEmpty();
    });

    document.addEventListener('smartSearch:allPersonsCleared', function(e) {
        console.log('تم مسح جميع الاختيارات');
        updateAddButton();
        hideSelectedPersonsContainer();
    });
});

// إظهار حاوي الأشخاص المختارين
function showSelectedPersonsContainer() {
    const container = document.getElementById('selected-persons');
    container.style.display = 'block';
}

// إخفاء حاوي الأشخاص المختارين إذا كان فارغاً
function hideSelectedPersonsIfEmpty() {
    if (smartSearch && smartSearch.selectedPersons.size === 0) {
        hideSelectedPersonsContainer();
    }
}

// إخفاء حاوي الأشخاص المختارين
function hideSelectedPersonsContainer() {
    const container = document.getElementById('selected-persons');
    container.style.display = 'none';
}

// تحديث حالة زر الإضافة
function updateAddButton() {
    const addBtn = document.getElementById('add-selected-btn');
    const selectedCount = smartSearch ? smartSearch.selectedPersons.size : 0;

    if (selectedCount > 0) {
        addBtn.disabled = false;
        addBtn.innerHTML = `<i class="fas fa-plus-circle me-2"></i>إضافة المختارين للدورة (${selectedCount})`;
    } else {
        addBtn.disabled = true;
        addBtn.innerHTML = '<i class="fas fa-plus-circle me-2"></i>إضافة المختارين للدورة';
    }
}

// إضافة جميع الأشخاص المختارين للدورة
async function addAllSelectedPersons() {
    console.log('🚀 بدء عملية إضافة المشاركين...');

    if (!smartSearch || smartSearch.selectedPersons.size === 0) {
        showAlert('لم يتم اختيار أي أشخاص', 'warning');
        // التأكد من إلغاء الحماية في حالة الخروج المبكر
        isOperationInProgress = false;
        return;
    }

    const selectedPersons = smartSearch.getSelectedPersons();
    const addBtn = document.getElementById('add-selected-btn');

    console.log(`📊 عدد الأشخاص المختارين: ${selectedPersons.length}`);

    // 🔒 تفعيل الحماية من إخفاء النتائج
    isOperationInProgress = true;
    console.log('🔒 تم تفعيل حماية العمليات');

    // إضافة مؤشر بصري للحماية
    const resultsContainer = document.getElementById('search-results');
    if (resultsContainer) {
        resultsContainer.style.border = '2px solid #28a745';
        resultsContainer.style.boxShadow = '0 0 10px rgba(40, 167, 69, 0.3)';
    }

    // تعطيل الزر أثناء المعالجة
    addBtn.disabled = true;
    addBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';

    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    const successNames = [];

    for (const person of selectedPersons) {
        try {
            console.log(`🔄 محاولة إضافة: ${person.name} (ID: ${person.id})`);

            const csrfToken = getCSRFToken();
            const headers = {
                'Content-Type': 'application/json',
            };

            if (csrfToken) {
                headers['X-CSRFToken'] = csrfToken;
            }

            const response = await fetch(`/course/{{ course.id }}/add_participant_api`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({person_id: person.id})
            });

            console.log(`📡 استجابة الخادم لـ ${person.name}:`, response.status);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`📋 بيانات الاستجابة لـ ${person.name}:`, data);

            if (data.success) {
                successCount++;
                successNames.push(person.name);
                addParticipantToList(data.person);
                smartSearch.removeSelectedPerson(person.id);
                console.log(`✅ تم إضافة ${person.name} بنجاح`);
            } else {
                errorCount++;
                errors.push(`${person.name}: ${data.message}`);
                console.log(`❌ فشل إضافة ${person.name}: ${data.message}`);
            }
        } catch (error) {
            errorCount++;
            errors.push(`${person.name}: خطأ في الاتصال (${error.message})`);
            console.error(`❌ خطأ في إضافة ${person.name}:`, error);
        }
    }

    // إظهار النتائج التفصيلية
    if (successCount > 0) {
        showAlert(`✅ تم إضافة ${successCount} شخص بنجاح: ${successNames.join(', ')}`, 'success');
        updateParticipantCount();
    }

    if (errorCount > 0) {
        showAlert(`❌ فشل في إضافة ${errorCount} شخص: ${errors.join(' | ')}`, 'danger');
    }

    // إعادة تفعيل الزر
    addBtn.disabled = false;
    addBtn.innerHTML = '<i class="fas fa-plus-circle me-2"></i>إضافة المختارين للدورة';
    updateAddButton();

    // 🔓 إلغاء الحماية من إخفاء النتائج
    isOperationInProgress = false;
    console.log('🔓 تم إلغاء حماية العمليات');

    // إزالة المؤشر البصري للحماية
    if (resultsContainer) {
        resultsContainer.style.border = '';
        resultsContainer.style.boxShadow = '';
    }

    console.log(`🏁 انتهت العملية - نجح: ${successCount}, فشل: ${errorCount}`);
}

// مسح جميع الاختيارات
function clearAllSelected() {
    if (smartSearch) {
        smartSearch.clearAllSelected();
    }
}

// إضافة شخص للدورة
function addPersonToCourse(personId, personName) {
    // 🔒 تفعيل الحماية من إخفاء النتائج
    isOperationInProgress = true;
    console.log(`🔒 تفعيل حماية العمليات لإضافة ${personName}`);

    const csrfToken = getCSRFToken();
    const headers = {
        'Content-Type': 'application/json',
    };

    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    fetch(`/course/{{ course.id }}/add_participant_api`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({person_id: personId})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            addParticipantToList(data.person);
            updateParticipantCount();
            document.getElementById('search-results').style.display = 'none';
            document.getElementById('smart-search').value = '';
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        showAlert('حدث خطأ في الإضافة', 'danger');
        console.error('Error:', error);
    })
    .finally(() => {
        // 🔓 إلغاء الحماية من إخفاء النتائج
        isOperationInProgress = false;
        console.log(`🔓 إلغاء حماية العمليات لإضافة ${personName}`);
    });
}

// 📥 الذهاب لصفحة قاعدة البيانات لتحميل النموذج
function downloadSampleExcel() {
    // إظهار رسالة توجيه
    showAlert('جاري التوجه لصفحة قاعدة البيانات...', 'info');

    // التوجه لصفحة قاعدة البيانات في تبويب جديد
    window.open('/person_data_table', '_blank');

    // رسالة توضيحية
    setTimeout(() => {
        showAlert('تم فتح صفحة قاعدة البيانات! يمكنك تحميل النموذج من هناك 📊', 'success');
    }, 500);
}



// 🎨 تحديث معاينة البيانات أثناء الكتابة
function updateDataPreview() {
    const previewDiv = document.getElementById('data-preview');

    // جمع البيانات من النموذج
    const formData = {
        full_name: document.getElementById('full_name').value.trim(),
        nickname: document.getElementById('nickname').value.trim(),
        age: document.getElementById('age').value.trim(),
        governorate: document.getElementById('governorate').value.trim(),
        directorate: document.getElementById('directorate').value.trim(),
        uzla: document.getElementById('uzla').value.trim(),
        village: document.getElementById('village').value.trim(),
        qualification: document.getElementById('qualification').value.trim(),
        marital_status: document.getElementById('marital_status').value.trim(),
        job: document.getElementById('job').value.trim(),
        agency: document.getElementById('agency').value.trim(),
        work_place: document.getElementById('work_place').value.trim(),
        national_number: document.getElementById('national_number').value.trim(),
        military_number: document.getElementById('military_number').value.trim(),
        phone: document.getElementById('phone').value.trim()
    };

    // إنشاء معاينة HTML
    let previewHTML = '<div class="row">';

    // الحقول المطلوبة
    if (formData.full_name) {
        previewHTML += `<div class="col-md-6"><strong>الاسم الشخصي:</strong> ${formData.full_name}</div>`;
    }
    if (formData.national_number) {
        previewHTML += `<div class="col-md-6"><strong>الرقم الوطني:</strong> ${formData.national_number}</div>`;
    }

    // الحقول الاختيارية
    if (formData.nickname) {
        previewHTML += `<div class="col-md-6"><strong>الاسم المستعار:</strong> ${formData.nickname}</div>`;
    }
    if (formData.age) {
        previewHTML += `<div class="col-md-6"><strong>العمر:</strong> ${formData.age}</div>`;
    }
    if (formData.governorate) {
        previewHTML += `<div class="col-md-6"><strong>المحافظة:</strong> ${formData.governorate}</div>`;
    }
    if (formData.directorate) {
        previewHTML += `<div class="col-md-6"><strong>المديرية:</strong> ${formData.directorate}</div>`;
    }
    if (formData.uzla) {
        previewHTML += `<div class="col-md-6"><strong>العزلة:</strong> ${formData.uzla}</div>`;
    }
    if (formData.village) {
        previewHTML += `<div class="col-md-6"><strong>الحي/القرية:</strong> ${formData.village}</div>`;
    }
    if (formData.qualification) {
        previewHTML += `<div class="col-md-6"><strong>المؤهل العلمي:</strong> ${formData.qualification}</div>`;
    }
    if (formData.marital_status) {
        previewHTML += `<div class="col-md-6"><strong>الحالة الاجتماعية:</strong> ${formData.marital_status}</div>`;
    }
    if (formData.job) {
        previewHTML += `<div class="col-md-6"><strong>العمل:</strong> ${formData.job}</div>`;
    }
    if (formData.agency) {
        previewHTML += `<div class="col-md-6"><strong>الإدارة:</strong> ${formData.agency}</div>`;
    }
    if (formData.work_place) {
        previewHTML += `<div class="col-md-6"><strong>مكان العمل:</strong> ${formData.work_place}</div>`;
    }
    if (formData.military_number) {
        previewHTML += `<div class="col-md-6"><strong>الرقم العسكري:</strong> ${formData.military_number}</div>`;
    }
    if (formData.phone) {
        previewHTML += `<div class="col-md-6"><strong>رقم التلفون:</strong> ${formData.phone}</div>`;
    }

    previewHTML += '</div>';

    // إظهار المعاينة أو رسالة فارغة
    if (formData.full_name || formData.national_number) {
        previewDiv.innerHTML = previewHTML;
    } else {
        previewDiv.innerHTML = '<div class="text-muted text-center"><i class="fas fa-keyboard me-2"></i>أدخل البيانات لرؤية المعاينة...</div>';
    }
}



// إضافة مشارك لقائمة العرض
function addParticipantToList(person) {
    const participantsList = document.getElementById('participants-list');

    // إزالة رسالة "لا يوجد مشاركين" إذا كانت موجودة
    const emptyMessage = participantsList.querySelector('.text-center.text-muted');
    if (emptyMessage) {
        emptyMessage.remove();
    }

    const participantCard = document.createElement('div');
    participantCard.className = 'participant-card';
    participantCard.setAttribute('data-participant-id', person.id);
    participantCard.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h6 class="mb-1">${person.full_name}</h6>
                <small class="text-muted">
                    الرقم الوطني: ${person.national_number}
                    ${person.military_number ? '| الرقم العسكري: ' + person.military_number : ''}
                </small>
            </div>
            <button class="btn btn-danger-smart btn-sm"
                    onclick="removeParticipant(${person.id}, '${person.full_name}')">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    participantsList.appendChild(participantCard);
}

// إزالة مشارك من الدورة
function removeParticipant(personId, personName) {
    if (!confirm(`هل أنت متأكد من إزالة ${personName} من الدورة؟`)) {
        return;
    }

    const csrfToken = getCSRFToken();
    const headers = {
        'Content-Type': 'application/json',
    };

    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    fetch(`/course/{{ course.id }}/remove_participant_api`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({person_id: personId})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            document.querySelector(`[data-participant-id="${personId}"]`).remove();
            updateParticipantCount();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        showAlert('حدث خطأ في الإزالة', 'danger');
        console.error('Error:', error);
    });
}

// تحديث عداد المشاركين
function updateParticipantCount() {
    const count = document.querySelectorAll('.participant-card').length;
    document.getElementById('current-count').textContent = count;
}

// فتح نافذة الاستيراد
function openImportModal() {
    new bootstrap.Modal(document.getElementById('importModal')).show();
}











// تحليل ملف Excel
function analyzeExcel() {
    console.log('DEBUG: analyzeExcel function called');

    const fileInput = document.getElementById('excel-file');
    const file = fileInput.files[0];

    console.log('DEBUG: File input:', fileInput);
    console.log('DEBUG: Selected file:', file);

    if (!file) {
        console.log('DEBUG: No file selected');
        showAlert('يرجى اختيار ملف Excel', 'warning');
        return;
    }

    const formData = new FormData();
    formData.append('excel_file', file);

    console.log('DEBUG: FormData created, file appended');
    console.log('DEBUG: Sending request to:', `/course/{{ course.id }}/import_participants_api`);

    showLoading(true);

    fetch(`/course/{{ course.id }}/import_participants_api`, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('DEBUG: Response received:', response);
        console.log('DEBUG: Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('DEBUG: Response data:', data);
        showLoading(false);

        if (data.success) {
            console.log('DEBUG: Success! Analysis data:', data.analysis);
            analysisData = data.analysis;
            displayAnalysisResults(data.analysis, data.total_rows);
            document.getElementById('upload-section').style.display = 'none';
            document.getElementById('analysis-section').style.display = 'block';
        } else {
            console.log('DEBUG: Error in response:', data.message);
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.log('DEBUG: Fetch error:', error);
        showLoading(false);
        showAlert('حدث خطأ في تحليل الملف', 'danger');
        console.error('Error:', error);
    });
}

// عرض نتائج التحليل
function displayAnalysisResults(analysis, totalRows) {
    const resultsDiv = document.getElementById('analysis-results');

    let html = `<div class="alert alert-info">تم تحليل ${totalRows} سجل من الملف</div>`;

    // الموجودين في قاعدة البيانات
    if (analysis.existing_in_db.length > 0) {
        html += `
            <div class="analysis-section">
                <h6 class="text-success"><i class="fas fa-check-circle me-2"></i>موجود في القاعدة (${analysis.existing_in_db.length})</h6>
                ${analysis.existing_in_db.map(person => `
                    <div class="analysis-item existing-item">
                        <input type="checkbox" class="form-check-input me-2" value="${person.id}" checked>
                        <strong>${person.name}</strong> - ${person.national_id}
                    </div>
                `).join('')}
            </div>
        `;
    }

    // الأشخاص الجدد
    if (analysis.new_people.length > 0) {
        html += `
            <div class="analysis-section">
                <h6 class="text-warning"><i class="fas fa-plus-circle me-2"></i>أشخاص جدد (${analysis.new_people.length})</h6>
                ${analysis.new_people.map((person, index) => `
                    <div class="analysis-item new-item">
                        <input type="checkbox" class="form-check-input me-2" value="${index}" checked>
                        <strong>${person.name}</strong> - ${person.national_id}
                    </div>
                `).join('')}
            </div>
        `;
    }

    // المسجلين بالفعل في الدورة
    if (analysis.existing_in_course.length > 0) {
        html += `
            <div class="analysis-section">
                <h6 class="text-info"><i class="fas fa-info-circle me-2"></i>مسجل بالفعل في الدورة (${analysis.existing_in_course.length})</h6>
                ${analysis.existing_in_course.map(person => `
                    <div class="analysis-item duplicate-item">
                        <strong>${person.name}</strong> - ${person.national_id}
                    </div>
                `).join('')}
            </div>
        `;
    }

    // البيانات غير الصحيحة
    if (analysis.invalid_data.length > 0) {
        html += `
            <div class="analysis-section">
                <h6 class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>بيانات غير صحيحة (${analysis.invalid_data.length})</h6>
                ${analysis.invalid_data.map(item => `
                    <div class="analysis-item invalid-item">
                        السطر ${item.row}: ${item.name || 'غير محدد'} - ${item.reason}
                    </div>
                `).join('')}
            </div>
        `;
    }

    resultsDiv.innerHTML = html;
}

// معالجة الاستيراد
function processImport() {
    if (!analysisData) {
        showAlert('لا توجد بيانات للمعالجة', 'warning');
        return;
    }

    // جمع المحدد للإضافة
    const existingToAdd = [];
    const newPeopleToAdd = [];

    // الموجودين في القاعدة
    document.querySelectorAll('.existing-item input[type="checkbox"]:checked').forEach(checkbox => {
        existingToAdd.push(parseInt(checkbox.value));
    });

    // الأشخاص الجدد
    document.querySelectorAll('.new-item input[type="checkbox"]:checked').forEach(checkbox => {
        const index = parseInt(checkbox.value);
        newPeopleToAdd.push(analysisData.new_people[index]);
    });

    showLoading(true);

    fetch(`/course/{{ course.id }}/process_import_api`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            existing_to_add: existingToAdd,
            new_people: newPeopleToAdd
        })
    })
    .then(response => response.json())
    .then(data => {
        showLoading(false);

        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('importModal')).hide();
            setTimeout(() => {
                location.reload(); // إعادة تحميل الصفحة لعرض المشاركين الجدد
            }, 1000);
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        showLoading(false);
        showAlert('حدث خطأ في المعالجة', 'danger');
        console.error('Error:', error);
    });
}

// إعادة تعيين الاستيراد
function resetImport() {
    document.getElementById('upload-section').style.display = 'block';
    document.getElementById('analysis-section').style.display = 'none';
    document.getElementById('excel-file').value = '';
    analysisData = null;
}

// إظهار/إخفاء مؤشر التحميل
function showLoading(show) {
    document.getElementById('loading-spinner').style.display = show ? 'block' : 'none';
}

// عرض التنبيهات
function showAlert(message, type) {
    // إزالة أي تنبيهات موجودة
    const existingAlerts = document.querySelectorAll('.custom-alert');
    existingAlerts.forEach(alert => alert.remove());

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show custom-alert`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-radius: 8px;
        border: none;
    `;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// متغير لتتبع حالة العمليات النشطة
let isOperationInProgress = false;

// دالة للحصول على CSRF token
function getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : null;
}

// إخفاء نتائج البحث عند النقر خارجها (مع حماية للعمليات النشطة)
document.addEventListener('click', function(event) {
    // عدم إخفاء النتائج إذا كانت هناك عملية جارية
    if (isOperationInProgress) {
        console.log('🔒 منع إخفاء النتائج - عملية جارية');
        return;
    }

    const searchContainer = document.querySelector('.search-position');
    const selectedContainer = document.getElementById('selected-persons');
    const addButton = document.getElementById('add-selected-btn');

    // عدم إخفاء النتائج إذا تم النقر على:
    // 1. منطقة البحث
    // 2. منطقة الأشخاص المختارين
    // 3. زر الإضافة
    // 4. أي زر إضافة في النتائج
    if (searchContainer && searchContainer.contains(event.target)) {
        return;
    }

    if (selectedContainer && selectedContainer.contains(event.target)) {
        return;
    }

    if (addButton && addButton.contains(event.target)) {
        return;
    }

    // التحقق من أزرار الإضافة في النتائج
    if (event.target.closest('.add-person-btn') || event.target.closest('[onclick*="addSelectedPerson"]')) {
        return;
    }

    // إخفاء النتائج فقط إذا لم تكن هناك عملية جارية
    const resultsContainer = document.getElementById('search-results');
    if (resultsContainer) {
        resultsContainer.style.display = 'none';
        console.log('🔍 تم إخفاء نتائج البحث');
    }
});

// تفعيل tooltips عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل جميع tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}