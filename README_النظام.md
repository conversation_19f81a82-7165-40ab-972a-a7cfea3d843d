# نظام التدريب والتأهيل

نظام شامل لإدارة التدريب والتأهيل مع إمكانيات متقدمة لإدارة البيانات والتقارير.

## 🚀 التشغيل السريع

### للمبتدئين (يثبت المتطلبات تلقائياً)
```bash
python تشغيل_النظام_المحدث.py
```

### للمستخدمين المتقدمين
```bash
python تشغيل_سريع.py
```

### على Windows (ملف Batch)
```cmd
تشغيل_النظام.bat
```

## 📋 المتطلبات

- Python 3.8 أو أحدث
- المكتبات المطلوبة (سيتم تثبيتها تلقائياً):
  - Flask
  - Flask-SQLAlchemy
  - Flask-Login
  - Flask-WTF
  - pandas
  - numpy
  - arabic-reshaper
  - python-bidi

## 🔑 بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin

## 🌟 الميزات الرئيسية

### 👥 إدارة البيانات الشخصية
- إضافة وتعديل بيانات الأشخاص
- استيراد من ملفات Excel
- تصحيح الأسماء المكررة والمتشابهة
- بحث متقدم وتصفية ذكية
- تصدير البيانات بصيغ متعددة

### 📚 إدارة الدورات التدريبية
- إنشاء وإدارة الدورات
- إضافة المشاركين والمدربين
- تتبع الحضور والغياب
- إدارة المواد التدريبية
- جدولة الدورات

### 📊 التقارير والإحصائيات
- تقارير شاملة عن الدورات
- إحصائيات المشاركين
- تقارير قابلة للتصدير (Excel, PDF)
- رسوم بيانية تفاعلية
- تحليلات متقدمة

### 🔐 الأمان والصلاحيات
- نظام تسجيل دخول آمن
- صلاحيات متعددة المستويات
- حماية CSRF
- تشفير كلمات المرور

## 🌐 الصفحات المهمة

بعد تشغيل النظام:

### الصفحات الرئيسية
- **الصفحة الرئيسية:** http://localhost:5000
- **تسجيل الدخول:** http://localhost:5000/login

### إدارة البيانات
- **لوحة البيانات:** http://localhost:5000/person_data
- **جدول البيانات:** http://localhost:5000/person_data_table
- **قائمة الأشخاص:** http://localhost:5000/simple_person_list

### التقارير
- **لوحة التقارير:** http://localhost:5000/reports/dashboard
- **تقارير الدورات:** http://localhost:5000/course_reports

### الإدارة
- **إدارة المستخدمين:** http://localhost:5000/users
- **الجداول المرجعية:** http://localhost:5000/reference_tables

## 📁 هيكل المشروع

```
نظام التدريب/
├── app.py                          # التطبيق الرئيسي
├── تشغيل_النظام_المحدث.py           # ملف التشغيل المحسن
├── تشغيل_سريع.py                   # تشغيل سريع
├── تشغيل_النظام.bat                # ملف Windows Batch
├── دليل_التشغيل.md                 # دليل التشغيل المفصل
├── training_system.db              # قاعدة البيانات
├── templates/                      # قوالب HTML
│   ├── layout.html
│   ├── home.html
│   ├── person_data/
│   ├── reports/
│   └── ...
├── static/                         # الملفات الثابتة
│   ├── css/
│   ├── js/
│   ├── img/
│   └── uploads/
├── person_data_routes.py           # مسارات إدارة البيانات
├── reports_generator.py            # مولد التقارير
├── backup_utils.py                 # أدوات النسخ الاحتياطي
└── requirements.txt                # قائمة المتطلبات
```

## 🔧 حل المشاكل الشائعة

### خطأ في استيراد الوحدات
```bash
pip install flask flask-sqlalchemy flask-login flask-wtf pandas numpy arabic-reshaper python-bidi
```

### المنفذ 5000 مستخدم
1. أغلق أي برنامج يستخدم المنفذ 5000
2. أو أعد تشغيل الحاسوب

### قاعدة البيانات فارغة
- النظام سينشئ قاعدة البيانات تلقائياً عند التشغيل الأول

### مشاكل الترميز العربي
- تأكد من تثبيت `arabic-reshaper` و `python-bidi`

## 📖 دليل الاستخدام

### إضافة بيانات جديدة
1. اذهب إلى "إدارة البيانات"
2. اختر "إضافة شخص جديد"
3. املأ البيانات المطلوبة
4. احفظ

### استيراد من Excel
1. اذهب إلى "إدارة البيانات"
2. اختر "استيراد من Excel"
3. ارفع الملف
4. راجع البيانات واحفظ

### إنشاء دورة تدريبية
1. اذهب إلى "الدورات"
2. اختر "إضافة دورة جديدة"
3. املأ تفاصيل الدورة
4. أضف المشاركين

### عرض التقارير
1. اذهب إلى "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد المعايير
4. اعرض أو صدّر التقرير

## 🔄 النسخ الاحتياطي

النظام يدعم إنشاء نسخ احتياطية تلقائية:
- اذهب إلى "الإدارة" > "النسخ الاحتياطي"
- اختر البيانات المراد نسخها
- حدد مكان الحفظ
- انقر "إنشاء نسخة احتياطية"

## 🆕 التحديثات

للحصول على أحدث إصدار:
1. احتفظ بنسخة احتياطية من قاعدة البيانات
2. استبدل الملفات القديمة بالجديدة
3. شغل النظام مرة أخرى

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. راجع ملف `دليل_التشغيل.md`
2. تحقق من رسائل الخطأ في وحدة التحكم
3. تأكد من تثبيت جميع المتطلبات
4. تأكد من إصدار Python (3.8+)

## ⚠️ ملاحظات مهمة

- هذا النظام مصمم للاستخدام المحلي
- لا تستخدمه على الإنترنت بدون إعدادات أمان إضافية
- احتفظ بنسخ احتياطية دورية من قاعدة البيانات
- غيّر كلمة مرور المدير الافتراضية

## 📄 الترخيص

هذا النظام مطور للاستخدام الداخلي. جميع الحقوق محفوظة.

---

**تم تطوير هذا النظام باستخدام Flask و Python مع دعم كامل للغة العربية**
