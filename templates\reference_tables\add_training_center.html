{% extends "layout.html" %}

{% block content %}
<div class="container">
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">إضافة مركز تدريبي</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                {{ form.hidden_tag() }}
                <div class="mb-3">
                    {{ form.name.label(class="form-label") }}
                    {% if form.name.errors %}
                        {{ form.name(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.name.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.name(class="form-control") }}
                    {% endif %}
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        {{ form.center_type_id.label(class="form-label") }}
                        {% if form.center_type_id.errors %}
                            {{ form.center_type_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.center_type_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.center_type_id(class="form-select") }}
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {{ form.location_id.label(class="form-label") }}
                        {% if form.location_id.errors %}
                            {{ form.location_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.location_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.location_id(class="form-select") }}
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        {{ form.governorate_id.label(class="form-label") }}
                        {% if form.governorate_id.errors %}
                            {{ form.governorate_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.governorate_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.governorate_id(class="form-select") }}
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {{ form.directorate_id.label(class="form-label") }}
                        {% if form.directorate_id.errors %}
                            {{ form.directorate_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.directorate_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.directorate_id(class="form-select") }}
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        {{ form.agency_id.label(class="form-label") }}
                        {% if form.agency_id.errors %}
                            {{ form.agency_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.agency_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.agency_id(class="form-select") }}
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {{ form.capacity.label(class="form-label") }}
                        {% if form.capacity.errors %}
                            {{ form.capacity(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.capacity.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.capacity(class="form-control") }}
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        {{ form.is_ready(class="form-check-input") }}
                        {{ form.is_ready.label(class="form-check-label") }}
                    </div>
                </div>
                
                <div class="mb-3 not-ready-reason" style="display: none;">
                    {{ form.not_ready_reason.label(class="form-label") }}
                    {% if form.not_ready_reason.errors %}
                        {{ form.not_ready_reason(class="form-control is-invalid", rows=3) }}
                        <div class="invalid-feedback">
                            {% for error in form.not_ready_reason.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.not_ready_reason(class="form-control", rows=3) }}
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    {{ form.notes.label(class="form-label") }}
                    {% if form.notes.errors %}
                        {{ form.notes(class="form-control is-invalid", rows=3) }}
                        <div class="invalid-feedback">
                            {% for error in form.notes.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.notes(class="form-control", rows=3) }}
                    {% endif %}
                </div>
                
                <div class="d-flex justify-content-between">
                    {{ form.submit(class="btn btn-primary") }}
                    <a href="{{ url_for('training_centers') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // الحصول على عناصر المحافظة والمديرية
        const governorateSelect = document.getElementById('governorate_id');
        const directorateSelect = document.getElementById('directorate_id');
        
        // إضافة مستمع حدث لتغيير المحافظة
        governorateSelect.addEventListener('change', function() {
            const governorateId = this.value;
            
            // إفراغ قائمة المديريات
            directorateSelect.innerHTML = '<option value="0">اختر المديرية</option>';
            
            // إذا تم اختيار محافظة
            if (governorateId != 0) {
                // إرسال طلب AJAX للحصول على المديريات
                fetch(`/api/directorates/${governorateId}`)
                    .then(response => response.json())
                    .then(data => {
                        // إضافة المديريات إلى القائمة المنسدلة
                        data.forEach(directorate => {
                            const option = document.createElement('option');
                            option.value = directorate.id;
                            option.textContent = directorate.name;
                            directorateSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error fetching directorates:', error));
            }
        });
        
        // التحكم في ظهور حقل سبب عدم الجهوزية
        const isReadyCheckbox = document.getElementById('is_ready');
        const notReadyReasonDiv = document.querySelector('.not-ready-reason');
        
        function toggleNotReadyReason() {
            if (isReadyCheckbox.checked) {
                notReadyReasonDiv.style.display = 'none';
            } else {
                notReadyReasonDiv.style.display = 'block';
            }
        }
        
        // تنفيذ الدالة عند تحميل الصفحة
        toggleNotReadyReason();
        
        // إضافة مستمع حدث لتغيير حالة الجهوزية
        isReadyCheckbox.addEventListener('change', toggleNotReadyReason);
    });
</script>
{% endblock %}
