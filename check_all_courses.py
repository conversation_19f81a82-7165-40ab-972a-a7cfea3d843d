#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, CourseParticipant, Course, PersonData

def check_all_courses():
    with app.app_context():
        print("🔍 فحص جميع الدورات والمشاركين...")
        print("=" * 60)
        
        # جلب جميع الدورات
        courses = Course.query.all()
        print(f"إجمالي عدد الدورات: {len(courses)}")
        
        for course in courses:
            participants_count = CourseParticipant.query.filter_by(course_id=course.id).count()
            print(f"\nالدورة ID: {course.id}")
            print(f"رقم الدورة: {course.course_number}")
            print(f"اسم الدورة: {course.title}")
            print(f"عدد المشاركين: {participants_count}")
            
            if participants_count > 0:
                participants = CourseParticipant.query.filter_by(course_id=course.id).all()
                print("المشاركين:")
                for i, p in enumerate(participants, 1):
                    name = p.personal_data.full_name if p.personal_data else f"ID: {p.personal_data_id}"
                    print(f"  {i}. {name}")
            print("-" * 40)

if __name__ == '__main__':
    check_all_courses()
