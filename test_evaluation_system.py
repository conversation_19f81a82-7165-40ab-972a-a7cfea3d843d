#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام التقييم
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Course, CourseParticipant

def test_evaluation_system():
    """اختبار نظام التقييم"""
    
    with app.app_context():
        print("🔍 اختبار نظام التقييم...")
        
        # فحص الجداول
        try:
            result = db.engine.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%evaluation%'").fetchall()
            print(f"📊 جداول التقييم الموجودة:")
            for table in result:
                print(f"   - {table[0]}")
            
            # فحص معايير التقييم
            criteria = db.engine.execute("SELECT * FROM evaluation_criteria").fetchall()
            print(f"\n📝 معايير التقييم ({len(criteria)}):")
            for criterion in criteria:
                print(f"   - {criterion['name']} (الدرجة العظمى: {criterion['max_score']})")
            
            # فحص القوالب
            templates = db.engine.execute("SELECT * FROM evaluation_templates").fetchall()
            print(f"\n📋 قوالب التقييم ({len(templates)}):")
            for template in templates:
                print(f"   - {template['name']} ({'افتراضي' if template['is_default'] else 'مخصص'})")
            
            # فحص الدورات المتاحة للتقييم
            courses = Course.query.all()
            print(f"\n📚 الدورات المتاحة للتقييم ({len(courses)}):")
            for course in courses[:5]:  # أول 5 دورات
                participants_count = CourseParticipant.query.filter_by(course_id=course.id).count()
                print(f"   - {course.title} ({participants_count} مشارك)")
            
            print(f"\n✅ نظام التقييم جاهز للاستخدام!")
            
        except Exception as e:
            print(f"❌ خطأ في فحص نظام التقييم: {str(e)}")

def create_sample_evaluation():
    """إنشاء تقييم تجريبي"""
    
    with app.app_context():
        print("\n🧪 إنشاء تقييم تجريبي...")
        
        try:
            # البحث عن أول دورة ومشارك
            course = Course.query.first()
            if not course:
                print("❌ لا توجد دورات")
                return
            
            participant = CourseParticipant.query.filter_by(course_id=course.id).first()
            if not participant:
                print("❌ لا يوجد مشاركين في الدورة")
                return
            
            print(f"📚 الدورة: {course.title}")
            print(f"👤 المشارك: {participant.personal_data.full_name if participant.personal_data else 'غير محدد'}")
            
            # إنشاء تقييم تجريبي
            evaluation_data = {
                'course_id': course.id,
                'participant_id': participant.id,
                'evaluator_id': 1,  # افتراض أن المستخدم رقم 1 موجود
                'total_score': 95.5,
                'percentage': 83.0,
                'grade': 'جيد جداً',
                'notes': 'أداء ممتاز في جميع المعايير',
                'is_final': 1
            }
            
            # حفظ التقييم
            db.engine.execute("""
                INSERT INTO participant_evaluations 
                (course_id, participant_id, evaluator_id, total_score, percentage, grade, notes, is_final)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                evaluation_data['course_id'],
                evaluation_data['participant_id'],
                evaluation_data['evaluator_id'],
                evaluation_data['total_score'],
                evaluation_data['percentage'],
                evaluation_data['grade'],
                evaluation_data['notes'],
                evaluation_data['is_final']
            ))
            
            # الحصول على معرف التقييم
            result = db.engine.execute("SELECT last_insert_rowid()").fetchone()
            evaluation_id = result[0]
            
            # إضافة تفاصيل التقييم
            criteria_scores = [
                (evaluation_id, 1, 18.0, 'حضور منتظم'),  # الحضور والانضباط
                (evaluation_id, 2, 19.0, 'مشاركة فعالة'),  # المشاركة والتفاعل
                (evaluation_id, 3, 27.0, 'أداء جيد في الاختبار'),  # الاختبار النظري
                (evaluation_id, 4, 26.5, 'تطبيق ممتاز'),  # الاختبار العملي
                (evaluation_id, 6, 14.0, 'مهارات تواصل جيدة')  # المهارات الشخصية
            ]
            
            for eval_id, criteria_id, score, notes in criteria_scores:
                db.engine.execute("""
                    INSERT INTO evaluation_details (evaluation_id, criteria_id, score, notes)
                    VALUES (?, ?, ?, ?)
                """, (eval_id, criteria_id, score, notes))
            
            print(f"✅ تم إنشاء تقييم تجريبي برقم: {evaluation_id}")
            
            # عرض التقييم المحفوظ
            saved_evaluation = db.engine.execute("""
                SELECT pe.*, pd.full_name
                FROM participant_evaluations pe
                JOIN course_participant cp ON pe.participant_id = cp.id
                JOIN person_data pd ON cp.personal_data_id = pd.id
                WHERE pe.id = ?
            """, (evaluation_id,)).fetchone()
            
            if saved_evaluation:
                print(f"📊 تفاصيل التقييم:")
                print(f"   - المشارك: {saved_evaluation['full_name']}")
                print(f"   - الدرجة الإجمالية: {saved_evaluation['total_score']}")
                print(f"   - النسبة المئوية: {saved_evaluation['percentage']}%")
                print(f"   - التقدير: {saved_evaluation['grade']}")
                print(f"   - الملاحظات: {saved_evaluation['notes']}")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء التقييم التجريبي: {str(e)}")

if __name__ == "__main__":
    test_evaluation_system()
    create_sample_evaluation()
