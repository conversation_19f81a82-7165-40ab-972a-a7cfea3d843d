{% extends "layout.html" %}

{% block title %}لوحة التحكم{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-pages.css') }}">
<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.chart-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
}

.recent-activities {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title">
            <i class="fas fa-tachometer-alt icon-lg"></i>
            لوحة التحكم
        </div>
        <div class="page-subtitle">
            نظرة شاملة على إحصائيات النظام والأنشطة الحديثة
        </div>
    </div>

    <div class="content-wrapper">
        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stats-card primary">
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-number" id="totalUsers">{{ total_users or 0 }}</div>
                <div class="stats-label">إجمالي المستخدمين</div>
            </div>
            
            <div class="stats-card success">
                <div class="stats-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="stats-number" id="totalCourses">{{ total_courses or 0 }}</div>
                <div class="stats-label">إجمالي الدورات</div>
            </div>
            
            <div class="stats-card warning">
                <div class="stats-icon">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <div class="stats-number" id="totalParticipants">{{ total_participants or 0 }}</div>
                <div class="stats-label">إجمالي المشاركين</div>
            </div>
            
            <div class="stats-card info">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-number" id="completionRate">{{ completion_rate or 0 }}%</div>
                <div class="stats-label">معدل الإنجاز</div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="row g-4 mb-4">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-bar text-primary"></i>
                        إحصائيات الدورات الشهرية
                    </h5>
                    <canvas id="coursesChart" height="300"></canvas>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-pie-chart text-success"></i>
                        توزيع المستخدمين
                    </h5>
                    <canvas id="usersChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- الأنشطة الحديثة -->
        <div class="recent-activities">
            <h5 class="mb-3">
                <i class="fas fa-clock text-info"></i>
                الأنشطة الحديثة
            </h5>
            
            <div id="recentActivities">
                <!-- سيتم تحميل الأنشطة هنا -->
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                    <p class="text-muted mt-2">جاري تحميل الأنشطة...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تحديث الإحصائيات
function updateStats() {
    fetch('/api/dashboard-stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('totalUsers').textContent = data.total_users || 0;
            document.getElementById('totalCourses').textContent = data.total_courses || 0;
            document.getElementById('totalParticipants').textContent = data.total_participants || 0;
            document.getElementById('completionRate').textContent = (data.completion_rate || 0) + '%';
        })
        .catch(error => console.error('خطأ في تحميل الإحصائيات:', error));
}

// تحميل الأنشطة الحديثة
function loadRecentActivities() {
    fetch('/api/recent-activities')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('recentActivities');
            if (data.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">لا توجد أنشطة حديثة</p>';
                return;
            }
            
            container.innerHTML = data.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon bg-${activity.type}">
                        <i class="${activity.icon} text-white"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${activity.title}</div>
                        <div class="text-muted small">${activity.description}</div>
                    </div>
                    <div class="text-muted small">${activity.time}</div>
                </div>
            `).join('');
        })
        .catch(error => {
            console.error('خطأ في تحميل الأنشطة:', error);
            document.getElementById('recentActivities').innerHTML = 
                '<p class="text-danger text-center">خطأ في تحميل الأنشطة</p>';
        });
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateStats();
    loadRecentActivities();
    
    // تحديث البيانات كل 30 ثانية
    setInterval(updateStats, 30000);
    setInterval(loadRecentActivities, 60000);
});
</script>
{% endblock %}