{% extends "layout.html" %}

{% block styles %}
<style>
    .excel-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        direction: rtl;
        table-layout: auto;
    }

    .excel-table th, .excel-table td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: center;
        overflow: visible;
        min-width: 150px;
    }

    .excel-table th {
        background-color: #007bff;
        color: white;
        position: sticky;
        top: 0;
        z-index: 10;
        font-weight: bold;
        white-space: nowrap;
        font-size: 16px;
        padding: 15px;
    }

    .excel-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .excel-table tr:hover {
        background-color: #e6f2ff;
    }

    .excel-table input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: white;
        text-align: center;
        font-size: 16px;
        height: 40px;
    }

    .excel-table input:focus {
        outline: none;
        border-color: #4CAF50;
        box-shadow: 0 0 8px rgba(76, 175, 80, 0.7);
    }

    .excel-container {
        overflow-x: auto;
        max-height: 70vh;
        margin-bottom: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        padding: 10px;
    }

    #fieldsOrderInfo {
        background-color: #e8f4ff;
        border-right: 5px solid #007bff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;
        position: sticky;
        top: 0;
        z-index: 100;
    }

    #fieldsOrderInfo h6 {
        color: #0056b3;
        font-size: 18px;
        font-weight: bold;
    }

    #fieldsOrderInfo ol {
        padding-right: 20px;
        font-size: 14px;
    }

    #fieldsOrderInfo li {
        margin-bottom: 3px;
    }

    .excel-actions {
        margin-bottom: 20px;
    }

    .excel-actions .btn {
        margin-right: 10px;
    }

    .excel-import {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .excel-row-actions {
        white-space: nowrap;
    }

    .excel-row-actions .btn {
        padding: 2px 5px;
        font-size: 12px;
    }

    .excel-add-row {
        margin-top: 10px;
    }

    /* تنسيق الصفحة 1 */
    .page-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .page-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .page-subtitle {
        font-size: 18px;
        color: #666;
    }

    /* تنسيق القائمة الجانبية */
    .sidebar {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .sidebar-link {
        display: block;
        padding: 10px;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        margin-bottom: 5px;
        transition: all 0.3s;
    }

    .sidebar-link:hover {
        background-color: #e9ecef;
        color: #007bff;
    }

    .sidebar-link.active {
        background-color: #007bff;
        color: white;
    }

    .sidebar-dropdown-menu {
        padding-right: 20px;
        display: none;
    }

    .sidebar-dropdown-menu.show {
        display: block;
    }

    .sidebar-dropdown-menu .sidebar-link {
        font-size: 0.9rem;
        padding: 8px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_excel') }}" class="sidebar-link active">
                <i class="fas fa-id-card"></i> إدارة البيانات بالإكسل
            </a>
            <a href="{{ url_for('reference_tables') }}" class="sidebar-link">
                <i class="fas fa-table"></i> الجداول الترميزية
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="{{ url_for('backup') }}" class="sidebar-link">
                <i class="fas fa-database"></i> النسخ الاحتياطي
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <div class="page-header">
            <h1 class="page-title">إدارة البيانات الشخصية</h1>
            <p class="page-subtitle">إدخال وتعديل البيانات الشخصية بشكل مرن</p>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-file-excel me-2"></i>
                استيراد البيانات من ملف إكسل
            </div>
            <div class="card-body">
                <form action="{{ url_for('import_personal_data_excel') }}" method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="excel_file" class="form-label">ملف الإكسل</label>
                                <input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xlsx,.xls" required>
                                <div class="form-text">
                                    يجب أن يحتوي ملف الإكسل على الأعمدة التالية بالترتيب:
                                </div>
                                <div class="alert alert-info mt-2">
                                    <h6 class="mb-2">ترتيب الحقول المطلوب:</h6>
                                    <ol class="mb-0">
                                        <li><strong>الاسم الشخصي</strong> (مطلوب)</li>
                                        <li>الاسم المستعار</li>
                                        <li>العمر</li>
                                        <li>المحافظة</li>
                                        <li>المديرية</li>
                                        <li>العزلة</li>
                                        <li>الحي/القرية</li>
                                        <li>المؤهل العلمي</li>
                                        <li>الحالة الاجتماعية</li>
                                        <li>العمل</li>
                                        <li>الإدارة</li>
                                        <li>مكان العمل</li>
                                        <li>الرقم الوطني</li>
                                        <li>الرقم العسكري</li>
                                        <li>رقم التلفون</li>
                                    </ol>
                                    <p class="mt-2 mb-0"><small>ملاحظة: يمكن للنظام التعرف على الحقول حتى لو كانت أسماؤها مختلفة قليلاً، لكن الترتيب المذكور أعلاه يضمن استيراد البيانات بشكل صحيح.</small></p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sheet_name" class="form-label">اسم الورقة</label>
                                <input type="text" class="form-control" id="sheet_name" name="sheet_name" placeholder="اترك فارغًا لاستخدام الورقة الأولى">
                                <div class="form-text">
                                    إذا تركت هذا الحقل فارغًا، سيتم استخدام الورقة الأولى في ملف الإكسل.
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="check_duplicates" name="check_duplicates" checked>
                                    <label class="form-check-label" for="check_duplicates">
                                        فحص السجلات المكررة قبل الاستيراد
                                    </label>
                                </div>
                                <div class="form-text">
                                    سيقوم النظام بفحص السجلات الموجودة مسبقاً في قاعدة البيانات وعرضها بشكل منفصل.
                                </div>
                            </div>

                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>ملاحظة هامة:</strong> لن يتم استيراد السجلات الموجودة مسبقاً في قاعدة البيانات لتجنب التكرار.
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>
                            استيراد البيانات
                        </button>

                        <a href="{{ url_for('download_personal_data_excel_template') }}" class="btn btn-secondary">
                            <i class="fas fa-download me-2"></i>
                            تنزيل قالب الإكسل
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-table me-2"></i>
                البيانات الشخصية
            </div>
            <div class="card-body">
                <!-- أزرار الإجراءات -->
                <div class="excel-actions">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="بحث في البيانات...">
                                <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge bg-info me-2">
                                <i class="fas fa-info-circle me-1"></i>
                                عدد السجلات: <span id="recordCount">{{ personal_data|length }}</span>
                            </span>
                            <button class="btn btn-sm btn-outline-info" id="toggleFieldsOrderBtn" title="إظهار/إخفاء ترتيب الحقول">
                                <i class="fas fa-list-ol"></i>
                                ترتيب الحقول
                            </button>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="alert alert-info" id="fieldsOrderInfo">
                                <h6 class="mb-2"><strong>ترتيب الحقول المطلوب:</strong></h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <ol class="mb-0" start="1">
                                            <li><strong>الاسم الشخصي</strong> (مطلوب)</li>
                                            <li>الاسم المستعار</li>
                                            <li>العمر</li>
                                            <li>المحافظة</li>
                                            <li>المديرية</li>
                                        </ol>
                                    </div>
                                    <div class="col-md-4">
                                        <ol class="mb-0" start="6">
                                            <li>العزلة</li>
                                            <li>الحي/القرية</li>
                                            <li>المؤهل العلمي</li>
                                            <li>الحالة الاجتماعية</li>
                                            <li>العمل</li>
                                        </ol>
                                    </div>
                                    <div class="col-md-4">
                                        <ol class="mb-0" start="11">
                                            <li>الإدارة</li>
                                            <li>مكان العمل</li>
                                            <li>الرقم الوطني</li>
                                            <li>الرقم العسكري</li>
                                            <li>رقم التلفون</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="btn-toolbar">
                        <div class="btn-group me-2">
                            <button class="btn btn-success" id="addRowBtn">
                                <i class="fas fa-plus me-2"></i>
                                إضافة صف جديد
                            </button>
                        </div>

                        <div class="btn-group me-2">
                            <button class="btn btn-primary" id="saveAllBtn">
                                <i class="fas fa-save me-2"></i>
                                حفظ جميع التغييرات
                            </button>
                        </div>

                        <div class="btn-group">
                            <a href="{{ url_for('export_personal_data_excel') }}" class="btn btn-warning">
                                <i class="fas fa-file-export me-2"></i>
                                تصدير إلى إكسل
                            </a>
                        </div>

                        <div class="btn-group ms-2">
                            <a href="{{ url_for('personal_data_excel_advanced') }}" class="btn btn-info">
                                <i class="fas fa-cogs me-2"></i>
                                الواجهة المتقدمة
                            </a>
                        </div>

                        <div class="btn-group ms-2">
                            <a href="{{ url_for('personal_data_excel_devextreme') }}" class="btn btn-primary">
                                <i class="fas fa-table me-2"></i>
                                الواجهة المتطورة
                            </a>
                        </div>
                    </div>
                </div>

                <!-- جدول البيانات -->
                <div class="excel-container">
                    <table class="excel-table" id="personalDataTable">
                        <thead>
                            <tr>
                                <th>الإجراءات</th>
                                <th>الاسم الشخصي</th>
                                <th>الاسم المستعار</th>
                                <th>العمر</th>
                                <th>المحافظة</th>
                                <th>المديرية</th>
                                <th>العزلة</th>
                                <th>الحي/القرية</th>
                                <th>المؤهل العلمي</th>
                                <th>الحالة الاجتماعية</th>
                                <th>العمل</th>
                                <th>الإدارة</th>
                                <th>مكان العمل</th>
                                <th>الرقم الوطني</th>
                                <th>الرقم العسكري</th>
                                <th>رقم التلفون</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for person in personal_data %}
                            <tr data-id="{{ person.id }}">
                                <td class="excel-row-actions">
                                    <button class="btn btn-sm btn-danger delete-row">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button class="btn btn-sm btn-info save-row">
                                        <i class="fas fa-save"></i>
                                    </button>
                                </td>
                                <td><input type="text" name="full_name" value="{{ person.full_name }}" required style="width: 250px; min-width: 250px;"></td>
                                <td><input type="text" name="nickname" value="{{ person.nickname if person.nickname else '' }}" style="width: 180px; min-width: 180px;"></td>
                                <td><input type="text" name="age" value="{{ person.age if person.age else '' }}" style="width: 100px; min-width: 100px;"></td>
                                <td>
                                    <input type="text" name="governorate" value="{{ person.governorate.name if person.governorate else '' }}" style="width: 180px; min-width: 180px;">
                                    <input type="hidden" name="governorate_id" value="{{ person.governorate_id if person.governorate_id else '' }}">
                                </td>
                                <td>
                                    <input type="text" name="directorate" value="{{ person.directorate.name if person.directorate else '' }}" style="width: 180px; min-width: 180px;">
                                    <input type="hidden" name="directorate_id" value="{{ person.directorate_id if person.directorate_id else '' }}">
                                </td>
                                <td><input type="text" name="uzla" value="{{ person.uzla if person.uzla else '' }}" style="width: 180px; min-width: 180px;"></td>
                                <td>
                                    <input type="text" name="village" value="{{ person.village.name if person.village else '' }}" style="width: 180px; min-width: 180px;">
                                    <input type="hidden" name="village_id" value="{{ person.village_id if person.village_id else '' }}">
                                </td>
                                <td>
                                    <input type="text" name="qualification" value="{{ person.qualification_type.name if person.qualification_type else '' }}" style="width: 180px; min-width: 180px;">
                                    <input type="hidden" name="qualification_type_id" value="{{ person.qualification_type_id if person.qualification_type_id else '' }}">
                                </td>
                                <td>
                                    <input type="text" name="marital_status" value="{{ person.marital_status.name if person.marital_status else '' }}" style="width: 150px; min-width: 150px;">
                                    <input type="hidden" name="marital_status_id" value="{{ person.marital_status_id if person.marital_status_id else '' }}">
                                </td>
                                <td><input type="text" name="job" value="{{ person.job_title }}" style="width: 180px; min-width: 180px;"></td>
                                <td>
                                    <input type="text" name="agency" value="{{ person.agency.name if person.agency else '' }}" style="width: 180px; min-width: 180px;">
                                    <input type="hidden" name="agency_id" value="{{ person.agency_id if person.agency_id else '' }}">
                                </td>
                                <td><input type="text" name="work_place" value="{{ person.work_place }}" style="width: 180px; min-width: 180px;"></td>
                                <td><input type="text" name="national_number" value="{{ person.national_number }}" style="width: 150px; min-width: 150px;"></td>
                                <td><input type="text" name="military_number" value="{{ person.military_number }}" style="width: 150px; min-width: 150px;"></td>
                                <td><input type="text" name="phone" value="{{ person.phone_yemen_mobile }}" style="width: 150px; min-width: 150px;"></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تفعيل القائمة المنسدلة
        $('.sidebar-dropdown > a').click(function(e) {
            e.preventDefault();
            $(this).next('.sidebar-dropdown-menu').slideToggle();
        });

        // إضافة صف جديد
        $('#addRowBtn').on('click', function(e) {
            e.preventDefault();
            console.log("تم النقر على زر إضافة صف جديد");

            // إنشاء عناصر HTML للصف الجديد
            var newRow = document.createElement('tr');
            newRow.setAttribute('data-id', 'new');

            // إضافة خلية الإجراءات
            var actionsCell = document.createElement('td');
            actionsCell.className = 'excel-row-actions';
            actionsCell.innerHTML = `
                <button class="btn btn-sm btn-danger delete-row">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="btn btn-sm btn-info save-row">
                    <i class="fas fa-save"></i>
                </button>
            `;
            newRow.appendChild(actionsCell);

            // إضافة خلية الاسم الشخصي
            var nameCell = document.createElement('td');
            var nameInput = document.createElement('input');
            nameInput.type = 'text';
            nameInput.name = 'full_name';
            nameInput.required = true;
            nameInput.style.width = '250px';
            nameInput.style.minWidth = '250px';
            nameCell.appendChild(nameInput);
            newRow.appendChild(nameCell);

            // إضافة خلية الاسم المستعار
            var nicknameCell = document.createElement('td');
            var nicknameInput = document.createElement('input');
            nicknameInput.type = 'text';
            nicknameInput.name = 'nickname';
            nicknameInput.style.width = '180px';
            nicknameInput.style.minWidth = '180px';
            nicknameCell.appendChild(nicknameInput);
            newRow.appendChild(nicknameCell);

            // إضافة خلية العمر
            var ageCell = document.createElement('td');
            var ageInput = document.createElement('input');
            ageInput.type = 'text';
            ageInput.name = 'age';
            ageInput.style.width = '100px';
            ageInput.style.minWidth = '100px';
            ageCell.appendChild(ageInput);
            newRow.appendChild(ageCell);

            // المحافظة
            var governorateCell = document.createElement('td');
            var governorateInput = document.createElement('input');
            governorateInput.type = 'text';
            governorateInput.name = 'governorate';
            governorateInput.style.width = '180px';
            governorateInput.style.minWidth = '180px';
            governorateCell.appendChild(governorateInput);

            // إضافة حقل مخفي لمعرف المحافظة
            var governorateIdInput = document.createElement('input');
            governorateIdInput.type = 'hidden';
            governorateIdInput.name = 'governorate_id';
            governorateIdInput.value = '';
            governorateCell.appendChild(governorateIdInput);

            newRow.appendChild(governorateCell);

            // المديرية
            var directorateCell = document.createElement('td');
            var directorateInput = document.createElement('input');
            directorateInput.type = 'text';
            directorateInput.name = 'directorate';
            directorateInput.style.width = '180px';
            directorateInput.style.minWidth = '180px';
            directorateCell.appendChild(directorateInput);

            // إضافة حقل مخفي لمعرف المديرية
            var directorateIdInput = document.createElement('input');
            directorateIdInput.type = 'hidden';
            directorateIdInput.name = 'directorate_id';
            directorateIdInput.value = '';
            directorateCell.appendChild(directorateIdInput);

            newRow.appendChild(directorateCell);

            // العزلة
            var uzlaCell = document.createElement('td');
            var uzlaInput = document.createElement('input');
            uzlaInput.type = 'text';
            uzlaInput.name = 'uzla';
            uzlaInput.style.width = '180px';
            uzlaInput.style.minWidth = '180px';
            uzlaCell.appendChild(uzlaInput);
            newRow.appendChild(uzlaCell);

            // الحي/القرية
            var villageCell = document.createElement('td');
            var villageInput = document.createElement('input');
            villageInput.type = 'text';
            villageInput.name = 'village';
            villageInput.style.width = '180px';
            villageInput.style.minWidth = '180px';
            villageCell.appendChild(villageInput);

            // إضافة حقل مخفي لمعرف القرية
            var villageIdInput = document.createElement('input');
            villageIdInput.type = 'hidden';
            villageIdInput.name = 'village_id';
            villageIdInput.value = '';
            villageCell.appendChild(villageIdInput);

            newRow.appendChild(villageCell);

            // المؤهل العلمي
            var qualificationCell = document.createElement('td');
            var qualificationInput = document.createElement('input');
            qualificationInput.type = 'text';
            qualificationInput.name = 'qualification';
            qualificationInput.style.width = '180px';
            qualificationInput.style.minWidth = '180px';
            qualificationCell.appendChild(qualificationInput);

            // إضافة حقل مخفي لمعرف المؤهل العلمي
            var qualificationIdInput = document.createElement('input');
            qualificationIdInput.type = 'hidden';
            qualificationIdInput.name = 'qualification_type_id';
            qualificationIdInput.value = '';
            qualificationCell.appendChild(qualificationIdInput);

            newRow.appendChild(qualificationCell);

            // الحالة الاجتماعية
            var maritalStatusCell = document.createElement('td');
            var maritalStatusInput = document.createElement('input');
            maritalStatusInput.type = 'text';
            maritalStatusInput.name = 'marital_status';
            maritalStatusInput.style.width = '150px';
            maritalStatusInput.style.minWidth = '150px';
            maritalStatusCell.appendChild(maritalStatusInput);

            // إضافة حقل مخفي لمعرف الحالة الاجتماعية
            var maritalStatusIdInput = document.createElement('input');
            maritalStatusIdInput.type = 'hidden';
            maritalStatusIdInput.name = 'marital_status_id';
            maritalStatusIdInput.value = '';
            maritalStatusCell.appendChild(maritalStatusIdInput);

            newRow.appendChild(maritalStatusCell);

            // العمل
            var jobCell = document.createElement('td');
            var jobInput = document.createElement('input');
            jobInput.type = 'text';
            jobInput.name = 'job';
            jobInput.style.width = '180px';
            jobInput.style.minWidth = '180px';
            jobCell.appendChild(jobInput);
            newRow.appendChild(jobCell);

            // الإدارة
            var agencyCell = document.createElement('td');
            var agencyInput = document.createElement('input');
            agencyInput.type = 'text';
            agencyInput.name = 'agency';
            agencyInput.style.width = '180px';
            agencyInput.style.minWidth = '180px';
            agencyCell.appendChild(agencyInput);

            // إضافة حقل مخفي لمعرف الإدارة
            var agencyIdInput = document.createElement('input');
            agencyIdInput.type = 'hidden';
            agencyIdInput.name = 'agency_id';
            agencyIdInput.value = '';
            agencyCell.appendChild(agencyIdInput);

            newRow.appendChild(agencyCell);

            // مكان العمل
            var workPlaceCell = document.createElement('td');
            var workPlaceInput = document.createElement('input');
            workPlaceInput.type = 'text';
            workPlaceInput.name = 'work_place';
            workPlaceInput.style.width = '180px';
            workPlaceInput.style.minWidth = '180px';
            workPlaceCell.appendChild(workPlaceInput);
            newRow.appendChild(workPlaceCell);

            // الرقم الوطني
            var nationalNumberCell = document.createElement('td');
            var nationalNumberInput = document.createElement('input');
            nationalNumberInput.type = 'text';
            nationalNumberInput.name = 'national_number';
            nationalNumberInput.style.width = '150px';
            nationalNumberInput.style.minWidth = '150px';
            nationalNumberCell.appendChild(nationalNumberInput);
            newRow.appendChild(nationalNumberCell);

            // الرقم العسكري
            var militaryNumberCell = document.createElement('td');
            var militaryNumberInput = document.createElement('input');
            militaryNumberInput.type = 'text';
            militaryNumberInput.name = 'military_number';
            militaryNumberInput.style.width = '150px';
            militaryNumberInput.style.minWidth = '150px';
            militaryNumberCell.appendChild(militaryNumberInput);
            newRow.appendChild(militaryNumberCell);

            // رقم التلفون
            var phoneCell = document.createElement('td');
            var phoneInput = document.createElement('input');
            phoneInput.type = 'text';
            phoneInput.name = 'phone';
            phoneInput.style.width = '150px';
            phoneInput.style.minWidth = '150px';
            phoneCell.appendChild(phoneInput);
            newRow.appendChild(phoneCell);

            // إضافة حقول إضافية غير مرئية للمستخدم
            // الرقم الوظيفي
            var workNumberInput = document.createElement('input');
            workNumberInput.type = 'hidden';
            workNumberInput.name = 'work_number';
            workNumberInput.value = '';
            newRow.appendChild(workNumberInput);

            // الرتبة الوظيفية
            var workRankInput = document.createElement('input');
            workRankInput.type = 'hidden';
            workRankInput.name = 'work_rank';
            workRankInput.value = '';
            newRow.appendChild(workRankInput);

            // إضافة الصف إلى الجدول
            document.querySelector('#personalDataTable tbody').appendChild(newRow);

            // عرض رسالة تأكيد
            showAlert('تم إضافة صف جديد. قم بإدخال البيانات ثم اضغط على زر الحفظ.', 'info');
        });

        // حذف صف
        $(document).on('click', '.delete-row', function() {
            var row = $(this).closest('tr');
            var id = row.data('id');

            if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
                if (id !== 'new') {
                    // إرسال طلب حذف إلى الخادم
                    $.ajax({
                        url: '/personal_data/excel/' + id + '/delete',
                        type: 'POST',
                        success: function(response) {
                            if (response.success) {
                                row.remove();
                                showAlert('تم حذف السجل بنجاح', 'success');
                            } else {
                                showAlert('فشل حذف السجل: ' + response.message, 'danger');
                            }
                        },
                        error: function() {
                            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                        }
                    });
                } else {
                    row.remove();
                }
            }
        });

        // حفظ صف
        $(document).on('click', '.save-row', async function() {
            var row = $(this).closest('tr');
            var id = row.data('id');
            var data = {};

            // جمع البيانات من الصف
            row.find('input').each(function() {
                var name = $(this).attr('name');
                var value = $(this).val();
                data[name] = value;
            });

            // التحقق من وجود الاسم
            if (!data.full_name) {
                showAlert('يجب إدخال الاسم الشخصي', 'danger');
                return;
            }

            // البحث عن معرفات الجداول الترميزية
            try {
                // المحافظة
                if (data.governorate) {
                    const govResponse = await $.ajax({
                        url: '/lookup/governorate',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ name: data.governorate })
                    });
                    if (govResponse.id) {
                        data.governorate_id = govResponse.id;
                    }
                }

                // المديرية
                if (data.directorate) {
                    const dirResponse = await $.ajax({
                        url: '/lookup/directorate',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ name: data.directorate })
                    });
                    if (dirResponse.id) {
                        data.directorate_id = dirResponse.id;
                    }
                }

                // القرية
                if (data.village) {
                    const villageResponse = await $.ajax({
                        url: '/lookup/village',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ name: data.village })
                    });
                    if (villageResponse.id) {
                        data.village_id = villageResponse.id;
                    }
                }

                // المؤهل العلمي
                if (data.qualification) {
                    const qualResponse = await $.ajax({
                        url: '/lookup/qualification',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ name: data.qualification })
                    });
                    if (qualResponse.id) {
                        data.qualification_type_id = qualResponse.id;
                    }
                }

                // الإدارة
                if (data.agency) {
                    const agencyResponse = await $.ajax({
                        url: '/lookup/agency',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ name: data.agency })
                    });
                    if (agencyResponse.id) {
                        data.agency_id = agencyResponse.id;
                    }
                }

                // الحالة الاجتماعية
                if (data.marital_status) {
                    const maritalResponse = await $.ajax({
                        url: '/lookup/marital_status',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ name: data.marital_status })
                    });
                    if (maritalResponse.id) {
                        data.marital_status_id = maritalResponse.id;
                    }
                }
            } catch (error) {
                console.error("خطأ في البحث عن المعرفات:", error);
            }

            // تحويل أسماء الحقول إلى الأسماء المتوقعة في الخادم
            var serverData = {
                full_name: data.full_name || "",
                nickname: data.nickname || "",
                age: data.age || "",
                national_number: data.national_number || "",
                military_number: data.military_number || "",
                job: data.job || "",
                work_place: data.work_place || "",
                phone: data.phone || "",
                // إضافة الحقول الأخرى التي يتوقعها الخادم
                work_number: data.work_number || "",
                work_rank: data.work_rank || "",
                // إضافة الحقول المرتبطة بالجداول الترميزية كنصوص بسيطة
                governorate: data.governorate || "",
                directorate: data.directorate || "",
                village: data.village || "",
                qualification: data.qualification || "",
                agency: data.agency || "",
                marital_status: data.marital_status || ""
            };

            // التأكد من أن القيم "None" تُرسل كسلاسل نصية وليس كقيم null
            for (var key in serverData) {
                if (serverData[key] === null || serverData[key] === undefined) {
                    serverData[key] = "";
                }
            }

            // عرض البيانات في وحدة التحكم للتصحيح
            console.log("البيانات المرسلة إلى الخادم:", serverData);

            // إرسال البيانات إلى الخادم
            $.ajax({
                url: id === 'new' ? '/personal_data/excel/add' : '/personal_data/excel/' + id + '/update',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(serverData),
                success: function(response) {
                    if (response.success) {
                        if (id === 'new') {
                            row.attr('data-id', response.id);
                        }
                        showAlert('تم حفظ البيانات بنجاح', 'success');
                    } else {
                        showAlert('فشل حفظ البيانات: ' + response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                }
            });
        });

        // حفظ جميع التغييرات
        $('#saveAllBtn').click(function() {
            var allData = [];

            // جمع البيانات من جميع الصفوف
            $('#personalDataTable tbody tr').each(function() {
                var row = $(this);
                var id = row.data('id');
                var rowData = { id: id };

                row.find('input').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    rowData[name] = value;
                });

                // تحويل أسماء الحقول إلى الأسماء المتوقعة في الخادم
                var serverData = {
                    id: rowData.id,
                    full_name: rowData.full_name || "",
                    nickname: rowData.nickname || "",
                    age: rowData.age || "",
                    national_number: rowData.national_number || "",
                    military_number: rowData.military_number || "",
                    job: rowData.job || "",
                    work_place: rowData.work_place || "",
                    phone: rowData.phone || "",
                    // إضافة الحقول الأخرى التي يتوقعها الخادم
                    work_number: rowData.work_number || "",
                    work_rank: rowData.work_rank || "",
                    // إضافة الحقول المرتبطة بالجداول الترميزية كنصوص بسيطة
                    governorate: rowData.governorate || "",
                    directorate: rowData.directorate || "",
                    village: rowData.village || "",
                    qualification: rowData.qualification || "",
                    agency: rowData.agency || "",
                    marital_status: rowData.marital_status || ""
                };

                // التأكد من أن القيم "None" تُرسل كسلاسل نصية وليس كقيم null
                for (var key in serverData) {
                    if (serverData[key] === null || serverData[key] === undefined) {
                        serverData[key] = "";
                    }
                }

                allData.push(serverData);
            });

            // إرسال البيانات إلى الخادم
            $.ajax({
                url: '/personal_data/excel/save_all',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ data: allData }),
                success: function(response) {
                    if (response.success) {
                        showAlert('تم حفظ جميع البيانات بنجاح', 'success');
                        // تحديث معرفات الصفوف الجديدة
                        for (var i = 0; i < response.new_ids.length; i++) {
                            var item = response.new_ids[i];
                            $('tr[data-id="' + item.old_id + '"]').attr('data-id', item.new_id);
                        }
                    } else {
                        showAlert('فشل حفظ البيانات: ' + response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                }
            });
        });

        // عرض رسالة تنبيه
        function showAlert(message, type) {
            var alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            $('.excel-actions').before(alertHtml);

            // إخفاء التنبيه بعد 5 ثوانٍ
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        }

        // البحث في البيانات
        $('#searchBtn').click(function() {
            searchData();
        });

        $('#searchInput').keyup(function(e) {
            if (e.keyCode === 13) {
                searchData();
            }
        });

        // إظهار/إخفاء مربع ترتيب الحقول
        $('#toggleFieldsOrderBtn').click(function() {
            var fieldsOrderInfo = $('#fieldsOrderInfo');
            if (fieldsOrderInfo.is(':visible')) {
                fieldsOrderInfo.slideUp();
                $(this).removeClass('btn-info').addClass('btn-outline-info');
                localStorage.setItem('fieldsOrderInfoVisible', 'false');
            } else {
                fieldsOrderInfo.slideDown();
                $(this).removeClass('btn-outline-info').addClass('btn-info');
                localStorage.setItem('fieldsOrderInfoVisible', 'true');
            }
        });

        // التحقق من حالة مربع ترتيب الحقول عند تحميل الصفحة
        $(document).ready(function() {
            var fieldsOrderInfoVisible = localStorage.getItem('fieldsOrderInfoVisible');
            if (fieldsOrderInfoVisible === 'false') {
                $('#fieldsOrderInfo').hide();
                $('#toggleFieldsOrderBtn').removeClass('btn-info').addClass('btn-outline-info');
            } else {
                $('#toggleFieldsOrderBtn').removeClass('btn-outline-info').addClass('btn-info');
            }
        });

        function searchData() {
            var searchText = $('#searchInput').val().toLowerCase();
            var visibleCount = 0;

            if (searchText === '') {
                // إظهار جميع الصفوف إذا كان البحث فارغاً
                $('#personalDataTable tbody tr').show();
                visibleCount = $('#personalDataTable tbody tr').length;
            } else {
                // البحث في جميع الخلايا
                $('#personalDataTable tbody tr').each(function() {
                    var rowText = $(this).text().toLowerCase();
                    if (rowText.indexOf(searchText) > -1) {
                        $(this).show();
                        visibleCount++;
                    } else {
                        $(this).hide();
                    }
                });
            }

            // تحديث عداد السجلات المعروضة
            $('#recordCount').text(visibleCount);

            if (visibleCount === 0) {
                showAlert('لم يتم العثور على نتائج للبحث', 'warning');
            }
        }
    });
</script>
{% endblock %}
