# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Build artifacts
build/
dist/
*.egg-info/

# Logs
*.log
logs/

# Temporary files
tmp/
temp/

# Documentation
docs/
*.md
README*

# EXE build files
exe_build/
*.exe
*.spec

# Backup files
*.bak
*.backup
