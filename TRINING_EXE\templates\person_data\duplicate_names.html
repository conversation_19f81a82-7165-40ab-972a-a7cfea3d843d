<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأسماء المكررة</title>
    <!-- Bootstrap CSS - محلي -->
    <link href="/static/libs/bootstrap/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome - محلي -->
    <link href="/static/libs/fontawesome/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .header-section {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #dc3545;
        }
        .duplicate-group {
            background: white;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .duplicate-header {
            background: #dc3545;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        .duplicate-content {
            padding: 0;
        }
        .person-row {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }
        .person-row:last-child {
            border-bottom: none;
        }
        .person-row:nth-child(even) {
            background-color: #f8f9fa;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
            margin-left: 10px;
        }
        .field-value {
            color: #6c757d;
        }
        .btn-export {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40,167,69,0.3);
            color: white;
        }
        .btn-back {
            background: linear-gradient(135deg, #6c757d, #495057);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(108,117,125,0.3);
            color: white;
        }
        .no-duplicates {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .no-duplicates i {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-section">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-users-slash me-3"></i>
                        الأسماء المكررة
                    </h1>
                    <p class="mb-0 mt-2 opacity-75">عرض وإدارة الأسماء المكررة في قاعدة البيانات</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{{ url_for('person_data.person_data_table') }}" class="btn btn-back me-2">
                        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                    </a>
                    {% if duplicates %}
                    <a href="{{ url_for('person_data.export_duplicates') }}" class="btn btn-export">
                        <i class="fas fa-file-excel me-2"></i>تصدير إلى Excel
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Statistics -->
        <div class="stats-card">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-chart-bar me-2"></i>إحصائيات الأسماء المكررة</h5>
                </div>
                <div class="col-md-6 text-end">
                    <span class="badge bg-danger fs-6">{{ total_duplicates }} مجموعة مكررة</span>
                </div>
            </div>
        </div>

        <!-- Duplicates Content -->
        {% if duplicates %}
            {% for duplicate in duplicates %}
            <div class="duplicate-group">
                <div class="duplicate-header">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    الاسم: {{ duplicate.name }} 
                    <span class="badge bg-light text-dark ms-2">{{ duplicate.count }} تكرار</span>
                </div>
                <div class="duplicate-content">
                    {% for person in duplicate.persons %}
                    <div class="person-row">
                        <div class="row">
                            <div class="col-md-2">
                                <span class="field-label">الرقم:</span>
                                <span class="field-value">{{ person.id }}</span>
                            </div>
                            <div class="col-md-2">
                                <span class="field-label">الاسم المستعار:</span>
                                <span class="field-value">{{ person.nickname or 'غير محدد' }}</span>
                            </div>
                            <div class="col-md-1">
                                <span class="field-label">العمر:</span>
                                <span class="field-value">{{ person.age or 'غير محدد' }}</span>
                            </div>
                            <div class="col-md-2">
                                <span class="field-label">المحافظة:</span>
                                <span class="field-value">{{ person.governorate or 'غير محدد' }}</span>
                            </div>
                            <div class="col-md-2">
                                <span class="field-label">المديرية:</span>
                                <span class="field-value">{{ person.directorate or 'غير محدد' }}</span>
                            </div>
                            <div class="col-md-3">
                                <span class="field-label">الرقم الوطني:</span>
                                <span class="field-value">{{ person.national_number or 'غير محدد' }}</span>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-2">
                                <span class="field-label">العزلة:</span>
                                <span class="field-value">{{ person.uzla or 'غير محدد' }}</span>
                            </div>
                            <div class="col-md-2">
                                <span class="field-label">الحي/القرية:</span>
                                <span class="field-value">{{ person.village or 'غير محدد' }}</span>
                            </div>
                            <div class="col-md-2">
                                <span class="field-label">المؤهل:</span>
                                <span class="field-value">{{ person.qualification or 'غير محدد' }}</span>
                            </div>
                            <div class="col-md-2">
                                <span class="field-label">العمل:</span>
                                <span class="field-value">{{ person.job or 'غير محدد' }}</span>
                            </div>
                            <div class="col-md-2">
                                <span class="field-label">الإدارة:</span>
                                <span class="field-value">{{ person.agency or 'غير محدد' }}</span>
                            </div>
                            <div class="col-md-2">
                                <span class="field-label">التلفون:</span>
                                <span class="field-value">{{ person.phone or 'غير محدد' }}</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="no-duplicates">
                <i class="fas fa-check-circle"></i>
                <h3>لا توجد أسماء مكررة</h3>
                <p class="text-muted">جميع الأسماء في قاعدة البيانات فريدة</p>
            </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
