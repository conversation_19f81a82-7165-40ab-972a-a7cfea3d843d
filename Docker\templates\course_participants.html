{% extends "layout.html" %}

{% block styles %}
<style>
    .course-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .course-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        color: #ffcc00;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    }
    
    .course-title {
        font-size: 1.8rem;
        margin-bottom: 10px;
    }
    
    .course-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-top: 15px;
    }
    
    .course-info-item {
        background-color: rgba(255, 255, 255, 0.2);
        padding: 8px 15px;
        border-radius: 10px;
        font-size: 0.9rem;
    }
    
    .course-info-item i {
        margin-left: 5px;
    }
    
    .data-card {
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        border: none;
        margin-bottom: 20px;
    }
    
    .data-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
    
    .data-card-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 15px 20px;
        font-weight: bold;
        border-radius: 15px 15px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .data-table {
        width: 100%;
    }
    
    .data-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    
    .data-table th, .data-table td {
        padding: 12px 15px;
        text-align: right;
    }
    
    .data-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    
    .data-table tr:hover {
        background-color: #e9ecef;
    }
    
    .btn-add {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 10px 20px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
        color: white;
    }
    
    .btn-view {
        background-color: #28a745;
        color: white;
    }
    
    .btn-edit {
        background-color: #ffc107;
        color: white;
    }
    
    .btn-delete {
        background-color: #dc3545;
        color: white;
    }
    
    .btn-sm {
        padding: 5px 10px;
        font-size: 0.875rem;
        border-radius: 5px;
    }
    
    .status-badge {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-active {
        background-color: #28a745;
        color: white;
    }
    
    .status-completed {
        background-color: #007bff;
        color: white;
    }
    
    .status-dropped {
        background-color: #dc3545;
        color: white;
    }
    
    .payment-badge {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .payment-pending {
        background-color: #ffc107;
        color: white;
    }
    
    .payment-paid {
        background-color: #28a745;
        color: white;
    }
    
    .payment-cancelled {
        background-color: #dc3545;
        color: white;
    }
    
    .stats-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin: 10px 0;
    }
    
    .stats-title {
        font-size: 1rem;
        color: #6c757d;
    }
    
    .stats-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    
    .stats-participants .stats-icon {
        color: #4a6bff;
    }
    
    .stats-graduates .stats-icon {
        color: #28a745;
    }
    
    .stats-dropouts .stats-icon {
        color: #dc3545;
    }
    
    .stats-allowance .stats-icon {
        color: #ffc107;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="course-header">
        <div class="course-number">{{ course.course_number }}</div>
        <div class="course-title">{{ course.title }}</div>
        <div class="course-info">
            <div class="course-info-item"><i class="fas fa-building"></i> {{ course.agency or 'غير محدد' }}</div>
            <div class="course-info-item"><i class="fas fa-map-marker-alt"></i> {{ course.center_name or 'غير محدد' }}</div>
            <div class="course-info-item"><i class="fas fa-users"></i> {{ course.participant_type or 'غير محدد' }}</div>
            <div class="course-info-item"><i class="fas fa-calendar-alt"></i> {{ course.start_date.strftime('%Y-%m-%d') }} إلى {{ course.end_date.strftime('%Y-%m-%d') }}</div>
            <div class="course-info-item"><i class="fas fa-clock"></i> {{ course.duration_days }} يوم</div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card stats-participants">
                <div class="stats-icon"><i class="fas fa-users"></i></div>
                <div class="stats-number">{{ course.total_participants or 0 }}</div>
                <div class="stats-title">إجمالي المشاركين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-graduates">
                <div class="stats-icon"><i class="fas fa-graduation-cap"></i></div>
                <div class="stats-number">{{ course.total_graduates or 0 }}</div>
                <div class="stats-title">الخريجين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-dropouts">
                <div class="stats-icon"><i class="fas fa-user-slash"></i></div>
                <div class="stats-number">{{ course.total_dropouts or 0 }}</div>
                <div class="stats-title">المنسحبين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card stats-allowance">
                <div class="stats-icon"><i class="fas fa-money-bill-wave"></i></div>
                <div class="stats-number">{{ course.total_allowance or 0 }}</div>
                <div class="stats-title">إجمالي المبلغ</div>
            </div>
        </div>
    </div>
    
    <div class="data-card">
        <div class="data-card-header">
            <span><i class="fas fa-users me-2"></i> المشاركين في الدورة</span>
            <a href="{{ url_for('add_course_participant', course_id=course.id) }}" class="btn btn-add btn-sm">
                <i class="fas fa-plus-circle me-1"></i> إضافة مشارك
            </a>
        </div>
        <div class="card-body">
            {% if participants %}
            <div class="table-responsive">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>تاريخ الدخول</th>
                            <th>تاريخ الخروج</th>
                            <th>الحالة</th>
                            <th>حالة الدفع</th>
                            <th>إجمالي المبلغ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for participant in participants %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ participant.personal_data.full_name }}</td>
                            <td>{{ participant.entry_date.strftime('%Y-%m-%d') if participant.entry_date else 'غير محدد' }}</td>
                            <td>{{ participant.exit_date.strftime('%Y-%m-%d') if participant.exit_date else 'غير محدد' }}</td>
                            <td>
                                {% if participant.status == 'active' %}
                                <span class="status-badge status-active">نشط</span>
                                {% elif participant.status == 'completed' %}
                                <span class="status-badge status-completed">أكمل الدورة</span>
                                {% elif participant.status == 'dropped' %}
                                <span class="status-badge status-dropped">انسحب</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if participant.payment_status == 'pending' %}
                                <span class="payment-badge payment-pending">معلق</span>
                                {% elif participant.payment_status == 'paid' %}
                                <span class="payment-badge payment-paid">تم الدفع</span>
                                {% elif participant.payment_status == 'cancelled' %}
                                <span class="payment-badge payment-cancelled">ملغي</span>
                                {% endif %}
                            </td>
                            <td>{{ participant.total_allowance or 0 }}</td>
                            <td>
                                <a href="{{ url_for('edit_course_participant', participant_id=participant.id) }}" class="btn btn-sm btn-edit">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{{ url_for('delete_course_participant', participant_id=participant.id) }}" class="btn btn-sm btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذا المشارك؟');">
                                    <i class="fas fa-trash-alt"></i> حذف
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا يوجد مشاركين في هذه الدورة حتى الآن.
            </div>
            {% endif %}
        </div>
    </div>
    
    <div class="mt-4">
        <a href="{{ url_for('course_details', course_id=course.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى تفاصيل الدورة
        </a>
    </div>
</div>
{% endblock %}
