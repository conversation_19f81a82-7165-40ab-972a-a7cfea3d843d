from flask import Flask, render_template, request, jsonify
from datetime import datetime, timedelta
import json
from collections import defaultdict

class ReportsGenerator:
    def __init__(self, db_instance=None):
        self.db = db_instance

    def set_db(self, db_instance):
        """تعيين مثيل قاعدة البيانات"""
        self.db = db_instance

    def generate_comprehensive_report(self, start_date, end_date):
        """إنشاء تقرير شامل للفترة المحددة"""
        try:
            if not self.db:
                raise Exception("لم يتم تعيين قاعدة البيانات")

            # الحصول على البيانات الأساسية
            basic_stats = self._get_basic_statistics(start_date, end_date)
            chart_data = self._get_chart_data(start_date, end_date)
            table_data = self._get_table_data(start_date, end_date)

            return {
                'success': True,
                'totalCourses': basic_stats['total_courses'],
                'totalParticipants': basic_stats['total_participants'],
                'totalCenters': basic_stats['total_centers'],
                'totalGraduates': basic_stats['total_graduates'],
                'chartData': chart_data,
                'tableData': table_data
            }

        except Exception as e:
            return {
                'success': False,
                'message': str(e)
            }

    def _get_basic_statistics(self, start_date, end_date):
        """الحصول على الإحصائيات الأساسية"""
        from app import Course, CourseParticipant, TrainingCenter

        # تحويل التواريخ
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')

        # إجمالي الدورات
        total_courses = Course.query.filter(
            Course.start_date >= start_dt,
            Course.start_date <= end_dt
        ).count()

        # إجمالي المشاركين
        course_ids = [c.id for c in Course.query.filter(
            Course.start_date >= start_dt,
            Course.start_date <= end_dt
        ).all()]

        total_participants = CourseParticipant.query.filter(
            CourseParticipant.course_id.in_(course_ids)
        ).count() if course_ids else 0

        # عدد المراكز
        center_ids = [c.center_id for c in Course.query.filter(
            Course.start_date >= start_dt,
            Course.start_date <= end_dt,
            Course.center_id.isnot(None)
        ).all()]
        total_centers = len(set(center_ids)) if center_ids else 0

        # إجمالي الخريجين (المكملين للدورة)
        total_graduates = CourseParticipant.query.filter(
            CourseParticipant.course_id.in_(course_ids),
            CourseParticipant.status == 'مكتمل'
        ).count() if course_ids else 0

        return {
            'total_courses': total_courses,
            'total_participants': total_participants,
            'total_centers': total_centers,
            'total_graduates': total_graduates
        }

    def _get_chart_data(self, start_date, end_date):
        """الحصول على بيانات الرسوم البيانية"""
        from app import Course, CourseParticipant, TrainingCenter
        from sqlalchemy import func

        # تحويل التواريخ
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')

        # بيانات المستويات
        courses_in_period = Course.query.filter(
            Course.start_date >= start_dt,
            Course.start_date <= end_dt
        ).all()

        level1 = level2 = level3 = 0

        for course in courses_in_period:
            participant_count = CourseParticipant.query.filter_by(course_id=course.id).count()

            if course.level in ['مبتدئ', 'أساسي', 'beginner', '1']:
                level1 += participant_count
            elif course.level in ['متوسط', 'intermediate', '2']:
                level2 += participant_count
            elif course.level in ['متقدم', 'advanced', '3']:
                level3 += participant_count

        # بيانات المراكز
        center_data = self.db.session.query(
            TrainingCenter.name,
            func.count(CourseParticipant.id).label('participant_count')
        ).join(Course, Course.center_id == TrainingCenter.id)\
         .join(CourseParticipant, CourseParticipant.course_id == Course.id)\
         .filter(Course.start_date >= start_dt, Course.start_date <= end_dt)\
         .group_by(TrainingCenter.id, TrainingCenter.name)\
         .order_by(func.count(CourseParticipant.id).desc())\
         .limit(10).all()

        center_names = [row.name for row in center_data]
        center_participants = [row.participant_count for row in center_data]

        # بيانات مسارات التدريب
        course_path_stats = []
        try:
            from app import CoursePath
            path_data = self.db.session.query(
                CoursePath.name,
                func.count(Course.id).label('count')
            ).join(Course, Course.path_id == CoursePath.id)\
             .filter(Course.start_date >= start_dt, Course.start_date <= end_dt)\
             .filter(CoursePath.name.isnot(None))\
             .filter(CoursePath.name != '')\
             .group_by(CoursePath.name).all()

            course_path_stats = [{'name': row.name, 'count': row.count} for row in path_data]
        except Exception as e:
            print(f"خطأ في جلب بيانات مسارات التدريب: {e}")

        # بيانات المراكز حسب الجهات
        center_agency_stats = []
        try:
            from app import Agency
            agency_data = self.db.session.query(
                Agency.name,
                func.count(TrainingCenter.id).label('count')
            ).join(TrainingCenter, TrainingCenter.agency_id == Agency.id)\
             .filter(Agency.name.isnot(None))\
             .filter(Agency.name != '')\
             .group_by(Agency.name).all()

            center_agency_stats = [{'name': row.name, 'count': row.count} for row in agency_data]
        except Exception as e:
            print(f"خطأ في جلب بيانات الجهات: {e}")

        # بيانات المواقع (استخدام مدن المراكز)
        course_location_stats = []
        try:
            location_data = self.db.session.query(
                TrainingCenter.city,
                func.count(Course.id).label('count')
            ).join(Course, Course.center_id == TrainingCenter.id)\
             .filter(Course.start_date >= start_dt, Course.start_date <= end_dt)\
             .filter(TrainingCenter.city.isnot(None))\
             .filter(TrainingCenter.city != '')\
             .group_by(TrainingCenter.city).all()

            course_location_stats = [{'name': row.city, 'count': row.count} for row in location_data]
        except Exception as e:
            print(f"خطأ في جلب بيانات المواقع: {e}")

        # بيانات نوع المشاركين (استخدام الجنس)
        participant_type_stats = []
        try:
            from app import Person
            type_data = self.db.session.query(
                Person.gender,
                func.count(CourseParticipant.id).label('count')
            ).join(CourseParticipant, CourseParticipant.person_id == Person.id)\
             .join(Course, CourseParticipant.course_id == Course.id)\
             .filter(Course.start_date >= start_dt, Course.start_date <= end_dt)\
             .filter(Person.gender.isnot(None))\
             .group_by(Person.gender).all()

            gender_map = {'M': 'ذكر', 'F': 'أنثى'}
            participant_type_stats = [{'name': gender_map.get(row.gender, row.gender), 'count': row.count} for row in type_data]
        except Exception as e:
            print(f"خطأ في جلب بيانات نوع المشاركين: {e}")

        # بيانات تصنيف القوة (استخدام الرتب)
        force_classification_stats = []
        try:
            from app import Person
            rank_data = self.db.session.query(
                Person.rank,
                func.count(CourseParticipant.id).label('count')
            ).join(CourseParticipant, CourseParticipant.person_id == Person.id)\
             .join(Course, CourseParticipant.course_id == Course.id)\
             .filter(Course.start_date >= start_dt, Course.start_date <= end_dt)\
             .filter(Person.rank.isnot(None))\
             .filter(Person.rank != '')\
             .group_by(Person.rank).all()

            force_classification_stats = [{'name': row.rank, 'count': row.count} for row in rank_data]
        except Exception as e:
            print(f"خطأ في جلب بيانات تصنيف القوة: {e}")

        return {
            'level1': level1,
            'level2': level2,
            'level3': level3,
            'centerNames': center_names,
            'centerParticipants': center_participants,
            'coursePathStats': course_path_stats,
            'centerAgencyStats': center_agency_stats,
            'courseLocationStats': course_location_stats,
            'participantTypeStats': participant_type_stats,
            'forceClassificationStats': force_classification_stats
        }

    def _get_table_data(self, start_date, end_date):
        """الحصول على بيانات الجداول التفصيلية"""
        from app import Course, CourseParticipant, TrainingCenter, Agency

        # تحويل التواريخ
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')

        # بيانات جدول المستويات حسب المراكز
        centers = TrainingCenter.query.all()
        level_data = []

        for center in centers:
            courses = Course.query.filter(
                Course.center_id == center.id,
                Course.start_date >= start_dt,
                Course.start_date <= end_dt
            ).all()

            if not courses:
                continue

            level1_courses = level2_courses = level3_courses = 0
            level1_participants = level2_participants = level3_participants = 0
            level1_graduates = level2_graduates = level3_graduates = 0

            for course in courses:
                participants = CourseParticipant.query.filter_by(course_id=course.id).all()
                graduates = CourseParticipant.query.filter_by(course_id=course.id, status='مكتمل').all()

                if course.level in ['مبتدئ', 'أساسي', 'beginner', '1']:
                    level1_courses += 1
                    level1_participants += len(participants)
                    level1_graduates += len(graduates)
                elif course.level in ['متوسط', 'intermediate', '2']:
                    level2_courses += 1
                    level2_participants += len(participants)
                    level2_graduates += len(graduates)
                elif course.level in ['متقدم', 'advanced', '3']:
                    level3_courses += 1
                    level3_participants += len(participants)
                    level3_graduates += len(graduates)

            total = level1_participants + level2_participants + level3_participants

            level_data.append({
                'center': center.name,
                'level1_courses': level1_courses,
                'level1_participants': level1_participants,
                'level1_graduates': level1_graduates,
                'level2_courses': level2_courses,
                'level2_participants': level2_participants,
                'level2_graduates': level2_graduates,
                'level3_courses': level3_courses,
                'level3_participants': level3_participants,
                'level3_graduates': level3_graduates,
                'total': total
            })

        # بيانات جدول المشاركين التفصيلي
        courses_in_period = Course.query.filter(
            Course.start_date >= start_dt,
            Course.start_date <= end_dt
        ).all()

        participants_table_data = []
        for course in courses_in_period:
            center = course.center
            agency_name = center.agency.name if center and center.agency else 'غير محدد'
            center_name = center.name if center else 'غير محدد'

            level_text = 'غير محدد'
            if course.level in ['مبتدئ', 'أساسي', 'beginner', '1']:
                level_text = 'المستوى الأول'
            elif course.level in ['متوسط', 'intermediate', '2']:
                level_text = 'المستوى الثاني'
            elif course.level in ['متقدم', 'advanced', '3']:
                level_text = 'المستوى الثالث'

            participant_count = CourseParticipant.query.filter_by(course_id=course.id).count()

            participants_table_data.append({
                'agency': agency_name,
                'center': center_name,
                'director': course.trainer.username if course.trainer else 'غير محدد',
                'level': level_text,
                'duration': f"{course.duration_days} أيام" if course.duration_days else 'غير محدد',
                'participants': participant_count,
                'notes': course.notes or '-'
            })

        return {
            'levelData': level_data,
            'participantsData': participants_table_data
        }

# إنشاء مثيل من مولد التقارير
reports_generator = ReportsGenerator()
