# نظام التدريب والتأهيل - Docker

## 🐳 تشغيل النظام باستخدام Docker

### المتطلبات الأساسية
- Docker Desktop مثبت على النظام
- Docker Compose (مدمج مع Docker Desktop)

### 🚀 التشغيل السريع

#### 1. تشغيل النظام على المنفذ الافتراضي (5000)
```bash
docker-compose up -d
```

#### 2. الوصول إلى النظام
افتح المتصفح وانتقل إلى: http://localhost:5000

#### 3. بيانات تسجيل الدخول
- **الإيميل**: <EMAIL>
- **كلمة المرور**: admin123

### 🔧 تخصيص المنفذ

#### تغيير المنفذ الخارجي (المنفذ الذي تصل إليه من المتصفح)
```bash
# تشغيل النظام على المنفذ 8080 بدلاً من 5000
docker-compose -f docker-compose.yml up -d --build -e EXTERNAL_PORT=8080
```

أو تعديل ملف docker-compose.yml:
```yaml
ports:
  - "8080:5000"  # تغيير 8080 إلى المنفذ المطلوب
```

#### تغيير المنفذ الداخلي للتطبيق
```bash
# تشغيل التطبيق على منفذ داخلي مختلف
docker run -d -p 5000:8080 -e PORT=8080 training_system
```

### 📋 أوامر Docker المفيدة

#### بناء الصورة
```bash
docker build -t training_system .
```

#### تشغيل الحاوية
```bash
docker run -d -p 5000:5000 --name training_system training_system
```

#### تشغيل على منفذ مختلف
```bash
# تشغيل على المنفذ 8080
docker run -d -p 8080:5000 -e PORT=5000 --name training_system training_system
```

#### عرض السجلات
```bash
docker-compose logs -f
```

#### إيقاف النظام
```bash
docker-compose down
```

#### إعادة بناء وتشغيل
```bash
docker-compose up -d --build
```

### 💾 إدارة البيانات

#### النسخ الاحتياطي لقاعدة البيانات
```bash
docker cp training_system:/app/training_system.db ./backup_$(date +%Y%m%d_%H%M%S).db
```

#### استعادة قاعدة البيانات
```bash
docker cp ./backup_file.db training_system:/app/training_system.db
docker-compose restart
```

### 🔍 استكشاف الأخطاء

#### دخول إلى الحاوية
```bash
docker exec -it training_system bash
```

#### فحص حالة الحاوية
```bash
docker ps
docker-compose ps
```

#### عرض استخدام الموارد
```bash
docker stats training_system
```

### 🌐 أمثلة على تشغيل النظام على منافذ مختلفة

#### المنفذ 3000
```bash
docker run -d -p 3000:5000 --name training_system_3000 training_system
```

#### المنفذ 8080
```bash
docker run -d -p 8080:5000 --name training_system_8080 training_system
```

#### تشغيل عدة نسخ على منافذ مختلفة
```bash
# النسخة الأولى على المنفذ 5000
docker run -d -p 5000:5000 --name training_system_5000 training_system

# النسخة الثانية على المنفذ 5001
docker run -d -p 5001:5000 --name training_system_5001 training_system

# النسخة الثالثة على المنفذ 8080
docker run -d -p 8080:5000 --name training_system_8080 training_system
```

### ⚙️ متغيرات البيئة المتاحة

| المتغير | الوصف | القيمة الافتراضية |
|---------|--------|------------------|
| `PORT` | المنفذ الداخلي للتطبيق | 5000 |
| `FLASK_ENV` | بيئة Flask | production |
| `PYTHONDONTWRITEBYTECODE` | منع إنشاء ملفات .pyc | 1 |
| `PYTHONUNBUFFERED` | إخراج مباشر للسجلات | 1 |

### 🔒 الأمان

- التطبيق يعمل بمستخدم غير جذر داخل الحاوية
- قاعدة البيانات محفوظة خارج الحاوية
- الملفات المرفوعة محفوظة خارج الحاوية

### 📝 ملاحظات مهمة

1. **حفظ البيانات**: البيانات محفوظة في ملفات خارج الحاوية، لذا لن تفقد البيانات عند إعادة تشغيل الحاوية
2. **الأداء**: النظام محسن للعمل في بيئة الإنتاج
3. **التحديثات**: لتطبيق التحديثات، استخدم `docker-compose up -d --build`
4. **النسخ الاحتياطي**: احرص على عمل نسخ احتياطية دورية لقاعدة البيانات

### 🆘 الدعم الفني

في حالة وجود مشاكل:
1. تحقق من سجلات النظام: `docker-compose logs`
2. تأكد من أن المنفذ غير مستخدم من برنامج آخر
3. أعد تشغيل النظام: `docker-compose restart`
