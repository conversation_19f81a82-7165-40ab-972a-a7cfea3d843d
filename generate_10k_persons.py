#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء ملف Excel يحتوي على 10,000 شخص لاختبار الاستيراد
"""

import pandas as pd
import random
from datetime import datetime, timedelta
import uuid

def generate_arabic_names():
    """إنشاء أسماء عربية"""
    
    # أسماء أولى للذكور
    male_first_names = [
        'محمد', 'أحمد', 'علي', 'حسن', 'حسين', 'عبدالله', 'عبدالرحمن', 'عبدالعزيز',
        'خالد', 'سعد', 'فهد', 'عبدالمجيد', 'عبدالكريم', 'يوسف', 'إبراهيم', 'عمر',
        'عثمان', 'زياد', 'طارق', 'ماجد', 'سلطان', 'بندر', 'تركي', 'نواف',
        'سلمان', 'فيصل', 'عبدالإله', 'عبدالمحسن', 'عبدالناصر', 'عبدالحميد', 'عبدالقادر', 'عبدالرزاق',
        'محمود', 'مصطفى', 'حمد', 'سعود', 'راشد', 'مشعل', 'بدر', 'نايف',
        'عادل', 'وليد', 'هشام', 'كريم', 'أمين', 'جمال', 'كمال', 'نبيل',
        'صالح', 'عامر', 'ياسر', 'مازن', 'هاني', 'رامي', 'سامي', 'عاصم'
    ]
    
    # أسماء أولى للإناث
    female_first_names = [
        'فاطمة', 'عائشة', 'خديجة', 'زينب', 'مريم', 'سارة', 'نورا', 'هند',
        'أمل', 'رنا', 'ريم', 'لينا', 'دانا', 'رهف', 'غلا', 'شهد',
        'جود', 'لمى', 'رغد', 'أسماء', 'هيا', 'منى', 'سمر', 'نهى',
        'ندى', 'رؤى', 'أريج', 'بشرى', 'سلمى', 'ليلى', 'هيفاء', 'وفاء',
        'إيمان', 'أمان', 'حنان', 'سناء', 'هناء', 'رباب', 'زهراء', 'شيماء',
        'دعاء', 'رجاء', 'نجلاء', 'علياء', 'صفاء', 'ضحى', 'سحر', 'نجوى',
        'سميرة', 'كريمة', 'حليمة', 'سعاد', 'زهرة', 'وردة', 'ياسمين', 'جميلة'
    ]
    
    # أسماء العائلات
    family_names = [
        'العتيبي', 'المطيري', 'الدوسري', 'الشمري', 'القحطاني', 'الغامدي', 'الزهراني', 'الحربي',
        'العنزي', 'الرشيد', 'آل سعود', 'آل الشيخ', 'الفيصل', 'السديري', 'الثنيان', 'الكبير',
        'الصغير', 'الطويل', 'القصير', 'الأحمر', 'الأسود', 'الأبيض', 'الأخضر', 'الأزرق',
        'المالكي', 'الهاشمي', 'العباسي', 'الأموي', 'الفاطمي', 'العلوي', 'الحسني', 'الحسيني',
        'البكري', 'العمري', 'العثماني', 'الصديقي', 'الفاروقي', 'الكرار', 'الأنصاري', 'المهاجر',
        'التميمي', 'الخزرجي', 'الأوسي', 'القرشي', 'الهاشمي', 'المخزومي', 'الزهري', 'الأدرعي',
        'السلمي', 'الجهني', 'البلوي', 'الحويطي', 'العطوي', 'الرويلي', 'الشرعي', 'البقمي',
        'الثقفي', 'الهذلي', 'الكناني', 'الليثي', 'الخثعمي', 'الأزدي', 'الكندي', 'الحميري'
    ]
    
    return male_first_names, female_first_names, family_names

def generate_cities():
    """إنشاء أسماء المدن السعودية"""
    return [
        'الرياض', 'جدة', 'مكة المكرمة', 'المدينة المنورة', 'الدمام', 'الخبر', 'الظهران',
        'تبوك', 'بريدة', 'خميس مشيط', 'حائل', 'الجبيل', 'الطائف', 'ينبع', 'أبها',
        'نجران', 'جازان', 'عرعر', 'سكاكا', 'القطيف', 'الأحساء', 'الباحة', 'القنفذة',
        'محايل عسير', 'بيشة', 'الخرج', 'الزلفي', 'المجمعة', 'الدوادمي', 'عفيف', 'شقراء',
        'القويعية', 'وادي الدواسر', 'السليل', 'الخماسين', 'حوطة بني تميم', 'الأفلاج', 'رماح'
    ]

def generate_departments():
    """إنشاء أسماء الأقسام"""
    return [
        'الموارد البشرية', 'المالية', 'التسويق', 'المبيعات', 'تقنية المعلومات', 'الهندسة',
        'الإنتاج', 'الجودة', 'الأمن والسلامة', 'الخدمات العامة', 'العلاقات العامة', 'القانونية',
        'التدريب والتطوير', 'البحث والتطوير', 'المشتريات', 'المخازن', 'النقل واللوجستيات',
        'خدمة العملاء', 'الصيانة', 'الإدارة العليا', 'التخطيط الاستراتيجي', 'إدارة المشاريع',
        'المحاسبة', 'المراجعة الداخلية', 'إدارة المخاطر', 'الامتثال', 'التطوير التنظيمي'
    ]

def generate_job_titles():
    """إنشاء المسميات الوظيفية"""
    return [
        'مدير عام', 'مدير إدارة', 'مدير قسم', 'رئيس قسم', 'مشرف', 'موظف أول', 'موظف',
        'محاسب', 'محاسب أول', 'مراجع', 'مراجع أول', 'مهندس', 'مهندس أول', 'فني',
        'فني أول', 'أخصائي', 'أخصائي أول', 'مستشار', 'خبير', 'محلل', 'محلل أول',
        'منسق', 'منسق أول', 'مساعد إداري', 'سكرتير', 'سكرتير تنفيذي', 'مترجم',
        'مصمم', 'مصمم جرافيك', 'مطور', 'مطور أول', 'مبرمج', 'مبرمج أول', 'محرر',
        'كاتب', 'باحث', 'مستشار قانوني', 'مدقق', 'مراقب', 'حارس أمن', 'سائق'
    ]

def generate_education_levels():
    """إنشاء المستويات التعليمية"""
    return [
        'دكتوراه', 'ماجستير', 'بكالوريوس', 'دبلوم عالي', 'دبلوم', 'ثانوية عامة',
        'متوسط', 'ابتدائي', 'دورات تخصصية', 'شهادات مهنية'
    ]

def generate_phone_number():
    """إنشاء رقم هاتف سعودي"""
    prefixes = ['050', '053', '054', '055', '056', '057', '058', '059']
    prefix = random.choice(prefixes)
    number = ''.join([str(random.randint(0, 9)) for _ in range(7)])
    return f"{prefix}{number}"

def generate_email(first_name, last_name):
    """إنشاء بريد إلكتروني"""
    domains = ['gmail.com', 'hotmail.com', 'yahoo.com', 'outlook.com', 'company.com.sa']
    # تحويل الأسماء العربية إلى أرقام أو أحرف إنجليزية
    email_name = f"user{random.randint(1000, 9999)}"
    domain = random.choice(domains)
    return f"{email_name}@{domain}"

def generate_national_id():
    """إنشاء رقم هوية وطنية (10 أرقام)"""
    # رقم الهوية السعودية يبدأ بـ 1 أو 2
    first_digit = random.choice([1, 2])
    remaining_digits = ''.join([str(random.randint(0, 9)) for _ in range(9)])
    return f"{first_digit}{remaining_digits}"

def generate_birth_date():
    """إنشاء تاريخ ميلاد"""
    start_date = datetime(1960, 1, 1)
    end_date = datetime(2005, 12, 31)
    time_between = end_date - start_date
    days_between = time_between.days
    random_days = random.randrange(days_between)
    birth_date = start_date + timedelta(days=random_days)
    return birth_date.strftime('%Y-%m-%d')

def generate_salary():
    """إنشاء راتب"""
    salary_ranges = [
        (3000, 5000),   # موظف عادي
        (5000, 8000),   # موظف أول
        (8000, 12000),  # مشرف
        (12000, 20000), # مدير قسم
        (20000, 35000), # مدير إدارة
        (35000, 60000)  # مدير عام
    ]
    min_salary, max_salary = random.choice(salary_ranges)
    return random.randint(min_salary, max_salary)

def generate_10k_persons():
    """إنشاء 10,000 شخص"""
    
    print("🚀 بدء إنشاء ملف 10,000 شخص...")
    
    # الحصول على البيانات الأساسية
    male_first_names, female_first_names, family_names = generate_arabic_names()
    cities = generate_cities()
    departments = generate_departments()
    job_titles = generate_job_titles()
    education_levels = generate_education_levels()
    
    persons = []
    
    print("📊 إنشاء البيانات...")
    
    for i in range(10000):
        if i % 1000 == 0:
            print(f"   تم إنشاء {i} شخص...")
        
        # تحديد الجنس
        gender = random.choice(['ذكر', 'أنثى'])
        
        # اختيار الاسم الأول حسب الجنس
        if gender == 'ذكر':
            first_name = random.choice(male_first_names)
        else:
            first_name = random.choice(female_first_names)
        
        # اختيار اسم العائلة
        last_name = random.choice(family_names)
        
        # إنشاء بيانات الشخص
        person = {
            'الرقم': i + 1,
            'الاسم الأول': first_name,
            'الاسم الأخير': last_name,
            'الاسم الكامل': f"{first_name} {last_name}",
            'الجنس': gender,
            'تاريخ الميلاد': generate_birth_date(),
            'رقم الهوية الوطنية': generate_national_id(),
            'رقم الهاتف': generate_phone_number(),
            'البريد الإلكتروني': generate_email(first_name, last_name),
            'المدينة': random.choice(cities),
            'القسم': random.choice(departments),
            'المسمى الوظيفي': random.choice(job_titles),
            'المستوى التعليمي': random.choice(education_levels),
            'الراتب': generate_salary(),
            'تاريخ التوظيف': generate_birth_date(),
            'الحالة': random.choice(['نشط', 'غير نشط', 'إجازة', 'منتدب']),
            'ملاحظات': random.choice(['', 'موظف متميز', 'يحتاج تدريب', 'مرشح للترقية', ''])
        }
        
        persons.append(person)
    
    print("📁 إنشاء ملف Excel...")
    
    # إنشاء DataFrame
    df = pd.DataFrame(persons)
    
    # حفظ الملف
    filename = f'اختبار_استيراد_10000_شخص_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    
    # إنشاء ملف Excel مع تنسيق
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='الأشخاص', index=False)
        
        # الحصول على worksheet للتنسيق
        worksheet = writer.sheets['الأشخاص']
        
        # تعديل عرض الأعمدة
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    print(f"✅ تم إنشاء الملف: {filename}")
    
    # إنشاء ملخص
    summary = {
        'إجمالي الأشخاص': len(persons),
        'الذكور': len([p for p in persons if p['الجنس'] == 'ذكر']),
        'الإناث': len([p for p in persons if p['الجنس'] == 'أنثى']),
        'المدن المختلفة': len(set([p['المدينة'] for p in persons])),
        'الأقسام المختلفة': len(set([p['القسم'] for p in persons])),
        'المسميات الوظيفية': len(set([p['المسمى الوظيفي'] for p in persons])),
        'متوسط الراتب': sum([p['الراتب'] for p in persons]) / len(persons)
    }
    
    print("\n📊 ملخص البيانات:")
    for key, value in summary.items():
        if key == 'متوسط الراتب':
            print(f"   {key}: {value:,.0f} ريال")
        else:
            print(f"   {key}: {value:,}")
    
    return filename, summary

def create_import_test_script():
    """إنشاء سكريبت اختبار الاستيراد"""
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار استيراد 10,000 شخص إلى النظام
"""

import pandas as pd
import requests
import time
import json
from datetime import datetime

def test_import_10k_persons(excel_file):
    """اختبار استيراد 10,000 شخص"""
    
    print("🧪 اختبار استيراد 10,000 شخص")
    print("=" * 50)
    
    # قراءة ملف Excel
    print("📖 قراءة ملف Excel...")
    try:
        df = pd.read_excel(excel_file)
        print(f"   ✅ تم قراءة {len(df)} شخص من الملف")
    except Exception as e:
        print(f"   ❌ خطأ في قراءة الملف: {e}")
        return False
    
    # تسجيل الدخول
    print("🔐 تسجيل الدخول...")
    session = requests.Session()
    
    login_page = session.get('http://localhost:5000/login')
    csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
    csrf_token = csrf_match.group(1) if csrf_match else None
    
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    if csrf_token:
        login_data['csrf_token'] = csrf_token
    
    response = session.post('http://localhost:5000/login', data=login_data)
    
    if 'dashboard' not in response.url:
        print("   ❌ فشل تسجيل الدخول")
        return False
    
    print("   ✅ تم تسجيل الدخول بنجاح")
    
    # اختبار الاستيراد على دفعات
    batch_sizes = [100, 500, 1000, 2000, 5000, 10000]
    
    for batch_size in batch_sizes:
        print(f"\\n📦 اختبار استيراد {batch_size} شخص...")
        
        # أخذ عينة من البيانات
        sample_df = df.head(batch_size)
        
        # تحويل إلى JSON
        persons_data = sample_df.to_dict('records')
        
        # قياس الوقت
        start_time = time.time()
        
        try:
            # إرسال البيانات
            response = session.post(
                'http://localhost:5000/api/import-persons',
                json={'persons': persons_data},
                headers={'Content-Type': 'application/json'}
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ نجح الاستيراد في {duration:.2f} ثانية")
                    print(f"      📊 تم استيراد: {result.get('imported', 0)} شخص")
                    print(f"      ⚠️ تم تخطي: {result.get('skipped', 0)} شخص")
                    print(f"      🚀 معدل الاستيراد: {batch_size/duration:.0f} شخص/ثانية")
                else:
                    print(f"   ❌ فشل الاستيراد: {result.get('message')}")
            else:
                print(f"   ❌ خطأ HTTP: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ خطأ في الاستيراد: {e}")
    
    return True

if __name__ == '__main__':
    import re
    
    # البحث عن ملف Excel
    import glob
    excel_files = glob.glob('اختبار_استيراد_10000_شخص_*.xlsx')
    
    if excel_files:
        latest_file = max(excel_files)
        print(f"📁 استخدام الملف: {latest_file}")
        test_import_10k_persons(latest_file)
    else:
        print("❌ لم يتم العثور على ملف Excel للاختبار")
        print("يرجى تشغيل generate_10k_persons.py أولاً")
'''
    
    with open('test_import_10k.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("📝 تم إنشاء ملف اختبار الاستيراد: test_import_10k.py")

def main():
    """الدالة الرئيسية"""
    
    print("🎯 مولد ملف اختبار استيراد 10,000 شخص")
    print("=" * 60)
    
    try:
        # إنشاء ملف الأشخاص
        filename, summary = generate_10k_persons()
        
        # إنشاء سكريبت الاختبار
        create_import_test_script()
        
        print(f"\\n🎉 تم الإنجاز بنجاح!")
        print(f"📁 ملف Excel: {filename}")
        print(f"📝 ملف الاختبار: test_import_10k.py")
        
        print(f"\\n📋 خطوات الاختبار:")
        print(f"1. تأكد من تشغيل الخادم: python app.py")
        print(f"2. شغل اختبار الاستيراد: python test_import_10k.py")
        print(f"3. راقب الأداء والسرعة")
        print(f"4. تحقق من استيراد البيانات في النظام")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == '__main__':
    main()
