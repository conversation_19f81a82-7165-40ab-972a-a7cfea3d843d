#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار التطبيق الأصلي للتأكد من عمل الاستثناء
"""

import requests
import json

def test_original_app_search():
    """اختبار البحث في التطبيق الأصلي"""
    print("🔍 اختبار البحث في التطبيق الأصلي (app.py)...")
    
    try:
        # اختبار البحث عن "علي" في التطبيق الأصلي
        response = requests.get(
            "http://localhost:5001/course/1/search_people?q=علي", 
            timeout=10
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"📊 نتائج البحث في التطبيق الأصلي: {len(results)} شخص")
            
            # البحث عن "علي صالح محمد الحميري1"
            found_ali = None
            for person in results:
                print(f"   - {person['name']} (ID: {person['id']}) - مشارك: {person.get('is_participant', 'غير محدد')}")
                if "علي صالح محمد الحميري1" in person['name']:
                    found_ali = person
            
            if found_ali:
                print(f"\n❌ المشكلة في التطبيق الأصلي: تم العثور على {found_ali['name']}!")
                print(f"   ID: {found_ali['id']}")
                print(f"   مشارك: {found_ali.get('is_participant', 'غير محدد')}")
                return True
            else:
                print("\n✅ التطبيق الأصلي يعمل بشكل صحيح - لا يظهر المشاركين الحاليين")
                return False
        else:
            print(f"❌ خطأ في التطبيق الأصلي: {response.status_code}")
            print(f"📄 الاستجابة: {response.text[:200]}")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التطبيق الأصلي: {e}")
        return None

def test_both_apps():
    """مقارنة بين التطبيقين"""
    print("🔄 مقارنة بين التطبيق الأصلي والبسيط...")
    
    # اختبار التطبيق الأصلي
    print("\n1️⃣ التطبيق الأصلي (localhost:5001):")
    original_has_issue = test_original_app_search()
    
    # اختبار التطبيق البسيط
    print("\n2️⃣ التطبيق البسيط (localhost:5002):")
    try:
        response = requests.get("http://localhost:5002/course/1/search_people?q=علي", timeout=10)
        if response.status_code == 200:
            results = response.json()
            print(f"📊 نتائج البحث في التطبيق البسيط: {len(results)} شخص")
            
            found_ali = any("علي صالح محمد الحميري1" in person['name'] for person in results)
            
            if found_ali:
                print("❌ التطبيق البسيط يظهر المشاركين الحاليين")
                simple_has_issue = True
            else:
                print("✅ التطبيق البسيط لا يظهر المشاركين الحاليين")
                simple_has_issue = False
        else:
            print(f"❌ خطأ في التطبيق البسيط: {response.status_code}")
            simple_has_issue = None
    except Exception as e:
        print(f"❌ خطأ في اختبار التطبيق البسيط: {e}")
        simple_has_issue = None
    
    # الخلاصة
    print("\n" + "="*50)
    print("📊 خلاصة المقارنة:")
    print(f"   التطبيق الأصلي: {'❌ يظهر المشاركين' if original_has_issue else '✅ لا يظهر المشاركين' if original_has_issue is False else '❓ غير محدد'}")
    print(f"   التطبيق البسيط: {'❌ يظهر المشاركين' if simple_has_issue else '✅ لا يظهر المشاركين' if simple_has_issue is False else '❓ غير محدد'}")

def test_manage_participants_page():
    """اختبار صفحة إدارة المشاركين"""
    print("\n🌐 اختبار صفحة إدارة المشاركين...")
    
    try:
        response = requests.get("http://localhost:5001/manage_participants/1/", timeout=10)
        
        if response.status_code == 200:
            print("✅ صفحة إدارة المشاركين تعمل")
            print("🌐 يمكنك فتح الرابط في المتصفح:")
            print("   http://localhost:5001/manage_participants/1/")
        else:
            print(f"❌ خطأ في صفحة إدارة المشاركين: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الصفحة: {e}")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل للتطبيق الأصلي")
    print("=" * 50)
    
    # اختبار التطبيقين
    test_both_apps()
    
    # اختبار صفحة إدارة المشاركين
    test_manage_participants_page()
    
    print("\n" + "="*50)
    print("💡 توصيات:")
    print("1. افتح صفحة إدارة المشاركين في المتصفح")
    print("2. جرب البحث عن 'علي صالح محمد الحميري1'")
    print("3. تأكد من عدم ظهوره في النتائج")
    print("4. إذا ظهر، فالمشكلة في الواجهة الأمامية")

if __name__ == "__main__":
    main()
