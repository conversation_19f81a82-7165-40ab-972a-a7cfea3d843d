import sqlite3
import os

# تحديد مسار قاعدة البيانات
DB_PATH = 'training_system.db'  # مسار قاعدة البيانات المستخدم في التطبيق

def add_work_place_text_column():
    """إضافة عمود work_place_text إلى جدول personal_data"""
    try:
        # التحقق من وجود قاعدة البيانات
        if not os.path.exists(DB_PATH):
            print(f"خطأ: قاعدة البيانات غير موجودة في المسار {DB_PATH}")
            return False

        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(personal_data)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        if 'work_place_text' in column_names:
            print("العمود work_place_text موجود بالفعل في الجدول")
            conn.close()
            return True

        # إضافة العمود الجديد
        cursor.execute("ALTER TABLE personal_data ADD COLUMN work_place_text TEXT")

        # نسخ البيانات من work_place إلى work_place_text
        cursor.execute("UPDATE personal_data SET work_place_text = work_place WHERE work_place IS NOT NULL")

        # حفظ التغييرات
        conn.commit()
        print("تم إضافة العمود work_place_text بنجاح")

        # إغلاق الاتصال
        conn.close()
        return True

    except Exception as e:
        print(f"حدث خطأ أثناء تحديث قاعدة البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    add_work_place_text_column()
