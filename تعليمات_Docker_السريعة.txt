========================================
    نظام التدريب والتأهيل - Docker
========================================

🎯 تم تجميع جميع ملفات Docker في مجلد منفصل!

📍 المسار: E:\app\TRINING\Docker

========================================
🚀 للبدء السريع:
========================================

1️⃣ انتقل إلى مجلد Docker:
   E:\app\TRINING\Docker

2️⃣ انقر نقراً مزدوجاً على ملف:
   START_HERE.bat

3️⃣ اختر طريقة التشغيل المناسبة:
   - المنفذ 5000 (افتراضي)
   - المنفذ 5001
   - المنفذ 8080
   - منفذ مخصص

========================================
📁 محتويات مجلد Docker:
========================================

📋 ملفات التشغيل السريع:
- START_HERE.bat (ابدأ من هنا)
- docker_start.bat (المنفذ 5000)
- docker_start_5001.bat (المنفذ 5001)
- docker_start_8080.bat (المنفذ 8080)
- docker_custom_port.bat (منفذ مخصص)
- docker_stop.bat (إيقاف النظام)

🔧 ملفات Docker:
- Dockerfile
- docker-compose.yml
- docker-compose-5001.yml
- docker-compose-8080.yml

📚 ملفات التوثيق:
- README.md
- README_DOCKER.md
- DOCKER_README.md
- تعليمات_Docker.txt

🗂️ ملفات النظام:
- app.py
- training_system.db
- requirements.txt
- templates/ (مجلد القوالب)
- static/ (مجلد الملفات الثابتة)

========================================
🔑 بيانات تسجيل الدخول:
========================================

الإيميل: <EMAIL>
كلمة المرور: admin123

========================================
🌐 الوصول إلى النظام:
========================================

بعد التشغيل، افتح المتصفح وانتقل إلى:
- المنفذ 5000: http://localhost:5000
- المنفذ 5001: http://localhost:5001
- المنفذ 8080: http://localhost:8080

========================================
✨ المميزات:
========================================

✅ تشغيل سهل بنقرة واحدة
✅ إمكانية تشغيل عدة نسخ على منافذ مختلفة
✅ لا يحتاج إلى تثبيت Python
✅ يعمل على أي نظام يدعم Docker
✅ حفظ البيانات خارج الحاوية
✅ أمان عالي وعزل كامل

========================================
📞 للمساعدة:
========================================

راجع الملفات التالية في مجلد Docker:
- تعليمات_Docker.txt (تعليمات شاملة)
- README_DOCKER.md (دليل تفصيلي)

========================================
🔄 إصدار النظام: 2025.1 - Docker
📅 تاريخ الإصدار: يونيو 2025
📍 مجلد Docker: E:\app\TRINING\Docker
========================================
