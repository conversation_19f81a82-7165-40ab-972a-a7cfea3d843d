{% extends "layout.html" %}

{% block content %}
<div class="container">
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">إضافة نوع مركز تدريبي</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                {{ form.hidden_tag() }}
                <div class="mb-3">
                    {{ form.name.label(class="form-label") }}
                    {% if form.name.errors %}
                        {{ form.name(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.name.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.name(class="form-control") }}
                    {% endif %}
                </div>
                <div class="mb-3">
                    {{ form.description.label(class="form-label") }}
                    {% if form.description.errors %}
                        {{ form.description(class="form-control is-invalid", rows=3) }}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.description(class="form-control", rows=3) }}
                    {% endif %}
                </div>
                <div class="d-flex justify-content-between">
                    {{ form.submit(class="btn btn-primary") }}
                    <a href="{{ url_for('training_center_types') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
