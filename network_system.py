#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌐 نظام الشبكة المتقدم
Advanced Network System for Multi-User Access
"""

import os
import sys
import socket
import threading
import json
import ssl
import time
import hashlib
import secrets
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
from flask import Flask, request, session, jsonify
from werkzeug.security import generate_password_hash, check_password_hash
import sqlite3

class NetworkManager:
    """مدير الشبكة المتقدم"""
    
    def __init__(self):
        self.active_connections = {}
        self.user_sessions = {}
        self.security_manager = SecurityManager()
        self.load_balancer = LoadBalancer()
        self.logger = self.setup_logging()
        
    def setup_logging(self):
        """إعداد نظام التسجيل للشبكة"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logger = logging.getLogger('network')
        logger.setLevel(logging.INFO)
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # معالج ملف للشبكة
        network_handler = logging.FileHandler(
            log_dir / f"network_{datetime.now().strftime('%Y%m%d')}.log",
            encoding='utf-8'
        )
        network_handler.setFormatter(formatter)
        logger.addHandler(network_handler)
        
        return logger
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            # إنشاء socket مؤقت للحصول على IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "127.0.0.1"
    
    def scan_network_ports(self, host="localhost", start_port=5000, end_port=5010):
        """فحص المنافذ المتاحة"""
        available_ports = []
        
        for port in range(start_port, end_port + 1):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                if result != 0:  # المنفذ متاح
                    available_ports.append(port)
                sock.close()
            except:
                continue
        
        return available_ports
    
    def create_server_config(self, host="0.0.0.0", port=5000):
        """إنشاء إعدادات الخادم"""
        local_ip = self.get_local_ip()
        available_ports = self.scan_network_ports()
        
        if port not in available_ports and available_ports:
            port = available_ports[0]
        
        config = {
            'host': host,
            'port': port,
            'local_ip': local_ip,
            'external_urls': [
                f"http://{local_ip}:{port}",
                f"http://localhost:{port}",
                f"http://127.0.0.1:{port}"
            ],
            'ssl_enabled': False,
            'max_connections': 100,
            'timeout': 30,
            'created_at': datetime.now().isoformat()
        }
        
        return config
    
    def setup_ssl(self, cert_file=None, key_file=None):
        """إعداد SSL للأمان"""
        if not cert_file or not key_file:
            # إنشاء شهادة ذاتية التوقيع
            cert_file, key_file = self.generate_self_signed_cert()
        
        ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        ssl_context.load_cert_chain(cert_file, key_file)
        
        return ssl_context
    
    def generate_self_signed_cert(self):
        """إنشاء شهادة SSL ذاتية التوقيع"""
        try:
            from cryptography import x509
            from cryptography.x509.oid import NameOID
            from cryptography.hazmat.primitives import hashes
            from cryptography.hazmat.primitives.asymmetric import rsa
            from cryptography.hazmat.primitives import serialization
            
            # إنشاء مفتاح خاص
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
            )
            
            # إنشاء الشهادة
            subject = issuer = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, "SA"),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Riyadh"),
                x509.NameAttribute(NameOID.LOCALITY_NAME, "Riyadh"),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Training System"),
                x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
            ])
            
            cert = x509.CertificateBuilder().subject_name(
                subject
            ).issuer_name(
                issuer
            ).public_key(
                private_key.public_key()
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                datetime.utcnow()
            ).not_valid_after(
                datetime.utcnow() + timedelta(days=365)
            ).add_extension(
                x509.SubjectAlternativeName([
                    x509.DNSName("localhost"),
                    x509.DNSName("127.0.0.1"),
                ]),
                critical=False,
            ).sign(private_key, hashes.SHA256())
            
            # حفظ الملفات
            cert_dir = Path("ssl")
            cert_dir.mkdir(exist_ok=True)
            
            cert_file = cert_dir / "cert.pem"
            key_file = cert_dir / "key.pem"
            
            with open(cert_file, "wb") as f:
                f.write(cert.public_bytes(serialization.Encoding.PEM))
            
            with open(key_file, "wb") as f:
                f.write(private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                ))
            
            return str(cert_file), str(key_file)
            
        except ImportError:
            self.logger.warning("cryptography library not available, SSL disabled")
            return None, None
    
    def monitor_connections(self):
        """مراقبة الاتصالات النشطة"""
        def monitor():
            while True:
                try:
                    current_time = time.time()
                    expired_sessions = []
                    
                    for session_id, session_data in self.user_sessions.items():
                        if current_time - session_data['last_activity'] > 1800:  # 30 دقيقة
                            expired_sessions.append(session_id)
                    
                    for session_id in expired_sessions:
                        del self.user_sessions[session_id]
                        self.logger.info(f"Expired session: {session_id}")
                    
                    time.sleep(60)  # فحص كل دقيقة
                    
                except Exception as e:
                    self.logger.error(f"Error in connection monitoring: {str(e)}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def get_network_status(self):
        """الحصول على حالة الشبكة"""
        return {
            'active_connections': len(self.active_connections),
            'active_sessions': len(self.user_sessions),
            'local_ip': self.get_local_ip(),
            'timestamp': datetime.now().isoformat()
        }

class SecurityManager:
    """مدير الأمان"""
    
    def __init__(self):
        self.failed_attempts = {}
        self.blocked_ips = {}
        self.session_tokens = {}
        
    def generate_session_token(self, user_id: str) -> str:
        """إنشاء رمز جلسة آمن"""
        token = secrets.token_urlsafe(32)
        self.session_tokens[token] = {
            'user_id': user_id,
            'created_at': datetime.now(),
            'last_used': datetime.now()
        }
        return token
    
    def validate_session_token(self, token: str) -> Optional[str]:
        """التحقق من صحة رمز الجلسة"""
        if token in self.session_tokens:
            session_data = self.session_tokens[token]
            
            # فحص انتهاء الصلاحية
            if datetime.now() - session_data['created_at'] > timedelta(hours=8):
                del self.session_tokens[token]
                return None
            
            # تحديث آخر استخدام
            session_data['last_used'] = datetime.now()
            return session_data['user_id']
        
        return None
    
    def check_rate_limit(self, ip_address: str, max_attempts=5, window_minutes=15) -> bool:
        """فحص حد المحاولات"""
        current_time = datetime.now()
        
        if ip_address in self.failed_attempts:
            attempts = self.failed_attempts[ip_address]
            # تنظيف المحاولات القديمة
            attempts = [attempt for attempt in attempts 
                       if current_time - attempt < timedelta(minutes=window_minutes)]
            self.failed_attempts[ip_address] = attempts
            
            if len(attempts) >= max_attempts:
                return False
        
        return True
    
    def record_failed_attempt(self, ip_address: str):
        """تسجيل محاولة فاشلة"""
        if ip_address not in self.failed_attempts:
            self.failed_attempts[ip_address] = []
        
        self.failed_attempts[ip_address].append(datetime.now())
    
    def is_ip_blocked(self, ip_address: str) -> bool:
        """فحص ما إذا كان IP محظور"""
        if ip_address in self.blocked_ips:
            block_time = self.blocked_ips[ip_address]
            if datetime.now() - block_time > timedelta(hours=1):
                del self.blocked_ips[ip_address]
                return False
            return True
        return False
    
    def block_ip(self, ip_address: str):
        """حظر عنوان IP"""
        self.blocked_ips[ip_address] = datetime.now()

class LoadBalancer:
    """موزع الأحمال"""
    
    def __init__(self):
        self.servers = []
        self.current_server = 0
        
    def add_server(self, host: str, port: int):
        """إضافة خادم"""
        self.servers.append({'host': host, 'port': port, 'active': True})
    
    def get_next_server(self) -> Optional[Dict]:
        """الحصول على الخادم التالي (Round Robin)"""
        if not self.servers:
            return None
        
        active_servers = [s for s in self.servers if s['active']]
        if not active_servers:
            return None
        
        server = active_servers[self.current_server % len(active_servers)]
        self.current_server += 1
        return server
    
    def check_server_health(self, server: Dict) -> bool:
        """فحص صحة الخادم"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((server['host'], server['port']))
            sock.close()
            return result == 0
        except:
            return False

class NetworkFlaskApp:
    """تطبيق Flask محسن للشبكة"""
    
    def __init__(self, app: Flask):
        self.app = app
        self.network_manager = NetworkManager()
        self.setup_network_routes()
        self.setup_middleware()
    
    def setup_middleware(self):
        """إعداد middleware للشبكة"""
        
        @self.app.before_request
        def before_request():
            # فحص IP المحظور
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            if self.network_manager.security_manager.is_ip_blocked(client_ip):
                return jsonify({'error': 'IP blocked'}), 403
            
            # فحص حد المحاولات
            if not self.network_manager.security_manager.check_rate_limit(client_ip):
                return jsonify({'error': 'Rate limit exceeded'}), 429
        
        @self.app.after_request
        def after_request(response):
            # إضافة headers الأمان
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            
            return response
    
    def setup_network_routes(self):
        """إعداد مسارات الشبكة"""
        
        @self.app.route('/api/network/status')
        def network_status():
            """حالة الشبكة"""
            return jsonify(self.network_manager.get_network_status())
        
        @self.app.route('/api/network/config')
        def network_config():
            """إعدادات الشبكة"""
            config = self.network_manager.create_server_config()
            return jsonify(config)
    
    def run_network_server(self, host='0.0.0.0', port=5000, ssl_enabled=False):
        """تشغيل الخادم مع إعدادات الشبكة"""
        config = self.network_manager.create_server_config(host, port)
        
        print("🌐 إعدادات الشبكة:")
        print("=" * 40)
        print(f"🖥️  الخادم المحلي: http://localhost:{config['port']}")
        print(f"🌍 عنوان IP المحلي: {config['local_ip']}")
        print(f"📡 الوصول من الشبكة: http://{config['local_ip']}:{config['port']}")
        print("=" * 40)
        print("🔗 يمكن للمستخدمين الوصول من:")
        for url in config['external_urls']:
            print(f"   • {url}")
        print("=" * 40)
        
        # بدء مراقبة الاتصالات
        self.network_manager.monitor_connections()
        
        # تشغيل الخادم
        if ssl_enabled:
            ssl_context = self.network_manager.setup_ssl()
            if ssl_context:
                self.app.run(
                    host=host,
                    port=port,
                    ssl_context=ssl_context,
                    threaded=True,
                    debug=False
                )
            else:
                print("⚠️ فشل في إعداد SSL، التشغيل بدون تشفير")
                self.app.run(host=host, port=port, threaded=True, debug=False)
        else:
            self.app.run(host=host, port=port, threaded=True, debug=False)

# دوال مساعدة
def setup_network_app(app: Flask) -> NetworkFlaskApp:
    """إعداد تطبيق الشبكة"""
    return NetworkFlaskApp(app)

def get_network_info():
    """الحصول على معلومات الشبكة"""
    manager = NetworkManager()
    return {
        'local_ip': manager.get_local_ip(),
        'available_ports': manager.scan_network_ports(),
        'config': manager.create_server_config()
    }

# مثال على الاستخدام
if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار نظام الشبكة...")
    
    info = get_network_info()
    print("📊 معلومات الشبكة:")
    print(json.dumps(info, indent=2, ensure_ascii=False))
