#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص حالة النظام
"""

def main():
    try:
        from app import app, db, Course, User, PersonData
        
        with app.app_context():
            print("🔍 فحص حالة النظام")
            print("=" * 40)
            
            # فحص الدورات
            courses = Course.query.all()
            print(f"📚 الدورات: {len(courses)}")
            for course in courses:
                trainer_name = "غير محدد"
                try:
                    if course.trainer:
                        trainer_name = course.trainer.username
                except:
                    pass
                print(f"   - {course.title} (المدرب: {trainer_name})")
            
            # فحص المستخدمين
            users = User.query.all()
            print(f"\n👥 المستخدمين: {len(users)}")
            for user in users:
                print(f"   - {user.username} ({user.role})")
            
            # فحص بيانات الأشخاص
            persons = PersonData.query.all()
            print(f"\n👤 بيانات الأشخاص: {len(persons)}")
            
            print("\n✅ النظام يعمل بشكل طبيعي!")
            
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")

if __name__ == "__main__":
    main()
