# -*- mode: python ; coding: utf-8 -*-
# نظام تحليل الأسماء - PyInstaller Configuration
# Training System - PyInstaller Configuration

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# جمع البيانات والملفات المطلوبة
datas = []
datas += collect_data_files('flask')
datas += collect_data_files('flask_sqlalchemy')
datas += collect_data_files('flask_login')
datas += collect_data_files('flask_wtf')
datas += collect_data_files('wtforms')
datas += collect_data_files('pandas')
datas += collect_data_files('openpyxl')
datas += collect_data_files('xlsxwriter')
datas += collect_data_files('arabic_reshaper')
datas += collect_data_files('bidi')

# إضافة ملفات التطبيق
datas += [('templates', 'templates')]
datas += [('static', 'static')]
datas += [('requirements.txt', '.')]

# جمع الوحدات المخفية
hiddenimports = []
hiddenimports += collect_submodules('flask')
hiddenimports += collect_submodules('flask_sqlalchemy')
hiddenimports += collect_submodules('flask_login')
hiddenimports += collect_submodules('flask_wtf')
hiddenimports += collect_submodules('wtforms')
hiddenimports += collect_submodules('pandas')
hiddenimports += collect_submodules('openpyxl')
hiddenimports += collect_submodules('xlsxwriter')
hiddenimports += collect_submodules('arabic_reshaper')
hiddenimports += collect_submodules('bidi')
hiddenimports += collect_submodules('email_validator')

# إضافة وحدات إضافية
hiddenimports += [
    'sqlalchemy.dialects.sqlite',
    'sqlalchemy.pool',
    'email_validator',
    'dns.resolver',
    'dns.rdatatype',
    'dns.rdataclass',
    'dns.name'
]

block_cipher = None

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='TrainingSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='static/favicon.ico'  # إضافة أيقونة إذا كانت متوفرة
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='TrainingSystem'
)
