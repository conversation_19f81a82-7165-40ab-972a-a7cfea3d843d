#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت إصلاح أعمدة قاعدة البيانات المفقودة
"""

import sqlite3
import os
from datetime import datetime

def backup_database():
    """إنشاء نسخة احتياطية"""
    try:
        db_path = 'training_system.db'
        if os.path.exists(db_path):
            backup_path = f'training_system_backup_columns_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
            import shutil
            shutil.copy2(db_path, backup_path)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
            return backup_path
        else:
            print("⚠️ لم يتم العثور على قاعدة البيانات")
            return None
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return None

def check_table_columns(cursor, table_name):
    """فحص أعمدة الجدول"""
    try:
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [row[1] for row in cursor.fetchall()]
        return columns
    except Exception as e:
        print(f"❌ خطأ في فحص جدول {table_name}: {str(e)}")
        return []

def add_missing_columns():
    """إضافة الأعمدة المفقودة"""
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        print("🔍 فحص الأعمدة المفقودة...")
        
        # فحص جدول personal_data
        personal_data_columns = check_table_columns(cursor, 'personal_data')
        print(f"📋 أعمدة جدول personal_data: {len(personal_data_columns)}")
        
        # الأعمدة المطلوبة
        required_columns = {
            'work_number': 'TEXT',
            'work_rank': 'TEXT',
            'work_place': 'TEXT',
            'governorate_id': 'INTEGER',
            'directorate_id': 'INTEGER',
            'village_id': 'INTEGER',
            'agency_id': 'INTEGER'
        }
        
        added_count = 0
        for column_name, column_type in required_columns.items():
            if column_name not in personal_data_columns:
                try:
                    cursor.execute(f"ALTER TABLE personal_data ADD COLUMN {column_name} {column_type}")
                    print(f"✅ تم إضافة العمود: {column_name} ({column_type})")
                    added_count += 1
                except Exception as e:
                    print(f"❌ خطأ في إضافة العمود {column_name}: {str(e)}")
            else:
                print(f"ℹ️ العمود {column_name} موجود بالفعل")
        
        # فحص جدول person_data
        person_data_columns = check_table_columns(cursor, 'person_data')
        print(f"📋 أعمدة جدول person_data: {len(person_data_columns)}")
        
        # التأكد من وجود الأعمدة الأساسية في person_data
        person_data_required = {
            'full_name': 'TEXT NOT NULL',
            'nickname': 'TEXT',
            'age': 'INTEGER',
            'governorate': 'TEXT',
            'directorate': 'TEXT',
            'uzla': 'TEXT',
            'village': 'TEXT',
            'qualification': 'TEXT',
            'marital_status': 'TEXT',
            'job': 'TEXT',
            'agency': 'TEXT',
            'work_place': 'TEXT',
            'national_number': 'TEXT',
            'military_number': 'TEXT',
            'phone': 'TEXT'
        }
        
        for column_name, column_type in person_data_required.items():
            if column_name not in person_data_columns:
                try:
                    # إزالة NOT NULL للأعمدة المضافة لاحقاً
                    safe_type = column_type.replace(' NOT NULL', '')
                    cursor.execute(f"ALTER TABLE person_data ADD COLUMN {column_name} {safe_type}")
                    print(f"✅ تم إضافة العمود: {column_name} ({safe_type}) إلى person_data")
                    added_count += 1
                except Exception as e:
                    print(f"❌ خطأ في إضافة العمود {column_name} إلى person_data: {str(e)}")
        
        conn.commit()
        conn.close()
        
        print(f"🎉 تم إضافة {added_count} عمود جديد")
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في إضافة الأعمدة: {str(e)}")
        return False

def verify_tables():
    """التحقق من الجداول بعد الإصلاح"""
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        print("\n🔍 التحقق من الجداول بعد الإصلاح:")
        
        # فحص personal_data
        personal_data_columns = check_table_columns(cursor, 'personal_data')
        print(f"📋 personal_data: {len(personal_data_columns)} عمود")
        
        # فحص person_data
        person_data_columns = check_table_columns(cursor, 'person_data')
        print(f"📋 person_data: {len(person_data_columns)} عمود")
        
        # فحص course
        course_columns = check_table_columns(cursor, 'course')
        print(f"📋 course: {len(course_columns)} عمود")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح أعمدة قاعدة البيانات")
    print("=" * 50)
    
    # إنشاء نسخة احتياطية
    backup_path = backup_database()
    
    # إضافة الأعمدة المفقودة
    if add_missing_columns():
        print("✅ تم إصلاح الأعمدة بنجاح")
    else:
        print("❌ فشل في إصلاح الأعمدة")
        return
    
    # التحقق من النتائج
    verify_tables()
    
    print("=" * 50)
    print("🎉 تم الانتهاء من إصلاح قاعدة البيانات")
    if backup_path:
        print(f"💾 النسخة الاحتياطية: {backup_path}")

if __name__ == "__main__":
    main()
