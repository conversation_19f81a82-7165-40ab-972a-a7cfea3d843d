{% extends "layout.html" %}

{% block styles %}
<style>
    .form-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }

    .form-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: bold;
        margin-bottom: 8px;
    }

    .form-control {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: #4a6bff;
        box-shadow: 0 0 0 0.25rem rgba(74, 107, 255, 0.25);
    }

    .form-text {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .btn-submit {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
    }

    .btn-cancel {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-cancel:hover {
        transform: translateY(-2px);
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .course-info {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .course-info-title {
        font-weight: bold;
        margin-bottom: 10px;
    }

    .course-info-item {
        margin-bottom: 5px;
    }

    .course-info-label {
        font-weight: bold;
        color: #6c757d;
    }

    .file-types {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-top: 10px;
    }

    .file-types-title {
        font-weight: bold;
        margin-bottom: 10px;
    }

    .file-type-item {
        display: inline-block;
        margin-left: 15px;
        margin-bottom: 10px;
    }

    .file-type-icon {
        margin-left: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link active">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <h2 class="mb-4">إضافة مادة تعليمية</h2>

        <div class="course-info">
            <div class="course-info-title">
                <i class="fas fa-info-circle me-2"></i> معلومات الدورة
            </div>
            <div class="course-info-item">
                <span class="course-info-label">عنوان الدورة:</span>
                <span>{{ course.title }}</span>
            </div>
            <div class="course-info-item">
                <span class="course-info-label">المدرب:</span>
                <span>{{ course.trainer.username }}</span>
            </div>
            <div class="course-info-item">
                <span class="course-info-label">مدة الدورة:</span>
                <span>{{ course.duration_days }} يوم</span>
            </div>
        </div>

        <div class="form-card">
            <div class="form-header">
                <h4><i class="fas fa-plus-circle me-2"></i> إضافة مادة تعليمية جديدة</h4>
                <p class="text-muted">أدخل معلومات المادة التعليمية وقم بتحميل الملف</p>
            </div>

            <form method="POST" enctype="multipart/form-data">
                {{ form.hidden_tag() }}

                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            {{ form.title.label(class="form-label") }}
                            {% if form.title.errors %}
                                {{ form.title(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.title.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.title(class="form-control", placeholder="أدخل عنوان المادة التعليمية") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.day_number.label(class="form-label") }}
                            {% if form.day_number.errors %}
                                {{ form.day_number(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.day_number.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.day_number(class="form-select") }}
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    {{ form.description.label(class="form-label") }}
                    {% if form.description.errors %}
                        {{ form.description(class="form-control is-invalid", rows=3) }}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.description(class="form-control", rows=3, placeholder="أدخل وصفاً للمادة التعليمية (اختياري)") }}
                    {% endif %}
                </div>

                <div class="form-group">
                    {{ form.file.label(class="form-label") }}
                    {% if form.file.errors %}
                        {{ form.file(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.file.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.file(class="form-control") }}
                    {% endif %}

                    <div class="file-types">
                        <div class="file-types-title">أنواع الملفات المدعومة:</div>
                        <div class="file-type-item">
                            <span class="file-type-icon"><i class="fas fa-file-pdf text-danger"></i></span>
                            <span>PDF</span>
                        </div>
                        <div class="file-type-item">
                            <span class="file-type-icon"><i class="fas fa-file-powerpoint text-warning"></i></span>
                            <span>PowerPoint</span>
                        </div>
                        <div class="file-type-item">
                            <span class="file-type-icon"><i class="fas fa-file-word text-primary"></i></span>
                            <span>Word</span>
                        </div>
                        <div class="file-type-item">
                            <span class="file-type-icon"><i class="fas fa-file-excel text-success"></i></span>
                            <span>Excel</span>
                        </div>
                        <div class="file-type-item">
                            <span class="file-type-icon"><i class="fas fa-file-image text-purple"></i></span>
                            <span>صور</span>
                        </div>
                        <div class="file-type-item">
                            <span class="file-type-icon"><i class="fas fa-file-video text-danger"></i></span>
                            <span>فيديو</span>
                        </div>
                        <div class="file-type-item">
                            <span class="file-type-icon"><i class="fas fa-file-archive text-secondary"></i></span>
                            <span>ZIP</span>
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>الحد الأقصى لحجم الملف:</strong> {{ max_file_size }}
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ url_for('course_details', course_id=course.id) }}" class="btn btn-secondary btn-cancel">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                    {{ form.submit(class="btn btn-primary btn-submit") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
