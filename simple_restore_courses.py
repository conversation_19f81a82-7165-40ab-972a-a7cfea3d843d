#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
استعادة الدورات بطريقة مبسطة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        from app import app, db, Course, User
        from datetime import datetime

        with app.app_context():
            print("🔄 استعادة الدورات الثلاث...")

            # فحص المستخدم admin
            admin_user = User.query.filter_by(role='admin').first()
            if not admin_user:
                print("❌ لا يوجد مستخدم admin")
                return

            print(f"✅ العثور على admin: {admin_user.username}")

            # حذف الدورات الموجودة
            Course.query.delete()
            db.session.commit()
            print("🗑️ تم حذف الدورات الموجودة")

            # إنشاء الدورات الثلاث
            courses_data = [
                {
                    'course_number': '3455667',
                    'title': 'المبيعات2',
                    'description': 'دورة تدريبية في المبيعات والتسويق',
                    'category': 'management',
                    'level': 'intermediate',
                    'start_date': datetime(2025, 5, 22),
                    'end_date': datetime(2025, 5, 31),
                    'duration_days': 10,
                    'trainer_id': admin_user.id
                },
                {
                    'course_number': '10054',
                    'title': 'تدريب الذكاء الاصطناعي',
                    'description': 'دورة تدريبية في الذكاء الاصطناعي والتعلم الآلي',
                    'category': 'technology',
                    'level': 'advanced',
                    'start_date': datetime(2025, 5, 22),
                    'end_date': datetime(2025, 6, 20),
                    'duration_days': 30,
                    'trainer_id': admin_user.id
                },
                {
                    'course_number': '1100255',
                    'title': 'تدريب الذكاء الاصطناعي 2',
                    'description': 'دورة متقدمة في الذكاء الاصطناعي',
                    'category': 'technology',
                    'level': 'expert',
                    'start_date': datetime(2025, 5, 22),
                    'end_date': datetime(2025, 5, 31),
                    'duration_days': 10,
                    'trainer_id': admin_user.id
                }
            ]

            # إضافة الدورات
            for course_data in courses_data:
                course = Course(**course_data)
                db.session.add(course)
                print(f"✅ تم إنشاء: {course_data['course_number']} - {course_data['title']}")

            # حفظ التغييرات
            db.session.commit()

            # التحقق من النتيجة
            courses_count = Course.query.count()
            print(f"\n🎉 تم إنشاء {courses_count} دورة بنجاح!")

            # عرض الدورات
            courses = Course.query.all()
            for course in courses:
                print(f"   - {course.course_number}: {course.title}")

    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
