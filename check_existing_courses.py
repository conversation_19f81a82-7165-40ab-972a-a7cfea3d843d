from app import app, db, Course, CourseParticipant

with app.app_context():
    print("🔍 فحص جميع الدورات الموجودة...")
    print("=" * 60)
    
    courses = Course.query.all()
    print(f"إجمالي عدد الدورات: {len(courses)}")
    
    for course in courses:
        participants_count = CourseParticipant.query.filter_by(course_id=course.id).count()
        print(f"\n🎓 الدورة ID: {course.id}")
        print(f"   رقم الدورة: {course.course_number}")
        print(f"   اسم الدورة: {course.title}")
        print(f"   المشاركين المسجل: {course.total_participants}")
        print(f"   المشاركين الفعلي: {participants_count}")
        
        if course.course_number == '3455667':
            print("   🎯 هذه هي الدورة المطلوبة!")
        
        print("-" * 40)
