import pandas as pd

# إنشاء بيانات تجريبية شاملة بنفس ترتيب قالب بيانات الأشخاص
data = {
    'الاسم الشخصي': [
        'أحمد محمد علي السالمي',
        'فاطمة أحمد سالم الحميري',
        'محمد عبدالله حسن الزبيدي',
        'عائشة علي محمد الشامي',
        'يوسف سالم أحمد العولقي',
        'خالد حسن محمد الحداد',
        'مريم عبدالله سعيد البكري',
        'عبدالرحمن أحمد محمد',
        'زينب محمد علي',
        'سعد عبدالله حسن'
    ],
    'الاسم المستعار': [
        'أبو محمد',
        'أم أحمد',
        'أبو عبدالله',
        'أم علي',
        'أبو سالم',
        'أبو حسن',
        'أم عبدالله',
        'أبو عبدالرحمن',
        'أم زينب',
        'أبو سعد'
    ],
    'العمر': [
        35, 28, 42, 31, 38, 45, 33, 29, 26, 40
    ],
    'المحافظة': [
        'صنعاء', 'عدن', 'تعز', 'الحديدة', 'إب', 'ذمار', 'صعدة', 'حجة', 'لحج', 'أبين'
    ],
    'المديرية': [
        'الثورة', 'كريتر', 'صالة', 'الحوك', 'إب', 'ذمار', 'صعدة', 'حجة', 'لحج', 'أبين'
    ],
    'العزلة': [
        'شعوب', 'كريتر', 'صالة', 'الحوك', 'إب', 'ذمار', 'صعدة', 'حجة', 'لحج', 'أبين'
    ],
    'الحي/القرية': [
        'الحصبة', 'كريتر', 'صالة', 'الحوك', 'إب', 'ذمار', 'صعدة', 'حجة', 'لحج', 'أبين'
    ],
    'المؤهل العلمي': [
        'بكالوريوس', 'ثانوي', 'دبلوم', 'بكالوريوس', 'ماجستير', 'ثانوي', 'دبلوم', 'بكالوريوس', 'دبلوم', 'ثانوي'
    ],
    'الحالة الاجتماعية': [
        'متزوج', 'متزوج', 'متزوج', 'متزوج', 'أعزب', 'متزوج', 'متزوج', 'أعزب', 'أعزب', 'متزوج'
    ],
    'العمل': [
        'مهندس', 'معلمة', 'محاسب', 'طبيبة', 'مدير', 'فني', 'ممرضة', 'مبرمج', 'صيدلانية', 'محامي'
    ],
    'الإدارة': [
        'الهندسة', 'التعليم', 'المالية', 'الصحة', 'الإدارة', 'الصيانة', 'التمريض', 'تقنية المعلومات', 'الصيدلة', 'القانونية'
    ],
    'مكان العمل': [
        'وزارة الأشغال', 'وزارة التربية', 'وزارة المالية', 'وزارة الصحة', 'الديوان', 'البلدية', 'المستشفى', 'شركة تقنية', 'صيدلية', 'محكمة'
    ],
    'الرقم الوطني': [
        '01234567890',
        '01234567891',
        '01234567892',
        '01234567893',
        '01234567894',
        '01234567895',
        '01234567896',
        '01234567897',
        '01234567898',
        '01234567899'
    ],
    'الرقم العسكري': [
        'M001', 'M002', 'M003', 'M004', 'M005', 'M006', 'M007', 'M008', 'M009', 'M010'
    ],
    'رقم التلفون': [
        '777123456', '733987654', '770555444', '712333222', '777888999', '733111222', '770444555', '777666333', '733222111', '770999888'
    ]
}

# إنشاء DataFrame
df = pd.DataFrame(data)

# حفظ كملف Excel بنفس اسم القالب
df.to_excel('static/participants_sample.xlsx', index=False, engine='openpyxl')

# حفظ نسخة إضافية بالاسم الجديد
df.to_excel('static/قالب_بيانات_الأشخاص_نموذج.xlsx', index=False, engine='openpyxl')

print("✅ تم إنشاء ملف Excel بنفس قالب بيانات الأشخاص!")
print(f"📁 الملف الأساسي: static/participants_sample.xlsx")
print(f"📁 الملف الإضافي: static/قالب_بيانات_الأشخاص_نموذج.xlsx")
print(f"📊 عدد الأعمدة: {len(df.columns)}")
print(f"📈 عدد الصفوف: {len(df)}")
print("\n🗂️ ترتيب الأعمدة (نفس قالب بيانات الأشخاص):")
for i, col in enumerate(df.columns, 1):
    print(f"  {i:2d}. {col}")

print("\n🎯 الملف جاهز للاستخدام في:")
print("  • استيراد المشاركين من Excel")
print("  • اختبار نظام منع التكرار")
print("  • تجربة التحقق الذكي من الأسماء")
print(f"\n🌐 رابط التحميل: http://localhost:5000/static/participants_sample.xlsx")
