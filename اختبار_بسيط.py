#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء ملف Excel بسيط لاختبار النظام
"""

import pandas as pd
from datetime import datetime

def create_simple_test():
    """إنشاء ملف Excel بسيط للاختبار"""
    
    # بيانات بسيطة للاختبار
    test_data = [
        {"الاسم الشخصي": "محمد أحمد علي", "الرقم الوطني": "12345678901", "رقم الهاتف": "0501234567"},
        {"الاسم الشخصي": "أحمد محمد سالم", "الرقم الوطني": "98765432109", "رقم الهاتف": "0509876543"},
        {"الاسم الشخصي": "علي حسن محمد", "الرقم الوطني": "11111111111", "رقم الهاتف": "0501111111"},
        {"الاسم الشخصي": "احمد ابراهيم", "الرقم الوطني": "22222222222", "رقم الهاتف": "0502222222"},
        {"الاسم الشخصي": "خالد سعد المطيري", "الرقم الوطني": "33333333333", "رقم الهاتف": "0503333333"},
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(test_data)
    
    # إنشاء اسم الملف
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"اختبار_بسيط_{timestamp}.xlsx"
    
    # حفظ الملف
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف الاختبار البسيط: {filename}")
    print(f"📊 عدد السجلات: {len(test_data)}")
    
    return filename

if __name__ == '__main__':
    create_simple_test()
