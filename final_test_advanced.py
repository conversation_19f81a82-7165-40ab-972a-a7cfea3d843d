#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل للنظام المتقدم
"""

import requests
import re
import json
import time

def test_advanced_system():
    print("🧪 اختبار النظام المتقدم الشامل")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # 1. اختبار تسجيل الدخول
        print("1️⃣ اختبار تسجيل الدخول...")
        
        login_page = session.get('http://localhost:5000/login')
        csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
        csrf_token = csrf_match.group(1) if csrf_match else None
        
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123'
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token
        
        response = session.post('http://localhost:5000/login', data=login_data)
        
        if 'dashboard' in response.url:
            print("   ✅ تم تسجيل الدخول بنجاح")
        else:
            print("   ❌ فشل تسجيل الدخول")
            return False
        
        # 2. اختبار الوصول للصفحة المتقدمة
        print("2️⃣ اختبار الوصول للصفحة المتقدمة...")
        
        advanced_page = session.get('http://localhost:5000/admin/advanced-users')
        
        if advanced_page.status_code == 200:
            print("   ✅ تم الوصول للصفحة المتقدمة")
            
            # فحص محتوى الصفحة
            content_checks = [
                ('عرض شجرة النظام', 'عرض شجرة النظام'),
                ('إدارة الأدوار', 'إدارة الأدوار'),
                ('إدارة المستخدمين', 'إدارة المستخدمين'),
                ('showSystemTree', 'showSystemTree'),
                ('showAddRoleModal', 'showAddRoleModal'),
                ('showAddUserModal', 'showAddUserModal')
            ]
            
            for check_name, check_text in content_checks:
                if check_text in advanced_page.text:
                    print(f"      ✅ {check_name} موجود")
                else:
                    print(f"      ❌ {check_name} مفقود")
        else:
            print(f"   ❌ فشل الوصول للصفحة: {advanced_page.status_code}")
            return False
        
        # 3. اختبار API شجرة النظام
        print("3️⃣ اختبار API شجرة النظام...")
        
        tree_response = session.get('http://localhost:5000/api/system-tree')
        
        if tree_response.status_code == 200:
            try:
                tree_data = tree_response.json()
                print(f"   ✅ تم الحصول على شجرة النظام ({len(tree_data)} وحدة)")
                
                # فحص بعض الوحدات المهمة
                expected_modules = ['dashboard', 'users_management', 'courses_management', 'reports']
                for module in expected_modules:
                    if module in tree_data:
                        permissions_count = len(tree_data[module].get('permissions', {}))
                        print(f"      ✅ وحدة {module} موجودة ({permissions_count} صلاحية)")
                    else:
                        print(f"      ❌ وحدة {module} مفقودة")
                
                # فحص هيكل البيانات
                sample_module = list(tree_data.keys())[0]
                sample_data = tree_data[sample_module]
                
                required_keys = ['name', 'icon', 'permissions']
                for key in required_keys:
                    if key in sample_data:
                        print(f"      ✅ مفتاح {key} موجود")
                    else:
                        print(f"      ❌ مفتاح {key} مفقود")
                        
            except json.JSONDecodeError:
                print("   ❌ خطأ في تحليل JSON لشجرة النظام")
                return False
        else:
            print(f"   ❌ فشل في الحصول على شجرة النظام: {tree_response.status_code}")
            return False
        
        # 4. اختبار API الأدوار
        print("4️⃣ اختبار API الأدوار...")
        
        roles_response = session.get('http://localhost:5000/api/roles')
        
        if roles_response.status_code == 200:
            try:
                roles_data = roles_response.json()
                print(f"   ✅ تم الحصول على الأدوار ({len(roles_data)} دور)")
                
                for role in roles_data:
                    role_name = role.get('display_name', role.get('name', 'غير محدد'))
                    print(f"      📋 {role_name} (ID: {role.get('id')})")
                    
            except json.JSONDecodeError:
                print("   ❌ خطأ في تحليل JSON للأدوار")
                return False
        else:
            print(f"   ❌ فشل في الحصول على الأدوار: {roles_response.status_code}")
            return False
        
        # 5. اختبار APIs المحتوى الديناميكي
        print("5️⃣ اختبار APIs المحتوى الديناميكي...")
        
        # اختبار محتوى إدارة الأدوار
        roles_content_response = session.get('http://localhost:5000/api/roles-management-content')
        if roles_content_response.status_code == 200:
            print("   ✅ API محتوى إدارة الأدوار يعمل")
            if 'table' in roles_content_response.text.lower():
                print("      ✅ يحتوي على جدول")
        else:
            print(f"   ❌ فشل API محتوى إدارة الأدوار: {roles_content_response.status_code}")
        
        # اختبار محتوى إدارة المستخدمين
        users_content_response = session.get('http://localhost:5000/api/users-management-content')
        if users_content_response.status_code == 200:
            print("   ✅ API محتوى إدارة المستخدمين يعمل")
            if 'table' in users_content_response.text.lower():
                print("      ✅ يحتوي على جدول")
        else:
            print(f"   ❌ فشل API محتوى إدارة المستخدمين: {users_content_response.status_code}")
        
        # 6. اختبار إنشاء دور جديد
        print("6️⃣ اختبار إنشاء دور جديد...")
        
        new_role_data = {
            'name': f'test_role_{int(time.time())}',
            'description': 'دور اختبار تم إنشاؤه تلقائياً',
            'permissions': [
                'dashboard.view',
                'courses_management.view',
                'reports.view'
            ]
        }
        
        create_role_response = session.post(
            'http://localhost:5000/api/roles',
            json=new_role_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if create_role_response.status_code == 200:
            try:
                result = create_role_response.json()
                if result.get('success'):
                    print(f"   ✅ تم إنشاء الدور: {new_role_data['name']}")
                else:
                    print(f"   ❌ فشل إنشاء الدور: {result.get('message')}")
            except json.JSONDecodeError:
                print("   ❌ خطأ في تحليل استجابة إنشاء الدور")
        else:
            print(f"   ❌ خطأ في إنشاء الدور: {create_role_response.status_code}")
        
        print("\n" + "="*60)
        print("📊 ملخص الاختبار:")
        print("✅ النظام المتقدم يعمل بشكل ممتاز!")
        print("✅ جميع APIs تعمل بشكل صحيح")
        print("✅ الواجهة الأمامية محملة بشكل صحيح")
        print("✅ إنشاء الأدوار يعمل")
        
        print("\n🔗 روابط مهمة:")
        print("   النظام المتقدم: http://localhost:5000/admin/advanced-users")
        print("   النظام العادي: http://localhost:5000/admin/users")
        print("   تسجيل الدخول: <EMAIL> / admin123")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من تشغيل الخادم
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code != 200:
            print("❌ الخادم غير متاح")
            return
    except:
        print("❌ لا يمكن الوصول للخادم على localhost:5000")
        print("يرجى التأكد من تشغيل الخادم بـ: python app.py")
        return
    
    # اختبار النظام
    success = test_advanced_system()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت! النظام المتقدم جاهز للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت.")

if __name__ == '__main__':
    main()
