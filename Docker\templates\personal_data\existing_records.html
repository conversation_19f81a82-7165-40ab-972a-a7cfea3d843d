{% extends "layout.html" %}

{% block styles %}
<style>
    .existing-records-table {
        width: 100%;
        border-collapse: collapse;
        direction: rtl;
    }

    .existing-records-table th, .existing-records-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
    }

    .existing-records-table th {
        background-color: #f2f2f2;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .existing-records-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .existing-records-table tr:hover {
        background-color: #f1f1f1;
    }

    .existing-records-container {
        overflow-x: auto;
        max-height: 70vh;
        margin-bottom: 20px;
    }

    /* تنسيق الصفحة */
    .page-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .page-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .page-subtitle {
        font-size: 18px;
        color: #666;
    }

    /* تنسيق القائمة الجانبية */
    .sidebar {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .sidebar-link {
        display: block;
        padding: 10px;
        margin-bottom: 5px;
        border-radius: 5px;
        color: #333;
        text-decoration: none;
        transition: all 0.3s;
    }

    .sidebar-link:hover, .sidebar-link.active {
        background-color: #e9ecef;
        color: #007bff;
    }

    .sidebar-link i {
        margin-left: 10px;
        width: 20px;
        text-align: center;
    }

    .sidebar-dropdown-menu {
        padding-right: 20px;
        display: none;
    }

    .sidebar-dropdown-menu.show {
        display: block;
    }

    .sidebar-dropdown-menu .sidebar-link {
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <div class="sidebar-dropdown">
                <a href="#" class="sidebar-link dropdown-toggle active">
                    <i class="fas fa-id-card"></i> البيانات الشخصية
                </a>
                <div class="sidebar-dropdown-menu show">
                    <a href="{{ url_for('personal_data_list') }}" class="sidebar-link">
                        <i class="fas fa-list me-2"></i>قائمة البيانات
                    </a>
                    <a href="{{ url_for('add_personal_data') }}" class="sidebar-link">
                        <i class="fas fa-plus me-2"></i>إضافة بيانات
                    </a>
                    <a href="{{ url_for('personal_data_excel') }}" class="sidebar-link active">
                        <i class="fas fa-file-excel me-2"></i>إدارة البيانات (إكسل)
                    </a>
                </div>
            </div>
            <a href="{{ url_for('reference_tables') }}" class="sidebar-link">
                <i class="fas fa-table"></i> الجداول الترميزية
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="{{ url_for('backup') }}" class="sidebar-link">
                <i class="fas fa-database"></i> النسخ الاحتياطي
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <div class="page-header">
            <h1 class="page-title">السجلات الموجودة مسبقاً</h1>
            <p class="page-subtitle">قائمة بالسجلات التي تم العثور عليها في قاعدة البيانات أثناء عملية الاستيراد</p>
        </div>

        <div class="card">
            <div class="card-header bg-warning text-white">
                <i class="fas fa-exclamation-triangle me-2"></i>
                تنبيه: هذه السجلات موجودة مسبقاً في قاعدة البيانات
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5 class="alert-heading">ملخص عملية الاستيراد</h5>
                    <hr>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h3>{{ session.import_summary.total_records }}</h3>
                                    <p class="mb-0">إجمالي السجلات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white mb-3">
                                <div class="card-body text-center">
                                    <h3>{{ session.import_summary.success_count }}</h3>
                                    <p class="mb-0">تم استيرادها بنجاح</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark mb-3">
                                <div class="card-body text-center">
                                    <h3>{{ existing_records|length }}</h3>
                                    <p class="mb-0">موجودة مسبقاً</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white mb-3">
                                <div class="card-body text-center">
                                    <h3>{{ session.import_summary.error_count - existing_records|length }}</h3>
                                    <p class="mb-0">فشل استيرادها</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p>تم العثور على <strong>{{ existing_records|length }}</strong> سجل موجود مسبقاً في قاعدة البيانات. لم يتم استيراد هذه السجلات لتجنب التكرار.</p>
                </div>

                <div class="existing-records-container">
                    <table class="existing-records-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الاسم في الملف</th>
                                <th>الاسم الموجود مسبقاً</th>
                                <th>الرقم الوطني</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in existing_records %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ record.full_name }}</td>
                                <td>{{ record.existing_name }}</td>
                                <td>{{ record.national_number }}</td>
                                <td>
                                    <a href="{{ url_for('view_personal_data', personal_data_id=record.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                    <a href="{{ url_for('personal_data_excel') }}" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة إلى إدارة البيانات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تفعيل القائمة المنسدلة في الشريط الجانبي
        $('.sidebar-link.dropdown-toggle').click(function(e) {
            e.preventDefault();
            $(this).next('.sidebar-dropdown-menu').toggleClass('show');
        });
    });
</script>
{% endblock %}
