{% extends "layout.html" %}

{% block styles %}
<!-- Chart.js CSS - محلي (إذا كان متوفراً) -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/reports.css') }}">
<style>
    .reports-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .reports-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        animation: float 20s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .reports-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .date-filter-section {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e3f2fd;
    }

    .filter-card {
        background: linear-gradient(135deg, #f8f9ff, #e3f2fd);
        border-radius: 15px;
        padding: 20px;
        border: 2px solid #667eea;
        transition: all 0.3s ease;
    }

    .filter-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
    }

    .date-input {
        border: 2px solid #e0e0e0;
        border-radius: 12px;
        padding: 12px 20px;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .date-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }

    .btn-generate {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        color: white;
        font-weight: bold;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-generate:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e3f2fd;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
    }

    .stat-label {
        color: #666;
        font-size: 1rem;
        font-weight: 500;
    }

    .charts-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 25px;
        margin-bottom: 30px;
    }

    .chart-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e3f2fd;
        height: 450px;
        display: flex;
        flex-direction: column;
    }

    .chart-title {
        font-size: 1.3rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
        flex-shrink: 0;
    }

    .chart-container {
        flex: 1;
        position: relative;
        width: 100%;
        height: 350px !important;
        max-height: 350px !important;
        overflow: hidden;
    }

    /* حل جذري قوي لمنع تمدد الكانفاس */
    .chart-container {
        width: 400px !important;
        height: 300px !important;
        max-width: 400px !important;
        max-height: 300px !important;
        min-width: 400px !important;
        min-height: 300px !important;
        overflow: hidden !important;
        contain: layout style size !important;
        position: relative !important;
        display: block !important;
        box-sizing: border-box !important;
    }

    .chart-container canvas {
        width: 400px !important;
        height: 300px !important;
        max-width: 400px !important;
        max-height: 300px !important;
        min-width: 400px !important;
        min-height: 300px !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        display: block !important;
        box-sizing: border-box !important;
    }

    /* منع تمدد الكانفاس بقوة مطلقة */
    canvas {
        width: 400px !important;
        height: 300px !important;
        max-width: 400px !important;
        max-height: 300px !important;
        min-width: 400px !important;
        min-height: 300px !important;
    }

    #levelChart, #centerChart, #coursePathChart,
    #centerAgencyChart, #courseLocationChart,
    #participantTypeChart, #forceClassificationChart {
        width: 400px !important;
        height: 300px !important;
        max-width: 400px !important;
        max-height: 300px !important;
        min-width: 400px !important;
        min-height: 300px !important;
        display: block !important;
        box-sizing: border-box !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
    }

    /* منع أي تمدد من Chart.js */
    canvas[data-chartjs-render-monitor] {
        width: 400px !important;
        height: 300px !important;
        max-width: 400px !important;
        max-height: 300px !important;
    }

    .chart-card .chart-container {
        width: 100% !important;
        height: 350px !important;
        max-height: 350px !important;
        min-height: 350px !important;
    }

    .detailed-tables {
        display: grid;
        gap: 25px;
    }

    .table-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e3f2fd;
        overflow: hidden;
    }

    .table-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 15px;
        margin: -25px -25px 20px -25px;
        font-size: 1.2rem;
        font-weight: bold;
        text-align: center;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }

    .data-table th {
        background: linear-gradient(135deg, #f8f9ff, #e3f2fd);
        color: #333;
        font-weight: bold;
        padding: 12px 10px;
        text-align: center;
        border: 1px solid #ddd;
        font-size: 0.9rem;
    }

    .data-table td {
        padding: 10px;
        text-align: center;
        border: 1px solid #ddd;
        transition: background-color 0.3s ease;
    }

    .data-table tbody tr:hover {
        background-color: #f8f9ff;
    }

    .data-table tbody tr:nth-child(even) {
        background-color: #fafbff;
    }

    .export-section {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin-top: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e3f2fd;
        text-align: center;
    }

    .export-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .btn-export {
        padding: 12px 25px;
        border-radius: 25px;
        border: none;
        font-weight: bold;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-export.pdf {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
    }

    .btn-export.excel {
        background: linear-gradient(135deg, #00b894, #00a085);
        color: white;
    }

    .btn-export.print {
        background: linear-gradient(135deg, #6c5ce7, #5f3dc4);
        color: white;
    }

    .btn-export:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .visual-separator {
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 2px;
        margin: 25px 0;
    }

    @media (max-width: 768px) {
        .charts-section {
            grid-template-columns: 1fr;
        }

        .chart-card {
            height: 400px;
        }

        .chart-container {
            min-height: 250px;
            max-height: 300px;
        }

        .reports-title {
            font-size: 1.8rem;
        }

        .export-buttons {
            flex-direction: column;
            align-items: center;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .chart-card {
            height: 350px;
            padding: 15px;
        }

        .chart-container {
            min-height: 200px;
            max-height: 250px;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .chart-title {
            font-size: 1.1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link active">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <div class="main-container">
            <!-- Loading Overlay -->
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
            </div>

            <!-- Header -->
            <h2 class="page-title">
                <i class="fas fa-chart-line"></i>
                التقارير التفاعلية المتطورة
            </h2>
            <p class="text-center mb-4" style="color: #6b7280; font-size: 1.1rem; font-weight: 500;">
                نظام تقارير شامل مع تحليلات بصرية وإحصائيات تفصيلية
            </p>

        <!-- Date Filter Section -->
        <div class="date-filter-section">
            <div class="filter-card">
                <h4 class="text-center mb-4">
                    <i class="fas fa-calendar-alt me-2"></i>تحديد فترة التقرير
                </h4>
                <form id="reportForm" method="POST">
                    <div class="row align-items-end">
                        <div class="col-md-4">
                            <label class="form-label fw-bold">من تاريخ:</label>
                            <input type="date" name="start_date" class="form-control date-input" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label fw-bold">إلى تاريخ:</label>
                            <input type="date" name="end_date" class="form-control date-input" required>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-generate w-100">
                                <i class="fas fa-magic me-2"></i>إنشاء التقرير
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results Section (Hidden by default) -->
        <div id="resultsSection" style="display: none;">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="stat-number" id="totalCourses">0</div>
                    <div class="stat-label">إجمالي الدورات</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number" id="totalParticipants">0</div>
                    <div class="stat-label">إجمالي المشاركين</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stat-number" id="totalCenters">0</div>
                    <div class="stat-label">عدد المراكز</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-medal"></i>
                    </div>
                    <div class="stat-number" id="totalGraduates">0</div>
                    <div class="stat-label">إجمالي الخريجين</div>
                </div>
            </div>

            <div class="visual-separator"></div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-card">
                    <div class="chart-title">
                        <i class="fas fa-chart-pie me-2"></i>توزيع الدورات حسب المستوى
                    </div>
                    <div class="chart-container">
                        <canvas id="levelChart"></canvas>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-title">
                        <i class="fas fa-chart-bar me-2"></i>توزيع المشاركين حسب المراكز
                    </div>
                    <div class="chart-container">
                        <canvas id="centerChart"></canvas>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-title">
                        <i class="fas fa-route me-2"></i>توزيع الدورات حسب مسارات التدريب
                    </div>
                    <div class="chart-container">
                        <canvas id="coursePathChart"></canvas>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-title">
                        <i class="fas fa-building me-2"></i>توزيع المراكز حسب الجهات
                    </div>
                    <div class="chart-container">
                        <canvas id="centerAgencyChart"></canvas>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-title">
                        <i class="fas fa-map-marker-alt me-2"></i>توزيع الدورات حسب المواقع
                    </div>
                    <div class="chart-container">
                        <canvas id="courseLocationChart"></canvas>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-title">
                        <i class="fas fa-users me-2"></i>توزيع المشاركين حسب أنواع المشاركين
                    </div>
                    <div class="chart-container">
                        <canvas id="participantTypeChart"></canvas>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-title">
                        <i class="fas fa-shield-alt me-2"></i>توزيع المشاركين حسب تصنيفات القوة
                    </div>
                    <div class="chart-container">
                        <canvas id="forceClassificationChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="visual-separator"></div>

            <!-- Detailed Tables -->
            <div class="detailed-tables">
                <!-- Courses by Level Table -->
                <div class="table-card">
                    <div class="table-header">
                        <i class="fas fa-layer-group me-2"></i>تفصيل الدورات حسب المستويات والمراكز
                    </div>
                    <div class="table-responsive">
                        <table class="data-table" id="levelTable">
                            <thead>
                                <tr>
                                    <th rowspan="2">المركز</th>
                                    <th colspan="3">المستوى الأول</th>
                                    <th colspan="3">المستوى الثاني</th>
                                    <th colspan="3">المستوى الثالث</th>
                                    <th rowspan="2">الإجمالي</th>
                                </tr>
                                <tr>
                                    <th>عدد الدورات</th>
                                    <th>المشاركين</th>
                                    <th>الخريجين</th>
                                    <th>عدد الدورات</th>
                                    <th>المشاركين</th>
                                    <th>الخريجين</th>
                                    <th>عدد الدورات</th>
                                    <th>المشاركين</th>
                                    <th>الخريجين</th>
                                </tr>
                            </thead>
                            <tbody id="levelTableBody">
                                <!-- Data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Participants Details Table -->
                <div class="table-card">
                    <div class="table-header">
                        <i class="fas fa-users me-2"></i>تفاصيل المشاركين والدورات
                    </div>
                    <div class="table-responsive">
                        <table class="data-table" id="participantsTable">
                            <thead>
                                <tr>
                                    <th>م</th>
                                    <th>الجهة</th>
                                    <th>اسم المركز أو المعسكر</th>
                                    <th>اسم مدير الدورة</th>
                                    <th>نوع المستوى</th>
                                    <th>مدة الدورة</th>
                                    <th>عدد المشاركين</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody id="participantsTableBody">
                                <!-- Data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Export Section -->
            <div class="export-section">
                <h4 class="mb-4">
                    <i class="fas fa-download me-2"></i>تصدير وطباعة التقرير
                </h4>
                <div class="export-buttons">
                    <button class="btn-export pdf" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                    <button class="btn-export excel" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i>
                        تصدير Excel
                    </button>
                    <button class="btn-export print" onclick="printReport()">
                        <i class="fas fa-print"></i>
                        طباعة التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js - محلي -->
<script src="{{ url_for('static', filename='libs/chartjs/chart.min.js') }}"></script>
<!-- jsPDF - محلي -->
<script src="{{ url_for('static', filename='libs/other/jspdf.umd.min.js') }}"></script>
<!-- XLSX - محلي -->
<script src="{{ url_for('static', filename='libs/other/xlsx.full.min.js') }}"></script>

<script>
let levelChart, centerChart;

// تحديث الأرقام بتأثير العد التصاعدي
function animateNumber(element, finalNumber) {
    let currentNumber = 0;
    const increment = finalNumber / 50;
    const timer = setInterval(() => {
        currentNumber += increment;
        if (currentNumber >= finalNumber) {
            element.textContent = finalNumber;
            clearInterval(timer);
        } else {
            element.textContent = Math.floor(currentNumber);
        }
    }, 30);
}

// دالة قوية لإجبار حجم الكانفاس
function forceCanvasSize(canvas, width = 400, height = 300) {
    if (!canvas) return;

    console.log(`🔧 إجبار حجم الكانفاس: ${canvas.id} إلى ${width}x${height}`);

    // إيقاف أي رسوم بيانية موجودة
    if (canvas.chart) {
        canvas.chart.destroy();
        canvas.chart = null;
    }

    // إزالة جميع الخصائص المتعلقة بـ Chart.js
    canvas.removeAttribute('data-chartjs-render-monitor');
    canvas.removeAttribute('style');

    // تطبيق الأبعاد بقوة مطلقة
    Object.assign(canvas.style, {
        width: `${width}px`,
        height: `${height}px`,
        maxWidth: `${width}px`,
        maxHeight: `${height}px`,
        minWidth: `${width}px`,
        minHeight: `${height}px`,
        display: 'block',
        boxSizing: 'border-box',
        position: 'absolute',
        top: '0',
        left: '0'
    });

    // تعيين الخصائص المباشرة
    canvas.width = width;
    canvas.height = height;
    canvas.setAttribute('width', width);
    canvas.setAttribute('height', height);

    // إجبار حجم الحاوي
    const container = canvas.closest('.chart-container');
    if (container) {
        Object.assign(container.style, {
            width: `${width}px`,
            height: `${height}px`,
            maxWidth: `${width}px`,
            maxHeight: `${height}px`,
            minWidth: `${width}px`,
            minHeight: `${height}px`,
            overflow: 'hidden',
            contain: 'layout style size',
            position: 'relative',
            display: 'block',
            boxSizing: 'border-box'
        });
    }

    console.log(`✅ تم إجبار الحجم: ${canvas.width}x${canvas.height}`);
}

// دالة مراقبة مستمرة لمنع التمدد
function startCanvasMonitor() {
    const canvasIds = ['levelChart', 'centerChart', 'coursePathChart', 'centerAgencyChart', 'courseLocationChart', 'participantTypeChart', 'forceClassificationChart'];

    setInterval(() => {
        canvasIds.forEach(id => {
            const canvas = document.getElementById(id);
            if (canvas) {
                // فحص إذا تغير الحجم
                if (canvas.width !== 400 || canvas.height !== 300 ||
                    canvas.style.width !== '400px' || canvas.style.height !== '300px') {
                    console.warn(`⚠️ تم اكتشاف تمدد في ${id}: ${canvas.width}x${canvas.height}`);
                    forceCanvasSize(canvas, 400, 300);
                }
            }
        });
    }, 25); // فحص كل 25ms
}

// دالة لإظهار رسالة عدم وجود بيانات
function showNoDataMessage() {
    const canvasIds = ['levelChart', 'centerChart', 'coursePathChart', 'centerAgencyChart', 'courseLocationChart', 'participantTypeChart', 'forceClassificationChart'];

    canvasIds.forEach(id => {
        const canvas = document.getElementById(id);
        if (canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#666';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('لا توجد بيانات للعرض', canvas.width / 2, canvas.height / 2);
        }
    });
}

// إنشاء الرسوم البيانية
function createCharts(data) {
    console.log('إنشاء الرسوم البيانية مع البيانات:', data);

    // التحقق من وجود البيانات وتعيين قيم افتراضية
    if (!data) {
        console.error('❌ لا توجد بيانات للرسوم البيانية');
        return;
    }

    // تعيين قيم افتراضية للبيانات المفقودة
    const chartData = {
        level1: data.level1 || 0,
        level2: data.level2 || 0,
        level3: data.level3 || 0,
        centerNames: data.centerNames || [],
        centerParticipants: data.centerParticipants || [],
        coursePathStats: data.coursePathStats || [],
        centerAgencyStats: data.centerAgencyStats || [],
        courseLocationStats: data.courseLocationStats || [],
        participantTypeStats: data.participantTypeStats || [],
        forceClassificationStats: data.forceClassificationStats || []
    };

    console.log('📊 البيانات المعالجة:', chartData);

    // فحص البيانات بالتفصيل
    console.log(`📈 بيانات المستويات: الأول=${chartData.level1}, الثاني=${chartData.level2}, الثالث=${chartData.level3}`);
    console.log(`🏢 بيانات المراكز: ${chartData.centerNames.length} مركز, ${chartData.centerParticipants.length} قيمة`);

    // التحقق من وجود بيانات صالحة للعرض
    const hasLevelData = chartData.level1 > 0 || chartData.level2 > 0 || chartData.level3 > 0;
    const hasCenterData = chartData.centerNames.length > 0 && chartData.centerParticipants.length > 0;

    console.log(`✅ بيانات المستويات صالحة: ${hasLevelData}`);
    console.log(`✅ بيانات المراكز صالحة: ${hasCenterData}`);

    // إذا لم تكن هناك بيانات، أظهر رسالة
    if (!hasLevelData && !hasCenterData) {
        console.warn('⚠️ لا توجد بيانات صالحة للعرض');
        showNoDataMessage();
        return;
    }

    // تدمير جميع الرسوم البيانية الموجودة أولاً
    if (window.levelChart && typeof window.levelChart.destroy === 'function') {
        window.levelChart.destroy();
        window.levelChart = null;
    }
    if (window.centerChart && typeof window.centerChart.destroy === 'function') {
        window.centerChart.destroy();
        window.centerChart = null;
    }
    if (window.coursePathChart && typeof window.coursePathChart.destroy === 'function') {
        window.coursePathChart.destroy();
        window.coursePathChart = null;
    }
    if (window.centerAgencyChart && typeof window.centerAgencyChart.destroy === 'function') {
        window.centerAgencyChart.destroy();
        window.centerAgencyChart = null;
    }
    if (window.courseLocationChart && typeof window.courseLocationChart.destroy === 'function') {
        window.courseLocationChart.destroy();
        window.courseLocationChart = null;
    }
    if (window.participantTypeChart && typeof window.participantTypeChart.destroy === 'function') {
        window.participantTypeChart.destroy();
        window.participantTypeChart = null;
    }
    if (window.forceClassificationChart && typeof window.forceClassificationChart.destroy === 'function') {
        window.forceClassificationChart.destroy();
        window.forceClassificationChart = null;
    }

    // إجبار حجم جميع الكانفاس قبل إنشاء الرسوم البيانية
    const canvasIds = ['levelChart', 'centerChart', 'coursePathChart', 'centerAgencyChart', 'courseLocationChart', 'participantTypeChart', 'forceClassificationChart'];
    canvasIds.forEach(id => {
        const canvas = document.getElementById(id);
        if (canvas) {
            forceCanvasSize(canvas, 400, 300);
        }
    });

    // تعيين حجم الكانفاس بشكل صريح
    const levelCanvas = document.getElementById('levelChart');
    const centerCanvas = document.getElementById('centerChart');
    const coursePathCanvas = document.getElementById('coursePathChart');
    const centerAgencyCanvas = document.getElementById('centerAgencyChart');
    const courseLocationCanvas = document.getElementById('courseLocationChart');
    const participantTypeCanvas = document.getElementById('participantTypeChart');
    const forceClassificationCanvas = document.getElementById('forceClassificationChart');

    // التحقق من وجود العناصر
    if (!levelCanvas || !centerCanvas) {
        console.error('عناصر الكانفاس غير موجودة');
        return;
    }

    // إجبار أبعاد الكانفاس
    forceCanvasSize(levelCanvas, 400, 300);
    forceCanvasSize(centerCanvas, 400, 300);
    if (coursePathCanvas) forceCanvasSize(coursePathCanvas, 400, 300);
    if (centerAgencyCanvas) forceCanvasSize(centerAgencyCanvas, 400, 300);
    if (courseLocationCanvas) forceCanvasSize(courseLocationCanvas, 400, 300);
    if (participantTypeCanvas) forceCanvasSize(participantTypeCanvas, 400, 300);
    if (forceClassificationCanvas) forceCanvasSize(forceClassificationCanvas, 400, 300);

    // رسم بياني دائري للمستويات
    const levelCtx = levelCanvas.getContext('2d');
    if (window.levelChart && typeof window.levelChart.destroy === 'function') {
        window.levelChart.destroy();
    }

    console.log(`🎯 إنشاء رسم المستويات بالبيانات: [${chartData.level1}, ${chartData.level2}, ${chartData.level3}]`);

    window.levelChart = new Chart(levelCtx, {
        type: 'doughnut',
        data: {
            labels: ['المستوى الأول', 'المستوى الثاني', 'المستوى الثالث'],
            datasets: [{
                data: [chartData.level1, chartData.level2, chartData.level3],
                backgroundColor: [
                    'rgba(102, 126, 234, 0.8)',
                    'rgba(118, 75, 162, 0.8)',
                    'rgba(255, 107, 107, 0.8)'
                ],
                borderColor: [
                    'rgba(102, 126, 234, 1)',
                    'rgba(118, 75, 162, 1)',
                    'rgba(255, 107, 107, 1)'
                ],
                borderWidth: 3,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: false,
            maintainAspectRatio: false,
            onResize: function(chart, size) {
                // منع أي تغيير في الحجم
                console.log('🚫 منع تغيير حجم الرسم البياني الأول');
                forceCanvasSize(chart.canvas, 400, 300);
                return false;
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        font: {
                            size: 12,
                            family: 'Arial'
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                duration: 1500
            },
            layout: {
                padding: {
                    top: 10,
                    bottom: 10
                }
            }
        }
    });

    // إجبار الحجم فوراً بعد الإنشاء
    setTimeout(() => {
        forceCanvasSize(levelCanvas, 400, 300);
        console.log('✅ تم إنشاء رسم المستويات بنجاح');
    }, 10);

    // رسم بياني عمودي للمراكز
    const centerCtx = centerCanvas.getContext('2d');
    if (window.centerChart && typeof window.centerChart.destroy === 'function') {
        window.centerChart.destroy();
    }

    console.log(`🏢 إنشاء رسم المراكز بالبيانات: أسماء=${chartData.centerNames}, قيم=${chartData.centerParticipants}`);

    window.centerChart = new Chart(centerCtx, {
        type: 'bar',
        data: {
            labels: chartData.centerNames,
            datasets: [{
                label: 'عدد المشاركين',
                data: chartData.centerParticipants,
                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                borderColor: 'rgba(102, 126, 234, 1)',
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: false,
            maintainAspectRatio: false,
            onResize: function(chart, size) {
                // منع أي تغيير في الحجم
                console.log('🚫 منع تغيير حجم الرسم البياني الثاني');
                forceCanvasSize(chart.canvas, 400, 300);
                return false;
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(102, 126, 234, 1)',
                    borderWidth: 1
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 10
                        },
                        maxRotation: 45,
                        minRotation: 0
                    }
                }
            },
            animation: {
                duration: 1500,
                easing: 'easeInOutQuart'
            },
            layout: {
                padding: {
                    top: 10,
                    bottom: 10,
                    left: 5,
                    right: 5
                }
            }
        }
    });

    // إجبار الحجم فوراً بعد الإنشاء
    setTimeout(() => {
        forceCanvasSize(centerCanvas, 400, 300);
        console.log('✅ تم إنشاء رسم المراكز بنجاح');
    }, 20);

    // 3. رسم بياني لتوزيع الدورات حسب مسارات التدريب
    if (coursePathCanvas && chartData.coursePathStats && chartData.coursePathStats.length > 0) {
        const coursePathCtx = coursePathCanvas.getContext('2d');
        if (window.coursePathChart && typeof window.coursePathChart.destroy === 'function') {
            window.coursePathChart.destroy();
        }

        window.coursePathChart = new Chart(coursePathCtx, {
            type: 'doughnut',
            data: {
                labels: chartData.coursePathStats.map(item => item.name),
                datasets: [{
                    data: chartData.coursePathStats.map(item => item.count),
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(255, 107, 107, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                onResize: function(chart, size) {
                    console.log('🚫 منع تغيير حجم رسم مسارات التدريب');
                    forceCanvasSize(chart.canvas, 400, 300);
                    return false;
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { padding: 15, font: { size: 11 } }
                    }
                }
            }
        });
    }

    // 4. رسم بياني لتوزيع المراكز حسب الجهات
    if (centerAgencyCanvas && chartData.centerAgencyStats && chartData.centerAgencyStats.length > 0) {
        const centerAgencyCtx = centerAgencyCanvas.getContext('2d');
        if (window.centerAgencyChart && typeof window.centerAgencyChart.destroy === 'function') {
            window.centerAgencyChart.destroy();
        }

        window.centerAgencyChart = new Chart(centerAgencyCtx, {
            type: 'bar',
            data: {
                labels: chartData.centerAgencyStats.map(item => item.name),
                datasets: [{
                    data: chartData.centerAgencyStats.map(item => item.count),
                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                    borderColor: 'rgba(102, 126, 234, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    y: { beginAtZero: true },
                    x: { ticks: { maxRotation: 45 } }
                }
            }
        });
    }

    // 5. رسم بياني لتوزيع الدورات حسب المواقع
    if (courseLocationCanvas && chartData.courseLocationStats && chartData.courseLocationStats.length > 0) {
        const courseLocationCtx = courseLocationCanvas.getContext('2d');
        if (window.courseLocationChart && typeof window.courseLocationChart.destroy === 'function') {
            window.courseLocationChart.destroy();
        }

        window.courseLocationChart = new Chart(courseLocationCtx, {
            type: 'bar',
            data: {
                labels: chartData.courseLocationStats.slice(0, 8).map(item => item.name),
                datasets: [{
                    data: chartData.courseLocationStats.slice(0, 8).map(item => item.count),
                    backgroundColor: 'rgba(118, 75, 162, 0.8)',
                    borderColor: 'rgba(118, 75, 162, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    y: { beginAtZero: true },
                    x: { ticks: { maxRotation: 45, font: { size: 10 } } }
                }
            }
        });
    }

    // 6. رسم بياني لتوزيع المشاركين حسب أنواع المشاركين
    if (participantTypeCanvas && chartData.participantTypeStats && chartData.participantTypeStats.length > 0) {
        const participantTypeCtx = participantTypeCanvas.getContext('2d');
        if (window.participantTypeChart && typeof window.participantTypeChart.destroy === 'function') {
            window.participantTypeChart.destroy();
        }

        window.participantTypeChart = new Chart(participantTypeCtx, {
            type: 'pie',
            data: {
                labels: chartData.participantTypeStats.map(item => item.name),
                datasets: [{
                    data: chartData.participantTypeStats.map(item => item.count),
                    backgroundColor: [
                        'rgba(255, 107, 107, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { padding: 15, font: { size: 11 } }
                    }
                }
            }
        });
    }

    // 7. رسم بياني لتوزيع المشاركين حسب تصنيفات القوة
    if (forceClassificationCanvas && chartData.forceClassificationStats && chartData.forceClassificationStats.length > 0) {
        const forceClassificationCtx = forceClassificationCanvas.getContext('2d');
        if (window.forceClassificationChart && typeof window.forceClassificationChart.destroy === 'function') {
            window.forceClassificationChart.destroy();
        }

        window.forceClassificationChart = new Chart(forceClassificationCtx, {
            type: 'doughnut',
            data: {
                labels: chartData.forceClassificationStats.map(item => item.name),
                datasets: [{
                    data: chartData.forceClassificationStats.map(item => item.count),
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(153, 102, 255, 0.8)',
                        'rgba(255, 159, 64, 0.8)',
                        'rgba(199, 199, 199, 0.8)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { padding: 15, font: { size: 11 } }
                    }
                }
            }
        });
    }
}

// معالجة إرسال النموذج
document.getElementById('reportForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // إظهار شاشة التحميل
    document.getElementById('loadingOverlay').style.display = 'flex';

    const formData = new FormData(this);

    fetch('/generate_report', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    })
    .then(text => {
        let data;
        try {
            data = JSON.parse(text);
        } catch (e) {
            console.error('خطأ في تحليل JSON:', e);
            console.error('النص المستلم:', text);
            throw new Error('استجابة غير صالحة من الخادم');
        }

        // إخفاء شاشة التحميل
        document.getElementById('loadingOverlay').style.display = 'none';

        if (data.success) {
            // إظهار قسم النتائج
            document.getElementById('resultsSection').style.display = 'block';

            // تحديث الإحصائيات مع التأثير البصري
            animateNumber(document.getElementById('totalCourses'), data.totalCourses || 0);
            animateNumber(document.getElementById('totalParticipants'), data.totalParticipants || 0);
            animateNumber(document.getElementById('totalCenters'), data.totalCenters || 0);
            animateNumber(document.getElementById('totalGraduates'), data.totalGraduates || 0);

            // إنشاء الرسوم البيانية مع حماية إضافية
            setTimeout(() => {
                createCharts(data.chartData || {});
                // تطبيق حماية إضافية بعد إنشاء الرسوم البيانية
                setTimeout(() => {
                    const canvasIds = ['levelChart', 'centerChart', 'coursePathChart', 'centerAgencyChart', 'courseLocationChart', 'participantTypeChart', 'forceClassificationChart'];
                    canvasIds.forEach(id => {
                        const canvas = document.getElementById(id);
                        if (canvas) {
                            forceCanvasSize(canvas, 400, 300);
                        }
                    });
                }, 100);
            }, 50);

            // تحديث الجداول
            updateTables(data.tableData || {});

            // التمرير إلى النتائج
            document.getElementById('resultsSection').scrollIntoView({
                behavior: 'smooth'
            });
        } else {
            alert('حدث خطأ في إنشاء التقرير: ' + (data.message || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        document.getElementById('loadingOverlay').style.display = 'none';
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال بالخادم: ' + error.message);
    });
});

// تحديث الجداول
function updateTables(tableData) {
    console.log('📋 تحديث الجداول مع البيانات:', tableData);

    // التحقق من وجود البيانات
    if (!tableData) {
        console.error('❌ لا توجد بيانات للجداول');
        return;
    }

    // تحديث جدول المستويات
    const levelTableBody = document.getElementById('levelTableBody');
    if (levelTableBody) {
        levelTableBody.innerHTML = '';

        const levelData = tableData.levelData || [];
        console.log(`📊 بيانات جدول المستويات: ${levelData.length} صف`);

        levelData.forEach(row => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td class="fw-bold">${row.center || 'غير محدد'}</td>
                <td>${row.level1_courses || 0}</td>
                <td>${row.level1_participants || 0}</td>
                <td>${row.level1_graduates || 0}</td>
                <td>${row.level2_courses || 0}</td>
                <td>${row.level2_participants || 0}</td>
                <td>${row.level2_graduates || 0}</td>
                <td>${row.level3_courses || 0}</td>
                <td>${row.level3_participants || 0}</td>
                <td>${row.level3_graduates || 0}</td>
                <td class="fw-bold">${row.total || 0}</td>
            `;
            levelTableBody.appendChild(tr);
        });
    }

    // تحديث جدول المشاركين
    const participantsTableBody = document.getElementById('participantsTableBody');
    if (participantsTableBody) {
        participantsTableBody.innerHTML = '';

        const participantsData = tableData.participantsData || [];
        console.log(`👥 بيانات جدول المشاركين: ${participantsData.length} صف`);

        participantsData.forEach((row, index) => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${index + 1}</td>
                <td>${row.agency || 'غير محدد'}</td>
                <td>${row.center || 'غير محدد'}</td>
                <td>${row.director || 'غير محدد'}</td>
                <td>${row.level || 'غير محدد'}</td>
                <td>${row.duration || 'غير محدد'}</td>
                <td>${row.participants || 0}</td>
                <td>${row.notes || 'لا توجد ملاحظات'}</td>
            `;
            participantsTableBody.appendChild(tr);
        });
    }
}

// تصدير إلى PDF
function exportToPDF() {
    window.print();
}

// تصدير إلى Excel
function exportToExcel() {
    const wb = XLSX.utils.book_new();

    // تصدير جدول المستويات
    const levelTable = document.getElementById('levelTable');
    const levelWS = XLSX.utils.table_to_sheet(levelTable);
    XLSX.utils.book_append_sheet(wb, levelWS, 'تفصيل المستويات');

    // تصدير جدول المشاركين
    const participantsTable = document.getElementById('participantsTable');
    const participantsWS = XLSX.utils.table_to_sheet(participantsTable);
    XLSX.utils.book_append_sheet(wb, participantsWS, 'تفاصيل المشاركين');

    // حفظ الملف
    const today = new Date().toISOString().split('T')[0];
    XLSX.writeFile(wb, `تقرير_الدورات_${today}.xlsx`);
}

// طباعة التقرير
function printReport() {
    window.print();
}

// مراقب قوي لمنع تمدد الكانفاس
function monitorCanvasSize() {
    const canvases = [
        'levelChart', 'centerChart', 'coursePathChart', 'centerAgencyChart',
        'courseLocationChart', 'participantTypeChart', 'forceClassificationChart'
    ];

    const canvasElements = canvases.map(id => document.getElementById(id)).filter(el => el);

    if (canvasElements.length > 0) {
        console.log('بدء مراقبة الكانفاس...');

        // إعادة تعيين فورية
        canvasElements.forEach(canvas => {
            resetCanvasSize(canvas, 500, 350);
        });

        // مراقبة مكثفة كل 50ms
        const monitor = setInterval(() => {
            canvasElements.forEach(canvas => {
                if (canvas.height > 300 || canvas.width > 400 ||
                    canvas.style.height === '' || canvas.style.width === '' ||
                    canvas.height > 400 || canvas.width > 500) {
                    console.warn(`⚠️ كانفاس متمدد: ${canvas.id} - ${canvas.width}x${canvas.height}`);
                    resetCanvasSize(canvas, 400, 300);
                }
            });
        }, 50);

        // مراقب إضافي للتغييرات في DOM
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' &&
                    (mutation.attributeName === 'width' || mutation.attributeName === 'height' ||
                     mutation.attributeName === 'style')) {
                    const canvas = mutation.target;
                    if (canvasElements.includes(canvas)) {
                        console.log(`🔄 تغيير في الكانفاس: ${canvas.id}`);
                        resetCanvasSize(canvas, 500, 350);
                    }
                }
            });
        });

        // مراقبة جميع الكانفاس
        canvasElements.forEach(canvas => {
            observer.observe(canvas, {
                attributes: true,
                attributeFilter: ['width', 'height', 'style']
            });
        });
    }
}

// تحديد التاريخ الافتراضي (آخر شهر)
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

    document.querySelector('input[name="end_date"]').value = today.toISOString().split('T')[0];
    document.querySelector('input[name="start_date"]').value = lastMonth.toISOString().split('T')[0];

    // بدء مراقبة حجم الكانفاس
    monitorCanvasSize();

    // بدء المراقب المستمر
    startCanvasMonitor();

    // تعطيل Chart.js من تغيير الحجم تماماً
    if (typeof Chart !== 'undefined') {
        // حفظ الدالة الأصلية
        const originalResize = Chart.prototype.resize;

        // استبدال دالة resize
        Chart.prototype.resize = function(width, height) {
            console.log('🚫 تم منع Chart.js من تغيير الحجم');
            // إجبار الحجم الثابت
            if (this.canvas) {
                forceCanvasSize(this.canvas, 400, 300);
            }
            return this;
        };

        console.log('✅ تم تعطيل دالة resize في Chart.js');
    }

    // تعطيل ResizeObserver تماماً لمنع Chart.js من مراقبة تغيير الحجم
    if (typeof ResizeObserver !== 'undefined') {
        const originalResizeObserver = window.ResizeObserver;
        window.ResizeObserver = function(callback) {
            // إنشاء ResizeObserver وهمي لا يفعل شيئاً
            return {
                observe: function() { console.log('🚫 تم منع ResizeObserver'); },
                unobserve: function() {},
                disconnect: function() {}
            };
        };
        console.log('✅ تم تعطيل ResizeObserver');
    }
});
</script>
        </div> <!-- إغلاق main-container -->
    </div>
</div>
{% endblock %}
