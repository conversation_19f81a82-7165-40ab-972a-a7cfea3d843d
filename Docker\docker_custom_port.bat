@echo off
chcp 65001 > nul
title نظام التدريب والتأهيل - Docker - منفذ مخصص

echo.
echo ========================================
echo  نظام التدريب والتأهيل - منفذ مخصص
echo ========================================
echo.

set /p PORT="أدخل رقم المنفذ المطلوب (افتراضي 5000): "
if "%PORT%"=="" set PORT=5000

echo.
echo 🔍 التحقق من وجود Docker...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker غير مثبت على النظام
    pause
    exit /b 1
)

echo ✅ Docker متاح
echo.

echo 🐳 بناء الصورة...
docker build -t training_system .

if %errorlevel% equ 0 (
    echo.
    echo 🚀 تشغيل النظام على المنفذ %PORT%...
    docker run -d -p %PORT%:5000 --name training_system_%PORT% training_system
    
    if %errorlevel% equ 0 (
        echo.
        echo ========================================
        echo ✅ تم تشغيل النظام بنجاح!
        echo ========================================
        echo.
        echo 🌐 الوصول إلى النظام:
        echo    http://localhost:%PORT%
        echo.
        echo 🔑 بيانات تسجيل الدخول:
        echo    الإيميل: <EMAIL>
        echo    كلمة المرور: admin123
        echo.
        echo 📋 أوامر مفيدة:
        echo    عرض السجلات: docker logs training_system_%PORT%
        echo    إيقاف النظام: docker stop training_system_%PORT%
        echo    حذف الحاوية: docker rm training_system_%PORT%
        echo.
        echo ⏰ سيتم فتح المتصفح خلال 5 ثوان...
        timeout /t 5 /nobreak > nul
        start http://localhost:%PORT%
    ) else (
        echo ❌ فشل في تشغيل النظام
    )
) else (
    echo ❌ فشل في بناء الصورة
)

echo.
pause
