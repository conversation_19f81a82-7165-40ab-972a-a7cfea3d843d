{% extends "layout.html" %}

{% block styles %}
<style>
    .training-matrix {
        position: relative;
        width: 100%;
        height: 500px;
        margin: 20px 0;
        margin-top: 10px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 25px;
        padding: 20px;
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
    }

    .matrix-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 200px;
        height: 120px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        font-weight: bold;
        font-size: 1.2rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        z-index: 10;
        border: 3px solid white;
    }

    .matrix-item {
        position: absolute;
        width: 150px;
        height: 80px;
        background-color: white;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        z-index: 5;
        text-decoration: none;
        font-size: 0.9rem;
    }

    .matrix-item:hover {
        text-decoration: none;
        opacity: 0.8;
    }

    .matrix-line {
        position: absolute;
        background-color: #333;
        z-index: 1;
    }

    /* المواد */
    .materials {
        top: 15%;
        left: 50%;
        transform: translateX(-50%);
        background-color: #28a745;
        color: white;
    }

    /* المستويات */
    .levels {
        top: 15%;
        left: 75%;
        background-color: #17a2b8;
        color: white;
    }

    /* أنواع المشاركين */
    .participant-types {
        top: 15%;
        left: 25%;
        background-color: #6f42c1;
        color: white;
    }

    /* الدفعات */
    .payments {
        top: 30%;
        left: 75%;
        background-color: #fd7e14;
        color: white;
    }

    /* الخريجين */
    .graduates {
        top: 30%;
        left: 25%;
        background-color: #20c997;
        color: white;
    }

    /* الملتحقين */
    .enrollees {
        top: 45%;
        left: 75%;
        background-color: #e83e8c;
        color: white;
    }

    /* المدربين */
    .trainers {
        top: 45%;
        left: 25%;
        background-color: #007bff;
        color: white;
    }

    /* المالي والإحصاءات */
    .financial {
        top: 70%;
        left: 75%;
        background-color: #dc3545;
        color: white;
    }

    /* المراكز */
    .centers {
        top: 70%;
        left: 25%;
        background-color: #ffc107;
        color: black;
    }

    /* خطوط الاتصال */
    .line-center-materials {
        top: 42%;
        left: 50%;
        width: 2px;
        height: 15%;
        transform: translateX(-50%);
    }

    .line-materials-levels {
        top: 15%;
        left: 58%;
        width: 17%;
        height: 2px;
    }

    .line-materials-participant-types {
        top: 15%;
        left: 25%;
        width: 25%;
        height: 2px;
    }

    .line-materials-payments {
        top: 22%;
        left: 58%;
        width: 2px;
        height: 8%;
        transform: translateX(-50%) rotate(45deg);
    }

    .line-materials-graduates {
        top: 22%;
        left: 42%;
        width: 2px;
        height: 8%;
        transform: translateX(-50%) rotate(-45deg);
    }

    .line-center-enrollees {
        top: 45%;
        left: 58%;
        width: 17%;
        height: 2px;
    }

    .line-center-trainers {
        top: 45%;
        left: 25%;
        width: 17%;
        height: 2px;
    }

    .line-center-financial {
        top: 60%;
        left: 58%;
        width: 2px;
        height: 10%;
        transform: translateX(-50%) rotate(45deg);
    }

    .line-center-centers {
        top: 60%;
        left: 42%;
        width: 2px;
        height: 10%;
        transform: translateX(-50%) rotate(-45deg);
    }

    .welcome-section {
        text-align: right;
        margin-bottom: 20px;
        padding: 25px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        color: white;
        position: relative;
        overflow: hidden;
    }

    .welcome-section::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(180deg); }
    }

    .welcome-title {
        font-size: 2.5rem;
        font-weight: bold;
        color: white;
        margin-bottom: 15px;
        text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.4);
        position: relative;
        z-index: 2;
        background: linear-gradient(45deg, #ffffff, #f0f8ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .welcome-subtitle {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.95);
        margin-bottom: 20px;
        position: relative;
        z-index: 2;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    }

    .stats-card {
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        box-shadow:
            0 10px 30px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(102, 126, 234, 0.1);
        backdrop-filter: blur(5px);
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        /* animation: shimmer 3s infinite; */
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .stats-card:hover {
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow:
            0 15px 40px rgba(102, 126, 234, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 10px;
        position: relative;
        z-index: 2;
    }

    .stats-label {
        color: #6c757d;
        font-size: 0.95rem;
        margin-bottom: 0;
        position: relative;
        z-index: 2;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="welcome-section">
                <h1 class="welcome-title">نظام التدريب والتأهيل</h1>
                <p class="welcome-subtitle">منصة متكاملة لإدارة الدورات التدريبية والتأهيلية</p>
            </div>
        </div>
    </div>

    <div class="text-center mb-3">
        <h3 style="
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(102, 126, 234, 0.2);
            margin-bottom: 10px;
        ">مصفوفة إدارة النظام</h3>
        <p style="
            color: #6c757d;
            font-size: 1.1rem;
            font-weight: 500;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        ">اختر القسم المطلوب للوصول إلى الوظائف المختلفة</p>
    </div>

    <div class="training-matrix">
        <!-- المركز -->
        <div class="matrix-center">مصفوفة التدريب والتأهيل</div>

        <!-- العناصر المرتبطة -->
        <a href="{{ url_for('courses') }}" class="matrix-item materials">المواد</a>
        <a href="{{ url_for('course_levels') }}" class="matrix-item levels">المستويات</a>
        <a href="{{ url_for('participant_types') }}" class="matrix-item participant-types">أنواع المشاركين</a>
        <a href="{{ url_for('reports') }}" class="matrix-item payments">الدفعات</a>
        <a href="{{ url_for('reports') }}" class="matrix-item graduates">الخريجين</a>
        <a href="{{ url_for('personal_data_list') }}" class="matrix-item enrollees">الملتحقين</a>
        <a href="{{ url_for('users') }}" class="matrix-item trainers">المدربين</a>
        <a href="{{ url_for('reports') }}" class="matrix-item financial">المالي والإحصاءات</a>
        <a href="{{ url_for('training_centers') }}" class="matrix-item centers">المراكز</a>

        <!-- خطوط الاتصال -->
        <div class="matrix-line line-center-materials"></div>
        <div class="matrix-line line-materials-levels"></div>
        <div class="matrix-line line-materials-participant-types"></div>
        <div class="matrix-line line-materials-payments"></div>
        <div class="matrix-line line-materials-graduates"></div>
        <div class="matrix-line line-center-enrollees"></div>
        <div class="matrix-line line-center-trainers"></div>
        <div class="matrix-line line-center-financial"></div>
        <div class="matrix-line line-center-centers"></div>
    </div>

    <div class="row mt-4">
        <div class="col-md-4 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap fa-3x mb-3 text-primary"></i>
                    <h5 class="card-title stats-number">85</h5>
                    <p class="card-text stats-label">إجمالي الشهادات الممنوحة</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-3x mb-3 text-success"></i>
                    <h5 class="card-title stats-number">320</h5>
                    <p class="card-text stats-label">إجمالي ساعات التدريب</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-3x mb-3 text-info"></i>
                    <h5 class="card-title stats-number">150</h5>
                    <p class="card-text stats-label">إجمالي المتدربين</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
