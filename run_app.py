#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف تشغيل النظام المبسط
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app

    if __name__ == '__main__':
        print("🚀 بدء تشغيل نظام التدريب والتأهيل...")
        print("📍 الرابط: http://localhost:5000")
        print("🔑 المستخدم: admin")
        print("🔑 كلمة المرور: admin")
        print("=" * 50)

        app.run(
            host='localhost',
            port=5000,
            debug=True,
            threaded=True,
            use_reloader=False
        )

except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    print("🔧 تأكد من تثبيت جميع المتطلبات:")
    print("   pip install flask flask-sqlalchemy flask-login flask-wtf")
    input("اضغط Enter للخروج...")
