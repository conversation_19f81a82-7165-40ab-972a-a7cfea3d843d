#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار إصلاح مشكلة csrf_token في name_analysis
"""

def test_templates_fixed():
    """اختبار إصلاح templates"""
    print("🔍 اختبار إصلاح templates...")
    
    templates_to_check = [
        'templates/person_data/name_analysis.html',
        'templates/person_data/name_analysis_results.html',
        'templates/person_data_table.html'
    ]
    
    for template_path in templates_to_check:
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📄 فحص {template_path}:")
            
            # فحص csrf_token() القديم
            if 'csrf_token()' in content:
                print(f"   ❌ لا يزال يحتوي على csrf_token() القديم")
                # عرض السطور التي تحتوي على المشكلة
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if 'csrf_token()' in line:
                        print(f"      السطر {i}: {line.strip()}")
            else:
                print(f"   ✅ لا يحتوي على csrf_token() القديم")
            
            # فحص csrf_token الجديد
            if 'csrf_token }}' in content:
                print(f"   ✅ يحتوي على csrf_token الجديد الصحيح")
            else:
                print(f"   ⚠️ لا يحتوي على csrf_token الجديد")
                
        except FileNotFoundError:
            print(f"   ❌ الملف غير موجود: {template_path}")
        except Exception as e:
            print(f"   ❌ خطأ في قراءة الملف: {e}")

def test_routes_fixed():
    """اختبار إصلاح routes"""
    print("\n🔍 اختبار إصلاح routes...")
    
    try:
        with open('person_data_routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("📄 فحص person_data_routes.py:")
        
        # فحص import generate_csrf
        if 'from flask_wtf.csrf import generate_csrf' in content:
            print("   ✅ تم استيراد generate_csrf")
        else:
            print("   ❌ لم يتم استيراد generate_csrf")
        
        # فحص إضافة csrf_token للـ context
        routes_to_check = [
            'person_data_table.html',
            'name_analysis.html',
            'name_analysis_results.html'
        ]
        
        for route in routes_to_check:
            if f'{route}' in content and 'csrf_token=generate_csrf()' in content:
                print(f"   ✅ تم إضافة csrf_token لـ {route}")
            else:
                print(f"   ⚠️ قد لا يكون csrf_token مضاف لـ {route}")
                
    except FileNotFoundError:
        print("   ❌ ملف person_data_routes.py غير موجود")
    except Exception as e:
        print(f"   ❌ خطأ في قراءة الملف: {e}")

def test_app_csrf():
    """اختبار إعدادات CSRF في app.py"""
    print("\n🔍 اختبار إعدادات CSRF في app.py...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("📄 فحص app.py:")
        
        # فحص استيراد CSRFProtect
        if 'from flask_wtf import FlaskForm, CSRFProtect' in content:
            print("   ✅ تم استيراد CSRFProtect")
        else:
            print("   ❌ لم يتم استيراد CSRFProtect")
        
        # فحص تفعيل CSRF
        if 'csrf = CSRFProtect(app)' in content:
            print("   ✅ تم تفعيل CSRFProtect")
        else:
            print("   ❌ لم يتم تفعيل CSRFProtect")
        
        # فحص @csrf.exempt للـ API routes
        if '@csrf.exempt' in content:
            print("   ✅ تم إضافة @csrf.exempt للـ API routes")
        else:
            print("   ⚠️ قد لا يكون @csrf.exempt مضاف للـ API routes")
                
    except FileNotFoundError:
        print("   ❌ ملف app.py غير موجود")
    except Exception as e:
        print(f"   ❌ خطأ في قراءة الملف: {e}")

def show_test_urls():
    """عرض URLs للاختبار"""
    print("\n🌐 URLs للاختبار:")
    print("=" * 50)
    
    urls = [
        ("تحليل الأسماء", "http://localhost:5001/name_analysis"),
        ("جدول بيانات الأشخاص", "http://localhost:5001/person_data_table"),
        ("إدارة المشاركين", "http://localhost:5001/manage_participants/1/"),
        ("الصفحة الرئيسية", "http://localhost:5001/")
    ]
    
    for name, url in urls:
        print(f"📋 {name}:")
        print(f"   {url}")
    
    print("\n🔑 بيانات تسجيل الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار إصلاح مشكلة csrf_token في name_analysis")
    print("=" * 60)
    
    # 1. اختبار إصلاح templates
    test_templates_fixed()
    
    # 2. اختبار إصلاح routes
    test_routes_fixed()
    
    # 3. اختبار إعدادات CSRF
    test_app_csrf()
    
    # 4. عرض URLs للاختبار
    show_test_urls()
    
    # الخلاصة
    print("\n" + "=" * 60)
    print("📊 خلاصة الإصلاح:")
    print("=" * 60)
    
    print("✅ تم إصلاح المشاكل التالية:")
    print("   1. csrf_token() → csrf_token في templates")
    print("   2. إضافة generate_csrf() في routes")
    print("   3. إضافة csrf_token للـ context")
    print("   4. تفعيل CSRFProtect في app.py")
    print("   5. إضافة @csrf.exempt للـ API routes")
    
    print("\n🎯 النتيجة المتوقعة:")
    print("   ✅ رابط name_analysis يعمل بدون أخطاء csrf_token")
    print("   ✅ يمكن رفع ملفات Excel وتحليلها")
    print("   ✅ جميع النماذج تعمل بشكل صحيح")
    
    print("\n🚀 للاختبار الآن:")
    print("   1. افتح http://localhost:5001/")
    print("   2. سجل الدخول بـ admin/admin")
    print("   3. اختبر http://localhost:5001/name_analysis")
    print("   4. جرب رفع ملف Excel للتحليل")

if __name__ == "__main__":
    main()
