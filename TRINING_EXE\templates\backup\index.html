{% extends "layout.html" %}

{% block styles %}
<style>
    .backup-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
    }
    
    .backup-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }
    
    .backup-actions {
        display: flex;
        gap: 10px;
    }
    
    .backup-actions a {
        flex: 1;
    }
    
    .backup-form {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .backup-list {
        margin-top: 30px;
    }
    
    .backup-date {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .backup-size {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: bold;
    }
    
    .backup-empty {
        text-align: center;
        padding: 50px 0;
        color: #6c757d;
    }
    
    .backup-empty i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mb-4">
        <i class="fas fa-database me-2"></i>
        النسخ الاحتياطي لقاعدة البيانات
    </h1>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card backup-form">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-plus-circle me-2"></i>
                        إنشاء نسخة احتياطية جديدة
                    </h5>
                    
                    <form method="POST" action="{{ url_for('backup') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.backup_path.label(class="form-label") }}
                                    {{ form.backup_path(class="form-control", placeholder="اترك فارغًا لاستخدام المسار الافتراضي") }}
                                    <div class="form-text">
                                        إذا تركت هذا الحقل فارغًا، سيتم حفظ النسخة الاحتياطية في المجلد الافتراضي (backups).
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.backup_name.label(class="form-label") }}
                                    {{ form.backup_name(class="form-control", placeholder="اترك فارغًا لإنشاء اسم تلقائي") }}
                                    <div class="form-text">
                                        إذا تركت هذا الحقل فارغًا، سيتم إنشاء اسم تلقائي يتضمن التاريخ والوقت.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="backup-list">
        <h2 class="mb-4">
            <i class="fas fa-history me-2"></i>
            النسخ الاحتياطية المتوفرة
        </h2>
        
        {% if backups %}
            <div class="row">
                {% for backup in backups %}
                    <div class="col-md-4 mb-4">
                        <div class="card backup-card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-file-archive me-2"></i>
                                    {{ backup.name }}
                                </h5>
                                
                                <p class="backup-date">
                                    <i class="far fa-calendar-alt me-1"></i>
                                    {{ backup.date.strftime('%Y-%m-%d %H:%M:%S') }}
                                </p>
                                
                                <p class="backup-size">
                                    <i class="fas fa-weight me-1"></i>
                                    {% if backup.size < 1024 %}
                                        {{ backup.size }} بايت
                                    {% elif backup.size < 1024 * 1024 %}
                                        {{ (backup.size / 1024) | round(2) }} كيلوبايت
                                    {% else %}
                                        {{ (backup.size / (1024 * 1024)) | round(2) }} ميجابايت
                                    {% endif %}
                                </p>
                                
                                <div class="backup-actions">
                                    <a href="{{ url_for('download_backup', filename=backup.name) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-download me-1"></i>
                                        تنزيل
                                    </a>
                                    
                                    <a href="{{ url_for('restore_backup', filename=backup.name) }}" class="btn btn-sm btn-warning" onclick="return confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال قاعدة البيانات الحالية.')">
                                        <i class="fas fa-undo me-1"></i>
                                        استعادة
                                    </a>
                                    
                                    <a href="{{ url_for('delete_backup', filename=backup.name) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="backup-empty">
                <i class="fas fa-database d-block"></i>
                <p>لا توجد نسخ احتياطية متوفرة حاليًا.</p>
                <p>قم بإنشاء نسخة احتياطية جديدة باستخدام النموذج أعلاه.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
