#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشخيص مشكلة نموذج التقييم
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db

def debug_evaluation_form():
    """تشخيص مشكلة نموذج التقييم"""
    
    with app.app_context():
        print("🔍 تشخيص مشكلة نموذج التقييم...")
        
        course_id = 1
        
        # 1. فحص الدورة
        print(f"\n1️⃣ فحص الدورة {course_id}:")
        try:
            course_result = db.engine.execute("SELECT * FROM course WHERE id = ?", (course_id,)).fetchone()
            if course_result:
                print(f"   ✅ الدورة موجودة: {course_result['title']}")
            else:
                print(f"   ❌ الدورة غير موجودة")
                return
        except Exception as e:
            print(f"   ❌ خطأ في فحص الدورة: {str(e)}")
            return
        
        # 2. فحص المشاركين
        print(f"\n2️⃣ فحص المشاركين:")
        try:
            participants_result = db.engine.execute("SELECT * FROM course_participant WHERE course_id = ?", (course_id,)).fetchall()
            print(f"   ✅ عدد المشاركين: {len(participants_result)}")
            if participants_result:
                print(f"   👤 أول مشارك: ID {participants_result[0]['id']}")
        except Exception as e:
            print(f"   ❌ خطأ في فحص المشاركين: {str(e)}")
        
        # 3. فحص معايير التقييم
        print(f"\n3️⃣ فحص معايير التقييم:")
        try:
            # الاستعلام الأساسي
            criteria_query = """
                SELECT DISTINCT ec.id, ec.name, ec.description, ec.order_index
                FROM evaluation_criteria ec
                JOIN course_evaluation_criteria cec ON ec.id = cec.criteria_id
                WHERE cec.course_id = ? AND cec.is_active = 1 AND ec.is_active = 1
                ORDER BY ec.order_index
            """
            
            print(f"   🔍 تشغيل الاستعلام...")
            criteria_results = db.engine.execute(criteria_query, (course_id,)).fetchall()
            print(f"   ✅ عدد المعايير: {len(criteria_results)}")
            
            for criteria in criteria_results:
                print(f"   📝 معيار {criteria['id']}: {criteria['name']}")
                
                # فحص البنود
                items_query = """
                    SELECT id, name, description, max_score, order_index
                    FROM evaluation_items
                    WHERE criteria_id = ? AND is_active = 1
                    ORDER BY order_index
                """
                
                items_results = db.engine.execute(items_query, (criteria['id'],)).fetchall()
                print(f"      - عدد البنود: {len(items_results)}")
                
                for item in items_results[:2]:  # أول بندين فقط
                    print(f"        * {item['name']}: {item['max_score']} درجة")
        
        except Exception as e:
            print(f"   ❌ خطأ في فحص المعايير: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # 4. اختبار الاستعلام المبسط
        print(f"\n4️⃣ اختبار الاستعلام المبسط:")
        try:
            # فحص جدول evaluation_criteria
            criteria_count = db.engine.execute("SELECT COUNT(*) as count FROM evaluation_criteria").fetchone()
            print(f"   📊 evaluation_criteria: {criteria_count['count']} سجل")
            
            # فحص جدول course_evaluation_criteria
            course_criteria_count = db.engine.execute("SELECT COUNT(*) as count FROM course_evaluation_criteria WHERE course_id = ?", (course_id,)).fetchone()
            print(f"   📊 course_evaluation_criteria للدورة {course_id}: {course_criteria_count['count']} سجل")
            
            # فحص جدول evaluation_items
            items_count = db.engine.execute("SELECT COUNT(*) as count FROM evaluation_items").fetchone()
            print(f"   📊 evaluation_items: {items_count['count']} سجل")
            
        except Exception as e:
            print(f"   ❌ خطأ في الاستعلام المبسط: {str(e)}")
        
        # 5. اختبار محاكاة الطلب
        print(f"\n5️⃣ محاكاة طلب نموذج التقييم:")
        try:
            participant_ids = [1]  # افتراض وجود مشارك برقم 1
            
            print(f"   📝 معرفات المشاركين: {participant_ids}")
            
            # محاكاة الكود من app.py
            criteria_data = []
            total_max_score = 0
            
            # جلب المعايير المرتبطة بالدورة
            criteria_results = db.engine.execute("""
                SELECT DISTINCT ec.id, ec.name, ec.description, ec.order_index
                FROM evaluation_criteria ec
                JOIN course_evaluation_criteria cec ON ec.id = cec.criteria_id
                WHERE cec.course_id = ? AND cec.is_active = 1 AND ec.is_active = 1
                ORDER BY ec.order_index
            """, (course_id,)).fetchall()
            
            for criteria in criteria_results:
                # جلب البنود لكل معيار
                items_results = db.engine.execute("""
                    SELECT id, name, description, max_score, order_index
                    FROM evaluation_items
                    WHERE criteria_id = ? AND is_active = 1
                    ORDER BY order_index
                """, (criteria['id'],)).fetchall()
                
                items = []
                for item in items_results:
                    items.append({
                        'id': item['id'],
                        'name': item['name'],
                        'description': item['description'],
                        'max_score': item['max_score'],
                        'order_index': item['order_index']
                    })
                    total_max_score += item['max_score']
                
                if items:  # إضافة المعيار فقط إذا كان له بنود
                    criteria_data.append({
                        'id': criteria['id'],
                        'name': criteria['name'],
                        'description': criteria['description'],
                        'order_index': criteria['order_index'],
                        'items': items
                    })
            
            print(f"   ✅ تم تجميع البيانات بنجاح:")
            print(f"      - عدد المعايير: {len(criteria_data)}")
            print(f"      - الدرجة الإجمالية: {total_max_score}")
            
        except Exception as e:
            print(f"   ❌ خطأ في محاكاة الطلب: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_evaluation_form()
