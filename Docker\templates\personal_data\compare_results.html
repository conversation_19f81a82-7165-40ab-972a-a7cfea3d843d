{% extends "layout.html" %}

{% block styles %}
<style>
    .form-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }

    .form-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }

    .btn-primary {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
    }

    .btn-secondary {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
    }

    .tab-content {
        padding: 20px;
        background-color: #fff;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .nav-tabs .nav-link {
        border-radius: 10px 10px 0 0;
        padding: 12px 20px;
        font-weight: bold;
        color: #495057;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        margin-right: 5px;
    }

    .nav-tabs .nav-link.active {
        color: #4a6bff;
        background-color: #fff;
        border-bottom-color: transparent;
    }

    .result-section {
        margin-bottom: 30px;
    }

    .result-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }

    .result-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .result-table th, .result-table td {
        padding: 12px 15px;
        text-align: right;
        border: 1px solid #dee2e6;
    }

    .result-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    .result-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    .result-table tr:hover {
        background-color: #e9ecef;
    }

    .badge-success {
        background-color: #28a745;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
    }

    .badge-warning {
        background-color: #ffc107;
        color: #212529;
        padding: 5px 10px;
        border-radius: 5px;
    }

    .badge-danger {
        background-color: #dc3545;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
    }

    .badge-info {
        background-color: #17a2b8;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
    }

    .stats-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .stats-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .stats-value {
        font-size: 24px;
        font-weight: bold;
        color: #4a6bff;
    }

    .correction-highlight {
        background-color: #fffacd;
        padding: 2px 5px;
        border-radius: 3px;
        text-decoration: line-through;
    }

    .correction-new {
        background-color: #d4edda;
        padding: 2px 5px;
        border-radius: 3px;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_list') }}" class="sidebar-link active">
                <i class="fas fa-id-card"></i> البيانات الشخصية
            </a>
            <div class="sidebar-dropdown">
                <a href="#" class="sidebar-link dropdown-toggle">
                    <i class="fas fa-table"></i> الجداول الترميزية
                </a>
                <div class="sidebar-dropdown-menu">
                    <a href="{{ url_for('governorates') }}" class="sidebar-link">
                        <i class="fas fa-map-marker-alt"></i> المحافظات
                    </a>
                    <a href="{{ url_for('directorates') }}" class="sidebar-link">
                        <i class="fas fa-map"></i> المديريات
                    </a>
                </div>
            </div>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <h2 class="mb-4">نتائج المباينة بين ملفات الإكسل</h2>

        <div class="form-card">
            <div class="form-header">
                <h4><i class="fas fa-exchange-alt me-2"></i> ملخص المباينة</h4>
                <p class="text-muted">نتائج المقارنة بين الملفين: {{ file1_name }} و {{ file2_name }}</p>
            </div>

            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-title">إجمالي السجلات</div>
                        <div class="stats-value">{{ total_records }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-title">السجلات المتطابقة</div>
                        <div class="stats-value">{{ matching_records }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-title">السجلات المختلفة</div>
                        <div class="stats-value">{{ different_records }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-title">السجلات المكررة</div>
                        <div class="stats-value">{{ duplicate_records }}</div>
                    </div>
                </div>
            </div>

            <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="matching-tab" data-bs-toggle="tab" data-bs-target="#matching" type="button" role="tab" aria-controls="matching" aria-selected="true">
                        <i class="fas fa-check-circle me-2"></i>السجلات المتطابقة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="different-tab" data-bs-toggle="tab" data-bs-target="#different" type="button" role="tab" aria-controls="different" aria-selected="false">
                        <i class="fas fa-exclamation-circle me-2"></i>السجلات المختلفة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="unique1-tab" data-bs-toggle="tab" data-bs-target="#unique1" type="button" role="tab" aria-controls="unique1" aria-selected="false">
                        <i class="fas fa-file-alt me-2"></i>سجلات فقط في الملف الأول
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="unique2-tab" data-bs-toggle="tab" data-bs-target="#unique2" type="button" role="tab" aria-controls="unique2" aria-selected="false">
                        <i class="fas fa-file-alt me-2"></i>سجلات فقط في الملف الثاني
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="duplicates-tab" data-bs-toggle="tab" data-bs-target="#duplicates" type="button" role="tab" aria-controls="duplicates" aria-selected="false">
                        <i class="fas fa-copy me-2"></i>السجلات المكررة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="corrections-tab" data-bs-toggle="tab" data-bs-target="#corrections" type="button" role="tab" aria-controls="corrections" aria-selected="false">
                        <i class="fas fa-spell-check me-2"></i>التصحيحات المقترحة
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="myTabContent">
                <!-- السجلات المتطابقة -->
                <div class="tab-pane fade show active" id="matching" role="tabpanel" aria-labelledby="matching-tab">
                    {% if matching_data %}
                    <div class="table-responsive">
                        <table class="result-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>الرقم الوطني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الجهة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in matching_data %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ item.name }}</td>
                                    <td>{{ item.national_id }}</td>
                                    <td>{{ item.phone }}</td>
                                    <td>{{ item.agency }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">لا توجد سجلات متطابقة بين الملفين</div>
                    {% endif %}
                </div>

                <!-- السجلات المختلفة -->
                <div class="tab-pane fade" id="different" role="tabpanel" aria-labelledby="different-tab">
                    {% if different_data %}
                    <div class="table-responsive">
                        <table class="result-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الحقل</th>
                                    <th>القيمة في الملف الأول</th>
                                    <th>القيمة في الملف الثاني</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in different_data %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ item.field }}</td>
                                    <td>{{ item.value1 }}</td>
                                    <td>{{ item.value2 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">لا توجد سجلات مختلفة بين الملفين</div>
                    {% endif %}
                </div>

                <!-- سجلات فقط في الملف الأول -->
                <div class="tab-pane fade" id="unique1" role="tabpanel" aria-labelledby="unique1-tab">
                    {% if unique_file1 %}
                    <div class="table-responsive">
                        <table class="result-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>الرقم الوطني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الجهة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in unique_file1 %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ item.name }}</td>
                                    <td>{{ item.national_id }}</td>
                                    <td>{{ item.phone }}</td>
                                    <td>{{ item.agency }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">لا توجد سجلات فريدة في الملف الأول</div>
                    {% endif %}
                </div>

                <!-- سجلات فقط في الملف الثاني -->
                <div class="tab-pane fade" id="unique2" role="tabpanel" aria-labelledby="unique2-tab">
                    {% if unique_file2 %}
                    <div class="table-responsive">
                        <table class="result-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>الرقم الوطني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الجهة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in unique_file2 %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ item.name }}</td>
                                    <td>{{ item.national_id }}</td>
                                    <td>{{ item.phone }}</td>
                                    <td>{{ item.agency }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">لا توجد سجلات فريدة في الملف الثاني</div>
                    {% endif %}
                </div>

                <!-- السجلات المكررة -->
                <div class="tab-pane fade" id="duplicates" role="tabpanel" aria-labelledby="duplicates-tab">
                    {% if duplicates %}
                    <div class="table-responsive">
                        <table class="result-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>الرقم الوطني</th>
                                    <th>عدد التكرارات</th>
                                    <th>الملف</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in duplicates %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ item.name }}</td>
                                    <td>{{ item.national_id }}</td>
                                    <td>{{ item.count }}</td>
                                    <td>{{ item.file }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">لا توجد سجلات مكررة في الملفين</div>
                    {% endif %}
                </div>

                <!-- التصحيحات المقترحة -->
                <div class="tab-pane fade" id="corrections" role="tabpanel" aria-labelledby="corrections-tab">
                    {% if corrections %}
                    <div class="table-responsive">
                        <table class="result-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>النوع</th>
                                    <th>القيمة الأصلية</th>
                                    <th>القيمة المصححة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in corrections %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ item.type }}</td>
                                    <td><span class="correction-highlight">{{ item.original }}</span></td>
                                    <td><span class="correction-new">{{ item.corrected }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">لا توجد تصحيحات مقترحة</div>
                    {% endif %}
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="{{ url_for('export_comparison_results') }}" class="btn btn-primary">
                    <i class="fas fa-file-export me-2"></i>تصدير النتائج
                </a>
                <a href="{{ url_for('add_personal_data_new') }}" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
