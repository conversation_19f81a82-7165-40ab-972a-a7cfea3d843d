/* ========================================
   تصميم الصفحات الإدارية الموحد
   ======================================== */

/* ========================================
   1. تحسينات خاصة بالصفحات الإدارية
   ======================================== */

/* تحسين شجرة الصلاحيات */
.permission-tree {
    font-family: var(--font-family-arabic);
    max-height: 500px;
    overflow-y: auto;
    padding: var(--spacing-md);
    background: var(--bg-light);
    border-radius: var(--border-radius-md);
}

.module-header {
    background: var(--bg-gradient);
    color: var(--text-white);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
}

.sub-module-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: var(--text-white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin: var(--spacing-md) 0 var(--spacing-sm) var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.permission-item {
    padding: var(--spacing-sm) var(--spacing-md);
    margin: var(--spacing-xs) 0 var(--spacing-xs) var(--spacing-xl);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-right: 3px solid var(--primary-color);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.permission-item:hover {
    background-color: var(--bg-light);
    transform: translateX(5px);
    box-shadow: var(--shadow-sm);
    border-right-color: var(--primary-dark);
}

.permission-checkbox {
    transform: scale(1.2);
    accent-color: var(--primary-color);
}

.permission-label {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    cursor: pointer;
    user-select: none;
}

/* ========================================
   2. تحسين الجداول الإدارية
   ======================================== */
.admin-table {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.admin-table .table-header {
    background: var(--bg-gradient);
    color: var(--text-white);
    padding: var(--spacing-lg);
    display: flex;
    justify-content: between;
    align-items: center;
}

.admin-table .table-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.admin-table .table-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.admin-table table {
    margin-bottom: 0;
}

.admin-table .table th {
    background: var(--bg-dark);
    color: var(--text-white);
    font-weight: 600;
    font-size: var(--font-size-sm);
    padding: var(--spacing-md);
    border: none;
}

.admin-table .table td {
    padding: var(--spacing-md);
    vertical-align: middle;
    border-bottom: 1px solid var(--border-light);
}

.admin-table .table tbody tr:hover {
    background-color: var(--bg-light);
}

.admin-table .table tbody tr:last-child td {
    border-bottom: none;
}

/* ========================================
   3. تحسين النماذج الإدارية
   ======================================== */
.admin-form {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.admin-form .form-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.admin-form .form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.admin-form .section-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.admin-form .form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.admin-form .form-group {
    margin-bottom: var(--spacing-md);
}

.admin-form .form-label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.admin-form .form-label .required {
    color: var(--danger-color);
    font-weight: 700;
}

.admin-form .form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: all var(--transition-fast);
}

.admin-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.admin-form .form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

/* ========================================
   4. تحسين المودالات الإدارية
   ======================================== */
.admin-modal .modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
}

.admin-modal .modal-header {
    background: var(--bg-gradient);
    color: var(--text-white);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    padding: var(--spacing-lg);
    border-bottom: none;
}

.admin-modal .modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.admin-modal .modal-body {
    padding: var(--spacing-xl);
}

.admin-modal .modal-footer {
    background: var(--bg-light);
    border-top: 1px solid var(--border-light);
    padding: var(--spacing-lg);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* ========================================
   5. تحسين الإحصائيات والمؤشرات
   ======================================== */
.stats-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border-left: 4px solid var(--primary-color);
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stats-card .stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-md);
}

.stats-card .stats-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stats-card .stats-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.stats-card.primary {
    border-left-color: var(--primary-color);
}

.stats-card.primary .stats-icon {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.stats-card.success {
    border-left-color: var(--success-color);
}

.stats-card.success .stats-icon {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.stats-card.warning {
    border-left-color: var(--warning-color);
}

.stats-card.warning .stats-icon {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.stats-card.danger {
    border-left-color: var(--danger-color);
}

.stats-card.danger .stats-icon {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* ========================================
   6. تحسين نتائج الفحص
   ======================================== */
.check-results {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
}

.check-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-fast);
}

.check-item:hover {
    background: var(--bg-light);
}

.check-item.success {
    border-left: 4px solid var(--success-color);
    background: rgba(16, 185, 129, 0.05);
}

.check-item.warning {
    border-left: 4px solid var(--warning-color);
    background: rgba(245, 158, 11, 0.05);
}

.check-item.error {
    border-left: 4px solid var(--danger-color);
    background: rgba(239, 68, 68, 0.05);
}

.check-icon {
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
}

.check-content {
    flex: 1;
}

.check-title {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.check-message {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* ========================================
   7. تحسينات الاستجابة للشاشات الصغيرة
   ======================================== */
@media (max-width: 768px) {
    .admin-form .form-row {
        grid-template-columns: 1fr;
    }
    
    .admin-form .form-actions {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .permission-tree {
        max-height: 300px;
    }
    
    .permission-item {
        margin-left: var(--spacing-md);
    }
    
    .stats-card {
        text-align: center;
    }
    
    .check-item {
        flex-direction: column;
        text-align: center;
    }
}
