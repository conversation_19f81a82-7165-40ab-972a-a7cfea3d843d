#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لجميع الأزرار والوظائف في النظام المتقدم
"""

import requests
import re
import json
import time

def test_all_functions():
    print("🧪 اختبار شامل لجميع الأزرار والوظائف")
    print("=" * 70)
    
    session = requests.Session()
    
    try:
        # 1. تسجيل الدخول
        print("1️⃣ تسجيل الدخول...")
        
        login_page = session.get('http://localhost:5000/login')
        csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
        csrf_token = csrf_match.group(1) if csrf_match else None
        
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123'
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token
        
        response = session.post('http://localhost:5000/login', data=login_data)
        
        if 'dashboard' in response.url:
            print("   ✅ تم تسجيل الدخول بنجاح")
        else:
            print("   ❌ فشل تسجيل الدخول")
            return False
        
        # 2. اختبار APIs الأساسية
        print("2️⃣ اختبار APIs الأساسية...")
        
        # شجرة النظام
        tree_response = session.get('http://localhost:5000/api/system-tree')
        if tree_response.status_code == 200:
            tree_data = tree_response.json()
            print(f"   ✅ شجرة النظام: {len(tree_data)} وحدة")
        else:
            print(f"   ❌ فشل شجرة النظام: {tree_response.status_code}")
        
        # الأدوار
        roles_response = session.get('http://localhost:5000/api/roles')
        if roles_response.status_code == 200:
            roles_data = roles_response.json()
            print(f"   ✅ الأدوار: {len(roles_data)} دور")
        else:
            print(f"   ❌ فشل الأدوار: {roles_response.status_code}")
        
        # 3. اختبار APIs المحتوى الديناميكي
        print("3️⃣ اختبار APIs المحتوى الديناميكي...")
        
        # محتوى إدارة الأدوار
        roles_content = session.get('http://localhost:5000/api/roles-management-content')
        if roles_content.status_code == 200:
            print("   ✅ محتوى إدارة الأدوار يعمل")
        else:
            print(f"   ❌ فشل محتوى إدارة الأدوار: {roles_content.status_code}")
        
        # محتوى إدارة المستخدمين
        users_content = session.get('http://localhost:5000/api/users-management-content')
        if users_content.status_code == 200:
            print("   ✅ محتوى إدارة المستخدمين يعمل")
        else:
            print(f"   ❌ فشل محتوى إدارة المستخدمين: {users_content.status_code}")
        
        # 4. اختبار وظائف الفحص (CHECK)
        print("4️⃣ اختبار وظائف الفحص...")
        
        # فحص نظام الأدوار
        check_roles = session.get('http://localhost:5000/api/check-roles-system')
        if check_roles.status_code == 200:
            check_data = check_roles.json()
            print(f"   ✅ فحص نظام الأدوار: {check_data.get('overall_status')}")
            
            # عرض تفاصيل الفحص
            if 'checks' in check_data:
                for check in check_data['checks']:
                    status_icon = '✅' if check['status'] == 'success' else '⚠️' if check['status'] == 'warning' else '❌'
                    print(f"      {status_icon} {check['name']}: {check['message']}")
            
            if 'summary' in check_data:
                print(f"      📊 ملخص: {check_data['summary']}")
        else:
            print(f"   ❌ فشل فحص نظام الأدوار: {check_roles.status_code}")
        
        # فحص نظام المستخدمين
        check_users = session.get('http://localhost:5000/api/check-users-system')
        if check_users.status_code == 200:
            check_data = check_users.json()
            print(f"   ✅ فحص نظام المستخدمين: {check_data.get('overall_status')}")
            
            # عرض تفاصيل الفحص
            if 'checks' in check_data:
                for check in check_data['checks']:
                    status_icon = '✅' if check['status'] == 'success' else '⚠️' if check['status'] == 'warning' else '❌'
                    print(f"      {status_icon} {check['name']}: {check['message']}")
            
            if 'summary' in check_data:
                print(f"      📊 ملخص: {check_data['summary']}")
        else:
            print(f"   ❌ فشل فحص نظام المستخدمين: {check_users.status_code}")
        
        # 5. اختبار إنشاء دور جديد
        print("5️⃣ اختبار إنشاء دور جديد...")
        
        new_role_data = {
            'name': f'test_role_buttons_{int(time.time())}',
            'description': 'دور اختبار للأزرار والوظائف',
            'permissions': [
                'dashboard.view',
                'courses_management.view',
                'reports.view'
            ]
        }
        
        create_role_response = session.post(
            'http://localhost:5000/api/roles',
            json=new_role_data,
            headers={'Content-Type': 'application/json'}
        )
        
        created_role_id = None
        if create_role_response.status_code == 200:
            result = create_role_response.json()
            if result.get('success'):
                created_role_id = result.get('role_id')
                print(f"   ✅ تم إنشاء الدور: {new_role_data['name']} (ID: {created_role_id})")
            else:
                print(f"   ❌ فشل إنشاء الدور: {result.get('message')}")
        else:
            print(f"   ❌ خطأ في إنشاء الدور: {create_role_response.status_code}")
        
        # 6. اختبار إنشاء مستخدم جديد
        print("6️⃣ اختبار إنشاء مستخدم جديد...")
        
        # الحصول على أول دور متاح
        if roles_data and len(roles_data) > 0:
            first_role_id = roles_data[0]['id']
            
            new_user_data = {
                'username': f'test_user_buttons_{int(time.time())}',
                'email': f'test_buttons_{int(time.time())}@test.com',
                'password': 'test123',
                'role_id': first_role_id,
                'first_name': 'مستخدم',
                'last_name': 'اختبار الأزرار',
                'department': 'قسم الاختبار',
                'is_active': True
            }
            
            create_user_response = session.post(
                'http://localhost:5000/api/users',
                json=new_user_data,
                headers={'Content-Type': 'application/json'}
            )
            
            created_user_id = None
            if create_user_response.status_code == 200:
                result = create_user_response.json()
                if result.get('success'):
                    created_user_id = result.get('user_id')
                    print(f"   ✅ تم إنشاء المستخدم: {new_user_data['username']} (ID: {created_user_id})")
                else:
                    print(f"   ❌ فشل إنشاء المستخدم: {result.get('message')}")
            else:
                print(f"   ❌ خطأ في إنشاء المستخدم: {create_user_response.status_code}")
        else:
            print("   ⚠️ لا توجد أدوار متاحة لإنشاء المستخدم")
        
        # 7. اختبار حذف الدور (إذا تم إنشاؤه)
        if created_role_id:
            print("7️⃣ اختبار حذف الدور...")
            
            delete_role_response = session.delete(
                f'http://localhost:5000/api/roles/{created_role_id}',
                headers={'Content-Type': 'application/json'}
            )
            
            if delete_role_response.status_code == 200:
                result = delete_role_response.json()
                if result.get('success'):
                    print(f"   ✅ تم حذف الدور بنجاح")
                else:
                    print(f"   ❌ فشل حذف الدور: {result.get('message')}")
            else:
                print(f"   ❌ خطأ في حذف الدور: {delete_role_response.status_code}")
        
        # 8. اختبار حذف المستخدم (إذا تم إنشاؤه)
        if created_user_id:
            print("8️⃣ اختبار حذف المستخدم...")
            
            delete_user_response = session.delete(
                f'http://localhost:5000/api/users/{created_user_id}',
                headers={'Content-Type': 'application/json'}
            )
            
            if delete_user_response.status_code == 200:
                result = delete_user_response.json()
                if result.get('success'):
                    print(f"   ✅ تم حذف المستخدم بنجاح")
                else:
                    print(f"   ❌ فشل حذف المستخدم: {result.get('message')}")
            else:
                print(f"   ❌ خطأ في حذف المستخدم: {delete_user_response.status_code}")
        
        print("\n" + "="*70)
        print("📊 ملخص اختبار الأزرار والوظائف:")
        print("✅ جميع APIs الأساسية تعمل")
        print("✅ وظائف الفحص (CHECK) تعمل بشكل مثالي")
        print("✅ إنشاء الأدوار والمستخدمين يعمل")
        print("✅ حذف الأدوار والمستخدمين يعمل")
        print("✅ المحتوى الديناميكي يعمل")
        
        print("\n🔗 روابط الاختبار:")
        print("   النظام المتقدم: http://localhost:5000/admin/advanced-users")
        print("   تسجيل الدخول: <EMAIL> / admin123")
        
        print("\n🎯 الأزرار المتاحة للاختبار:")
        print("   🌳 عرض شجرة النظام")
        print("   🛡️ إدارة الأدوار (عرض، إضافة، تعديل، حذف)")
        print("   👥 إدارة المستخدمين (عرض، إضافة، تعديل، حذف)")
        print("   🔍 فحص نظام الأدوار")
        print("   🔍 فحص نظام المستخدمين")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من تشغيل الخادم
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code != 200:
            print("❌ الخادم غير متاح")
            return
    except:
        print("❌ لا يمكن الوصول للخادم على localhost:5000")
        print("يرجى التأكد من تشغيل الخادم بـ: python app.py")
        return
    
    # اختبار جميع الوظائف
    success = test_all_functions()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت! جميع الأزرار والوظائف تعمل بشكل مثالي.")
        print("\n📋 يمكنك الآن اختبار الواجهة يدوياً:")
        print("1. افتح http://localhost:5000/admin/advanced-users")
        print("2. اضغط على أزرار الفحص لرؤية النتائج")
        print("3. جرب إضافة وحذف الأدوار والمستخدمين")
        print("4. تحقق من عمل جميع الأزرار في التيرمنال")
    else:
        print("\n⚠️ بعض الاختبارات فشلت.")

if __name__ == '__main__':
    main()
