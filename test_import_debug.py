#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, PersonData

def test_import():
    """اختبار الاستيراد مباشرة"""

    with app.app_context():
        print("=== اختبار الاستيراد المباشر ===")

        # إنشاء بيانات اختبار
        test_data = {
            'الاسم الشخصي': ['علي صالح محمد', 'فاطمة أحمد علي', 'محمد حسن يحيى'],
            'الاسم المستعار': ['أبو محمد', 'أم أحمد', 'أبو علي'],
            'العمر': [30, 25, 35],
            'المحافظة': ['صنعاء', 'تعز', 'الحديدة'],
            'المديرية': ['الثورة', 'صالة', 'الحوك'],
            'العزلة': ['شعوب', 'الجند', 'الزهرة'],
            'الحي/القرية': ['حي السبعين', 'حي الجمهورية', 'حي الثورة'],
            'المؤهل العلمي': ['بكالوريوس', 'ثانوية', 'دبلوم'],
            'الحالة الاجتماعية': ['متزوج', 'عزباء', 'متزوج'],
            'العمل': ['مهندس', 'معلمة', 'طبيب'],
            'الإدارة': ['الهندسة', 'التعليم', 'الصحة'],
            'مكان العمل': ['وزارة الأشغال', 'مدرسة الأمل', 'مستشفى الثورة'],
            'الرقم الوطني': ['12345678', '87654321', '11223344'],
            'الرقم العسكري': ['M123456', 'M654321', 'M112233'],
            'رقم التلفون': ['777123456', '733987654', '770112233']
        }

        # إنشاء DataFrame
        df = pd.DataFrame(test_data)

        print(f"عدد الصفوف: {len(df)}")
        print(f"عدد الأعمدة: {len(df.columns)}")
        print("\n=== أسماء الأعمدة ===")
        for i, col in enumerate(df.columns):
            print(f"{i+1:2d}. '{col}'")

        print("\n=== أول صف من البيانات ===")
        first_row = df.iloc[0]
        for col, value in first_row.items():
            print(f"  {col}: '{value}'")

        # محاولة الاستيراد
        success_count = 0
        error_count = 0

        for index, row in df.iterrows():
            try:
                print(f"\n--- معالجة الصف {index + 1} ---")

                # التحقق من الاسم الشخصي
                if pd.isna(row['الاسم الشخصي']):
                    print("❌ الاسم الشخصي فارغ")
                    error_count += 1
                    continue

                full_name = str(row['الاسم الشخصي']).strip()
                print(f"الاسم الشخصي: '{full_name}'")

                # معالجة العمر
                age = None
                if 'العمر' in row and not pd.isna(row['العمر']):
                    try:
                        age = int(row['العمر'])
                        print(f"العمر: {age}")
                    except:
                        print("❌ خطأ في تحويل العمر")
                        age = None

                # معالجة الأرقام
                national_number = str(row['الرقم الوطني']).strip() if 'الرقم الوطني' in row and not pd.isna(row['الرقم الوطني']) else None
                military_number = str(row['الرقم العسكري']).strip() if 'الرقم العسكري' in row and not pd.isna(row['الرقم العسكري']) else None
                phone = str(row['رقم التلفون']).strip() if 'رقم التلفون' in row and not pd.isna(row['رقم التلفون']) else None

                print(f"الرقم الوطني: '{national_number}'")
                print(f"الرقم العسكري: '{military_number}'")
                print(f"رقم التلفون: '{phone}'")

                # معالجة البيانات النصية - التركيز على الحقول الثلاثة المشكوك فيها
                print("\n=== معالجة الحقول النصية ===")

                # المحافظة
                governorate_raw = row.get('المحافظة', None)
                print(f"المحافظة الخام: '{governorate_raw}' (نوع: {type(governorate_raw)})")
                if 'المحافظة' in row and not pd.isna(row['المحافظة']):
                    governorate = str(row['المحافظة']).strip()
                    print(f"المحافظة المعالجة: '{governorate}'")
                else:
                    governorate = None
                    print("المحافظة: None")

                # المديرية
                directorate_raw = row.get('المديرية', None)
                print(f"المديرية الخام: '{directorate_raw}' (نوع: {type(directorate_raw)})")
                if 'المديرية' in row and not pd.isna(row['المديرية']):
                    directorate = str(row['المديرية']).strip()
                    print(f"المديرية المعالجة: '{directorate}'")
                else:
                    directorate = None
                    print("المديرية: None")

                # العزلة
                uzla_raw = row.get('العزلة', None)
                print(f"العزلة الخام: '{uzla_raw}' (نوع: {type(uzla_raw)})")
                if 'العزلة' in row and not pd.isna(row['العزلة']):
                    uzla = str(row['العزلة']).strip()
                    print(f"العزلة المعالجة: '{uzla}'")
                else:
                    uzla = None
                    print("العزلة: None")

                # الحي/القرية
                village_raw = row.get('الحي/القرية', None)
                print(f"الحي/القرية الخام: '{village_raw}' (نوع: {type(village_raw)})")
                if 'الحي/القرية' in row and not pd.isna(row['الحي/القرية']):
                    village = str(row['الحي/القرية']).strip()
                    print(f"الحي/القرية المعالج: '{village}'")
                else:
                    village = None
                    print("الحي/القرية: None")

                # باقي الحقول
                qualification = str(row['المؤهل العلمي']).strip() if 'المؤهل العلمي' in row and not pd.isna(row['المؤهل العلمي']) else None
                marital_status = str(row['الحالة الاجتماعية']).strip() if 'الحالة الاجتماعية' in row and not pd.isna(row['الحالة الاجتماعية']) else None
                job = str(row['العمل']).strip() if 'العمل' in row and not pd.isna(row['العمل']) else None
                agency = str(row['الإدارة']).strip() if 'الإدارة' in row and not pd.isna(row['الإدارة']) else None
                work_place = str(row['مكان العمل']).strip() if 'مكان العمل' in row and not pd.isna(row['مكان العمل']) else None
                nickname = str(row['الاسم المستعار']).strip() if 'الاسم المستعار' in row and not pd.isna(row['الاسم المستعار']) else None

                # إنشاء كائن الشخص
                person = PersonData(
                    full_name=full_name,
                    nickname=nickname,
                    age=age,
                    governorate=governorate,
                    directorate=directorate,
                    uzla=uzla,
                    village=village,
                    qualification=qualification,
                    marital_status=marital_status,
                    job=job,
                    agency=agency,
                    work_place=work_place,
                    national_number=national_number,
                    military_number=military_number,
                    phone=phone
                )

                print(f"\n=== الكائن المُنشأ ===")
                print(f"الاسم الشخصي: '{person.full_name}'")
                print(f"المحافظة: '{person.governorate}'")
                print(f"المديرية: '{person.directorate}'")
                print(f"العزلة: '{person.uzla}'")
                print(f"الحي/القرية: '{person.village}'")
                print(f"المؤهل العلمي: '{person.qualification}'")
                print(f"العمل: '{person.job}'")
                print(f"الرقم الوطني: '{person.national_number}'")

                # حفظ في قاعدة البيانات
                db.session.add(person)
                db.session.commit()

                success_count += 1
                print("✅ تم الحفظ بنجاح")

            except Exception as e:
                print(f"❌ خطأ في الصف {index + 1}: {str(e)}")
                db.session.rollback()
                error_count += 1
                import traceback
                traceback.print_exc()

        print(f"\n=== النتائج النهائية ===")
        print(f"تم حفظ: {success_count} سجل")
        print(f"فشل: {error_count} سجل")

        # التحقق من البيانات المحفوظة
        print(f"\n=== التحقق من البيانات المحفوظة ===")
        saved_persons = PersonData.query.filter(PersonData.full_name.in_(['علي صالح محمد', 'فاطمة أحمد علي', 'محمد حسن يحيى'])).all()
        for person in saved_persons:
            print(f"\nID: {person.id}")
            print(f"الاسم: '{person.full_name}'")
            print(f"المحافظة: '{person.governorate}'")
            print(f"المديرية: '{person.directorate}'")
            print(f"العزلة: '{person.uzla}'")
            print(f"الحي/القرية: '{person.village}'")
            print(f"المؤهل: '{person.qualification}'")
            print(f"العمل: '{person.job}'")
            print(f"الرقم الوطني: '{person.national_number}'")

if __name__ == "__main__":
    test_import()
