@echo off
title Training System - Docker - Port 5000

echo.
echo ========================================
echo    Training System - Docker
echo ========================================
echo.

echo Checking Docker...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running
    echo Please start Docker Desktop first
    pause
    exit /b 1
)

echo Docker is available
echo.

echo Building and starting system on port 5000...
docker-compose up -d --build

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo SUCCESS: System started successfully!
    echo ========================================
    echo.
    echo Access the system at:
    echo    http://localhost:5000
    echo.
    echo Login credentials:
    echo    Email: <EMAIL>
    echo    Password: admin123
    echo.
    echo Opening browser in 5 seconds...
    timeout /t 5 /nobreak > nul
    start http://localhost:5000
) else (
    echo ERROR: Failed to start system
    echo Check logs: docker-compose logs
)

echo.
pause
