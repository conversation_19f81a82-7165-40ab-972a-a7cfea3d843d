/* ===== تصميم لوحة التحكم البسيط ===== */

/* إعدادات أساسية للوحة التحكم */
body {
    font-family: 'Cairo', sans-serif !important;
    background-color: #f8fafc;
    font-size: 14px;
    line-height: 1.6;
    color: #1f2937;
}

/* حماية أيقونات Font Awesome */
i, .fa, .fas, .far, .fal, .fab, [class*="fa-"] {
    font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Pro', 'Font Awesome 6 Brands', 'FontAwesome' !important;
}

/* الشريط الجانبي */
.sidebar {
    background-color: #1e40af;
    color: white;
    min-height: calc(100vh - 56px);
    padding: 20px 0;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* صورة المستخدم */
.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #3b82f6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.user-avatar i {
    font-size: 2rem;
    color: white;
}

/* روابط الشريط الجانبي */
.sidebar-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 12px 20px;
    display: block;
    text-decoration: none;
    border-radius: 8px;
    margin: 5px 15px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar-link:hover,
.sidebar-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    text-decoration: none;
}

.sidebar-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* الحاوي الرئيسي */
.main-container {
    background: white;
    border-radius: 8px;
    padding: 30px;
    margin-top: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

/* عناوين الصفحات */
.page-title {
    color: #1e40af;
    font-weight: 700;
    font-size: 1.75rem;
    margin-bottom: 20px;
}

.page-title i {
    margin-left: 10px;
    color: #3b82f6;
}

/* النماذج */
.form-label {
    color: #374151;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 5px;
}

.form-control,
.form-select {
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* الأزرار */
.btn {
    border-radius: 6px;
    padding: 10px 16px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background-color: #1e40af;
    color: white;
}

.btn-primary:hover {
    background-color: #1e3a8a;
}

.btn-success {
    background-color: #059669;
    color: white;
}

.btn-success:hover {
    background-color: #047857;
}

.btn-danger {
    background-color: #dc2626;
    color: white;
}

.btn-danger:hover {
    background-color: #b91c1c;
}

/* البطاقات */
.card {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 600;
    color: #374151;
}

.card-body {
    padding: 20px;
}

/* الجداول */
.table {
    font-size: 14px;
}

.table thead th {
    background-color: #f8fafc;
    border-bottom: 2px solid #e5e7eb;
    font-weight: 600;
    color: #374151;
}

.table tbody tr:hover {
    background-color: #f8fafc;
}

/* التنبيهات */
.alert {
    border-radius: 8px;
    font-size: 14px;
    border: none;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
}

.alert-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
}

/* قسم الفلترة */
.filter-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* الترقيم */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #d1d5db;
    color: #374151;
    background: white;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.pagination .page-item.active .page-link {
    background-color: #1e40af;
    border-color: #1e40af;
    color: white;
}

/* إحصائيات لوحة التحكم - تصميم مدمج */
.stat-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-bottom: 15px;
    height: auto;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.stat-icon {
    font-size: 1.5rem;
    margin-bottom: 8px;
    color: #1e40af;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1e40af;
    margin: 5px 0;
    line-height: 1;
}

.stat-label {
    font-size: 13px;
    color: #6b7280;
    font-weight: 500;
    margin: 0;
    line-height: 1.2;
}

/* ألوان مختلفة للبطاقات */
.stat-primary {
    border-left: 4px solid #1e40af;
}

.stat-primary .stat-icon,
.stat-primary .stat-number {
    color: #1e40af;
}

.stat-success {
    border-left: 4px solid #059669;
}

.stat-success .stat-icon,
.stat-success .stat-number {
    color: #059669;
}

.stat-info {
    border-left: 4px solid #0891b2;
}

.stat-info .stat-icon,
.stat-info .stat-number {
    color: #0891b2;
}

.stat-warning {
    border-left: 4px solid #d97706;
}

.stat-warning .stat-icon,
.stat-warning .stat-number {
    color: #d97706;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .sidebar {
        margin-bottom: 20px;
    }
    
    .main-container {
        padding: 20px;
        margin-top: 10px;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
}

/* تحسين التركيز */
.form-control:focus,
.form-select:focus,
.btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* تحسين الانتقالات */
.card,
.btn,
.form-control,
.sidebar-link {
    transition: all 0.3s ease;
}

/* إزالة التأثيرات المعقدة */
.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
