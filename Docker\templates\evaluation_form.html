<!-- نموذج التقييم -->
<form id="evaluationForm" onsubmit="submitEvaluation(event)">
    <div class="row">
        <div class="col-md-12">
            <h6 class="mb-3">
                <i class="fas fa-star text-warning"></i>
                تقييم المشاركين - الدورة: {{ course.title }}
            </h6>
            
            <!-- معلومات المشاركين -->
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h6 class="mb-0">المشاركين المحددين ({{ participants|length }})</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for participant in participants %}
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <input type="hidden" name="participant_ids" value="{{ participant.id }}">
                                <i class="fas fa-user text-primary me-2"></i>
                                <span>{{ participant.personal_data.full_name if participant.personal_data else 'غير محدد' }}</span>
                                <small class="text-muted ms-2">({{ participant.status }})</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- معايير التقييم -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">معايير التقييم</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th width="30%">المعيار</th>
                                    <th width="15%">الدرجة العظمى</th>
                                    <th width="15%">الوزن</th>
                                    <th width="20%">الدرجة المحققة</th>
                                    <th width="20%">ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- الحضور والانضباط -->
                                <tr>
                                    <td>
                                        <strong>الحضور والانضباط</strong>
                                        <br><small class="text-muted">التزام المشارك بالحضور والمواعيد</small>
                                    </td>
                                    <td class="text-center">20</td>
                                    <td class="text-center">1.0</td>
                                    <td>
                                        <input type="number" class="form-control score-input" 
                                               name="score_attendance" min="0" max="20" step="0.5" 
                                               onchange="calculateTotal()">
                                    </td>
                                    <td>
                                        <textarea class="form-control" name="notes_attendance" rows="2" 
                                                  placeholder="ملاحظات اختيارية"></textarea>
                                    </td>
                                </tr>

                                <!-- المشاركة والتفاعل -->
                                <tr>
                                    <td>
                                        <strong>المشاركة والتفاعل</strong>
                                        <br><small class="text-muted">مستوى مشاركة المشارك في الأنشطة والنقاشات</small>
                                    </td>
                                    <td class="text-center">20</td>
                                    <td class="text-center">1.0</td>
                                    <td>
                                        <input type="number" class="form-control score-input" 
                                               name="score_participation" min="0" max="20" step="0.5" 
                                               onchange="calculateTotal()">
                                    </td>
                                    <td>
                                        <textarea class="form-control" name="notes_participation" rows="2" 
                                                  placeholder="ملاحظات اختيارية"></textarea>
                                    </td>
                                </tr>

                                <!-- الاختبار النظري -->
                                <tr>
                                    <td>
                                        <strong>الاختبار النظري</strong>
                                        <br><small class="text-muted">درجة الاختبار النظري للمادة العلمية</small>
                                    </td>
                                    <td class="text-center">30</td>
                                    <td class="text-center">1.5</td>
                                    <td>
                                        <input type="number" class="form-control score-input" 
                                               name="score_theory" min="0" max="30" step="0.5" 
                                               onchange="calculateTotal()">
                                    </td>
                                    <td>
                                        <textarea class="form-control" name="notes_theory" rows="2" 
                                                  placeholder="ملاحظات اختيارية"></textarea>
                                    </td>
                                </tr>

                                <!-- الاختبار العملي -->
                                <tr>
                                    <td>
                                        <strong>الاختبار العملي</strong>
                                        <br><small class="text-muted">درجة الاختبار العملي والتطبيقي</small>
                                    </td>
                                    <td class="text-center">30</td>
                                    <td class="text-center">1.5</td>
                                    <td>
                                        <input type="number" class="form-control score-input" 
                                               name="score_practical" min="0" max="30" step="0.5" 
                                               onchange="calculateTotal()">
                                    </td>
                                    <td>
                                        <textarea class="form-control" name="notes_practical" rows="2" 
                                                  placeholder="ملاحظات اختيارية"></textarea>
                                    </td>
                                </tr>

                                <!-- المهارات الشخصية -->
                                <tr>
                                    <td>
                                        <strong>المهارات الشخصية</strong>
                                        <br><small class="text-muted">تقييم المهارات الشخصية والتواصل</small>
                                    </td>
                                    <td class="text-center">15</td>
                                    <td class="text-center">1.0</td>
                                    <td>
                                        <input type="number" class="form-control score-input" 
                                               name="score_personal" min="0" max="15" step="0.5" 
                                               onchange="calculateTotal()">
                                    </td>
                                    <td>
                                        <textarea class="form-control" name="notes_personal" rows="2" 
                                                  placeholder="ملاحظات اختيارية"></textarea>
                                    </td>
                                </tr>
                            </tbody>
                            <tfoot class="table-dark">
                                <tr>
                                    <th>الإجمالي</th>
                                    <th class="text-center">115</th>
                                    <th class="text-center">-</th>
                                    <th class="text-center">
                                        <span id="totalScore" class="fs-5 text-warning">0</span>
                                    </th>
                                    <th class="text-center">
                                        <span id="percentage" class="fs-5 text-warning">0%</span>
                                    </th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- التقدير النهائي -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">التقدير النهائي:</label>
                            <select class="form-select" name="final_grade" id="finalGrade">
                                <option value="">اختر التقدير</option>
                                <option value="ممتاز">ممتاز (90-100%)</option>
                                <option value="جيد جداً">جيد جداً (80-89%)</option>
                                <option value="جيد">جيد (70-79%)</option>
                                <option value="مقبول">مقبول (60-69%)</option>
                                <option value="ضعيف">ضعيف (أقل من 60%)</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">حالة التقييم:</label>
                            <select class="form-select" name="evaluation_status">
                                <option value="draft">مسودة</option>
                                <option value="final">نهائي</option>
                            </select>
                        </div>
                    </div>

                    <!-- ملاحظات عامة -->
                    <div class="mt-3">
                        <label class="form-label">ملاحظات عامة:</label>
                        <textarea class="form-control" name="general_notes" rows="3" 
                                  placeholder="ملاحظات عامة حول أداء المشارك/المشاركين"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الحفظ -->
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="submit" class="btn btn-success">
            <i class="fas fa-save"></i> حفظ التقييم
        </button>
        <button type="button" class="btn btn-primary" onclick="saveAndPrint()">
            <i class="fas fa-print"></i> حفظ وطباعة
        </button>
    </div>
</form>

<script>
function calculateTotal() {
    const scores = [
        parseFloat(document.querySelector('input[name="score_attendance"]').value) || 0,
        parseFloat(document.querySelector('input[name="score_participation"]').value) || 0,
        parseFloat(document.querySelector('input[name="score_theory"]').value) || 0,
        parseFloat(document.querySelector('input[name="score_practical"]').value) || 0,
        parseFloat(document.querySelector('input[name="score_personal"]').value) || 0
    ];
    
    const total = scores.reduce((sum, score) => sum + score, 0);
    const percentage = ((total / 115) * 100).toFixed(1);
    
    document.getElementById('totalScore').textContent = total.toFixed(1);
    document.getElementById('percentage').textContent = percentage + '%';
    
    // تحديد التقدير تلقائياً
    const gradeSelect = document.getElementById('finalGrade');
    if (percentage >= 90) {
        gradeSelect.value = 'ممتاز';
    } else if (percentage >= 80) {
        gradeSelect.value = 'جيد جداً';
    } else if (percentage >= 70) {
        gradeSelect.value = 'جيد';
    } else if (percentage >= 60) {
        gradeSelect.value = 'مقبول';
    } else {
        gradeSelect.value = 'ضعيف';
    }
}

function submitEvaluation(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    // إضافة الدرجة الإجمالية والنسبة المئوية
    data.total_score = document.getElementById('totalScore').textContent;
    data.percentage = parseFloat(document.getElementById('percentage').textContent);

    // الحصول على CSRF token
    const csrfToken = document.querySelector('meta[name=csrf-token]').getAttribute('content');

    fetch(`/course/{{ course.id }}/evaluation/save`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('تم حفظ التقييم بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('evaluationModal')).hide();
            location.reload(); // إعادة تحميل الصفحة
        } else {
            alert('حدث خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في حفظ التقييم');
    });
}

function saveAndPrint() {
    // حفظ التقييم أولاً ثم طباعته
    const form = document.getElementById('evaluationForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    data.total_score = document.getElementById('totalScore').textContent;
    data.percentage = parseFloat(document.getElementById('percentage').textContent);
    data.print_after_save = true;

    // الحصول على CSRF token
    const csrfToken = document.querySelector('meta[name=csrf-token]').getAttribute('content');

    fetch(`/course/{{ course.id }}/evaluation/save`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // فتح صفحة الطباعة
            window.open(`/course/{{ course.id }}/evaluation/print/${result.evaluation_id}`);
            bootstrap.Modal.getInstance(document.getElementById('evaluationModal')).hide();
            location.reload();
        } else {
            alert('حدث خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في حفظ التقييم');
    });
}
</script>
