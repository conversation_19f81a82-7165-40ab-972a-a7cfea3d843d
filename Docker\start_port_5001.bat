@echo off
title Training System - Docker - Port 5001

echo.
echo ========================================
echo  Training System - Port 5001
echo ========================================
echo.

echo Checking Docker...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running
    echo Please start Docker Desktop first
    pause
    exit /b 1
)

echo Docker is available
echo.

echo Starting system on port 5001...
docker-compose -f docker-compose-5001.yml up -d --build

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo SUCCESS: System started successfully!
    echo ========================================
    echo.
    echo Access the system at:
    echo    http://localhost:5001
    echo.
    echo Login credentials:
    echo    Email: <EMAIL>
    echo    Password: admin123
    echo.
    echo Opening browser in 5 seconds...
    timeout /t 5 /nobreak > nul
    start http://localhost:5001
) else (
    echo ERROR: Failed to start system
)

echo.
pause
