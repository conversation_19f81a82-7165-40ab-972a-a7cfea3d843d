
# 🚀 تعليمات تشغيل نظام التدريب على جهاز جديد

## الخطوة 1: تثبيت Python
1. ثبت Python 3.11+ من الموقع الرسمي
2. تأكد من إضافة Python إلى PATH أثناء التثبيت

## الخطوة 2: فك ضغط المشروع
1. فك ضغط الملف في أي مكان تريده
2. افتح Command Prompt/Terminal في مجلد المشروع

## الخطوة 3: تنشيط البيئة الافتراضية
### Windows:
```
venv\Scripts\activate
```

### Linux/Mac:
```
source venv/bin/activate
```

## الخطوة 4: تشغيل النظام
```
python START.py
```

أو استخدم الملفات المساعدة:
- Windows: انقر مزدوج على `start_app.bat`
- أو شغل: `python GO.py`

## الخطوة 5: الوصول للنظام
- افتح المتصفح واذهب إلى: http://localhost:5000
- اسم المستخدم: <EMAIL>
- كلمة المرور: admin123

## في حالة وجود مشاكل:
1. تأكد من تثبيت Python بشكل صحيح
2. شغل: `pip install -r requirements.txt`
3. تأكد من وجود ملف قاعدة البيانات

## للدعم:
- تحقق من ملف README.md
- راجع ملفات التشخيص في المجلد
