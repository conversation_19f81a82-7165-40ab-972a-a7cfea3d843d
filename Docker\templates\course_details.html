{% extends "layout.html" %}

{% block styles %}
<style>
    .course-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
    }

    .course-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .course-info {
        margin-top: 20px;
    }

    .course-info-item {
        margin-bottom: 10px;
    }

    .course-info-label {
        font-weight: bold;
        color: rgba(255, 255, 255, 0.8);
    }

    .course-description {
        background-color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .day-card {
        background-color: white;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .day-header {
        background-color: #f8f9fa;
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
        font-weight: bold;
    }

    .material-item {
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
        transition: all 0.3s;
    }

    .material-item:last-child {
        border-bottom: none;
    }

    .material-item:hover {
        background-color: #f8f9fa;
    }

    .material-title {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .material-description {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .material-type {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 20px;
        font-size: 0.8rem;
        margin-left: 10px;
    }

    .material-type-pdf {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .material-type-presentation {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .material-type-document {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }

    .material-type-spreadsheet {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }

    .material-type-image {
        background-color: rgba(111, 66, 193, 0.1);
        color: #6f42c1;
    }

    .material-type-video {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .material-type-other {
        background-color: rgba(108, 117, 125, 0.1);
        color: #6c757d;
    }

    .btn-group {
        display: flex;
        gap: 5px;
    }

    .btn-outline-success {
        color: #198754;
        border-color: #198754;
    }

    .btn-outline-success:hover {
        color: white;
        background-color: #198754;
    }

    .btn-outline-primary {
        color: #0d6efd;
        border-color: #0d6efd;
    }

    .btn-outline-primary:hover {
        color: white;
        background-color: #0d6efd;
    }

    .no-materials {
        padding: 20px;
        text-align: center;
        color: #6c757d;
    }

    .action-buttons {
        margin-bottom: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link active">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_excel') }}" class="sidebar-link">
                <i class="fas fa-id-card"></i> إدارة البيانات بالإكسل
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('reference_tables') }}" class="sidebar-link">
                <i class="fas fa-table"></i> الجداول الترميزية
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
            {% endif %}
        </div>
    </div>

    <div class="col-md-9">
        <div class="main-container">
            <div class="course-header">
            <div class="row">
                <div class="col-md-4">
                    {% if course.image %}
                        <img src="{{ url_for('static', filename='uploads/courses/' + course.image) }}" alt="{{ course.title }}" class="course-image">
                    {% else %}
                        <img src="{{ url_for('static', filename='img/default_course.jpg') }}" alt="{{ course.title }}" class="course-image">
                    {% endif %}
                </div>
                <div class="col-md-8">
                    <h2>{{ course.title }}</h2>
                    <div class="course-info">
                        <div class="course-info-item">
                            <span class="course-info-label">رقم الدورة العام:</span>
                            <span class="badge bg-primary">{{ course.course_number }}</span>
                        </div>
                        <div class="course-info-item">
                            <span class="course-info-label">المدرب:</span>
                            <span>{{ course.trainer.username }}</span>
                        </div>
                        <div class="course-info-item">
                            <span class="course-info-label">التصنيف:</span>
                            <span>
                                {% if course.category == 'programming' %}البرمجة
                                {% elif course.category == 'design' %}التصميم
                                {% elif course.category == 'management' %}الإدارة
                                {% elif course.category == 'marketing' %}التسويق
                                {% elif course.category == 'ai' %}الذكاء الاصطناعي
                                {% elif course.category == 'database' %}قواعد البيانات
                                {% else %}أخرى
                                {% endif %}
                            </span>
                        </div>
                        <div class="course-info-item">
                            <span class="course-info-label">المستوى:</span>
                            <span>
                                {% if course.level == 'beginner' %}مبتدئ
                                {% elif course.level == 'intermediate' %}متوسط
                                {% elif course.level == 'advanced' %}متقدم
                                {% endif %}
                            </span>
                        </div>
                        <div class="course-info-item">
                            <span class="course-info-label">تاريخ البدء:</span>
                            <span>{{ course.start_date.strftime('%Y-%m-%d') }}</span>
                        </div>
                        <div class="course-info-item">
                            <span class="course-info-label">تاريخ الانتهاء:</span>
                            <span>{{ course.end_date.strftime('%Y-%m-%d') }}</span>
                        </div>
                        <div class="course-info-item">
                            <span class="course-info-label">مدة الدورة:</span>
                            <span>{{ course.duration_days }} يوم</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            {% if current_user.role == 'trainee' %}
                {% if is_enrolled %}
                    <button class="btn btn-success" disabled>
                        <i class="fas fa-check-circle me-1"></i> أنت مسجل في هذه الدورة
                    </button>
                {% else %}
                    <a href="{{ url_for('enroll_course', course_id=course.id) }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-1"></i> التسجيل في الدورة
                    </a>
                {% endif %}
            {% endif %}

            {% if current_user.id == course.trainer_id or current_user.role == 'admin' %}
                <a href="{{ url_for('add_material', course_id=course.id) }}" class="btn btn-success">
                    <i class="fas fa-plus-circle me-1"></i> إضافة مادة تعليمية
                </a>
                <a href="{{ url_for('course_schedule', course_id=course.id) }}" class="btn btn-info">
                    <i class="fas fa-calendar-alt me-1"></i> الجدول الزمني
                </a>
                <a href="{{ url_for('course_participants', course_id=course.id) }}" class="btn btn-warning">
                    <i class="fas fa-users me-1"></i> المشاركين
                </a>
            {% endif %}

            <a href="{{ url_for('courses') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى الدورات
            </a>
        </div>

        <div class="course-description">
            <h4>وصف الدورة</h4>
            <p>{{ course.description }}</p>
        </div>

        <ul class="nav nav-tabs mb-4" id="courseTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials" type="button" role="tab" aria-controls="materials" aria-selected="true">
                    <i class="fas fa-book me-1"></i> المواد التعليمية
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="schedule-tab" data-bs-toggle="tab" data-bs-target="#schedule" type="button" role="tab" aria-controls="schedule" aria-selected="false">
                    <i class="fas fa-calendar-alt me-1"></i> الجدول الزمني
                </button>
            </li>
        </ul>

        <div class="tab-content" id="courseTabContent">
            <div class="tab-pane fade show active" id="materials" role="tabpanel" aria-labelledby="materials-tab">
                <h4>محتوى الدورة</h4>

                {% for day, materials in materials_by_day.items() %}
                    <div class="day-card">
                        <div class="day-header">
                            <i class="fas fa-calendar-day me-2"></i> اليوم {{ day }}
                        </div>

                        {% if materials %}
                            {% for material in materials %}
                                <div class="material-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="material-title">{{ material.title }}</div>
                                            {% if material.description %}
                                                <div class="material-description">{{ material.description }}</div>
                                            {% endif %}
                                        </div>

                                        {% if material.file_path %}
                                            <div>
                                                <span class="material-type material-type-{{ material.file_type }}">
                                                    {% if material.file_type == 'pdf' %}
                                                        <i class="fas fa-file-pdf"></i> PDF
                                                        {% set can_view = True %}
                                                    {% elif material.file_type == 'presentation' %}
                                                        <i class="fas fa-file-powerpoint"></i> عرض تقديمي
                                                        {% if material.file_path.endswith('.pptx') %}
                                                            (.pptx)
                                                        {% else %}
                                                            (.ppt)
                                                        {% endif %}
                                                        {% set can_view = False %}
                                                    {% elif material.file_type == 'document' %}
                                                        <i class="fas fa-file-word"></i> مستند
                                                        {% if material.file_path.endswith('.docx') %}
                                                            (.docx)
                                                        {% else %}
                                                            (.doc)
                                                        {% endif %}
                                                        {% set can_view = False %}
                                                    {% elif material.file_type == 'spreadsheet' %}
                                                        <i class="fas fa-file-excel"></i> جدول بيانات
                                                        {% if material.file_path.endswith('.xlsx') %}
                                                            (.xlsx)
                                                        {% else %}
                                                            (.xls)
                                                        {% endif %}
                                                        {% set can_view = False %}
                                                    {% elif material.file_type == 'image' %}
                                                        <i class="fas fa-file-image"></i> صورة
                                                        {% if material.file_path.endswith('.jpg') or material.file_path.endswith('.jpeg') %}
                                                            (.jpg)
                                                        {% else %}
                                                            (.png)
                                                        {% endif %}
                                                        {% set can_view = True %}
                                                    {% elif material.file_type == 'video' %}
                                                        <i class="fas fa-file-video"></i> فيديو (.mp4)
                                                        {% set can_view = True %}
                                                    {% else %}
                                                        <i class="fas fa-file"></i> ملف
                                                        {% set can_view = False %}
                                                    {% endif %}
                                                </span>
                                                <div class="btn-group ms-2">
                                                    {% if can_view %}
                                                    <a href="{{ url_for('view_material', material_id=material.id) }}" class="btn btn-sm btn-outline-success" target="_blank">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </a>
                                                    {% endif %}
                                                    <a href="{{ url_for('download_file', filename=material.file_path) }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-download"></i> تحميل
                                                    </a>
                                                    {% if current_user.id == course.trainer_id or current_user.role == 'admin' %}
                                                    <a href="{{ url_for('edit_material', material_id=material.id) }}" class="btn btn-sm btn-outline-warning">
                                                        <i class="fas fa-edit"></i> تعديل
                                                    </a>
                                                    <a href="{{ url_for('delete_material', material_id=material.id) }}" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذه المادة التعليمية؟');">
                                                        <i class="fas fa-trash-alt"></i> حذف
                                                    </a>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="no-materials">
                                <i class="fas fa-info-circle me-1"></i> لا توجد مواد تعليمية لهذا اليوم
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>

            <div class="tab-pane fade" id="schedule" role="tabpanel" aria-labelledby="schedule-tab">
                <h4>الجدول الزمني للدورة</h4>

                {% if current_user.id == course.trainer_id or current_user.role == 'admin' %}
                <div class="mb-3">
                    <a href="{{ url_for('course_schedule', course_id=course.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i> إدارة الجدول الزمني
                    </a>
                </div>
                {% endif %}

                {% for day, items in schedule_by_day.items() %}
                <div class="day-card">
                    <div class="day-header">
                        <i class="fas fa-calendar-day me-2"></i> اليوم {{ day }}
                    </div>

                    {% if items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th width="15%">الوقت</th>
                                    <th width="30%">العنوان</th>
                                    <th width="40%">الوصف</th>
                                    <th width="15%">المادة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items %}
                                <tr {% if item.is_break %}class="table-warning"{% endif %}>
                                    <td>{{ item.start_time }} - {{ item.end_time }}</td>
                                    <td>
                                        {% if item.is_break %}
                                        <i class="fas fa-coffee me-1 text-warning"></i>
                                        {% endif %}
                                        {{ item.title }}
                                    </td>
                                    <td>{{ item.description or '-' }}</td>
                                    <td>
                                        {% if item.material %}
                                        <a href="{{ url_for('view_material', material_id=item.material.id) }}" class="text-primary">
                                            <i class="fas fa-file me-1"></i> {{ item.material.title }}
                                        </a>
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="no-materials">
                        <i class="fas fa-info-circle me-1"></i> لا توجد فترات مضافة لهذا اليوم
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div> <!-- إغلاق main-container -->
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // استخدام Bootstrap Tab API
        var triggerTabList = [].slice.call(document.querySelectorAll('#courseTab button'));
        triggerTabList.forEach(function(triggerEl) {
            var tabTrigger = new bootstrap.Tab(triggerEl);

            triggerEl.addEventListener('click', function(event) {
                event.preventDefault();
                tabTrigger.show();
            });
        });

        // تفعيل علامة التبويب الأولى افتراضيًا
        var firstTabEl = document.querySelector('#courseTab button:first-child');
        var firstTab = new bootstrap.Tab(firstTabEl);
        firstTab.show();
    });
</script>
{% endblock %}