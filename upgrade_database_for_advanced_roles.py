#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ترقية قاعدة البيانات لنظام الأدوار المتقدم
Database Upgrade for Advanced Roles System
"""

import sqlite3
import json
from datetime import datetime
from system_tree_manager import SystemTreeManager

def upgrade_database():
    """ترقية قاعدة البيانات لدعم النظام المتقدم"""
    
    print("🔄 بدء ترقية قاعدة البيانات للنظام المتقدم...")
    
    conn = sqlite3.connect('training_system.db')
    cursor = conn.cursor()
    
    try:
        # 1. إضافة عمود permissions_json إلى جدول role إذا لم يكن موجوداً
        print("1️⃣ فحص وإضافة عمود permissions_json...")
        
        cursor.execute("PRAGMA table_info(role)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'permissions_json' not in columns:
            cursor.execute("ALTER TABLE role ADD COLUMN permissions_json TEXT")
            print("   ✅ تم إضافة عمود permissions_json")
        else:
            print("   ✅ عمود permissions_json موجود بالفعل")
        
        # 2. تحديث الأدوار الموجودة بهيكل الصلاحيات الجديد
        print("2️⃣ تحديث الأدوار الموجودة...")
        
        cursor.execute("SELECT id, name FROM role WHERE is_active = 1")
        existing_roles = cursor.fetchall()
        
        for role_id, role_name in existing_roles:
            # إنشاء هيكل صلاحيات افتراضي
            permissions_structure = SystemTreeManager.create_role_permissions_structure()
            
            # تطبيق صلاحيات حسب نوع الدور
            if role_name == 'admin':
                # المدير له جميع الصلاحيات
                for module_key, module_data in permissions_structure.items():
                    for perm_key in module_data['permissions']:
                        permissions_structure[module_key]['permissions'][perm_key]['enabled'] = True
                    for sub_key, sub_data in module_data['sub_modules'].items():
                        for perm_key in sub_data['permissions']:
                            permissions_structure[module_key]['sub_modules'][sub_key]['permissions'][perm_key]['enabled'] = True
            
            elif role_name == 'manager':
                # المدير له صلاحيات محدودة
                manager_permissions = [
                    'users_management.view', 'users_management.edit',
                    'courses_management.view', 'courses_management.add', 'courses_management.edit',
                    'reports.view', 'reports.export'
                ]
                for permission in manager_permissions:
                    parts = permission.split('.')
                    if len(parts) == 2:
                        module_key, perm_key = parts
                        if module_key in permissions_structure and perm_key in permissions_structure[module_key]['permissions']:
                            permissions_structure[module_key]['permissions'][perm_key]['enabled'] = True
            
            elif role_name == 'trainer':
                # المدرب له صلاحيات التدريب
                trainer_permissions = [
                    'courses_management.view', 'courses_management.edit',
                    'course_participants.view', 'course_participants.add', 'course_participants.edit',
                    'reports.view'
                ]
                for permission in trainer_permissions:
                    parts = permission.split('.')
                    if len(parts) == 2:
                        module_key, perm_key = parts
                        if module_key in permissions_structure and perm_key in permissions_structure[module_key]['permissions']:
                            permissions_structure[module_key]['permissions'][perm_key]['enabled'] = True
            
            elif role_name == 'data_entry':
                # مدخل البيانات
                data_entry_permissions = [
                    'persons_management.view', 'persons_management.add', 'persons_management.edit',
                    'courses_management.view'
                ]
                for permission in data_entry_permissions:
                    parts = permission.split('.')
                    if len(parts) == 2:
                        module_key, perm_key = parts
                        if module_key in permissions_structure and perm_key in permissions_structure[module_key]['permissions']:
                            permissions_structure[module_key]['permissions'][perm_key]['enabled'] = True
            
            elif role_name == 'viewer':
                # المشاهد له صلاحيات العرض فقط
                viewer_permissions = [
                    'dashboard.view',
                    'courses_management.view',
                    'persons_management.view',
                    'reports.view'
                ]
                for permission in viewer_permissions:
                    parts = permission.split('.')
                    if len(parts) == 2:
                        module_key, perm_key = parts
                        if module_key in permissions_structure and perm_key in permissions_structure[module_key]['permissions']:
                            permissions_structure[module_key]['permissions'][perm_key]['enabled'] = True
            
            # تحويل إلى JSON وحفظ
            permissions_json = SystemTreeManager.get_role_permissions_json(permissions_structure)
            
            cursor.execute("""
                UPDATE role 
                SET permissions_json = ? 
                WHERE id = ?
            """, (permissions_json, role_id))
            
            print(f"   ✅ تم تحديث الدور: {role_name}")
        
        # 3. إنشاء أدوار تجريبية جديدة
        print("3️⃣ إنشاء أدوار تجريبية جديدة...")
        
        new_roles = [
            {
                'name': 'hr_manager',
                'display_name': 'مدير الموارد البشرية',
                'description': 'مدير الموارد البشرية مع صلاحيات إدارة المستخدمين والتقارير',
                'permissions': [
                    'users_management.view', 'users_management.add', 'users_management.edit',
                    'roles_management.view',
                    'reports.view', 'reports.export',
                    'persons_management.view', 'persons_management.add', 'persons_management.edit'
                ]
            },
            {
                'name': 'training_coordinator',
                'display_name': 'منسق التدريب',
                'description': 'منسق التدريب مع صلاحيات إدارة الدورات والمشاركين',
                'permissions': [
                    'courses_management.view', 'courses_management.add', 'courses_management.edit',
                    'course_participants.view', 'course_participants.add', 'course_participants.edit',
                    'course_schedule.view', 'course_schedule.add', 'course_schedule.edit',
                    'reports.view', 'courses_reports.view', 'courses_reports.export'
                ]
            },
            {
                'name': 'financial_officer',
                'display_name': 'المسؤول المالي',
                'description': 'المسؤول المالي مع صلاحيات التقارير المالية',
                'permissions': [
                    'dashboard.view',
                    'courses_management.view',
                    'reports.view', 'reports.export',
                    'financial_reports.view', 'financial_reports.export'
                ]
            },
            {
                'name': 'system_admin',
                'display_name': 'مدير النظام التقني',
                'description': 'مدير النظام التقني مع صلاحيات الإعدادات والصيانة',
                'permissions': [
                    'system_settings.view', 'system_settings.edit',
                    'system_settings.backup', 'system_settings.restore',
                    'system_settings.logs', 'system_settings.maintenance',
                    'users_management.view', 'roles_management.view'
                ]
            }
        ]
        
        for role_data in new_roles:
            # التحقق من عدم وجود الدور
            cursor.execute("SELECT id FROM role WHERE name = ?", (role_data['name'],))
            if cursor.fetchone():
                print(f"   ⚠️ الدور {role_data['name']} موجود بالفعل")
                continue
            
            # إنشاء هيكل الصلاحيات
            permissions_structure = SystemTreeManager.create_role_permissions_structure()
            
            # تطبيق الصلاحيات المحددة
            for permission in role_data['permissions']:
                parts = permission.split('.')
                if len(parts) == 2:
                    module_key, perm_key = parts
                    if module_key in permissions_structure:
                        if perm_key in permissions_structure[module_key]['permissions']:
                            permissions_structure[module_key]['permissions'][perm_key]['enabled'] = True
                        else:
                            # البحث في الوحدات الفرعية
                            for sub_module in permissions_structure[module_key]['sub_modules'].values():
                                if perm_key in sub_module['permissions']:
                                    sub_module['permissions'][perm_key]['enabled'] = True
            
            permissions_json = SystemTreeManager.get_role_permissions_json(permissions_structure)
            
            # إدخال الدور الجديد
            cursor.execute("""
                INSERT INTO role (name, display_name, description, permissions_json, is_active, is_system_role, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                role_data['name'],
                role_data['display_name'],
                role_data['description'],
                permissions_json,
                1,
                0,
                datetime.now().isoformat()
            ))
            
            role_id = cursor.lastrowid
            
            # إدخال الصلاحيات في جدول role_permission
            for permission in role_data['permissions']:
                cursor.execute("""
                    INSERT OR IGNORE INTO role_permission (role_id, permission_name, is_active, created_at)
                    VALUES (?, ?, ?, ?)
                """, (role_id, permission, 1, datetime.now().isoformat()))
            
            print(f"   ✅ تم إنشاء الدور: {role_data['display_name']}")
        
        # 4. حفظ التغييرات
        conn.commit()
        print("4️⃣ تم حفظ جميع التغييرات")
        
        # 5. عرض ملخص
        cursor.execute("SELECT COUNT(*) FROM role WHERE is_active = 1")
        total_roles = cursor.fetchone()[0]
        
        print(f"\n📊 ملخص الترقية:")
        print(f"   إجمالي الأدوار: {total_roles}")
        print(f"   ✅ تم تحديث قاعدة البيانات بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ترقية قاعدة البيانات: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def main():
    """الدالة الرئيسية"""
    print("🚀 ترقية قاعدة البيانات لنظام الأدوار المتقدم")
    print("=" * 60)
    
    success = upgrade_database()
    
    if success:
        print("\n🎉 تمت الترقية بنجاح!")
        print("\nيمكنك الآن:")
        print("1. تشغيل النظام: python app.py")
        print("2. الوصول للنظام المتقدم: http://localhost:5000/admin/advanced-users")
        print("3. تسجيل الدخول بـ: <EMAIL> / admin123")
    else:
        print("\n❌ فشلت الترقية!")

if __name__ == '__main__':
    main()
