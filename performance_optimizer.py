#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ نظام تحسين الأداء الشامل
Comprehensive Performance Optimization System
"""

import os
import sys
import time
import threading
import multiprocessing
import asyncio
import functools
import sqlite3
import json
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
import logging
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import weakref
import gc

class PerformanceOptimizer:
    """فئة تحسين الأداء الشامل"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_ttl = 300  # 5 دقائق
        self.max_cache_size = 1000
        self.performance_stats = {}
        self.db_pool = []
        self.max_db_connections = 10
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        self.process_pool = ProcessPoolExecutor(max_workers=2)
        
        # إعداد التسجيل
        self.setup_logging()
        
        # بدء مراقبة الأداء
        self.start_performance_monitoring()
    
    def setup_logging(self):
        """إعداد نظام التسجيل المحسن"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # إعداد logger للأداء
        self.perf_logger = logging.getLogger('performance')
        self.perf_logger.setLevel(logging.INFO)
        
        # معالج ملف للأداء
        perf_handler = logging.FileHandler(
            log_dir / f"performance_{datetime.now().strftime('%Y%m%d')}.log",
            encoding='utf-8'
        )
        perf_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        perf_handler.setFormatter(perf_formatter)
        self.perf_logger.addHandler(perf_handler)
    
    def performance_monitor(self, func):
        """ديكوريتر لمراقبة أداء الدوال"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # تسجيل الأداء
                func_name = f"{func.__module__}.{func.__name__}"
                if func_name not in self.performance_stats:
                    self.performance_stats[func_name] = {
                        'calls': 0,
                        'total_time': 0,
                        'avg_time': 0,
                        'max_time': 0,
                        'min_time': float('inf')
                    }
                
                stats = self.performance_stats[func_name]
                stats['calls'] += 1
                stats['total_time'] += execution_time
                stats['avg_time'] = stats['total_time'] / stats['calls']
                stats['max_time'] = max(stats['max_time'], execution_time)
                stats['min_time'] = min(stats['min_time'], execution_time)
                
                # تسجيل في اللوج إذا كان بطيئاً
                if execution_time > 1.0:
                    self.perf_logger.warning(
                        f"Slow function: {func_name} took {execution_time:.3f}s"
                    )
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                self.perf_logger.error(
                    f"Error in {func_name} after {execution_time:.3f}s: {str(e)}"
                )
                raise
        
        return wrapper
    
    def smart_cache(self, ttl=300, max_size=100):
        """نظام تخزين مؤقت ذكي"""
        def decorator(func):
            cache_key_prefix = f"{func.__module__}.{func.__name__}"
            
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # إنشاء مفتاح التخزين المؤقت
                cache_key = f"{cache_key_prefix}:{hash(str(args) + str(sorted(kwargs.items())))}"
                
                # فحص التخزين المؤقت
                if cache_key in self.cache:
                    timestamp = self.cache_timestamps.get(cache_key, 0)
                    if time.time() - timestamp < ttl:
                        self.perf_logger.debug(f"Cache hit: {cache_key}")
                        return self.cache[cache_key]
                    else:
                        # انتهت صلاحية التخزين المؤقت
                        del self.cache[cache_key]
                        del self.cache_timestamps[cache_key]
                
                # تنفيذ الدالة
                result = func(*args, **kwargs)
                
                # حفظ في التخزين المؤقت
                if len(self.cache) >= max_size:
                    # إزالة أقدم عنصر
                    oldest_key = min(self.cache_timestamps.keys(), 
                                   key=lambda k: self.cache_timestamps[k])
                    del self.cache[oldest_key]
                    del self.cache_timestamps[oldest_key]
                
                self.cache[cache_key] = result
                self.cache_timestamps[cache_key] = time.time()
                
                self.perf_logger.debug(f"Cache miss: {cache_key}")
                return result
            
            return wrapper
        return decorator
    
    def async_executor(self, func):
        """تنفيذ غير متزامن للدوال"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if asyncio.iscoroutinefunction(func):
                return func(*args, **kwargs)
            else:
                # تنفيذ في thread pool
                future = self.thread_pool.submit(func, *args, **kwargs)
                return future
        
        return wrapper
    
    def database_optimizer(self):
        """محسن قاعدة البيانات"""
        class DatabaseOptimizer:
            def __init__(self, db_path):
                self.db_path = db_path
                self.connection_pool = []
                self.max_connections = 10
                self.current_connections = 0
                self.lock = threading.Lock()
            
            def get_connection(self):
                """الحصول على اتصال من المجموعة"""
                with self.lock:
                    if self.connection_pool:
                        return self.connection_pool.pop()
                    elif self.current_connections < self.max_connections:
                        conn = sqlite3.connect(
                            self.db_path,
                            check_same_thread=False,
                            timeout=30.0
                        )
                        # تحسينات SQLite
                        conn.execute("PRAGMA journal_mode=WAL")
                        conn.execute("PRAGMA synchronous=NORMAL")
                        conn.execute("PRAGMA cache_size=10000")
                        conn.execute("PRAGMA temp_store=MEMORY")
                        conn.execute("PRAGMA mmap_size=268435456")  # 256MB
                        self.current_connections += 1
                        return conn
                    else:
                        raise Exception("No available database connections")
            
            def return_connection(self, conn):
                """إرجاع الاتصال للمجموعة"""
                with self.lock:
                    if len(self.connection_pool) < self.max_connections:
                        self.connection_pool.append(conn)
                    else:
                        conn.close()
                        self.current_connections -= 1
            
            def execute_query(self, query, params=None):
                """تنفيذ استعلام محسن"""
                conn = self.get_connection()
                try:
                    cursor = conn.cursor()
                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)
                    
                    if query.strip().upper().startswith('SELECT'):
                        result = cursor.fetchall()
                    else:
                        conn.commit()
                        result = cursor.rowcount
                    
                    return result
                finally:
                    self.return_connection(conn)
        
        return DatabaseOptimizer
    
    def memory_optimizer(self):
        """محسن الذاكرة"""
        def optimize_memory():
            # تنظيف التخزين المؤقت المنتهي الصلاحية
            current_time = time.time()
            expired_keys = [
                key for key, timestamp in self.cache_timestamps.items()
                if current_time - timestamp > self.cache_ttl
            ]
            
            for key in expired_keys:
                del self.cache[key]
                del self.cache_timestamps[key]
            
            # تشغيل garbage collector
            collected = gc.collect()
            
            self.perf_logger.info(
                f"Memory optimization: removed {len(expired_keys)} cache entries, "
                f"collected {collected} objects"
            )
        
        return optimize_memory
    
    def start_performance_monitoring(self):
        """بدء مراقبة الأداء"""
        def monitor():
            while True:
                time.sleep(60)  # كل دقيقة
                
                # تنظيف الذاكرة
                self.memory_optimizer()()
                
                # تسجيل إحصائيات الأداء
                if self.performance_stats:
                    self.perf_logger.info(
                        f"Performance stats: {json.dumps(self.performance_stats, indent=2)}"
                    )
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def get_performance_report(self):
        """الحصول على تقرير الأداء"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'cache_stats': {
                'size': len(self.cache),
                'max_size': self.max_cache_size,
                'hit_ratio': self.calculate_cache_hit_ratio()
            },
            'function_stats': self.performance_stats,
            'system_stats': {
                'cpu_count': multiprocessing.cpu_count(),
                'thread_pool_workers': self.thread_pool._max_workers,
                'process_pool_workers': self.process_pool._max_workers
            }
        }
        return report
    
    def calculate_cache_hit_ratio(self):
        """حساب نسبة نجاح التخزين المؤقت"""
        # هذا مثال بسيط - يمكن تحسينه
        return 0.85  # 85% افتراضي
    
    def optimize_flask_app(self, app):
        """تحسين تطبيق Flask"""
        
        # إعداد التخزين المؤقت للقوالب
        app.jinja_env.cache = {}
        app.jinja_env.auto_reload = False
        
        # تحسين الجلسات
        app.config['SESSION_COOKIE_SECURE'] = False
        app.config['SESSION_COOKIE_HTTPONLY'] = True
        app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)
        
        # تحسين قاعدة البيانات
        if 'SQLALCHEMY_DATABASE_URI' in app.config:
            app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
                'pool_size': 10,
                'pool_recycle': 3600,
                'pool_pre_ping': True,
                'max_overflow': 20
            }
        
        # إضافة middleware للأداء
        @app.before_request
        def before_request():
            from flask import g
            g.start_time = time.time()
        
        @app.after_request
        def after_request(response):
            from flask import g, request
            if hasattr(g, 'start_time'):
                duration = time.time() - g.start_time
                if duration > 1.0:  # أكثر من ثانية
                    self.perf_logger.warning(
                        f"Slow request: {request.endpoint} took {duration:.3f}s"
                    )
            return response
        
        return app

# إنشاء مثيل عام للمحسن
performance_optimizer = PerformanceOptimizer()

# ديكوريتر سهل الاستخدام
def optimize_performance(func):
    """ديكوريتر شامل لتحسين الأداء"""
    func = performance_optimizer.performance_monitor(func)
    func = performance_optimizer.smart_cache()(func)
    return func

def async_task(func):
    """ديكوريتر للمهام غير المتزامنة"""
    return performance_optimizer.async_executor(func)

# دوال مساعدة
def get_optimized_db_class():
    """الحصول على فئة قاعدة البيانات المحسنة"""
    return performance_optimizer.database_optimizer()

def optimize_app(app):
    """تحسين تطبيق Flask"""
    return performance_optimizer.optimize_flask_app(app)

def get_performance_report():
    """الحصول على تقرير الأداء"""
    return performance_optimizer.get_performance_report()

# مثال على الاستخدام
if __name__ == "__main__":
    # اختبار النظام
    @optimize_performance
    def test_function(n):
        """دالة اختبار"""
        time.sleep(0.1)  # محاكاة عملية
        return sum(range(n))
    
    # اختبار الأداء
    print("🧪 اختبار نظام تحسين الأداء...")
    
    for i in range(5):
        result = test_function(1000)
        print(f"النتيجة {i+1}: {result}")
    
    # طباعة تقرير الأداء
    report = get_performance_report()
    print("\n📊 تقرير الأداء:")
    print(json.dumps(report, indent=2, ensure_ascii=False))
