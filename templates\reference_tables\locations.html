{% extends "layout.html" %}

{% block content %}
<div class="container">
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h3 class="mb-0">المواقع</h3>
            <a href="{{ url_for('add_location') }}" class="btn btn-light">
                <i class="fas fa-plus-circle"></i> إضافة موقع جديد
            </a>
        </div>
        <div class="card-body">
            {% if locations %}
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>اسم الموقع</th>
                            <th>العنوان</th>
                            <th>المحافظة</th>
                            <th>المديرية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for location in locations %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ location.name }}</td>
                            <td>{{ location.address }}</td>
                            <td>{{ location.governorate.name if location.governorate else '-' }}</td>
                            <td>{{ location.directorate.name if location.directorate else '-' }}</td>
                            <td>
                                <a href="{{ url_for('edit_location', location_id=location.id) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{{ url_for('delete_location', location_id=location.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الموقع؟')">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                لا توجد مواقع مضافة حالياً.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
