/**
 * نظام التفاعلات الموحد - Training System
 * =======================================
 */

// ========================================
// 1. المتغيرات العامة
// ========================================
const UnifiedSystem = {
    // إعدادات عامة
    settings: {
        animationDuration: 300,
        toastDuration: 5000,
        loadingDelay: 500,
        apiTimeout: 30000
    },
    
    // حالة النظام
    state: {
        isLoading: false,
        activeModals: [],
        notifications: []
    },
    
    // عناصر DOM المخزنة
    elements: {
        body: null,
        loadingOverlay: null,
        toastContainer: null
    }
};

// ========================================
// 2. التهيئة الأولية
// ========================================
document.addEventListener('DOMContentLoaded', function() {
    UnifiedSystem.init();
});

UnifiedSystem.init = function() {
    console.log('🚀 تهيئة نظام التفاعلات الموحد...');
    
    // تخزين عناصر DOM
    this.elements.body = document.body;
    
    // إنشاء عناصر النظام
    this.createSystemElements();
    
    // تهيئة المكونات
    this.initComponents();
    
    // تهيئة الأحداث
    this.initEvents();
    
    console.log('✅ تم تهيئة نظام التفاعلات الموحد بنجاح');
};

// ========================================
// 3. إنشاء عناصر النظام
// ========================================
UnifiedSystem.createSystemElements = function() {
    // إنشاء overlay التحميل
    this.createLoadingOverlay();
    
    // إنشاء حاوي التنبيهات
    this.createToastContainer();
};

UnifiedSystem.createLoadingOverlay = function() {
    const overlay = document.createElement('div');
    overlay.id = 'unified-loading-overlay';
    overlay.className = 'unified-loading-overlay';
    overlay.innerHTML = `
        <div class="unified-loading-content">
            <div class="unified-spinner"></div>
            <div class="unified-loading-text">جاري التحميل...</div>
        </div>
    `;
    
    // إضافة CSS للتحميل
    const style = document.createElement('style');
    style.textContent = `
        .unified-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .unified-loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .unified-loading-content {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            text-align: center;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .unified-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .unified-loading-text {
            color: #1e293b;
            font-weight: 500;
        }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(overlay);
    this.elements.loadingOverlay = overlay;
};

UnifiedSystem.createToastContainer = function() {
    const container = document.createElement('div');
    container.id = 'unified-toast-container';
    container.className = 'unified-toast-container';
    
    // إضافة CSS للتنبيهات
    const style = document.createElement('style');
    style.textContent = `
        .unified-toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9998;
            max-width: 400px;
        }
        
        .unified-toast {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            padding: 1rem;
            border-left: 4px solid;
            transform: translateX(100%);
            transition: all 0.3s ease;
            opacity: 0;
        }
        
        .unified-toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .unified-toast.success { border-left-color: #10b981; }
        .unified-toast.error { border-left-color: #ef4444; }
        .unified-toast.warning { border-left-color: #f59e0b; }
        .unified-toast.info { border-left-color: #06b6d4; }
        
        .unified-toast-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .unified-toast-title {
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .unified-toast-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #64748b;
        }
        
        .unified-toast-message {
            color: #64748b;
            font-size: 0.875rem;
        }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(container);
    this.elements.toastContainer = container;
};

// ========================================
// 4. تهيئة المكونات
// ========================================
UnifiedSystem.initComponents = function() {
    // تهيئة الأزرار
    this.initButtons();
    
    // تهيئة النماذج
    this.initForms();
    
    // تهيئة الجداول
    this.initTables();
    
    // تهيئة المودالات
    this.initModals();
};

UnifiedSystem.initButtons = function() {
    // إضافة تأثيرات الأزرار
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            // تأثير الموجة
            const ripple = document.createElement('span');
            ripple.className = 'btn-ripple';
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => ripple.remove(), 600);
        });
    });
    
    // إضافة CSS للموجة
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
};

UnifiedSystem.initForms = function() {
    // تحسين النماذج
    document.querySelectorAll('.form-control').forEach(input => {
        // تأثير التركيز
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
};

UnifiedSystem.initTables = function() {
    // تحسين الجداول
    document.querySelectorAll('.table').forEach(table => {
        // إضافة تأثيرات الصفوف
        table.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.01)';
                this.style.transition = 'all 0.2s ease';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    });
};

UnifiedSystem.initModals = function() {
    // تحسين المودالات
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('show.bs.modal', function() {
            UnifiedSystem.state.activeModals.push(this.id);
        });
        
        modal.addEventListener('hide.bs.modal', function() {
            const index = UnifiedSystem.state.activeModals.indexOf(this.id);
            if (index > -1) {
                UnifiedSystem.state.activeModals.splice(index, 1);
            }
        });
    });
};

// ========================================
// 5. تهيئة الأحداث
// ========================================
UnifiedSystem.initEvents = function() {
    // أحداث AJAX
    this.initAjaxEvents();
    
    // أحداث لوحة المفاتيح
    this.initKeyboardEvents();
    
    // أحداث النقر
    this.initClickEvents();
};

UnifiedSystem.initAjaxEvents = function() {
    // إظهار التحميل عند بدء AJAX
    $(document).ajaxStart(function() {
        UnifiedSystem.showLoading();
    });
    
    // إخفاء التحميل عند انتهاء AJAX
    $(document).ajaxStop(function() {
        UnifiedSystem.hideLoading();
    });
    
    // معالجة أخطاء AJAX
    $(document).ajaxError(function(event, xhr, settings, error) {
        UnifiedSystem.hideLoading();
        UnifiedSystem.showToast('خطأ في الاتصال', 'حدث خطأ أثناء تحميل البيانات', 'error');
    });
};

UnifiedSystem.initKeyboardEvents = function() {
    document.addEventListener('keydown', function(e) {
        // إغلاق المودالات بـ Escape
        if (e.key === 'Escape' && UnifiedSystem.state.activeModals.length > 0) {
            const lastModal = UnifiedSystem.state.activeModals[UnifiedSystem.state.activeModals.length - 1];
            const modal = document.getElementById(lastModal);
            if (modal) {
                bootstrap.Modal.getInstance(modal)?.hide();
            }
        }
    });
};

UnifiedSystem.initClickEvents = function() {
    // إغلاق التنبيهات بالنقر
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('unified-toast-close')) {
            const toast = e.target.closest('.unified-toast');
            UnifiedSystem.hideToast(toast);
        }
    });
};

// ========================================
// 6. وظائف التحميل
// ========================================
UnifiedSystem.showLoading = function(message = 'جاري التحميل...') {
    if (this.state.isLoading) return;
    
    this.state.isLoading = true;
    const overlay = this.elements.loadingOverlay;
    const text = overlay.querySelector('.unified-loading-text');
    
    text.textContent = message;
    overlay.classList.add('active');
};

UnifiedSystem.hideLoading = function() {
    this.state.isLoading = false;
    this.elements.loadingOverlay.classList.remove('active');
};

// ========================================
// 7. وظائف التنبيهات
// ========================================
UnifiedSystem.showToast = function(title, message, type = 'info', duration = null) {
    const toast = document.createElement('div');
    toast.className = `unified-toast ${type}`;
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    toast.innerHTML = `
        <div class="unified-toast-header">
            <div class="unified-toast-title">
                <i class="${icons[type]}"></i>
                ${title}
            </div>
            <button class="unified-toast-close">&times;</button>
        </div>
        <div class="unified-toast-message">${message}</div>
    `;
    
    this.elements.toastContainer.appendChild(toast);
    
    // إظهار التنبيه
    setTimeout(() => toast.classList.add('show'), 100);
    
    // إخفاء التنبيه تلقائياً
    const hideDelay = duration || this.settings.toastDuration;
    setTimeout(() => this.hideToast(toast), hideDelay);
    
    return toast;
};

UnifiedSystem.hideToast = function(toast) {
    toast.classList.remove('show');
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 300);
};

// ========================================
// 8. وظائف مساعدة
// ========================================
UnifiedSystem.formatNumber = function(number) {
    return new Intl.NumberFormat('ar-SA').format(number);
};

UnifiedSystem.formatDate = function(date) {
    return new Intl.DateTimeFormat('ar-SA').format(new Date(date));
};

UnifiedSystem.debounce = function(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// ========================================
// 9. تصدير للاستخدام العام
// ========================================
window.UnifiedSystem = UnifiedSystem;
