#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, CourseParticipant, Course, PersonData

def fix_names():
    with app.app_context():
        print("🔧 تصحيح الأسماء...")
        print("=" * 60)
        
        # البحث عن الأسماء الخاطئة وتصحيحها
        name_corrections = {
            'علي صالح محمد الحميري1': 'علياء صالح محمد الحميري',
            'فاطمة أحمد علي المقطري1': 'فاطمة أحمد علي المقطري',
            'علي أحمد محمد قاسم': 'علياء أحمد محمد قاسم',
            'علي أحمد علي قاسم': 'علياء أحمد علي قاسم',
            'علي عبدالله علي عبدالله': 'علياء عبدالله علي عبدالله'
        }
        
        print("📝 تصحيح الأسماء التالية:")
        for wrong_name, correct_name in name_corrections.items():
            print(f"   {wrong_name} ← {correct_name}")
        
        # تطبيق التصحيحات
        corrected_count = 0
        for wrong_name, correct_name in name_corrections.items():
            person = PersonData.query.filter_by(full_name=wrong_name).first()
            if person:
                person.full_name = correct_name
                corrected_count += 1
                print(f"✅ تم تصحيح: {wrong_name} إلى {correct_name}")
            else:
                print(f"⚠️ لم يتم العثور على: {wrong_name}")
        
        # حفظ التغييرات
        try:
            db.session.commit()
            print(f"\n✅ تم تصحيح {corrected_count} اسم بنجاح!")
        except Exception as e:
            db.session.rollback()
            print(f"\n❌ خطأ في حفظ التصحيحات: {str(e)}")
            return
        
        # عرض النتيجة
        print("\n" + "=" * 60)
        print("📊 الأسماء بعد التصحيح:")
        
        # عرض جميع الأسماء في الدورة 3455667
        course = Course.query.filter_by(course_number='3455667').first()
        if course:
            participants = CourseParticipant.query.filter_by(course_id=course.id).all()
            print(f"\nالمشاركين في دورة {course.course_number} - {course.title}:")
            for i, participant in enumerate(participants, 1):
                name = participant.personal_data.full_name if participant.personal_data else 'غير محدد'
                print(f"   {i}. {name}")

if __name__ == '__main__':
    fix_names()
