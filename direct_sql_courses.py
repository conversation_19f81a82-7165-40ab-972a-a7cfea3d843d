#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء الدورات باستخدام SQL مباشرة
"""

import sqlite3
from datetime import datetime

def create_courses_direct():
    """إنشاء الدورات باستخدام SQL مباشرة"""
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        print("🔄 إنشاء الدورات باستخدام SQL مباشرة...")
        
        # حذف الدورات الموجودة
        cursor.execute("DELETE FROM course")
        print("🗑️ تم حذف الدورات الموجودة")
        
        # التحقق من وجود مستخدم admin
        cursor.execute("SELECT id FROM user WHERE role = 'admin' LIMIT 1")
        admin_result = cursor.fetchone()
        
        if not admin_result:
            print("❌ لا يوجد مستخدم admin")
            return False
        
        admin_id = admin_result[0]
        print(f"✅ العثور على admin ID: {admin_id}")
        
        # بيانات الدورات الثلاث
        courses_data = [
            (
                '3455667',  # course_number
                'المبيعات2',  # title
                'دورة تدريبية في المبيعات والتسويق',  # description
                'management',  # category
                'intermediate',  # level
                'default_course.jpg',  # image
                '2025-05-22 00:00:00',  # start_date
                '2025-05-31 00:00:00',  # end_date
                '1446/11/22',  # start_date_hijri
                '1446/12/01',  # end_date_hijri
                10,  # duration_days
                admin_id,  # trainer_id
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # created_at
            ),
            (
                '10054',  # course_number
                'تدريب الذكاء الاصطناعي',  # title
                'دورة تدريبية في الذكاء الاصطناعي والتعلم الآلي',  # description
                'technology',  # category
                'advanced',  # level
                'default_course.jpg',  # image
                '2025-05-22 00:00:00',  # start_date
                '2025-06-20 00:00:00',  # end_date
                '1446/11/22',  # start_date_hijri
                '1446/12/20',  # end_date_hijri
                30,  # duration_days
                admin_id,  # trainer_id
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # created_at
            ),
            (
                '1100255',  # course_number
                'تدريب الذكاء الاصطناعي 2',  # title
                'دورة متقدمة في الذكاء الاصطناعي والتطبيقات العملية',  # description
                'technology',  # category
                'expert',  # level
                'default_course.jpg',  # image
                '2025-05-22 00:00:00',  # start_date
                '2025-05-31 00:00:00',  # end_date
                '1446/11/22',  # start_date_hijri
                '1446/12/01',  # end_date_hijri
                10,  # duration_days
                admin_id,  # trainer_id
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # created_at
            )
        ]
        
        # إدراج الدورات
        insert_sql = """
        INSERT INTO course (
            course_number, title, description, category, level, image,
            start_date, end_date, start_date_hijri, end_date_hijri,
            duration_days, trainer_id, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        for i, course_data in enumerate(courses_data, 1):
            try:
                cursor.execute(insert_sql, course_data)
                print(f"✅ تم إنشاء الدورة {i}: {course_data[0]} - {course_data[1]}")
            except Exception as e:
                print(f"❌ خطأ في إنشاء الدورة {i}: {str(e)}")
                return False
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من النتيجة
        cursor.execute("SELECT COUNT(*) FROM course")
        courses_count = cursor.fetchone()[0]
        
        print(f"\n🎉 تم إنشاء {courses_count} دورة بنجاح!")
        
        # عرض الدورات المنشأة
        cursor.execute("SELECT course_number, title, category, level FROM course")
        courses = cursor.fetchall()
        
        print("\n📚 الدورات المنشأة:")
        for course in courses:
            print(f"   - {course[0]}: {course[1]} ({course[2]} - {course[3]})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return False

def add_participants():
    """إضافة المشاركين إلى الدورة الأولى"""
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        print("\n🎯 إضافة المشاركين إلى الدورة الأولى...")
        
        # جلب الدورة الأولى
        cursor.execute("SELECT id FROM course WHERE course_number = '3455667'")
        course_result = cursor.fetchone()
        
        if not course_result:
            print("❌ لم يتم العثور على الدورة الأولى")
            return False
        
        course_id = course_result[0]
        print(f"✅ العثور على الدورة ID: {course_id}")
        
        # جلب جميع الأشخاص
        cursor.execute("SELECT id, full_name FROM person_data")
        persons = cursor.fetchall()
        
        if not persons:
            print("❌ لا يوجد أشخاص في قاعدة البيانات")
            return False
        
        print(f"👥 العثور على {len(persons)} شخص")
        
        # حذف المشاركين الموجودين
        cursor.execute("DELETE FROM course_participant WHERE course_id = ?", (course_id,))
        
        # إضافة المشاركين
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for person_id, person_name in persons:
            try:
                cursor.execute("""
                    INSERT INTO course_participant (
                        course_id, personal_data_id, registration_date, status
                    ) VALUES (?, ?, ?, ?)
                """, (course_id, person_id, current_time, 'enrolled'))
                
                print(f"✅ تم إضافة: {person_name}")
            except Exception as e:
                print(f"❌ خطأ في إضافة {person_name}: {str(e)}")
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من النتيجة
        cursor.execute("SELECT COUNT(*) FROM course_participant WHERE course_id = ?", (course_id,))
        participants_count = cursor.fetchone()[0]
        
        print(f"\n✅ تم إضافة {participants_count} مشارك إلى الدورة الأولى")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المشاركين: {str(e)}")
        return False

def verify_final_state():
    """التحقق من الحالة النهائية"""
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        print("\n📊 التحقق من الحالة النهائية:")
        print("=" * 50)
        
        # فحص الدورات
        cursor.execute("SELECT COUNT(*) FROM course")
        courses_count = cursor.fetchone()[0]
        print(f"📚 الدورات: {courses_count}")
        
        # فحص الأشخاص
        cursor.execute("SELECT COUNT(*) FROM person_data")
        persons_count = cursor.fetchone()[0]
        print(f"👤 الأشخاص: {persons_count}")
        
        # فحص المشاركين
        cursor.execute("SELECT COUNT(*) FROM course_participant")
        participants_count = cursor.fetchone()[0]
        print(f"🎯 المشاركين: {participants_count}")
        
        # عرض تفاصيل الدورات
        cursor.execute("""
            SELECT c.course_number, c.title, COUNT(cp.id) as participants
            FROM course c
            LEFT JOIN course_participant cp ON c.id = cp.course_id
            GROUP BY c.id, c.course_number, c.title
        """)
        
        courses_details = cursor.fetchall()
        print(f"\n📋 تفاصيل الدورات:")
        for course in courses_details:
            print(f"   - {course[0]}: {course[1]} ({course[2]} مشارك)")
        
        conn.close()
        
        return courses_count, persons_count, participants_count
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")
        return 0, 0, 0

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء الدورات الثلاث باستخدام SQL مباشرة")
    print("=" * 60)
    
    # إنشاء الدورات
    if create_courses_direct():
        print("✅ تم إنشاء الدورات بنجاح")
    else:
        print("❌ فشل في إنشاء الدورات")
        return
    
    # إضافة المشاركين
    if add_participants():
        print("✅ تم إضافة المشاركين بنجاح")
    else:
        print("❌ فشل في إضافة المشاركين")
    
    # التحقق من النتيجة النهائية
    courses_count, persons_count, participants_count = verify_final_state()
    
    print("\n" + "=" * 60)
    if courses_count == 3 and participants_count == persons_count:
        print("🎉 تم إنشاء الدورات والمشاركين بنجاح!")
        print("✅ النظام جاهز للاستخدام")
        print("\n🔗 يمكنك الآن زيارة:")
        print("   📚 الدورات: http://127.0.0.1:5000/courses")
        print("   👥 بيانات الأشخاص: http://127.0.0.1:5000/person_data/person_data_table")
    else:
        print("⚠️ هناك مشكلة في البيانات")

if __name__ == "__main__":
    main()
