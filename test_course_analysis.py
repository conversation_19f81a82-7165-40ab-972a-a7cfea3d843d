#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحليل الدورة رقم 3455667 (المبيعات2)
"""

from app import app, db, Course, CourseParticipant, PersonData
from person_data_routes import analyze_names_smart_advanced

def test_course_analysis():
    with app.app_context():
        print("🧪 اختبار تحليل الدورة المبيعات2...")
        print("=" * 60)
        
        # البحث عن الدورة
        course = Course.query.filter_by(course_number='3455667').first()
        if not course:
            print("❌ لم يتم العثور على الدورة")
            return
        
        print(f"✅ تم العثور على الدورة: {course.title} (ID: {course.id})")
        
        # جلب المشاركين الحاليين
        participants = CourseParticipant.query.filter_by(course_id=course.id).all()
        print(f"📊 عدد المشاركين الحاليين: {len(participants)}")
        
        # عرض أول 5 مشاركين
        print("\n👥 أول 5 مشاركين:")
        for i, p in enumerate(participants[:5], 1):
            name = p.personal_data.full_name if p.personal_data else f'ID: {p.personal_data_id}'
            print(f"   {i}. {name} (ID: {p.personal_data_id})")
        
        # إنشاء بيانات اختبار
        test_excel_data = []
        
        # إضافة أسماء موجودة في الدورة (يجب أن تظهر كمكررة)
        for i, p in enumerate(participants[:3], 1):
            if p.personal_data:
                test_excel_data.append({
                    'name': p.personal_data.full_name,
                    'national_id': p.personal_data.national_number or '',
                    'phone': p.personal_data.phone or '',
                    'military_id': p.personal_data.military_number or '',
                    'row_index': i
                })
        
        # إضافة أسماء موجودة في قاعدة البيانات لكن غير موجودة في الدورة
        all_people = PersonData.query.limit(10).all()
        participant_ids = [p.personal_data_id for p in participants]
        
        for person in all_people:
            if person.id not in participant_ids and len(test_excel_data) < 8:
                test_excel_data.append({
                    'name': person.full_name,
                    'national_id': person.national_number or '',
                    'phone': person.phone or '',
                    'military_id': person.military_number or '',
                    'row_index': len(test_excel_data) + 1
                })
        
        # إضافة أسماء جديدة (غير موجودة في قاعدة البيانات)
        new_names = [
            "أحمد محمد علي الجديد",
            "فاطمة سعد عبدالله الجديدة"
        ]
        
        for name in new_names:
            test_excel_data.append({
                'name': name,
                'national_id': f'99999999{len(test_excel_data)}',
                'phone': f'05555555{len(test_excel_data)}',
                'military_id': f'M{len(test_excel_data)}999',
                'row_index': len(test_excel_data) + 1
            })
        
        print(f"\n📋 بيانات الاختبار: {len(test_excel_data)} سجل")
        for i, record in enumerate(test_excel_data, 1):
            print(f"   {i}. {record['name']}")
        
        # جلب بيانات قاعدة البيانات
        db_data = []
        all_people = PersonData.query.all()
        for person in all_people:
            db_data.append({
                'id': person.id,
                'name': person.full_name,
                'national_id': person.national_number or '',
                'phone': person.phone or '',
                'military_id': person.military_number or ''
            })
        
        print(f"📊 بيانات قاعدة البيانات: {len(db_data)} سجل")
        
        # تشغيل التحليل الذكي
        print("\n🧠 تشغيل التحليل الذكي...")
        column_info = {
            'name_column': 'name',
            'national_id_column': 'national_id',
            'phone_column': 'phone',
            'military_id_column': 'military_id'
        }
        
        results = analyze_names_smart_advanced(
            test_excel_data, 
            db_data, 
            column_info, 
            course, 
            'course'
        )
        
        # عرض النتائج
        print("\n📊 نتائج التحليل:")
        print("=" * 40)
        
        stats = results['smart_statistics']
        print(f"إجمالي المعالج: {stats['total_processed']}")
        print(f"موجود في قاعدة البيانات: {stats['in_db_count']}")
        print(f"غير موجود في قاعدة البيانات: {stats['not_in_db_count']}")
        print(f"موجود في الدورة: {stats['in_course_count']}")
        print(f"غير موجود في الدورة: {stats['not_in_course_count']}")
        print(f"مكرر في الدورة: {stats['duplicate_course_count']}")
        print(f"مكرر في الدورة (جديد): {stats['duplicate_in_course_count']}")
        print(f"في قاعدة البيانات لكن ليس في الدورة: {stats['in_db_not_in_course_count']}")
        
        print("\n📋 تفاصيل الفئات:")
        print(f"duplicate_in_course: {len(results['duplicate_in_course'])} سجل")
        print(f"in_db_not_in_course: {len(results['in_db_not_in_course'])} سجل")
        print(f"not_in_db_new_person: {len(results['not_in_db_new_person'])} سجل")
        
        # عرض تفاصيل المكررين في الدورة
        if results['duplicate_in_course']:
            print("\n🔄 المكررين في الدورة:")
            for i, record in enumerate(results['duplicate_in_course'][:3], 1):
                print(f"   {i}. {record['corrected_name']} - {record['action_needed']}")
        
        # عرض تفاصيل الموجودين في قاعدة البيانات لكن ليس في الدورة
        if results['in_db_not_in_course']:
            print("\n➕ موجود في قاعدة البيانات - سيتم إضافته للدورة:")
            for i, record in enumerate(results['in_db_not_in_course'][:3], 1):
                print(f"   {i}. {record['corrected_name']} - {record['action_needed']}")
        
        # عرض تفاصيل الجدد
        if results['not_in_db_new_person']:
            print("\n🆕 جدد (غير موجودين في قاعدة البيانات):")
            for i, record in enumerate(results['not_in_db_new_person'][:3], 1):
                print(f"   {i}. {record['corrected_name']} - {record['action_needed']}")
        
        print("\n✅ انتهى الاختبار")

if __name__ == "__main__":
    test_course_analysis()
