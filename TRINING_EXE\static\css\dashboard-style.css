/* خلفية بيضاء ثابتة */
body {
    background: white;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;
    /* تحسينات الأداء للتمرير السلس */
    will-change: auto;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000;
    perspective: 1000;
}

/* أشعة الشمس الخافتة والمبدعة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 25%),
        radial-gradient(circle at 80% 30%, rgba(255, 255, 255, 0.06) 0%, transparent 30%),
        radial-gradient(circle at 40% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 35%),
        radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.07) 0%, transparent 25%),
        radial-gradient(circle at 10% 60%, rgba(255, 255, 255, 0.04) 0%, transparent 40%),
        radial-gradient(circle at 90% 10%, rgba(255, 255, 255, 0.06) 0%, transparent 30%),
        /* أشعة خطية خافتة */
        linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%),
        linear-gradient(135deg, transparent 40%, rgba(255, 255, 255, 0.015) 50%, transparent 60%),
        linear-gradient(-135deg, transparent 40%, rgba(255, 255, 255, 0.015) 50%, transparent 60%);
    pointer-events: none;
    z-index: -1;
}



/* خلفية بيضاء ثابتة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
    pointer-events: none;
    z-index: -1;
}



/* تحسين الشريط الجانبي بتصميم أزرق غامق متدرج ضخم */
.sidebar {
    background:
        linear-gradient(135deg,
            rgba(15, 23, 42, 0.98) 0%,     /* أزرق غامق جداً */
            rgba(30, 41, 59, 0.96) 25%,    /* أزرق غامق */
            rgba(51, 65, 85, 0.94) 50%,    /* أزرق متوسط */
            rgba(71, 85, 105, 0.92) 75%,   /* أزرق فاتح نسبياً */
            rgba(100, 116, 139, 0.90) 100% /* أزرق رمادي */
        );
    color: white;
    min-height: calc(100vh - 56px);
    padding-top: 35px;
    border-radius: 25px;
    box-shadow:
        0 30px 80px rgba(15, 23, 42, 0.7),
        0 20px 50px rgba(30, 41, 59, 0.6),
        0 10px 30px rgba(51, 65, 85, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(25px);
    border: 2px solid rgba(255, 255, 255, 0.08);
    position: relative;
    overflow: hidden;
    /* تحسينات الأداء */
    will-change: auto;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

/* تصميم صورة المستخدم الافتراضية بألوان متناسقة مع الأزرق الغامق */
.user-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 1) 0%,      /* أزرق رمادي */
        rgba(51, 65, 85, 1) 50%,      /* أزرق متوسط */
        rgba(30, 41, 59, 1) 100%      /* أزرق غامق */
    );
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    box-shadow:
        0 20px 40px rgba(15, 23, 42, 0.6),
        0 10px 25px rgba(30, 41, 59, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 3px solid rgba(255, 255, 255, 0.15);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.user-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 50%;
}

.user-avatar i {
    font-size: 2.5rem;
    color: white;
    z-index: 2;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.user-avatar:hover {
    box-shadow:
        0 25px 50px rgba(15, 23, 42, 0.8),
        0 15px 35px rgba(30, 41, 59, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.sidebar-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 15px 20px;
    display: block;
    text-decoration: none;
    transition: background-color 0.3s ease, transform 0.2s ease;
    border-radius: 15px;
    margin: 8px 15px;
    position: relative;
    overflow: hidden;
    font-weight: 500;
    /* تحسينات الأداء */
    will-change: transform;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.sidebar-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.sidebar-link:hover::before {
    left: 100%;
}

.sidebar-link:hover, .sidebar-link.active {
    color: white;
    background: linear-gradient(135deg,
        rgba(100, 116, 139, 0.5) 0%,    /* أزرق رمادي فاتح */
        rgba(71, 85, 105, 0.4) 50%,     /* أزرق متوسط */
        rgba(51, 65, 85, 0.3) 100%      /* أزرق غامق */
    );
    transform: translateX(5px) translateZ(0);
    box-shadow:
        0 10px 25px rgba(15, 23, 42, 0.4),
        0 5px 15px rgba(30, 41, 59, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.15);
}

.sidebar-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* تحسين الحاوي الرئيسي بخلفية بيضاء */
.main-container {
    background: white;
    border-radius: 30px;
    padding: 40px;
    margin-top: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    position: relative;
    overflow: hidden;
    /* تحسينات الأداء */
    will-change: auto;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.main-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: #f0f0f0;
    /* تحسينات الأداء */
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.main-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    pointer-events: none;
    opacity: 0.5;
}



/* تحسين العناوين */
.page-title {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 900;
    font-size: 2.5rem;
    filter: drop-shadow(0 4px 8px rgba(30, 64, 175, 0.4));
    margin-bottom: 30px;
}

.page-title i {
    margin-left: 15px;
    filter: drop-shadow(0 0 10px rgba(30, 64, 175, 0.5));
}

/* تحسين النماذج */
.form-label {
    color: #1e40af;
    font-weight: 600;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border: 1px solid #ddd;
    border-radius: 15px;
    padding: 12px 15px;
    background: white;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    background: white;
}

/* تحسين الأزرار */
.btn {
    border-radius: 15px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}



/* تحسين قسم الفلترة */
.filter-section {
    background: white;
    padding: 30px;
    border-radius: 25px;
    margin-bottom: 30px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* تحسين التنبيهات */
.alert {
    border-radius: 20px;
    border: none;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* تحسين الترقيم */
.pagination .page-link {
    border-radius: 15px;
    margin: 0 5px;
    border: 2px solid rgba(59, 130, 246, 0.2);
    color: #1e40af;
    background: rgba(255, 255, 255, 1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
}

.pagination .page-item.active .page-link {
    background: #1e40af;
    border-color: #1e40af;
    color: white;
}

/* تحسين شريط التنقل العلوي */
.navbar {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%) !important;
    backdrop-filter: blur(20px);
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 32px rgba(30, 64, 175, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    padding: 15px 0;
    position: relative;
    overflow: hidden;
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.navbar-brand {
    font-weight: 900 !important;
    font-size: 1.5rem !important;
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: rgba(255, 255, 255, 0.9) !important;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.navbar-brand i {
    color: #fbbf24;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 600 !important;
    padding: 12px 20px !important;
    border-radius: 15px !important;
    margin: 0 5px !important;
    transition: all 0.3s ease !important;
    position: relative;
    z-index: 2;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.nav-link.active {
    color: white !important;
    background: rgba(255, 255, 255, 0.25) !important;
    box-shadow:
        0 5px 15px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.dropdown-menu {
    background: rgba(255, 255, 255, 1) !important;
    backdrop-filter: blur(20px) !important;
    border: 2px solid rgba(59, 130, 246, 0.2) !important;
    border-radius: 20px !important;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    padding: 15px 0 !important;
    margin-top: 10px !important;
}

.dropdown-item {
    color: #1e40af !important;
    font-weight: 600 !important;
    padding: 12px 25px !important;
    border-radius: 15px !important;
    margin: 5px 15px !important;
    transition: all 0.3s ease !important;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    color: white !important;
    transform: translateX(5px);
}

.dropdown-divider {
    border-color: rgba(59, 130, 246, 0.2) !important;
    margin: 10px 15px !important;
}

.navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 15px !important;
    padding: 8px 12px !important;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}


