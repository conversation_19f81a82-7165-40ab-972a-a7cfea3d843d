#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بيانات الرسوم البيانية
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User
import json

def test_chart_data_api():
    """اختبار بيانات الرسوم البيانية من API"""
    
    with app.test_client() as client:
        with app.app_context():
            # البحث عن مستخدم مدير
            admin_user = User.query.filter_by(role='admin').first()
            
            if not admin_user:
                print("❌ لا يوجد مستخدم مدير في قاعدة البيانات!")
                return False
            
            print(f"👤 استخدام المستخدم المدير: {admin_user.username}")
            
            # تسجيل الدخول
            with client.session_transaction() as sess:
                sess['_user_id'] = str(admin_user.id)
                sess['_fresh'] = True
            
            # إرسال طلب التقرير
            data = {
                'start_date': '2020-01-01',
                'end_date': '2025-12-31'
            }
            
            print(f"📤 إرسال طلب التقرير: {data}")
            
            response = client.post('/generate_report', data=data)
            
            print(f"📥 رمز الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.get_json()
                    
                    if result and result.get('success'):
                        print("✅ نجح إنشاء التقرير!")
                        
                        # فحص بيانات الرسوم البيانية بالتفصيل
                        chart_data = result.get('chartData', {})
                        print(f"\n📈 بيانات الرسوم البيانية:")
                        print(f"   المستوى الأول: {chart_data.get('level1', 'غير موجود')}")
                        print(f"   المستوى الثاني: {chart_data.get('level2', 'غير موجود')}")
                        print(f"   المستوى الثالث: {chart_data.get('level3', 'غير موجود')}")
                        
                        center_names = chart_data.get('centerNames', [])
                        center_participants = chart_data.get('centerParticipants', [])
                        print(f"   أسماء المراكز: {len(center_names)} عنصر")
                        print(f"   مشاركي المراكز: {len(center_participants)} عنصر")
                        
                        if center_names and center_participants:
                            print("   أول 3 مراكز:")
                            for i in range(min(3, len(center_names))):
                                print(f"     {center_names[i]}: {center_participants[i]} مشارك")
                        
                        # فحص بيانات الجداول
                        table_data = result.get('tableData', {})
                        level_data = table_data.get('levelData', [])
                        participants_data = table_data.get('participantsData', [])
                        
                        print(f"\n📋 بيانات الجداول:")
                        print(f"   جدول المستويات: {len(level_data)} صف")
                        print(f"   جدول المشاركين: {len(participants_data)} صف")
                        
                        if level_data:
                            print("   أول صف من جدول المستويات:")
                            first_row = level_data[0]
                            print(f"     المركز: {first_row.get('center', 'غير محدد')}")
                            print(f"     الإجمالي: {first_row.get('total', 'غير محدد')}")
                        
                        # التحقق من وجود بيانات صالحة
                        has_level_data = any([
                            chart_data.get('level1', 0) > 0,
                            chart_data.get('level2', 0) > 0,
                            chart_data.get('level3', 0) > 0
                        ])
                        
                        has_center_data = len(center_names) > 0 and len(center_participants) > 0
                        
                        print(f"\n🔍 تحليل البيانات:")
                        print(f"   بيانات المستويات صالحة: {'✅' if has_level_data else '❌'}")
                        print(f"   بيانات المراكز صالحة: {'✅' if has_center_data else '❌'}")
                        
                        return has_level_data and has_center_data
                        
                    else:
                        print(f"❌ فشل إنشاء التقرير: {result.get('message', 'خطأ غير معروف')}")
                        return False
                        
                except Exception as e:
                    print(f"❌ خطأ في تحليل الاستجابة: {str(e)}")
                    print(f"📄 محتوى الاستجابة: {response.get_data(as_text=True)[:500]}...")
                    return False
            else:
                print(f"❌ فشل الطلب: {response.status_code}")
                print(f"📄 محتوى الخطأ: {response.get_data(as_text=True)}")
                return False

if __name__ == '__main__':
    print("🚀 بدء اختبار بيانات الرسوم البيانية...")
    
    success = test_chart_data_api()
    
    if success:
        print("\n✅ اختبار البيانات مكتمل بنجاح!")
    else:
        print("\n❌ فشل اختبار البيانات!")
