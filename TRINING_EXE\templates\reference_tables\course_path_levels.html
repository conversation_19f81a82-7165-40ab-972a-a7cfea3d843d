{% extends "layout.html" %}
{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h3 class="mb-0">مستويات المسارات التدريبية</h3>
            <div>
                <a href="{{ url_for('course_paths_tree') }}" class="btn btn-info me-2">
                    <i class="fas fa-sitemap"></i> عرض الشجرة
                </a>
                <a href="{{ url_for('add_course_path_level') }}" class="btn btn-light">
                    <i class="fas fa-plus-circle"></i> إضافة مستوى جديد
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if levels %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">الاسم</th>
                            <th scope="col">المسار</th>
                            <th scope="col">الترتيب</th>
                            <th scope="col">الرمز</th>
                            <th scope="col">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for level in levels %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ level.name }}</td>
                            <td>{{ level.path.name }}</td>
                            <td>{{ level.order }}</td>
                            <td>{{ level.code }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('edit_course_path_level', level_id=level.id) }}" class="btn btn-sm btn-primary">تعديل</a>
                                    <a href="{{ url_for('delete_course_path_level', level_id=level.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المستوى؟')">حذف</a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                لا توجد مستويات مضافة حتى الآن.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
