#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار صفحة إدارة المستخدمين
"""

import sqlite3
from datetime import datetime

def check_database():
    """فحص قاعدة البيانات"""
    print("فحص قاعدة البيانات...")
    
    conn = sqlite3.connect('training_system.db')
    cursor = conn.cursor()
    
    try:
        # فحص جدول المستخدمين
        cursor.execute("SELECT COUNT(*) FROM user")
        user_count = cursor.fetchone()[0]
        print(f"عدد المستخدمين: {user_count}")
        
        # فحص المستخدمين النشطين
        cursor.execute("SELECT COUNT(*) FROM user WHERE is_active = 1")
        active_count = cursor.fetchone()[0]
        print(f"المستخدمين النشطين: {active_count}")
        
        # فحص الأعمدة في جدول المستخدمين
        cursor.execute("PRAGMA table_info(user)")
        columns = [col[1] for col in cursor.fetchall()]
        print(f"أعمدة جدول المستخدمين: {', '.join(columns)}")
        
        # فحص بعض المستخدمين
        cursor.execute("SELECT id, username, email, role, is_active FROM user LIMIT 5")
        users = cursor.fetchall()
        print("\nعينة من المستخدمين:")
        for user in users:
            print(f"  ID: {user[0]}, Username: {user[1]}, Email: {user[2]}, Role: {user[3]}, Active: {user[4]}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في فحص قاعدة البيانات: {e}")
        return False
    finally:
        conn.close()

def test_template():
    """اختبار template"""
    print("\nفحص template...")
    
    try:
        with open('templates/admin/users_management.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص العناصر المهمة
        checks = [
            ('{% extends "layout.html" %}', 'extends layout'),
            ('{% block content %}', 'content block start'),
            ('{% endblock %}', 'content block end'),
            ('{% block scripts %}', 'scripts block'),
            ('users.items', 'users pagination'),
            ('user.get_full_name()', 'user full name method')
        ]
        
        for check, description in checks:
            if check in content:
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ {description}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في فحص template: {e}")
        return False

def test_route_directly():
    """اختبار route مباشرة"""
    print("\nاختبار route مباشرة...")
    
    try:
        # محاولة استيراد app
        import sys
        sys.path.append('.')
        
        from app import app, User
        
        with app.app_context():
            # فحص المستخدمين
            users = User.query.all()
            print(f"عدد المستخدمين في النموذج: {len(users)}")
            
            # فحص pagination
            users_paginated = User.query.paginate(page=1, per_page=20, error_out=False)
            print(f"عدد المستخدمين في الصفحة الأولى: {len(users_paginated.items)}")
            
            # فحص إحصائيات
            total_users = User.query.count()
            active_users = User.query.filter(User.is_active == True).count()
            print(f"إجمالي المستخدمين: {total_users}")
            print(f"المستخدمين النشطين: {active_users}")
            
        return True
        
    except Exception as e:
        print(f"خطأ في اختبار route: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("اختبار صفحة إدارة المستخدمين")
    print("=" * 50)
    
    # 1. فحص قاعدة البيانات
    db_ok = check_database()
    
    # 2. فحص template
    template_ok = test_template()
    
    # 3. اختبار route
    route_ok = test_route_directly()
    
    print("\n" + "=" * 50)
    print("ملخص النتائج:")
    print(f"قاعدة البيانات: {'✅' if db_ok else '❌'}")
    print(f"Template: {'✅' if template_ok else '❌'}")
    print(f"Route: {'✅' if route_ok else '❌'}")
    
    if db_ok and template_ok and route_ok:
        print("\n✅ جميع الاختبارات نجحت!")
    else:
        print("\n❌ هناك مشاكل تحتاج إصلاح")

if __name__ == '__main__':
    main()
