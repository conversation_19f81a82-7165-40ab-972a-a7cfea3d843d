{% extends "layout.html" %}

{% block styles %}
<style>
    .data-card {
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        border: none;
        margin-bottom: 20px;
    }

    .data-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    .data-card-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 15px 20px;
        font-weight: bold;
        border-radius: 15px 15px 0 0;
    }

    .btn-add {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 10px 20px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
        color: white;
    }

    .btn-view {
        background-color: #28a745;
        color: white;
    }

    .btn-edit {
        background-color: #ffc107;
        color: white;
    }

    .btn-delete {
        background-color: #dc3545;
        color: white;
    }

    .btn-sm {
        padding: 5px 10px;
        font-size: 0.875rem;
        border-radius: 5px;
    }

    .sidebar {
        background-color: #343a40;
        color: white;
        min-height: calc(100vh - 56px);
        padding-top: 20px;
    }

    .sidebar-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 10px 15px;
        display: block;
        text-decoration: none;
        transition: all 0.3s;
        border-radius: 5px;
        margin: 5px 10px;
    }

    .sidebar-link:hover, .sidebar-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .sidebar-link i {
        margin-left: 10px;
        width: 20px;
        text-align: center;
    }

    .sidebar-dropdown-menu {
        display: none;
        padding-right: 20px;
    }

    .sidebar-dropdown-menu.show {
        display: block;
    }

    .dropdown-toggle::after {
        display: inline-block;
        margin-right: 5px;
        vertical-align: middle;
        content: "";
        border-top: 0.3em solid;
        border-left: 0.3em solid transparent;
        border-right: 0.3em solid transparent;
    }

    .search-box {
        margin-bottom: 20px;
    }

    .search-box .form-control {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
    }

    .search-box .btn {
        border-radius: 10px;
        padding: 12px;
    }

    /* تنسيقات العرض الشجري */
    .tree-view {
        padding: 20px;
    }

    .tree-item {
        margin-bottom: 10px;
        border-radius: 8px;
        overflow: hidden;
    }

    .tree-parent {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 5px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s;
        border-right: 4px solid #4a6bff;
    }

    .tree-parent:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        transform: translateX(-5px);
    }

    .tree-parent-title {
        display: flex;
        align-items: center;
    }

    .tree-parent-title i {
        margin-left: 10px;
        color: #4a6bff;
        transition: transform 0.3s;
    }

    .tree-parent.active .tree-parent-title i {
        transform: rotate(90deg);
    }

    .tree-children {
        display: none;
        padding: 0 20px;
        margin-right: 20px;
        border-right: 2px dashed #4a6bff;
    }

    .tree-children.show {
        display: block;
        animation: fadeIn 0.5s;
    }

    .tree-child {
        background-color: #f8f9fa;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s;
        border-right: 3px solid #6c757d;
    }

    .tree-child:hover {
        background-color: #e9ecef;
        transform: translateX(-5px);
    }

    .tree-grandchild-container {
        display: none;
        padding: 0 20px;
        margin-right: 20px;
        border-right: 2px dotted #6c757d;
    }

    .tree-grandchild-container.show {
        display: block;
        animation: fadeIn 0.5s;
    }

    .tree-grandchild {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s;
        border-right: 2px solid #adb5bd;
    }

    .tree-grandchild:hover {
        background-color: #e9ecef;
        transform: translateX(-5px);
    }

    .tree-actions {
        display: flex;
        gap: 5px;
    }

    .tree-badge {
        background-color: #4a6bff;
        color: white;
        padding: 3px 8px;
        border-radius: 20px;
        font-size: 0.8rem;
        margin-right: 10px;
    }

    .tree-code {
        background-color: #e9ecef;
        color: #495057;
        padding: 3px 8px;
        border-radius: 20px;
        font-size: 0.8rem;
        margin-right: 10px;
    }

    .tree-agency {
        background-color: #28a745;
        color: white;
        padding: 3px 8px;
        border-radius: 20px;
        font-size: 0.8rem;
        margin-right: 10px;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_list') }}" class="sidebar-link">
                <i class="fas fa-id-card"></i> البيانات الشخصية
            </a>
            <a href="{{ url_for('reference_tables') }}" class="sidebar-link active">
                <i class="fas fa-table"></i> الجداول الترميزية
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>هيكل الأقسام</h2>
            <div>
                <a href="{{ url_for('reference_tables') }}" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-right me-1"></i> العودة إلى الجداول الترميزية
                </a>
                <a href="{{ url_for('departments') }}" class="btn btn-info me-2">
                    <i class="fas fa-list me-1"></i> عرض جدولي
                </a>
                <a href="{{ url_for('add_department') }}" class="btn btn-add">
                    <i class="fas fa-plus-circle me-1"></i> إضافة قسم جديد
                </a>
            </div>
        </div>

        <div class="search-box">
            <div class="input-group">
                <input type="text" id="searchInput" class="form-control" placeholder="بحث عن قسم...">
                <button class="btn btn-primary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>

        <div class="data-card">
            <div class="data-card-header">
                <i class="fas fa-project-diagram me-2"></i> هيكل الأقسام
            </div>
            <div class="card-body tree-view">
                {% if root_departments %}
                    {% for department in root_departments %}
                    <div class="tree-item">
                        <div class="tree-parent" data-id="{{ department.id }}">
                            <div class="tree-parent-title">
                                <i class="fas fa-chevron-left"></i>
                                <span>{{ department.name }}</span>
                                {% if department.code %}
                                <span class="tree-code">{{ department.code }}</span>
                                {% endif %}
                                {% if department.agency %}
                                <span class="tree-agency">{{ department.agency.name }}</span>
                                {% endif %}
                                {% if department.children %}
                                <span class="tree-badge">{{ department.children|length }} قسم فرعي</span>
                                {% endif %}
                            </div>
                            <div class="tree-actions">
                                <a href="{{ url_for('add_department', parent_id=department.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus"></i> إضافة قسم فرعي
                                </a>
                                <a href="{{ url_for('edit_department', department_id=department.id) }}" class="btn btn-sm btn-edit">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{{ url_for('delete_department', department_id=department.id) }}" class="btn btn-sm btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذا القسم وجميع الأقسام الفرعية التابعة له؟');">
                                    <i class="fas fa-trash-alt"></i> حذف
                                </a>
                            </div>
                        </div>
                        {% if department.children %}
                        <div class="tree-children" id="department-{{ department.id }}">
                            {% for child in department.children %}
                            <div class="tree-child" data-id="{{ child.id }}">
                                <div class="tree-parent-title">
                                    <i class="fas fa-chevron-left"></i>
                                    <span>{{ child.name }}</span>
                                    {% if child.code %}
                                    <span class="tree-code">{{ child.code }}</span>
                                    {% endif %}
                                    {% if child.agency %}
                                    <span class="tree-agency">{{ child.agency.name }}</span>
                                    {% endif %}
                                    {% if child.children %}
                                    <span class="tree-badge">{{ child.children|length }} قسم فرعي</span>
                                    {% endif %}
                                </div>
                                <div class="tree-actions">
                                    <a href="{{ url_for('add_department', parent_id=child.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus"></i> إضافة قسم فرعي
                                    </a>
                                    <a href="{{ url_for('edit_department', department_id=child.id) }}" class="btn btn-sm btn-edit">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <a href="{{ url_for('delete_department', department_id=child.id) }}" class="btn btn-sm btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذا القسم وجميع الأقسام الفرعية التابعة له؟');">
                                        <i class="fas fa-trash-alt"></i> حذف
                                    </a>
                                </div>
                            </div>
                            {% if child.children %}
                            <div class="tree-grandchild-container" id="department-{{ child.id }}">
                                {% for grandchild in child.children %}
                                <div class="tree-grandchild">
                                    <div>
                                        <span>{{ grandchild.name }}</span>
                                        {% if grandchild.code %}
                                        <span class="tree-code">{{ grandchild.code }}</span>
                                        {% endif %}
                                        {% if grandchild.agency %}
                                        <span class="tree-agency">{{ grandchild.agency.name }}</span>
                                        {% endif %}
                                    </div>
                                    <div class="tree-actions">
                                        <a href="{{ url_for('add_department', parent_id=grandchild.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-plus"></i> إضافة
                                        </a>
                                        <a href="{{ url_for('edit_department', department_id=grandchild.id) }}" class="btn btn-sm btn-edit">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <a href="{{ url_for('delete_department', department_id=grandchild.id) }}" class="btn btn-sm btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذا القسم؟');">
                                            <i class="fas fa-trash-alt"></i> حذف
                                        </a>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> لا توجد أقسام مسجلة حتى الآن.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تفعيل القائمة المنسدلة للجداول الترميزية
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
        
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const dropdownMenu = this.nextElementSibling;
                dropdownMenu.classList.toggle('show');
            });
        });

        // تفعيل العرض الشجري للأقسام الرئيسية
        const departmentItems = document.querySelectorAll('.tree-parent');
        
        departmentItems.forEach(item => {
            item.addEventListener('click', function(e) {
                if (e.target.closest('.btn')) return; // تجاهل النقر على الأزرار
                
                const departmentId = this.getAttribute('data-id');
                const childrenContainer = document.getElementById('department-' + departmentId);
                
                if (childrenContainer) {
                    this.classList.toggle('active');
                    childrenContainer.classList.toggle('show');
                }
            });
        });

        // تفعيل العرض الشجري للأقسام الفرعية
        const childDepartmentItems = document.querySelectorAll('.tree-child');
        
        childDepartmentItems.forEach(item => {
            item.addEventListener('click', function(e) {
                if (e.target.closest('.btn')) return; // تجاهل النقر على الأزرار
                
                const departmentId = this.getAttribute('data-id');
                const childrenContainer = document.getElementById('department-' + departmentId);
                
                if (childrenContainer) {
                    this.classList.toggle('active');
                    childrenContainer.classList.toggle('show');
                }
            });
        });

        // تفعيل البحث
        const searchInput = document.getElementById('searchInput');
        
        searchInput.addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const treeItems = document.querySelectorAll('.tree-item');
            
            treeItems.forEach(item => {
                const departmentText = item.querySelector('.tree-parent-title span').textContent.toLowerCase();
                const childDepartmentElements = item.querySelectorAll('.tree-child .tree-parent-title span');
                const grandchildDepartmentElements = item.querySelectorAll('.tree-grandchild span');
                
                let found = departmentText.includes(searchValue);
                
                // البحث في الأقسام الفرعية
                childDepartmentElements.forEach(element => {
                    if (element.textContent.toLowerCase().includes(searchValue)) {
                        found = true;
                        // عرض القسم الفرعي المطابق
                        const childDepartment = element.closest('.tree-child');
                        const childDepartmentId = childDepartment.getAttribute('data-id');
                        const childContainer = document.getElementById('department-' + childDepartmentId);
                        if (childContainer) {
                            childContainer.classList.add('show');
                            childDepartment.classList.add('active');
                        }
                    }
                });
                
                // البحث في الأقسام الفرعية الثانوية
                grandchildDepartmentElements.forEach(element => {
                    if (element.textContent.toLowerCase().includes(searchValue)) {
                        found = true;
                        // عرض القسم الفرعي الثانوي المطابق
                        const grandchildDepartment = element.closest('.tree-grandchild');
                        const childDepartment = grandchildDepartment.closest('.tree-grandchild-container').previousElementSibling;
                        const childDepartmentId = childDepartment.getAttribute('data-id');
                        const childContainer = document.getElementById('department-' + childDepartmentId);
                        if (childContainer) {
                            childContainer.classList.add('show');
                            childDepartment.classList.add('active');
                        }
                    }
                });
                
                if (found) {
                    item.style.display = '';
                    // عرض القسم الرئيسي المطابق
                    const departmentId = item.querySelector('.tree-parent').getAttribute('data-id');
                    const container = document.getElementById('department-' + departmentId);
                    if (container) {
                        container.classList.add('show');
                        item.querySelector('.tree-parent').classList.add('active');
                    }
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
</script>
{% endblock %}
