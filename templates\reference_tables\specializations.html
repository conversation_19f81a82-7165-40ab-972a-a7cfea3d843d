{% extends "layout.html" %}

{% block styles %}
<style>
    .reference-table {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .reference-table-header {
        background-color: #28a745;
        color: white;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .reference-table-title {
        margin: 0;
        font-size: 1.5rem;
    }

    .reference-table-body {
        padding: 20px;
    }

    .table {
        margin-bottom: 0;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    .btn-add {
        background-color: #28a745;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        transition: all 0.3s;
    }

    .btn-add:hover {
        background-color: #218838;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    .btn-edit {
        background-color: #ffc107;
        color: #212529;
    }

    .btn-delete {
        background-color: #dc3545;
        color: white;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }

    .badge {
        font-size: 0.9em;
        padding: 5px 10px;
        border-radius: 10px;
    }

    .badge-medical { background-color: #dc3545; color: white; }
    .badge-engineering { background-color: #fd7e14; color: white; }
    .badge-administrative { background-color: #6f42c1; color: white; }
    .badge-educational { background-color: #20c997; color: white; }
    .badge-technical { background-color: #17a2b8; color: white; }
    .badge-security { background-color: #343a40; color: white; }
    .badge-military { background-color: #495057; color: white; }
    .badge-other { background-color: #6c757d; color: white; }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('reference_tables') }}">الجداول الترميزية</a></li>
            <li class="breadcrumb-item active" aria-current="page">التخصصات</li>
        </ol>
    </nav>

    <div class="reference-table">
        <div class="reference-table-header">
            <h3 class="reference-table-title">التخصصات</h3>
            <a href="{{ url_for('add_specialization') }}" class="btn btn-add">
                <i class="fas fa-plus-circle me-1"></i> إضافة تخصص جديد
            </a>
        </div>
        <div class="reference-table-body">
            {% if specializations %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>الوصف</th>
                            <th>المجال</th>
                            <th>الرمز</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for specialization in specializations %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ specialization.name }}</td>
                            <td>{{ specialization.description or '-' }}</td>
                            <td>
                                {% if specialization.field == 'medical' %}
                                    <span class="badge badge-medical">طبي</span>
                                {% elif specialization.field == 'engineering' %}
                                    <span class="badge badge-engineering">هندسي</span>
                                {% elif specialization.field == 'administrative' %}
                                    <span class="badge badge-administrative">إداري</span>
                                {% elif specialization.field == 'educational' %}
                                    <span class="badge badge-educational">تعليمي</span>
                                {% elif specialization.field == 'technical' %}
                                    <span class="badge badge-technical">تقني</span>
                                {% elif specialization.field == 'security' %}
                                    <span class="badge badge-security">أمني</span>
                                {% elif specialization.field == 'military' %}
                                    <span class="badge badge-military">عسكري</span>
                                {% else %}
                                    <span class="badge badge-other">أخرى</span>
                                {% endif %}
                            </td>
                            <td>{{ specialization.code or '-' }}</td>
                            <td>
                                <a href="{{ url_for('edit_specialization', specialization_id=specialization.id) }}" class="btn btn-edit btn-sm">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{{ url_for('delete_specialization', specialization_id=specialization.id) }}" class="btn btn-delete btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذا التخصص؟');">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد تخصصات مضافة حتى الآن.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
