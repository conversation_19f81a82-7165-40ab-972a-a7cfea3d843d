@echo off
echo إنشاء اختصار على سطح المكتب...
echo Creating desktop shortcut...

set DESKTOP=%USERPROFILE%\Desktop
set SHORTCUT_NAME=نظام تحليل الأسماء.lnk
set TARGET_PATH=E:\app\TRINING\start_app.bat
set ICON_PATH=E:\app\TRINING\start_app.bat

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\%SHORTCUT_NAME%'); $Shortcut.TargetPath = '%TARGET_PATH%'; $Shortcut.WorkingDirectory = 'E:\app\TRINING'; $Shortcut.Description = 'نظام تحليل الأسماء - Training System'; $Shortcut.Save()}"

echo.
echo ✅ تم إنشاء الاختصار على سطح المكتب بنجاح!
echo ✅ Desktop shortcut created successfully!
echo.
echo يمكنك الآن تشغيل التطبيق من سطح المكتب
echo You can now run the application from desktop
echo.
pause
