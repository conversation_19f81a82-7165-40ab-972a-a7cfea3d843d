/* ========================================
   نظام التصميم الموحد - Training System
   ======================================== */

/* ========================================
   1. المتغيرات العامة (CSS Variables)
   ======================================== */
:root {
    /* الألوان الأساسية */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    
    /* ألوان الخلفية */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-dark: #1e293b;
    --bg-light: #f1f5f9;
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    /* ألوان النصوص */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-white: #ffffff;
    
    /* ألوان الحدود */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-dark: #cbd5e1;
    
    /* الخطوط */
    --font-family-primary: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
    --font-family-arabic: 'Cairo', 'Segoe UI', 'Tahoma', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* الانتقالات */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* الحدود المستديرة */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --border-radius-2xl: 1rem;
}

/* ========================================
   2. إعادة تعيين الأساسيات
   ======================================== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-arabic);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    direction: rtl;
    text-align: right;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ========================================
   3. التخطيط العام
   ======================================== */
.main-container {
    min-height: 100vh;
    background: var(--bg-primary);
}

.content-wrapper {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-md);
    padding: var(--spacing-xl);
}

.page-header {
    background: var(--bg-gradient);
    color: var(--text-white);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    margin-bottom: var(--spacing-lg);
}

.page-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.page-subtitle {
    font-size: var(--font-size-base);
    opacity: 0.9;
    font-weight: 400;
}

/* ========================================
   4. الأزرار الموحدة
   ======================================== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    user-select: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--text-white);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--text-white);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--text-white);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: var(--text-white);
}

.btn-outline-primary {
    background-color: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
}

/* ========================================
   5. الكروت الموحدة
   ======================================== */
.card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.card-header {
    background: var(--bg-gradient);
    color: var(--text-white);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background: var(--bg-light);
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-text {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ========================================
   6. النماذج الموحدة
   ======================================== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
}

/* ========================================
   7. الجداول الموحدة
   ======================================== */
.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th {
    background: var(--bg-gradient);
    color: var(--text-white);
    padding: var(--spacing-md);
    text-align: right;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: var(--bg-light);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* ========================================
   8. الأيقونات الموحدة
   ======================================== */
.icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.25em;
    height: 1.25em;
}

.icon-sm {
    width: 1em;
    height: 1em;
}

.icon-lg {
    width: 1.5em;
    height: 1.5em;
}

.icon-xl {
    width: 2em;
    height: 2em;
}

/* ========================================
   9. الشارات والتسميات
   ======================================== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border-radius: var(--border-radius-xl);
    white-space: nowrap;
}

.badge-primary {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.badge-success {
    background-color: var(--success-color);
    color: var(--text-white);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--text-white);
}

.badge-danger {
    background-color: var(--danger-color);
    color: var(--text-white);
}

.badge-info {
    background-color: var(--info-color);
    color: var(--text-white);
}

/* ========================================
   10. التنبيهات الموحدة
   ======================================== */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 1px solid transparent;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-info {
    background-color: rgba(6, 182, 212, 0.1);
    border-color: var(--info-color);
    color: var(--info-color);
}

/* ========================================
   11. الفئات المساعدة
   ======================================== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }
.text-muted { color: var(--text-muted); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* ========================================
   12. الاستجابة للشاشات المختلفة
   ======================================== */
@media (max-width: 768px) {
    .content-wrapper {
        margin: var(--spacing-sm);
        padding: var(--spacing-md);
    }
    
    .page-header {
        padding: var(--spacing-md);
    }
    
    .page-title {
        font-size: var(--font-size-xl);
    }
    
    .btn {
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
    }
    
    .table {
        font-size: var(--font-size-sm);
    }
    
    .table th,
    .table td {
        padding: var(--spacing-sm);
    }
}
