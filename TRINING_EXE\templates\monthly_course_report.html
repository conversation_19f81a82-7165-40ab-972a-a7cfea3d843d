{% extends "layout.html" %}

{% block styles %}
<style>
    .report-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        text-align: center;
    }
    
    .report-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .report-subtitle {
        font-size: 1.2rem;
        opacity: 0.8;
    }
    
    .stats-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stats-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 20px;
        flex: 1;
        min-width: 200px;
        text-align: center;
        transition: all 0.3s;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 10px 0;
    }
    
    .stats-title {
        font-size: 1.2rem;
        color: #6c757d;
    }
    
    .stats-icon {
        font-size: 3rem;
        margin-bottom: 15px;
    }
    
    .stats-courses .stats-icon {
        color: #4a6bff;
    }
    
    .stats-participants .stats-icon {
        color: #28a745;
    }
    
    .stats-graduates .stats-icon {
        color: #17a2b8;
    }
    
    .stats-dropouts .stats-icon {
        color: #dc3545;
    }
    
    .stats-allowance .stats-icon {
        color: #ffc107;
    }
    
    .data-card {
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        border: none;
        margin-bottom: 30px;
    }
    
    .data-card-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 15px 20px;
        font-weight: bold;
        border-radius: 15px 15px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .data-table {
        width: 100%;
    }
    
    .data-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    
    .data-table th, .data-table td {
        padding: 12px 15px;
        text-align: right;
    }
    
    .data-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    
    .data-table tr:hover {
        background-color: #e9ecef;
    }
    
    .btn-export {
        background-color: #28a745;
        color: white;
        border: none;
        border-radius: 10px;
        padding: 8px 15px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-export:hover {
        background-color: #218838;
        color: white;
    }
    
    .btn-back {
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 10px;
        padding: 8px 15px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-back:hover {
        background-color: #5a6268;
        color: white;
    }
    
    .course-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
        transition: all 0.3s;
    }
    
    .course-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
    
    .course-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 15px 20px;
    }
    
    .course-number {
        font-size: 1.2rem;
        font-weight: bold;
        color: #ffcc00;
    }
    
    .course-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .course-body {
        padding: 20px;
    }
    
    .course-info {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .course-info-item {
        background-color: #f8f9fa;
        padding: 8px 15px;
        border-radius: 10px;
        font-size: 0.9rem;
    }
    
    .course-stats {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
    }
    
    .course-stat {
        text-align: center;
        padding: 10px;
        border-radius: 10px;
        background-color: #f8f9fa;
        flex: 1;
        margin: 0 5px;
    }
    
    .course-stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .course-stat-title {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .financial-section {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #e9ecef;
    }
    
    .financial-title {
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .financial-items {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .financial-item {
        background-color: #f8f9fa;
        padding: 8px 15px;
        border-radius: 10px;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
    }
    
    .financial-item-label {
        margin-left: 5px;
        color: #6c757d;
    }
    
    .financial-item-value {
        font-weight: bold;
    }
    
    .financial-total {
        margin-top: 10px;
        font-weight: bold;
        text-align: left;
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="report-header">
        <div class="report-title">{{ title }}</div>
        <div class="report-subtitle">إحصائيات وتقارير عن الدورات التدريبية والمشاركين</div>
    </div>
    
    <div class="stats-row">
        <div class="stats-card stats-courses">
            <div class="stats-icon"><i class="fas fa-chalkboard-teacher"></i></div>
            <div class="stats-number">{{ total_courses }}</div>
            <div class="stats-title">إجمالي الدورات</div>
        </div>
        
        <div class="stats-card stats-participants">
            <div class="stats-icon"><i class="fas fa-users"></i></div>
            <div class="stats-number">{{ total_participants }}</div>
            <div class="stats-title">إجمالي المشاركين</div>
        </div>
        
        <div class="stats-card stats-graduates">
            <div class="stats-icon"><i class="fas fa-graduation-cap"></i></div>
            <div class="stats-number">{{ total_graduates }}</div>
            <div class="stats-title">إجمالي الخريجين</div>
        </div>
        
        <div class="stats-card stats-dropouts">
            <div class="stats-icon"><i class="fas fa-user-slash"></i></div>
            <div class="stats-number">{{ total_dropouts }}</div>
            <div class="stats-title">إجمالي المنسحبين</div>
        </div>
        
        <div class="stats-card stats-allowance">
            <div class="stats-icon"><i class="fas fa-money-bill-wave"></i></div>
            <div class="stats-number">{{ total_allowance }}</div>
            <div class="stats-title">إجمالي المبالغ المالية</div>
        </div>
    </div>
    
    <div class="d-flex justify-content-between mb-4">
        <a href="{{ url_for('course_reports') }}" class="btn btn-back">
            <i class="fas fa-arrow-right me-1"></i> العودة إلى التقارير
        </a>
        <a href="{{ url_for('export_course_report', month_year=month_year) }}" class="btn btn-export">
            <i class="fas fa-file-export me-1"></i> تصدير CSV
        </a>
    </div>
    
    <div class="data-card">
        <div class="data-card-header">
            <span><i class="fas fa-list me-2"></i> قائمة الدورات في شهر {{ month_name }} {{ year }}</span>
        </div>
        <div class="card-body">
            {% if courses %}
            <div class="row">
                {% for course in courses %}
                <div class="col-md-6">
                    <div class="course-card">
                        <div class="course-header">
                            <div class="course-number">{{ course.course_number }}</div>
                            <div class="course-title">{{ course.title }}</div>
                        </div>
                        <div class="course-body">
                            <div class="course-info">
                                <div class="course-info-item"><i class="fas fa-building me-1"></i> {{ course.agency or 'غير محدد' }}</div>
                                <div class="course-info-item"><i class="fas fa-map-marker-alt me-1"></i> {{ course.center_name or 'غير محدد' }}</div>
                                <div class="course-info-item"><i class="fas fa-calendar-alt me-1"></i> {{ course.start_date.strftime('%Y-%m-%d') }} إلى {{ course.end_date.strftime('%Y-%m-%d') }}</div>
                                <div class="course-info-item"><i class="fas fa-clock me-1"></i> {{ course.duration_days }} يوم</div>
                            </div>
                            
                            <div class="course-stats">
                                <div class="course-stat">
                                    <div class="course-stat-number">{{ course.total_participants or 0 }}</div>
                                    <div class="course-stat-title">المشاركين</div>
                                </div>
                                <div class="course-stat">
                                    <div class="course-stat-number">{{ course.total_graduates or 0 }}</div>
                                    <div class="course-stat-title">الخريجين</div>
                                </div>
                                <div class="course-stat">
                                    <div class="course-stat-number">{{ course.total_dropouts or 0 }}</div>
                                    <div class="course-stat-title">المنسحبين</div>
                                </div>
                            </div>
                            
                            <div class="financial-section">
                                <div class="financial-title">البيانات المالية</div>
                                <div class="financial-items">
                                    <div class="financial-item">
                                        <span class="financial-item-label">الإعاشة:</span>
                                        <span class="financial-item-value">{{ course.daily_allowance or 0 }}</span>
                                    </div>
                                    <div class="financial-item">
                                        <span class="financial-item-label">المواصلات:</span>
                                        <span class="financial-item-value">{{ course.transportation_allowance or 0 }}</span>
                                    </div>
                                    <div class="financial-item">
                                        <span class="financial-item-label">السكن:</span>
                                        <span class="financial-item-value">{{ course.accommodation_allowance or 0 }}</span>
                                    </div>
                                </div>
                                <div class="financial-total">
                                    الإجمالي: {{ course.total_allowance or 0 }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد دورات في هذا الشهر.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
