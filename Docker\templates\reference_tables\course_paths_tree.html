{% extends "layout.html" %}

{% block styles %}
<style>
    .tree-container {
        margin: 20px 0;
    }

    .path-card {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        overflow: hidden;
    }

    .path-header {
        background-color: #4a6bff;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .path-title {
        font-size: 1.3rem;
        margin: 0;
    }

    .path-code {
        background-color: rgba(255, 255, 255, 0.2);
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.9rem;
        margin-right: 10px;
    }

    .path-body {
        padding: 20px;
    }

    .path-description {
        color: #6c757d;
        margin-bottom: 20px;
    }

    .path-stats {
        display: flex;
        margin-bottom: 20px;
        gap: 20px;
    }

    .stat-item {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 10px 15px;
        text-align: center;
        flex: 1;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #4a6bff;
        display: block;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .levels-container {
        margin-top: 20px;
    }

    .level-item {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        position: relative;
        border-right: 4px solid #4a6bff;
    }

    .level-item:before {
        content: "";
        position: absolute;
        top: 0;
        right: -4px;
        height: 100%;
        width: 4px;
        background-color: #4a6bff;
    }

    .level-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .level-title {
        font-size: 1.1rem;
        font-weight: bold;
        margin: 0;
    }

    .level-code {
        background-color: #e9ecef;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
    }

    .level-order {
        background-color: #4a6bff;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-left: 10px;
    }

    .level-description {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .level-courses {
        background-color: #e9ecef;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 0.9rem;
        display: inline-block;
    }

    .level-actions {
        margin-top: 10px;
    }

    .btn-edit, .btn-view {
        padding: 5px 10px;
        font-size: 0.9rem;
        border-radius: 4px;
        text-decoration: none;
        margin-left: 5px;
    }

    .btn-edit {
        background-color: #ffc107;
        color: #212529;
    }

    .btn-view {
        background-color: #17a2b8;
        color: white;
    }

    .empty-message {
        background-color: #f8f9fa;
        padding: 20px;
        text-align: center;
        border-radius: 8px;
        color: #6c757d;
    }

    .tree-connector {
        width: 2px;
        background-color: #dee2e6;
        margin: 0 auto;
        height: 20px;
    }

    .path-actions {
        display: flex;
        gap: 10px;
    }

    .path-actions a {
        color: white;
        text-decoration: none;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 0.9rem;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .path-actions a:hover {
        background-color: rgba(255, 255, 255, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>{{ title }}</h2>
        <div>
            <a href="{{ url_for('course_paths') }}" class="btn btn-secondary">
                <i class="fas fa-list me-1"></i> قائمة المسارات
            </a>
            <a href="{{ url_for('add_course_path') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> إضافة مسار جديد
            </a>
        </div>
    </div>

    <div class="tree-container">
        {% if paths %}
            {% for path in paths %}
                <div class="path-card">
                    <div class="path-header">
                        <div>
                            <span class="path-code">{{ path.code }}</span>
                            <h3 class="path-title">{{ path.name }}</h3>
                        </div>
                        <div class="path-actions">
                            <a href="{{ url_for('edit_course_path', path_id=path.id) }}">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{{ url_for('add_course_path_level') }}?path_id={{ path.id }}&redirect_to=tree">
                                <i class="fas fa-plus-circle"></i> إضافة مستوى
                            </a>
                        </div>
                    </div>
                    <div class="path-body">
                        <p class="path-description">{{ path.description }}</p>

                        <div class="path-stats">
                            <div class="stat-item">
                                <span class="stat-value">{{ path.levels_count }}</span>
                                <span class="stat-label">عدد المستويات</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">{{ path.total_courses }}</span>
                                <span class="stat-label">عدد الدورات</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">{{ path.total_targets }}</span>
                                <span class="stat-label">عدد المستهدفين</span>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mt-3 mb-2">
                            <h4 class="mb-0">
                                <button class="btn btn-link toggle-levels p-0 text-decoration-none" data-path-id="{{ path.id }}">
                                    <i class="fas fa-chevron-down me-2 toggle-icon"></i> المستويات
                                </button>
                            </h4>
                            <a href="{{ url_for('add_course_path_level') }}?path_id={{ path.id }}&redirect_to=tree" class="btn btn-sm btn-success">
                                <i class="fas fa-plus-circle"></i> إضافة مستوى
                            </a>
                        </div>

                        <div class="levels-container" id="levels-{{ path.id }}" style="display: none;">
                            {% if path.levels %}
                                {% for level in path.levels %}
                                    <div class="level-item">
                                        <div class="level-header">
                                            <div class="d-flex align-items-center">
                                                <div class="level-order">{{ level.order }}</div>
                                                <h5 class="level-title">{{ level.name }}</h5>
                                                <span class="level-code ms-2">{{ level.code }}</span>
                                            </div>
                                            <div>
                                                <a href="{{ url_for('edit_course_path_level', level_id=level.id) }}?redirect_to=tree" class="btn-edit">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </a>
                                            </div>
                                        </div>
                                        <p class="level-description">{{ level.description }}</p>
                                        <div class="d-flex gap-3">
                                            <div class="level-courses">
                                                <i class="fas fa-book"></i> عدد الدورات: {{ level.courses_count }}
                                            </div>
                                            <div class="level-courses">
                                                <i class="fas fa-users"></i> عدد المستهدفين: {{ level.target_count }}
                                            </div>
                                        </div>
                                    </div>
                                    {% if not loop.last %}
                                        <div class="tree-connector"></div>
                                    {% endif %}
                                {% endfor %}
                            {% else %}
                                <div class="empty-message">
                                    <i class="fas fa-info-circle me-2"></i> لا توجد مستويات مضافة لهذا المسار.
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="empty-message">
                <i class="fas fa-info-circle me-2"></i> لا توجد مسارات تدريبية مضافة حالياً.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة مستمع الحدث لجميع أزرار التبديل
        const toggleButtons = document.querySelectorAll('.toggle-levels');
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const pathId = this.getAttribute('data-path-id');
                const levelsContainer = document.getElementById(`levels-${pathId}`);
                const toggleIcon = this.querySelector('.toggle-icon');

                // تبديل حالة العرض
                if (levelsContainer.style.display === 'none') {
                    levelsContainer.style.display = 'block';
                    toggleIcon.classList.remove('fa-chevron-down');
                    toggleIcon.classList.add('fa-chevron-up');
                } else {
                    levelsContainer.style.display = 'none';
                    toggleIcon.classList.remove('fa-chevron-up');
                    toggleIcon.classList.add('fa-chevron-down');
                }
            });
        });
    });
</script>
{% endblock %}