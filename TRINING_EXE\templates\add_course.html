{% extends "layout.html" %}

{% block styles %}
<style>
    .form-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }

    .form-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: bold;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4a6bff;
        box-shadow: 0 0 0 0.25rem rgba(74, 107, 255, 0.25);
    }

    .form-text {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .btn-submit {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
    }

    .btn-cancel {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-cancel:hover {
        transform: translateY(-2px);
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
    }

    .form-section-title {
        font-weight: bold;
        margin-bottom: 15px;
        color: #4a6bff;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link active">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <h2 class="mb-4">إضافة دورة تدريبية جديدة</h2>

        <form method="POST" enctype="multipart/form-data">
            {{ form.hidden_tag() }}

            <div class="form-card">
                <div class="form-header">
                    <h4><i class="fas fa-plus-circle me-2"></i> معلومات الدورة الأساسية</h4>
                    <p class="text-muted">أدخل المعلومات الأساسية للدورة التدريبية</p>
                </div>

                <!-- المسار والمستوى -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.path_id.label(class="form-label") }}
                            {% if form.path_id.errors %}
                                {{ form.path_id(class="form-select is-invalid", id="path_select") }}
                                <div class="invalid-feedback">
                                    {% for error in form.path_id.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.path_id(class="form-select", id="path_select") }}
                            {% endif %}
                            <small class="form-text">اختر المسار التدريبي</small>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.path_level_id.label(class="form-label") }}
                            {% if form.path_level_id.errors %}
                                {{ form.path_level_id(class="form-select is-invalid", id="path_level_select") }}
                                <div class="invalid-feedback">
                                    {% for error in form.path_level_id.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.path_level_id(class="form-select", id="path_level_select") }}
                            {% endif %}
                            <small class="form-text">اختر مستوى المسار</small>
                        </div>
                    </div>
                </div>

                <!-- المركز التدريبي ومعلوماته -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.center_id.label(class="form-label") }}
                            {% if form.center_id.errors %}
                                {{ form.center_id(class="form-select is-invalid", id="center_select") }}
                                <div class="invalid-feedback">
                                    {% for error in form.center_id.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.center_id(class="form-select", id="center_select") }}
                            {% endif %}
                            <small class="form-text">اختر المركز التدريبي</small>
                        </div>
                    </div>
                </div>

                <!-- معلومات المركز التدريبي (للعرض فقط) -->
                <div id="center_info" class="mt-3 p-3 bg-light rounded" style="display: none;">
                    <h5 class="mb-3">معلومات المركز التدريبي</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-2">
                                <strong>نوع المركز:</strong>
                                <span id="center_type">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>الموقع:</strong>
                                <span id="center_location">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>الجهة التابعة:</strong>
                                <span id="center_agency">-</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2">
                                <strong>السعة الاستيعابية:</strong>
                                <span id="center_capacity">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>الجهوزية:</strong>
                                <span id="center_readiness">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أرقام الدورة -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.place_course_number.label(class="form-label") }}
                            {% if form.place_course_number.errors %}
                                {{ form.place_course_number(class="form-control is-invalid", placeholder="أدخل رقم الدورة في المكان") }}
                                <div class="invalid-feedback">
                                    {% for error in form.place_course_number.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.place_course_number(class="form-control", placeholder="أدخل رقم الدورة في المكان") }}
                            {% endif %}
                            <small class="form-text">أدخل رقم الدورة في المكان</small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.agency_course_number.label(class="form-label") }}
                            {% if form.agency_course_number.errors %}
                                {{ form.agency_course_number(class="form-control is-invalid", placeholder="أدخل رقم الدورة في الجهة") }}
                                <div class="invalid-feedback">
                                    {% for error in form.agency_course_number.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.agency_course_number(class="form-control", placeholder="أدخل رقم الدورة في الجهة") }}
                            {% endif %}
                            <small class="form-text">أدخل رقم الدورة في الجهة</small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.course_number.label(class="form-label") }}
                            {% if form.course_number.errors %}
                                {{ form.course_number(class="form-control is-invalid", placeholder="أدخل رقم الدورة العام") }}
                                <div class="invalid-feedback">
                                    {% for error in form.course_number.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.course_number(class="form-control", placeholder="أدخل رقم الدورة العام") }}
                            {% endif %}
                            <small class="form-text">أدخل رقم الدورة العام (إجباري)</small>
                        </div>
                    </div>
                </div>

                <!-- معلومات الدورة -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.title.label(class="form-label") }}
                            {% if form.title.errors %}
                                {{ form.title(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.title.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.title(class="form-control", placeholder="أدخل عنوان الدورة") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.image.label(class="form-label") }}
                            {% if form.image.errors %}
                                {{ form.image(class="form-control is-invalid", accept=".jpg,.jpeg,.png,.gif,.bmp,.webp") }}
                                <div class="invalid-feedback">
                                    {% for error in form.image.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.image(class="form-control", accept=".jpg,.jpeg,.png,.gif,.bmp,.webp") }}
                            {% endif %}
                            <small class="form-text">اختر صورة للدورة بامتداد jpg أو png أو jpeg أو gif أو bmp أو webp (اختياري)</small>
                        </div>
                    </div>
                </div>

                <!-- رقم الدفعة ومدة الدورة وعدد المشاركين -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">رقم الدفعة</label>
                            <input type="number" name="batch_number" class="form-control" min="1" value="1">
                            <small class="form-text">أدخل رقم الدفعة</small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.duration_days.label(class="form-label") }}
                            {% if form.duration_days.errors %}
                                <input type="number" name="duration_days" class="form-control is-invalid" min="1" max="365" value="1" required>
                                <div class="invalid-feedback">
                                    {% for error in form.duration_days.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <input type="number" name="duration_days" class="form-control" min="1" max="365" value="1" required>
                            {% endif %}
                            <small class="form-text">أدخل مدة الدورة بالأيام</small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.target_count.label(class="form-label") }}
                            {% if form.target_count.errors %}
                                {{ form.target_count(class="form-control is-invalid", min="1") }}
                                <div class="invalid-feedback">
                                    {% for error in form.target_count.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.target_count(class="form-control", min="1") }}
                            {% endif %}
                            <small class="form-text">أدخل عدد المشاركين المستهدفين</small>
                        </div>
                    </div>
                </div>
                <!-- تصنيف القوة والجهة التابعة للمستهدفين -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.force_classification_id.label(class="form-label") }}
                            {% if form.force_classification_id.errors %}
                                {{ form.force_classification_id(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.force_classification_id.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.force_classification_id(class="form-select") }}
                            {% endif %}
                            <small class="form-text">اختر تصنيف القوة</small>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.target_agency_id.label(class="form-label") }}
                            {% if form.target_agency_id.errors %}
                                {{ form.target_agency_id(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.target_agency_id.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.target_agency_id(class="form-select") }}
                            {% endif %}
                            <small class="form-text">اختر الجهة التابعة للمستهدفين</small>
                        </div>
                    </div>
                </div>

                <!-- تاريخ الدخول (ميلادي وهجري) -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.entry_date.label(class="form-label") }}
                            {% if form.entry_date.errors %}
                                <input type="date" name="entry_date" class="form-control is-invalid" required>
                                <div class="invalid-feedback">
                                    {% for error in form.entry_date.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <input type="date" name="entry_date" class="form-control" required>
                            {% endif %}
                            <small class="form-text">يرجى تحديد تاريخ الدخول (ميلادي)</small>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">تاريخ الدخول (هجري)</label>
                            <div class="row">
                                <div class="col-md-3">
                                    {% if form.entry_date_hijri_day.errors %}
                                        {{ form.entry_date_hijri_day(class="form-select is-invalid") }}
                                        <div class="invalid-feedback">
                                            {% for error in form.entry_date_hijri_day.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        {{ form.entry_date_hijri_day(class="form-select") }}
                                    {% endif %}
                                    <small class="form-text">اليوم</small>
                                </div>
                                <div class="col-md-5">
                                    {% if form.entry_date_hijri_month.errors %}
                                        {{ form.entry_date_hijri_month(class="form-select is-invalid") }}
                                        <div class="invalid-feedback">
                                            {% for error in form.entry_date_hijri_month.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        {{ form.entry_date_hijri_month(class="form-select") }}
                                    {% endif %}
                                    <small class="form-text">الشهر</small>
                                </div>
                                <div class="col-md-4">
                                    {% if form.entry_date_hijri_year.errors %}
                                        {{ form.entry_date_hijri_year(class="form-select is-invalid") }}
                                        <div class="invalid-feedback">
                                            {% for error in form.entry_date_hijri_year.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        {{ form.entry_date_hijri_year(class="form-select") }}
                                    {% endif %}
                                    <small class="form-text">السنة</small>
                                </div>
                            </div>
                            <small class="form-text">أدخل تاريخ الدخول بالتقويم الهجري (إجباري)</small>
                        </div>
                    </div>
                </div>

                <!-- تاريخ الخروج (ميلادي وهجري) -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.exit_date.label(class="form-label") }}
                            {% if form.exit_date.errors %}
                                <input type="date" name="exit_date" class="form-control is-invalid" required>
                                <div class="invalid-feedback">
                                    {% for error in form.exit_date.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <input type="date" name="exit_date" class="form-control" required>
                            {% endif %}
                            <small class="form-text">يرجى تحديد تاريخ الخروج (ميلادي)</small>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">تاريخ الخروج (هجري)</label>
                            <div class="row">
                                <div class="col-md-3">
                                    {% if form.exit_date_hijri_day.errors %}
                                        {{ form.exit_date_hijri_day(class="form-select is-invalid") }}
                                        <div class="invalid-feedback">
                                            {% for error in form.exit_date_hijri_day.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        {{ form.exit_date_hijri_day(class="form-select") }}
                                    {% endif %}
                                    <small class="form-text">اليوم</small>
                                </div>
                                <div class="col-md-5">
                                    {% if form.exit_date_hijri_month.errors %}
                                        {{ form.exit_date_hijri_month(class="form-select is-invalid") }}
                                        <div class="invalid-feedback">
                                            {% for error in form.exit_date_hijri_month.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        {{ form.exit_date_hijri_month(class="form-select") }}
                                    {% endif %}
                                    <small class="form-text">الشهر</small>
                                </div>
                                <div class="col-md-4">
                                    {% if form.exit_date_hijri_year.errors %}
                                        {{ form.exit_date_hijri_year(class="form-select is-invalid") }}
                                        <div class="invalid-feedback">
                                            {% for error in form.exit_date_hijri_year.errors %}
                                                <span>{{ error }}</span>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        {{ form.exit_date_hijri_year(class="form-select") }}
                                    {% endif %}
                                    <small class="form-text">السنة</small>
                                </div>
                            </div>
                            <small class="form-text">أدخل تاريخ الخروج بالتقويم الهجري (إجباري)</small>
                        </div>
                    </div>
                </div>

                <!-- وصف الدورة -->
                <div class="form-group mt-3">
                    {{ form.description.label(class="form-label") }}
                    {% if form.description.errors %}
                        {{ form.description(class="form-control is-invalid", rows=5) }}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.description(class="form-control", rows=5, placeholder="أدخل وصفاً تفصيلياً للدورة") }}
                    {% endif %}
                </div>
            </div>

            <div class="form-card">
                <div class="form-header">
                    <h4><i class="fas fa-sticky-note me-2"></i> ملاحظات</h4>
                </div>

                <div class="form-group">
                    {{ form.notes.label(class="form-label") }}
                    {% if form.notes.errors %}
                        {{ form.notes(class="form-control is-invalid", rows=3) }}
                        <div class="invalid-feedback">
                            {% for error in form.notes.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.notes(class="form-control", rows=3, placeholder="أدخل أي ملاحظات إضافية") }}
                    {% endif %}
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ url_for('courses') }}" class="btn btn-secondary btn-cancel">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                    {{ form.submit(class="btn btn-primary btn-submit") }}
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث مستويات المسار عند تغيير المسار
        const pathSelect = document.getElementById('path_select');
        const pathLevelSelect = document.getElementById('path_level_select');

        pathSelect.addEventListener('change', function() {
            const pathId = this.value;

            // إفراغ قائمة المستويات
            pathLevelSelect.innerHTML = '<option value="0">اختر المستوى</option>';

            // إذا تم اختيار مسار
            if (pathId != 0) {
                // إرسال طلب AJAX للحصول على المستويات
                fetch(`/api/path_levels/${pathId}`)
                    .then(response => response.json())
                    .then(data => {
                        // إضافة المستويات إلى القائمة المنسدلة
                        data.forEach(level => {
                            const option = document.createElement('option');
                            option.value = level.id;
                            option.textContent = level.name;
                            pathLevelSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error fetching path levels:', error));
            }
        });

        // عرض معلومات المركز التدريبي عند اختياره
        const centerSelect = document.getElementById('center_select');
        const centerInfo = document.getElementById('center_info');
        const centerType = document.getElementById('center_type');
        const centerLocation = document.getElementById('center_location');
        const centerAgency = document.getElementById('center_agency');
        const centerCapacity = document.getElementById('center_capacity');
        const centerReadiness = document.getElementById('center_readiness');

        centerSelect.addEventListener('change', function() {
            const centerId = this.value;

            // إذا تم اختيار مركز
            if (centerId != 0) {
                // إرسال طلب AJAX للحصول على معلومات المركز
                fetch(`/api/training_centers/${centerId}`)
                    .then(response => response.json())
                    .then(data => {
                        // عرض معلومات المركز
                        centerType.textContent = data.center_type || '-';
                        centerLocation.textContent = data.location || '-';
                        centerAgency.textContent = data.agency || '-';
                        centerCapacity.textContent = data.capacity || '-';
                        centerReadiness.textContent = data.is_ready ? 'جاهز' : 'غير جاهز';

                        // إظهار قسم معلومات المركز
                        centerInfo.style.display = 'block';
                    })
                    .catch(error => {
                        console.error('Error fetching center info:', error);
                        centerInfo.style.display = 'none';
                    });
            } else {
                // إخفاء قسم معلومات المركز
                centerInfo.style.display = 'none';
            }
        });

        // حساب تاريخ الانتهاء تلقائيًا بناءً على تاريخ البدء ومدة الدورة
        const startDateInput = document.querySelector('input[name="start_date"]');
        const endDateInput = document.querySelector('input[name="end_date"]');
        const durationInput = document.querySelector('input[name="duration_days"]');

        function updateEndDate() {
            if (startDateInput.value && durationInput.value) {
                const startDate = new Date(startDateInput.value);
                const duration = parseInt(durationInput.value);

                if (!isNaN(startDate.getTime()) && !isNaN(duration)) {
                    const endDate = new Date(startDate);
                    endDate.setDate(startDate.getDate() + duration - 1);

                    // تنسيق التاريخ بصيغة YYYY-MM-DD
                    const year = endDate.getFullYear();
                    const month = String(endDate.getMonth() + 1).padStart(2, '0');
                    const day = String(endDate.getDate()).padStart(2, '0');

                    endDateInput.value = `${year}-${month}-${day}`;
                }
            }
        }

        startDateInput.addEventListener('change', updateEndDate);
        durationInput.addEventListener('change', updateEndDate);
        durationInput.addEventListener('input', updateEndDate);
    });
</script>
{% endblock %}
