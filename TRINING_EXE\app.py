#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة التدريب - Training Management System
تطبيق Flask لإدارة الدورات التدريبية والمشاركين
"""

import os
import sys
import sqlite3
from flask import Flask, render_template, request, redirect, url_for, flash
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

# إعداد المسارات للعمل مع PyInstaller
if getattr(sys, 'frozen', False):
    # التطبيق مجمع (exe)
    application_path = sys._MEIPASS
    base_dir = os.path.dirname(sys.executable)
else:
    # التطبيق يعمل كسكريبت بايثون عادي
    application_path = os.path.dirname(os.path.abspath(__file__))
    base_dir = application_path

# تحديد مسارات الموارد
TEMPLATE_DIR = os.path.join(application_path, "templates")
STATIC_DIR = os.path.join(application_path, "static")
DATABASE_DIR = os.path.join(application_path, "database")
DATABASE_PATH = os.path.join(DATABASE_DIR, "training_system.db")

# تأكد من وجود مجلد قاعدة البيانات
if not os.path.exists(DATABASE_DIR):
    os.makedirs(DATABASE_DIR)

print(f"🔍 Application path: {application_path}")
print(f"🔍 Template dir: {TEMPLATE_DIR}")
print(f"🔍 Static dir: {STATIC_DIR}")
print(f"🔍 Database path: {DATABASE_PATH}")

# إنشاء التطبيق
app = Flask(__name__, 
           template_folder=TEMPLATE_DIR,
           static_folder=STATIC_DIR)

app.config['SECRET_KEY'] = 'training-system-secret-key-2024'
app.config['DEBUG'] = True  # تمكين وضع Debug للتشخيص

# إعداد Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
login_manager.login_message_category = 'info'

class User(UserMixin):
    def __init__(self, id, username, email, role):
        self.id = id
        self.username = username
        self.email = email
        self.role = role

@login_manager.user_loader
def load_user(user_id):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
    user_data = cursor.fetchone()
    conn.close()
    
    if user_data:
        return User(user_data[0], user_data[1], user_data[2], user_data[4])
    return None

def get_db_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """تهيئة قاعدة البيانات وإنشاء الجداول والمستخدم الافتراضي"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # إنشاء جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(80) UNIQUE NOT NULL,
            email VARCHAR(120) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            role VARCHAR(20) NOT NULL DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول الدورات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS courses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            trainer_id INTEGER,
            start_date DATE,
            end_date DATE,
            location VARCHAR(200),
            max_participants INTEGER,
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (trainer_id) REFERENCES users (id)
        )
    ''')
    
    # إنشاء جدول الأشخاص
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS persons (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(200) NOT NULL,
            email VARCHAR(120),
            phone VARCHAR(20),
            organization VARCHAR(200),
            position VARCHAR(200),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول مشاركي الدورات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS course_participants (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            course_id INTEGER NOT NULL,
            person_id INTEGER NOT NULL,
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status VARCHAR(20) DEFAULT 'registered',
            FOREIGN KEY (course_id) REFERENCES courses (id),
            FOREIGN KEY (person_id) REFERENCES persons (id)
        )
    ''')
    
    # التحقق من وجود المستخدم <EMAIL>
    cursor.execute('SELECT * FROM users WHERE email = ?', ('<EMAIL>',))
    admin_user = cursor.fetchone()
    
    if not admin_user:
        # إنشاء المستخدم الافتراضي
        password_hash = generate_password_hash('admin123')
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, role)
            VALUES (?, ?, ?, ?)
        ''', ('admin', '<EMAIL>', password_hash, 'admin'))
        conn.commit()
        print("✅ تم إنشاء المستخدم الافتراضي: <EMAIL> / admin123")
    
    conn.close()

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM users WHERE email = ?', (email,))
        user_data = cursor.fetchone()
        conn.close()
        
        if user_data and check_password_hash(user_data[3], password):
            user = User(user_data[0], user_data[1], user_data[2], user_data[4])
            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('البريد الإلكتروني أو كلمة المرور غير صحيحة.', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # إحصائيات سريعة
    cursor.execute('SELECT COUNT(*) FROM courses')
    total_courses = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM persons')
    total_persons = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM course_participants')
    total_participants = cursor.fetchone()[0]
    
    conn.close()
    
    stats = {
        'total_courses': total_courses,
        'total_persons': total_persons,
        'total_participants': total_participants
    }
    
    return render_template('dashboard.html', stats=stats)

if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام إدارة التدريب...")
    
    # تهيئة قاعدة البيانات
    init_database()
    
    print("✅ تم تهيئة قاعدة البيانات بنجاح")
    print("🌐 الخادم يعمل على: http://localhost:5000")
    print("🔑 تسجيل الدخول: <EMAIL> / admin123")
    
    # تشغيل التطبيق
    app.run(host='0.0.0.0', port=5000, debug=True)
