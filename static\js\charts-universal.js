/**
 * 📊 نظام الرسوم البيانية الموحد
 * Universal Charts System - Compatible with all browsers
 */

// ===== إعدادات Chart.js العامة =====
if (typeof Chart !== 'undefined') {
    Chart.defaults.font.family = "'Tajawal', 'Inter', sans-serif";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#374151';
    Chart.defaults.responsive = false;
    Chart.defaults.maintainAspectRatio = false;
    Chart.defaults.interaction.intersect = false;
    Chart.defaults.plugins.legend.rtl = true;
    Chart.defaults.plugins.tooltip.rtl = true;
}

// ===== دالة حماية أحجام الكانفاس =====
function protectCanvasSize(canvas, width = 350, height = 280) {
    if (!canvas) return false;
    
    try {
        // إزالة خصائص Chart.js
        canvas.removeAttribute('data-chartjs-render-monitor');
        
        // تطبيق الأحجام بقوة
        const styles = {
            width: `${width}px`,
            height: `${height}px`,
            maxWidth: `${width}px`,
            maxHeight: `${height}px`,
            minWidth: `${width}px`,
            minHeight: `${height}px`,
            position: 'relative',
            display: 'block',
            boxSizing: 'border-box'
        };
        
        Object.assign(canvas.style, styles);
        canvas.width = width;
        canvas.height = height;
        
        return true;
    } catch (error) {
        console.warn('خطأ في حماية الكانفاس:', error);
        return false;
    }
}

// ===== دالة إنشاء رسم بياني محمي =====
function createProtectedChart(canvasId, config) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) {
        console.warn(`الكانفاس غير موجود: ${canvasId}`);
        return null;
    }
    
    // حماية الكانفاس
    protectCanvasSize(canvas);
    
    // إعدادات الحماية
    const protectedConfig = {
        ...config,
        options: {
            ...config.options,
            responsive: false,
            maintainAspectRatio: false,
            onResize: () => false, // منع تغيير الحجم
            animation: {
                ...config.options?.animation,
                onComplete: () => {
                    // إعادة حماية بعد الرسم
                    setTimeout(() => protectCanvasSize(canvas), 100);
                }
            }
        }
    };
    
    try {
        const chart = new Chart(canvas, protectedConfig);
        
        // مراقبة مستمرة
        const monitor = setInterval(() => {
            if (canvas.width !== 350 || canvas.height !== 280) {
                protectCanvasSize(canvas);
            }
        }, 500);
        
        // حفظ معرف المراقب
        chart._protectionMonitor = monitor;
        
        return chart;
    } catch (error) {
        console.error(`خطأ في إنشاء الرسم البياني ${canvasId}:`, error);
        return null;
    }
}

// ===== دالة تدمير رسم بياني محمي =====
function destroyProtectedChart(chart) {
    if (!chart) return;
    
    try {
        // إيقاف المراقبة
        if (chart._protectionMonitor) {
            clearInterval(chart._protectionMonitor);
        }
        
        // تدمير الرسم
        chart.destroy();
    } catch (error) {
        console.warn('خطأ في تدمير الرسم البياني:', error);
    }
}

// ===== إعدادات الألوان الموحدة =====
const CHART_COLORS = {
    primary: ['#2563eb', '#3b82f6', '#60a5fa', '#93c5fd', '#dbeafe'],
    success: ['#10b981', '#34d399', '#6ee7b7', '#a7f3d0', '#d1fae5'],
    warning: ['#f59e0b', '#fbbf24', '#fcd34d', '#fde68a', '#fef3c7'],
    danger: ['#ef4444', '#f87171', '#fca5a5', '#fecaca', '#fee2e2'],
    info: ['#06b6d4', '#22d3ee', '#67e8f9', '#a5f3fc', '#cffafe'],
    purple: ['#8b5cf6', '#a78bfa', '#c4b5fd', '#ddd6fe', '#ede9fe'],
    pink: ['#ec4899', '#f472b6', '#f9a8d4', '#fbb6ce', '#fce7f3']
};

// ===== دالة الحصول على ألوان متدرجة =====
function getChartColors(type = 'primary', count = 5) {
    const colors = CHART_COLORS[type] || CHART_COLORS.primary;
    if (count <= colors.length) {
        return colors.slice(0, count);
    }
    
    // إنشاء ألوان إضافية إذا احتجنا
    const result = [...colors];
    while (result.length < count) {
        result.push(...colors);
    }
    return result.slice(0, count);
}

// ===== إعدادات الرسوم البيانية الافتراضية =====
const DEFAULT_CHART_OPTIONS = {
    responsive: false,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom',
            rtl: true,
            labels: {
                padding: 15,
                usePointStyle: true,
                font: {
                    family: "'Tajawal', 'Inter', sans-serif",
                    size: 11
                }
            }
        },
        tooltip: {
            rtl: true,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: '#2563eb',
            borderWidth: 1,
            cornerRadius: 8,
            font: {
                family: "'Tajawal', 'Inter', sans-serif",
                size: 11
            }
        }
    },
    animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
    }
};

// ===== دالة إنشاء رسم دائري =====
function createPieChart(canvasId, data, title, colorType = 'primary') {
    const config = {
        type: 'pie',
        data: {
            labels: data.labels,
            datasets: [{
                data: data.values,
                backgroundColor: getChartColors(colorType, data.labels.length),
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            ...DEFAULT_CHART_OPTIONS,
            plugins: {
                ...DEFAULT_CHART_OPTIONS.plugins,
                title: {
                    display: true,
                    text: title,
                    font: {
                        family: "'Tajawal', 'Inter', sans-serif",
                        size: 14,
                        weight: 'bold'
                    }
                }
            }
        }
    };
    
    return createProtectedChart(canvasId, config);
}

// ===== دالة إنشاء رسم عمودي =====
function createBarChart(canvasId, data, title, colorType = 'primary') {
    const config = {
        type: 'bar',
        data: {
            labels: data.labels,
            datasets: [{
                label: title,
                data: data.values,
                backgroundColor: getChartColors(colorType, 1)[0],
                borderColor: getChartColors(colorType, 1)[0],
                borderWidth: 2,
                borderRadius: 4
            }]
        },
        options: {
            ...DEFAULT_CHART_OPTIONS,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        font: {
                            family: "'Tajawal', 'Inter', sans-serif",
                            size: 10
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            family: "'Tajawal', 'Inter', sans-serif",
                            size: 10
                        }
                    }
                }
            },
            plugins: {
                ...DEFAULT_CHART_OPTIONS.plugins,
                legend: {
                    display: false
                }
            }
        }
    };
    
    return createProtectedChart(canvasId, config);
}

// ===== دالة إنشاء رسم دونات =====
function createDoughnutChart(canvasId, data, title, colorType = 'primary') {
    const config = {
        type: 'doughnut',
        data: {
            labels: data.labels,
            datasets: [{
                data: data.values,
                backgroundColor: getChartColors(colorType, data.labels.length),
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            ...DEFAULT_CHART_OPTIONS,
            cutout: '60%',
            plugins: {
                ...DEFAULT_CHART_OPTIONS.plugins,
                title: {
                    display: true,
                    text: title,
                    font: {
                        family: "'Tajawal', 'Inter', sans-serif",
                        size: 14,
                        weight: 'bold'
                    }
                }
            }
        }
    };
    
    return createProtectedChart(canvasId, config);
}

// ===== دالة تحديث بيانات الرسم =====
function updateChartData(chart, newData) {
    if (!chart || !newData) return;
    
    try {
        chart.data.labels = newData.labels;
        chart.data.datasets[0].data = newData.values;
        chart.update('none'); // تحديث بدون رسوم متحركة
        
        // إعادة حماية الكانفاس
        setTimeout(() => {
            protectCanvasSize(chart.canvas);
        }, 100);
    } catch (error) {
        console.warn('خطأ في تحديث بيانات الرسم:', error);
    }
}

// ===== دالة إظهار حالة التحميل =====
function showChartLoading(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    container.innerHTML = `
        <div class="chart-loading">
            <div class="chart-loading-spinner"></div>
            <span>جاري تحميل البيانات...</span>
        </div>
    `;
}

// ===== دالة إظهار رسالة عدم وجود بيانات =====
function showChartNoData(containerId, message = 'لا توجد بيانات للعرض') {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    container.innerHTML = `
        <div class="chart-no-data">
            <i class="fas fa-chart-bar"></i>
            <div>${message}</div>
        </div>
    `;
}

// ===== تهيئة النظام عند تحميل الصفحة =====
document.addEventListener('DOMContentLoaded', function() {
    // تعطيل resize في Chart.js
    if (typeof Chart !== 'undefined') {
        const originalResize = Chart.prototype.resize;
        Chart.prototype.resize = function() {
            console.log('🚫 تم منع تغيير حجم الرسم البياني');
            return this;
        };
    }
    
    // حماية جميع الكانفاس الموجودة
    const canvasElements = document.querySelectorAll('canvas[id*="Chart"]');
    canvasElements.forEach(canvas => {
        protectCanvasSize(canvas);
    });
    
    console.log('✅ تم تهيئة نظام الرسوم البيانية الموحد');
});

// ===== تصدير الدوال للاستخدام العام =====
window.ChartUtils = {
    createProtectedChart,
    destroyProtectedChart,
    createPieChart,
    createBarChart,
    createDoughnutChart,
    updateChartData,
    showChartLoading,
    showChartNoData,
    protectCanvasSize,
    getChartColors,
    CHART_COLORS
};
