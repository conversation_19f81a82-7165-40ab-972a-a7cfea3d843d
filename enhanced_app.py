#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام التدريب والتأهيل المحسن - الإصدار المتقدم
Enhanced Training System - Advanced Version
"""

import os
import sys
import time
import threading
from datetime import datetime
from pathlib import Path

# إضافة المسار الحالي
sys.path.insert(0, str(Path(__file__).parent))

# استيراد الأنظمة المحسنة
try:
    from performance_optimizer import optimize_app, optimize_performance, get_performance_report
    from reliability_system import reliable, validate, get_system_health, create_backup
    from network_system import setup_network_app, get_network_info
    from testing_system import run_quick_test
    
    ENHANCED_SYSTEMS_AVAILABLE = True
    print("✅ تم تحميل الأنظمة المحسنة بنجاح")
except ImportError as e:
    print(f"⚠️ بعض الأنظمة المحسنة غير متاحة: {e}")
    ENHANCED_SYSTEMS_AVAILABLE = False

# استيراد التطبيق الأساسي
try:
    from app import app, db, User, Course, PersonData
    print("✅ تم تحميل التطبيق الأساسي بنجاح")
except ImportError as e:
    print(f"❌ فشل في تحميل التطبيق الأساسي: {e}")
    sys.exit(1)

class EnhancedTrainingSystem:
    """النظام المحسن للتدريب والتأهيل"""
    
    def __init__(self):
        self.app = app
        self.start_time = datetime.now()
        self.system_info = self.get_system_info()
        self.performance_enabled = ENHANCED_SYSTEMS_AVAILABLE
        self.network_app = None
        
        # تطبيق التحسينات
        self.apply_enhancements()
        
        # إعداد المراقبة
        self.setup_monitoring()
    
    def get_system_info(self):
        """الحصول على معلومات النظام"""
        import platform
        
        return {
            'platform': platform.system(),
            'python_version': platform.python_version(),
            'architecture': platform.architecture()[0],
            'processor': platform.processor(),
            'hostname': platform.node(),
            'enhanced_systems': ENHANCED_SYSTEMS_AVAILABLE,
            'start_time': self.start_time.isoformat()
        }
    
    def apply_enhancements(self):
        """تطبيق التحسينات على التطبيق"""
        print("🔧 تطبيق التحسينات...")
        
        if ENHANCED_SYSTEMS_AVAILABLE:
            # تحسين الأداء
            self.app = optimize_app(self.app)
            print("   ✅ تم تطبيق تحسينات الأداء")
            
            # إعداد الشبكة
            self.network_app = setup_network_app(self.app)
            print("   ✅ تم إعداد نظام الشبكة المتقدم")
            
            # إضافة مسارات محسنة
            self.add_enhanced_routes()
            print("   ✅ تم إضافة المسارات المحسنة")
        
        # إعداد قاعدة البيانات
        self.setup_database()
        print("   ✅ تم إعداد قاعدة البيانات")
    
    @reliable(max_retries=3)
    def setup_database(self):
        """إعداد قاعدة البيانات مع الموثوقية"""
        with self.app.app_context():
            try:
                db.create_all()
                
                # فحص وجود المستخدم الافتراضي
                admin_user = User.query.filter_by(email='<EMAIL>').first()
                if not admin_user:
                    # إنشاء المستخدم الافتراضي
                    from werkzeug.security import generate_password_hash
                    
                    admin_user = User(
                        username='admin',
                        email='<EMAIL>',
                        password_hash=generate_password_hash('admin123'),
                        role='admin'
                    )
                    db.session.add(admin_user)
                    db.session.commit()
                    print("   ✅ تم إنشاء المستخدم الافتراضي")
                
                return True
                
            except Exception as e:
                print(f"   ❌ خطأ في إعداد قاعدة البيانات: {e}")
                raise
    
    def add_enhanced_routes(self):
        """إضافة مسارات محسنة"""
        
        @self.app.route('/api/system/info')
        @optimize_performance
        def system_info():
            """معلومات النظام"""
            from flask import jsonify
            
            info = {
                'system': self.system_info,
                'uptime': str(datetime.now() - self.start_time),
                'enhanced_features': ENHANCED_SYSTEMS_AVAILABLE,
                'timestamp': datetime.now().isoformat()
            }
            
            if ENHANCED_SYSTEMS_AVAILABLE:
                info['performance'] = get_performance_report()
                info['health'] = get_system_health()
                info['network'] = get_network_info()
            
            return jsonify(info)
        
        @self.app.route('/api/system/health')
        @optimize_performance
        def system_health():
            """صحة النظام"""
            from flask import jsonify
            
            if ENHANCED_SYSTEMS_AVAILABLE:
                health = get_system_health()
            else:
                health = {
                    'overall_status': 'BASIC',
                    'message': 'Enhanced systems not available'
                }
            
            return jsonify(health)
        
        @self.app.route('/api/system/performance')
        @optimize_performance
        def system_performance():
            """أداء النظام"""
            from flask import jsonify
            
            if ENHANCED_SYSTEMS_AVAILABLE:
                performance = get_performance_report()
            else:
                performance = {
                    'status': 'BASIC',
                    'message': 'Performance monitoring not available'
                }
            
            return jsonify(performance)
        
        @self.app.route('/api/system/backup')
        @reliable()
        def create_system_backup():
            """إنشاء نسخة احتياطية"""
            from flask import jsonify
            
            try:
                if ENHANCED_SYSTEMS_AVAILABLE:
                    backup_path = create_backup('training_system.db')
                    return jsonify({
                        'success': True,
                        'backup_path': str(backup_path),
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': 'Backup system not available'
                    })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                })
        
        @self.app.route('/api/system/test')
        @optimize_performance
        def run_system_test():
            """تشغيل اختبار النظام"""
            from flask import jsonify
            
            try:
                if ENHANCED_SYSTEMS_AVAILABLE:
                    test_results = run_quick_test()
                    return jsonify({
                        'success': True,
                        'results': test_results,
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': 'Testing system not available'
                    })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                })
    
    def setup_monitoring(self):
        """إعداد مراقبة النظام"""
        def monitor_system():
            """مراقبة النظام في الخلفية"""
            while True:
                try:
                    if ENHANCED_SYSTEMS_AVAILABLE:
                        # فحص صحة النظام كل 10 دقائق
                        health = get_system_health()
                        if health.get('overall_status') != 'HEALTHY':
                            print(f"⚠️ تحذير: حالة النظام {health.get('overall_status')}")
                    
                    time.sleep(600)  # 10 دقائق
                    
                except Exception as e:
                    print(f"❌ خطأ في مراقبة النظام: {e}")
                    time.sleep(60)  # إعادة المحاولة بعد دقيقة
        
        # بدء المراقبة في thread منفصل
        monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        monitor_thread.start()
        print("   ✅ تم بدء مراقبة النظام")
    
    def print_startup_info(self):
        """طباعة معلومات بدء التشغيل"""
        print("\n" + "=" * 70)
        print("🚀 نظام التدريب والتأهيل المحسن")
        print("=" * 70)
        print(f"🐍 Python: {self.system_info['python_version']}")
        print(f"💻 النظام: {self.system_info['platform']} {self.system_info['architecture']}")
        print(f"🖥️  المعالج: {self.system_info['processor']}")
        print(f"🌐 اسم الجهاز: {self.system_info['hostname']}")
        print(f"⚡ الأنظمة المحسنة: {'✅ مفعلة' if ENHANCED_SYSTEMS_AVAILABLE else '❌ غير متاحة'}")
        print("=" * 70)
        
        if ENHANCED_SYSTEMS_AVAILABLE and self.network_app:
            network_info = get_network_info()
            print("🌐 معلومات الشبكة:")
            print(f"   📍 IP المحلي: {network_info['local_ip']}")
            print(f"   🔗 الروابط المتاحة:")
            for url in network_info['config']['external_urls']:
                print(f"      • {url}")
        else:
            print("🌐 الشبكة: http://localhost:5000")
        
        print("=" * 70)
        print("🔑 بيانات الدخول:")
        print("   📧 البريد: <EMAIL>")
        print("   🔒 كلمة المرور: admin123")
        print("=" * 70)
        
        if ENHANCED_SYSTEMS_AVAILABLE:
            print("🔧 المسارات المحسنة:")
            print("   📊 معلومات النظام: /api/system/info")
            print("   💚 صحة النظام: /api/system/health")
            print("   ⚡ أداء النظام: /api/system/performance")
            print("   💾 نسخة احتياطية: /api/system/backup")
            print("   🧪 اختبار النظام: /api/system/test")
            print("=" * 70)
    
    def run(self, host='0.0.0.0', port=5000, debug=False):
        """تشغيل النظام المحسن"""
        self.print_startup_info()
        
        print(f"🚀 بدء تشغيل الخادم على {host}:{port}...")
        print("⏰ جاري التحضير...")
        
        # تأخير قصير للتأكد من اكتمال التحضيرات
        time.sleep(2)
        
        try:
            if ENHANCED_SYSTEMS_AVAILABLE and self.network_app:
                # تشغيل مع الشبكة المحسنة
                self.network_app.run_network_server(host=host, port=port)
            else:
                # تشغيل عادي
                self.app.run(
                    host=host,
                    port=port,
                    debug=debug,
                    threaded=True,
                    use_reloader=False
                )
        except KeyboardInterrupt:
            print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل النظام: {e}")
        finally:
            print("👋 شكراً لاستخدام نظام التدريب والتأهيل!")

def main():
    """الدالة الرئيسية"""
    print("🔄 تحميل النظام المحسن...")
    
    try:
        # إنشاء النظام المحسن
        enhanced_system = EnhancedTrainingSystem()
        
        # تشغيل النظام
        enhanced_system.run()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام المحسن: {e}")
        print("🔄 محاولة التشغيل بالوضع الأساسي...")
        
        # تشغيل أساسي في حالة الفشل
        try:
            app.run(host='0.0.0.0', port=5000, debug=False)
        except Exception as basic_error:
            print(f"❌ فشل في التشغيل الأساسي أيضاً: {basic_error}")
            sys.exit(1)

if __name__ == "__main__":
    main()
