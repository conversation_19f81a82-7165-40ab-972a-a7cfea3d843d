#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف فحص حالة نظام التدريب والتأهيل
يتحقق من جميع المكونات ويعطي تقريراً شاملاً
"""

import os
import sys
import sqlite3
from datetime import datetime

def check_python():
    """فحص إصدار Python"""
    print("🔍 فحص Python:")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} (يتطلب 3.8+)")
        return False

def check_packages():
    """فحص الحزم المطلوبة"""
    print("\n🔍 فحص الحزم المطلوبة:")
    
    packages = [
        ('flask', 'Flask'),
        ('flask_sqlalchemy', 'Flask-SQLAlchemy'),
        ('flask_login', 'Flask-Login'),
        ('flask_wtf', 'Flask-WTF'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('arabic_reshaper', 'arabic-reshaper'),
        ('bidi', 'python-bidi'),
        ('wtforms', 'WTForms'),
        ('werkzeug', 'Werkzeug')
    ]
    
    missing = []
    for package, name in packages:
        try:
            __import__(package)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name}")
            missing.append(name)
    
    return len(missing) == 0, missing

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n🔍 فحص الملفات:")
    
    required_files = [
        'app.py',
        'person_data_routes.py',
        'reports_generator.py'
    ]
    
    optional_files = [
        'تشغيل_النظام_المحدث.py',
        'تشغيل_سريع.py',
        'run_server.py'
    ]
    
    missing = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
            missing.append(file)
    
    print("\n   ملفات التشغيل:")
    for file in optional_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
    
    return len(missing) == 0

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🔍 فحص قاعدة البيانات:")
    
    db_files = ['training_system.db', 'instance/training_system.db']
    db_found = None
    
    for db_file in db_files:
        if os.path.exists(db_file):
            db_found = db_file
            print(f"   ✅ قاعدة البيانات: {db_file}")
            break
    
    if not db_found:
        print("   ⚠️ لم يتم العثور على قاعدة البيانات (سيتم إنشاؤها تلقائياً)")
        return True
    
    # فحص محتويات قاعدة البيانات
    try:
        conn = sqlite3.connect(db_found)
        cursor = conn.cursor()
        
        # فحص الجداول الرئيسية
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        important_tables = ['user', 'course', 'person_data', 'enrollment']
        
        print("   الجداول الموجودة:")
        for table in important_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"     ✅ {table}: {count} سجل")
            else:
                print(f"     ❌ {table}: غير موجود")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def check_directories():
    """فحص المجلدات المطلوبة"""
    print("\n🔍 فحص المجلدات:")
    
    directories = [
        'templates',
        'static',
        'static/css',
        'static/js',
        'static/uploads'
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            files_count = len(os.listdir(directory)) if os.path.isdir(directory) else 0
            print(f"   ✅ {directory} ({files_count} ملف)")
        else:
            print(f"   ❌ {directory}")

def test_import():
    """اختبار استيراد التطبيق"""
    print("\n🔍 اختبار استيراد التطبيق:")
    
    try:
        from app import app, db
        print("   ✅ تم استيراد التطبيق بنجاح")
        
        # اختبار إنشاء context
        with app.app_context():
            print("   ✅ تم إنشاء application context")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استيراد التطبيق: {e}")
        return False

def main():
    print("=" * 70)
    print("🔍 فحص شامل لنظام التدريب والتأهيل")
    print("=" * 70)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # تشغيل جميع الفحوصات
    checks = []
    
    checks.append(("Python", check_python()))
    
    packages_ok, missing_packages = check_packages()
    checks.append(("الحزم المطلوبة", packages_ok))
    
    checks.append(("الملفات المطلوبة", check_files()))
    checks.append(("قاعدة البيانات", check_database()))
    
    check_directories()
    
    checks.append(("استيراد التطبيق", test_import()))
    
    # ملخص النتائج
    print("\n" + "=" * 70)
    print("📋 ملخص الفحص:")
    print("=" * 70)
    
    all_ok = True
    for check_name, result in checks:
        status = "✅ جاهز" if result else "❌ يحتاج إصلاح"
        print(f"   {check_name}: {status}")
        if not result:
            all_ok = False
    
    print("\n" + "=" * 70)
    if all_ok:
        print("🎉 النظام جاهز للتشغيل!")
        print("💡 يمكنك تشغيل النظام باستخدام:")
        print("   python تشغيل_النظام_المحدث.py")
    else:
        print("⚠️ النظام يحتاج إلى إصلاحات")
        if not packages_ok and missing_packages:
            print("💡 لتثبيت الحزم المفقودة:")
            print(f"   pip install {' '.join(missing_packages)}")
    
    print("=" * 70)

if __name__ == '__main__':
    main()
    input("\nاضغط Enter للخروج...")
