# نظام التدريب والتأهيل

نظام متكامل لإدارة عمليات التدريب والتأهيل، مبني باستخدام بايثون وإطار العمل Flask.

## المميزات

- واجهة مستخدم جذابة وسهلة الاستخدام
- نظام تسجيل دخول آمن
- إدارة المستخدمين (المتدربين، المدربين، المشرفين)
- إدارة الدورات التدريبية
- جدولة الدورات والمواعيد
- إصدار الشهادات
- تقارير وإحصائيات
- يعمل على شبكة من الأجهزة

## متطلبات النظام

- Python 3.6+
- Flask
- Flask-SQLAlchemy
- Flask-Login
- Flask-WTF
- Flask-Migrate
- Pillow
- email-validator

## طريقة التثبيت

1. قم بإنشاء بيئة افتراضية:
   ```
   python -m venv venv
   ```

2. قم بتفعيل البيئة الافتراضية:
   - في نظام Windows:
     ```
     venv\Scripts\activate
     ```
   - في نظام Linux/Mac:
     ```
     source venv/bin/activate
     ```

3. قم بتثبيت المكتبات المطلوبة:
   ```
   pip install -r requirements.txt
   ```

4. قم بتشغيل التطبيق:
   ```
   python app.py
   ```

5. افتح المتصفح وانتقل إلى العنوان:
   ```
   http://localhost:5000
   ```

## بيانات تسجيل الدخول الافتراضية

- البريد الإلكتروني: <EMAIL>
- كلمة المرور: admin123

## هيكل المشروع

```
training-system/
│
├── app.py                  # ملف التطبيق الرئيسي
├── requirements.txt        # قائمة المكتبات المطلوبة
│
├── static/                 # الملفات الثابتة
│   ├── css/                # ملفات CSS
│   ├── js/                 # ملفات JavaScript
│   └── img/                # الصور
│
└── templates/              # قوالب HTML
    ├── layout.html         # القالب الرئيسي
    ├── index.html          # الصفحة الرئيسية
    ├── login.html          # صفحة تسجيل الدخول
    └── dashboard.html      # لوحة التحكم
```

## تطوير النظام

1. قم بإنشاء فرع جديد لكل ميزة:
   ```
   git checkout -b feature/new-feature
   ```

2. قم بإجراء التغييرات وإضافتها:
   ```
   git add .
   git commit -m "إضافة ميزة جديدة"
   ```

3. قم بدمج التغييرات مع الفرع الرئيسي:
   ```
   git checkout main
   git merge feature/new-feature
   ```

## المساهمة

نرحب بمساهماتكم في تطوير هذا النظام. يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد للميزة (`git checkout -b feature/amazing-feature`)
3. قم بإجراء التغييرات وإضافتها (`git commit -m 'إضافة ميزة رائعة'`)
4. قم بدفع التغييرات إلى الفرع (`git push origin feature/amazing-feature`)
5. قم بفتح طلب دمج (Pull Request)

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.
