{% extends "layout.html" %}

{% block styles %}
<style>
    .table-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .table-title {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .search-section {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e3f2fd;
    }

    .search-form {
        display: flex;
        gap: 15px;
        align-items: center;
    }

    .search-input {
        flex: 1;
        padding: 12px 20px;
        border: 2px solid #e0e0e0;
        border-radius: 25px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

    .search-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn-search {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        padding: 12px 25px;
        border-radius: 25px;
        color: white;
        font-weight: bold;
        transition: transform 0.3s ease;
    }

    .btn-search:hover {
        transform: translateY(-2px);
        color: white;
    }

    .data-table-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e3f2fd;
        overflow-x: auto;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 0;
    }

    .data-table th {
        background: linear-gradient(135deg, #f8f9ff, #e3f2fd);
        color: #333;
        font-weight: bold;
        padding: 15px 12px;
        text-align: center;
        border-bottom: 2px solid #667eea;
        white-space: nowrap;
    }

    .data-table td {
        padding: 12px;
        text-align: center;
        border-bottom: 1px solid #f0f0f0;
        vertical-align: middle;
    }

    .data-table tbody tr {
        transition: background-color 0.3s ease;
    }

    .data-table tbody tr:hover {
        background-color: #f8f9ff;
    }

    .data-table tbody tr:nth-child(even) {
        background-color: #fafbff;
    }

    .person-name {
        font-weight: bold;
        color: #333;
        text-align: right;
    }

    .person-details {
        color: #666;
        font-size: 0.9rem;
        text-align: right;
    }

    .badge-custom {
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .badge-governorate {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .badge-qualification {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
    }

    .stats-summary {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e3f2fd;
        text-align: center;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
    }

    .empty-state {
        text-align: center;
        padding: 50px;
        color: #666;
    }

    .empty-state i {
        font-size: 4rem;
        color: #ddd;
        margin-bottom: 20px;
    }

    .action-buttons {
        text-align: center;
        margin-top: 25px;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        padding: 12px 25px;
        border-radius: 25px;
        color: white;
        font-weight: bold;
        text-decoration: none;
        display: inline-block;
        margin: 0 10px;
        transition: transform 0.3s ease;
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="table-header">
        <div class="table-title">
            <i class="fas fa-table me-3"></i>جدول الملتحقين
        </div>
        <div>عرض مفصل لجميع الأشخاص المسجلين في النظام</div>
    </div>

    <!-- Search Section -->
    <div class="search-section">
        <form method="GET" class="search-form">
            <input type="text" name="search" class="search-input"
                   placeholder="🔍 البحث بالاسم، الهاتف، الرقم الوطني، أو المحافظة..."
                   value="{{ search }}">
            <button type="submit" class="btn btn-search">
                <i class="fas fa-search me-2"></i>بحث
            </button>
            {% if search %}
            <a href="{{ url_for('personal_data_table') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
            {% endif %}
        </form>
    </div>

    <!-- Statistics Summary -->
    <div class="stats-summary">
        <div class="row">
            <div class="col-md-4">
                <div class="stats-number">{{ persons.total }}</div>
                <div>إجمالي النتائج</div>
            </div>
            <div class="col-md-4">
                <div class="stats-number">{{ persons.pages }}</div>
                <div>عدد الصفحات</div>
            </div>
            <div class="col-md-4">
                <div class="stats-number">{{ persons.page }}</div>
                <div>الصفحة الحالية</div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="data-table-container">
        {% if persons.items %}
        <table class="data-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>الاسم الكامل</th>
                    <th>المحافظة</th>
                    <th>المديرية</th>
                    <th>المؤهل العلمي</th>
                    <th>العمل</th>
                    <th>رقم الهاتف</th>
                    <th>الرقم الوطني</th>
                </tr>
            </thead>
            <tbody>
                {% for person in persons.items %}
                <tr>
                    <td>{{ (persons.page - 1) * persons.per_page + loop.index }}</td>
                    <td class="person-name">{{ person.full_name }}</td>
                    <td>
                        {% if person.governorate %}
                        <span class="badge badge-governorate">{{ person.governorate }}</span>
                        {% else %}
                        <span class="text-muted">غير محدد</span>
                        {% endif %}
                    </td>
                    <td class="person-details">{{ person.directorate or 'غير محدد' }}</td>
                    <td>
                        {% if person.qualification %}
                        <span class="badge badge-qualification">{{ person.qualification }}</span>
                        {% else %}
                        <span class="text-muted">غير محدد</span>
                        {% endif %}
                    </td>
                    <td class="person-details">{{ person.job or 'غير محدد' }}</td>
                    <td class="person-details">
                        {% if person.phone %}
                        <i class="fas fa-phone me-1"></i>{{ person.phone }}
                        {% else %}
                        <span class="text-muted">غير متوفر</span>
                        {% endif %}
                    </td>
                    <td class="person-details">{{ person.national_number or 'غير متوفر' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-search"></i>
            <h4>لا توجد نتائج</h4>
            {% if search %}
            <p>لم يتم العثور على أي نتائج للبحث عن "{{ search }}"</p>
            <a href="{{ url_for('personal_data_table') }}" class="btn-primary-custom">
                <i class="fas fa-list me-2"></i>عرض جميع البيانات
            </a>
            {% else %}
            <p>لا يوجد أشخاص مسجلين في النظام</p>
            <a href="{{ url_for('person_data.name_analysis') }}" class="btn-primary-custom">
                <i class="fas fa-plus me-2"></i>إضافة أشخاص جدد
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{{ url_for('personal_data_list') }}" class="btn-primary-custom">
            <i class="fas fa-chart-bar me-2"></i>عرض الإحصائيات
        </a>
        <a href="{{ url_for('person_data.name_analysis') }}" class="btn-primary-custom">
            <i class="fas fa-plus me-2"></i>إضافة ملتحقين جدد
        </a>
        <a href="{{ url_for('personal_data_excel') }}" class="btn-primary-custom">
            <i class="fas fa-file-excel me-2"></i>إدارة بالإكسل
        </a>
    </div>
</div>
{% endblock %}
