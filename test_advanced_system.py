#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار النظام المتقدم لإدارة المستخدمين والأدوار
"""

import requests
import re
import json

def test_advanced_system():
    print("🧪 اختبار النظام المتقدم لإدارة المستخدمين والأدوار")
    print("=" * 70)
    
    session = requests.Session()
    
    try:
        # 1. اختبار تسجيل الدخول
        print("1️⃣ اختبار تسجيل الدخول...")
        
        login_page = session.get('http://localhost:5000/login')
        csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
        csrf_token = csrf_match.group(1) if csrf_match else None
        
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123'
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token
        
        response = session.post('http://localhost:5000/login', data=login_data)
        
        if 'dashboard' in response.url:
            print("   ✅ تم تسجيل الدخول بنجاح")
        else:
            print("   ❌ فشل تسجيل الدخول")
            return False
        
        # 2. اختبار الوصول للصفحة المتقدمة
        print("2️⃣ اختبار الوصول للصفحة المتقدمة...")
        
        advanced_page = session.get('http://localhost:5000/admin/advanced-users')
        
        if advanced_page.status_code == 200:
            print("   ✅ تم الوصول للصفحة المتقدمة")
            
            # فحص محتوى الصفحة
            if 'عرض شجرة النظام' in advanced_page.text:
                print("   ✅ كارت شجرة النظام موجود")
            
            if 'إدارة الأدوار' in advanced_page.text:
                print("   ✅ كارت إدارة الأدوار موجود")
            
            if 'إدارة المستخدمين' in advanced_page.text:
                print("   ✅ كارت إدارة المستخدمين موجود")
        else:
            print(f"   ❌ فشل الوصول للصفحة: {advanced_page.status_code}")
            return False
        
        # 3. اختبار API شجرة النظام
        print("3️⃣ اختبار API شجرة النظام...")
        
        tree_response = session.get('http://localhost:5000/api/system-tree')
        
        if tree_response.status_code == 200:
            tree_data = tree_response.json()
            print(f"   ✅ تم الحصول على شجرة النظام ({len(tree_data)} وحدة)")
            
            # فحص بعض الوحدات المهمة
            expected_modules = ['dashboard', 'users_management', 'courses_management', 'reports']
            for module in expected_modules:
                if module in tree_data:
                    print(f"      ✅ وحدة {module} موجودة")
                else:
                    print(f"      ❌ وحدة {module} مفقودة")
        else:
            print(f"   ❌ فشل في الحصول على شجرة النظام: {tree_response.status_code}")
        
        # 4. اختبار API الأدوار
        print("4️⃣ اختبار API الأدوار...")
        
        roles_response = session.get('http://localhost:5000/api/roles')
        
        if roles_response.status_code == 200:
            roles_data = roles_response.json()
            print(f"   ✅ تم الحصول على الأدوار ({len(roles_data)} دور)")
            
            for role in roles_data:
                print(f"      📋 {role['display_name']} ({role['name']})")
        else:
            print(f"   ❌ فشل في الحصول على الأدوار: {roles_response.status_code}")
        
        # 5. اختبار إنشاء دور جديد
        print("5️⃣ اختبار إنشاء دور جديد...")
        
        import time
        new_role_data = {
            'name': f'test_role_{int(time.time())}',
            'description': 'دور اختبار تم إنشاؤه تلقائياً',
            'permissions': [
                'dashboard.view',
                'courses_management.view',
                'reports.view'
            ]
        }
        
        create_role_response = session.post(
            'http://localhost:5000/api/roles',
            json=new_role_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if create_role_response.status_code == 200:
            result = create_role_response.json()
            if result.get('success'):
                print(f"   ✅ تم إنشاء الدور: {new_role_data['name']}")
            else:
                print(f"   ❌ فشل إنشاء الدور: {result.get('message')}")
        else:
            print(f"   ❌ خطأ في إنشاء الدور: {create_role_response.status_code}")
        
        # 6. اختبار إنشاء مستخدم جديد
        print("6️⃣ اختبار إنشاء مستخدم جديد...")
        
        # الحصول على الأدوار أولاً
        roles_response = session.get('http://localhost:5000/api/roles')
        if roles_response.status_code == 200:
            roles = roles_response.json()
            if roles:
                first_role_id = roles[0]['id']
                
                new_user_data = {
                    'username': f'test_user_{int(time.time())}',
                    'email': f'test_{int(time.time())}@test.com',
                    'password': 'test123',
                    'role_id': first_role_id,
                    'first_name': 'مستخدم',
                    'last_name': 'اختبار',
                    'department': 'قسم الاختبار',
                    'is_active': True
                }
                
                create_user_response = session.post(
                    'http://localhost:5000/api/users',
                    json=new_user_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                if create_user_response.status_code == 200:
                    result = create_user_response.json()
                    if result.get('success'):
                        print(f"   ✅ تم إنشاء المستخدم: {new_user_data['username']}")
                    else:
                        print(f"   ❌ فشل إنشاء المستخدم: {result.get('message')}")
                else:
                    print(f"   ❌ خطأ في إنشاء المستخدم: {create_user_response.status_code}")
            else:
                print("   ⚠️ لا توجد أدوار متاحة لإنشاء المستخدم")
        
        print("\n" + "="*70)
        print("📊 ملخص الاختبار:")
        print("✅ النظام المتقدم يعمل بشكل ممتاز!")
        print("✅ جميع APIs تعمل بشكل صحيح")
        print("✅ إنشاء الأدوار والمستخدمين يعمل")
        
        print("\n🔗 روابط مهمة:")
        print("   النظام المتقدم: http://localhost:5000/admin/advanced-users")
        print("   النظام العادي: http://localhost:5000/admin/users")
        print("   تسجيل الدخول: <EMAIL> / admin123")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_system_tree_structure():
    """اختبار هيكل شجرة النظام"""
    print("\n🌳 اختبار هيكل شجرة النظام...")
    
    from system_tree_manager import SystemTreeManager
    
    tree = SystemTreeManager.get_system_tree()
    permissions = SystemTreeManager.get_all_permissions()
    
    print(f"📊 إحصائيات شجرة النظام:")
    print(f"   الوحدات الرئيسية: {len(tree)}")
    print(f"   إجمالي الصلاحيات: {len(permissions)}")
    
    print(f"\n📁 الوحدات الرئيسية:")
    for module_key, module_data in tree.items():
        permissions_count = len(module_data.get('permissions', {}))
        sub_modules_count = len(module_data.get('sub_modules', {}))
        
        print(f"   📂 {module_data['name']} ({module_key})")
        print(f"      🔘 الصلاحيات: {permissions_count}")
        print(f"      📁 الوحدات الفرعية: {sub_modules_count}")
        
        if sub_modules_count > 0:
            for sub_key, sub_data in module_data['sub_modules'].items():
                sub_permissions = len(sub_data.get('permissions', {}))
                print(f"         📂 {sub_data['name']} ({sub_key}) - {sub_permissions} صلاحية")

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من تشغيل الخادم
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code != 200:
            print("❌ الخادم غير متاح")
            return
    except:
        print("❌ لا يمكن الوصول للخادم على localhost:5000")
        return
    
    # اختبار النظام
    success = test_advanced_system()
    
    # اختبار هيكل شجرة النظام
    test_system_tree_structure()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت! النظام المتقدم جاهز للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت.")

if __name__ == '__main__':
    main()
