#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_tables():
    conn = sqlite3.connect('training_system.db')
    cursor = conn.cursor()
    
    # فحص جدول user_session
    try:
        cursor.execute('SELECT COUNT(*) FROM user_session')
        count = cursor.fetchone()[0]
        print(f'جدول user_session موجود: {count} سجل')
    except Exception as e:
        print(f'جدول user_session غير موجود: {e}')
    
    # فحص جميع الجداول
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    print(f'الجداول الموجودة: {tables}')
    
    conn.close()

if __name__ == '__main__':
    check_tables()
