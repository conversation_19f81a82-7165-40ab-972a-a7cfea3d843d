from app import app, db, PersonalData, User
import json
import random
import string

def generate_random_national_number():
    """
    توليد رقم وطني عشوائي
    """
    return ''.join(random.choice(string.digits) for _ in range(10))

def test_add_person_direct():
    """
    اختبار إضافة شخص جديد مباشرة إلى قاعدة البيانات
    """
    # إنشاء سياق التطبيق
    with app.app_context():
        try:
            # توليد رقم وطني عشوائي
            random_national_number = generate_random_national_number()

            # معالجة القيم النصية "None"
            military_number = "None"
            if military_number == "None" or military_number == "none":
                military_number = None

            work_place = "None"
            if work_place == "None" or work_place == "none":
                work_place = None

            # إنشاء كائن PersonalData جديد
            new_person = PersonalData(
                user_id=1,  # افتراض أن معرف المستخدم هو 1
                full_name="اختبار مباشر",
                national_number=random_national_number,
                military_number=military_number,
                nickname="اختبار",
                age="35",
                work_number="",
                job_title="مبرمج",
                work_place=work_place,
                work_rank="",
                phone_yemen_mobile="777987654"
            )

            # إضافة الكائن إلى قاعدة البيانات
            db.session.add(new_person)
            db.session.commit()

            print(f"تم إضافة الشخص بنجاح: {new_person.full_name}, الرقم الوطني: {new_person.national_number}")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"حدث خطأ أثناء إضافة الشخص: {str(e)}")
            return False

def test_add_person_api():
    """
    اختبار إضافة شخص جديد باستخدام واجهة API
    """
    # إنشاء سياق التطبيق
    with app.app_context():
        # توليد رقم وطني عشوائي
        random_national_number = generate_random_national_number()

        # إنشاء بيانات الطلب
        data = {
            'full_name': 'اختبار API',
            'nickname': 'اختبار',
            'age': '35',
            'national_number': random_national_number,
            'military_number': 'None',  # قيمة نصية "None" بدلاً من None
            'job': 'مبرمج',
            'work_place': 'None',  # قيمة نصية "None" بدلاً من None
            'phone': '777987654',
            'work_number': '',
            'work_rank': ''
        }

        # طباعة البيانات المرسلة
        print("البيانات المرسلة:")
        print(json.dumps(data, indent=2, ensure_ascii=False))

        try:
            # معالجة القيم النصية "None"
            military_number = data.get('military_number')
            if military_number == "None" or military_number == "none":
                military_number = None

            work_place = data.get('work_place')
            if work_place == "None" or work_place == "none":
                work_place = None

            # إنشاء كائن PersonalData جديد
            new_person = PersonalData(
                user_id=1,  # افتراض أن معرف المستخدم هو 1
                full_name=data.get('full_name'),
                national_number=data.get('national_number'),
                military_number=military_number,
                nickname=data.get('nickname'),
                age=data.get('age'),
                work_number=data.get('work_number'),
                job_title=data.get('job'),
                work_place=work_place,
                work_rank=data.get('work_rank'),
                phone_yemen_mobile=data.get('phone')
            )

            # إضافة الكائن إلى قاعدة البيانات
            db.session.add(new_person)
            db.session.commit()

            print(f"\nتم إضافة الشخص بنجاح: {new_person.full_name}, الرقم الوطني: {new_person.national_number}")

            # التحقق من وجود الشخص في قاعدة البيانات
            person = PersonalData.query.filter_by(national_number=random_national_number).first()
            if person:
                print(f"\nتم العثور على الشخص في قاعدة البيانات:")
                print(f"الاسم: {person.full_name}")
                print(f"الرقم الوطني: {person.national_number}")
                print(f"الرقم العسكري: {person.military_number}")
                print(f"مكان العمل: {person.work_place}")
            else:
                print("\nلم يتم العثور على الشخص في قاعدة البيانات!")

            return True
        except Exception as e:
            db.session.rollback()
            print(f"\nحدث خطأ أثناء إضافة الشخص: {str(e)}")
            return False

if __name__ == "__main__":
    # تشغيل الاختبار المباشر
    print("=== اختبار الإضافة المباشرة ===")
    direct_success = test_add_person_direct()
    print(f"\nنتيجة اختبار الإضافة المباشرة: {'نجاح' if direct_success else 'فشل'}")

    # تشغيل اختبار API
    print("\n=== اختبار الإضافة عبر API ===")
    api_success = test_add_person_api()
    print(f"\nنتيجة اختبار API: {'نجاح' if api_success else 'فشل'}")
