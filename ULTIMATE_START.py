#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🌟 نظام التدريب والتأهيل - التشغيل النهائي الشامل
Ultimate Training System Launcher
"""

import os
import sys
import time
import subprocess
import webbrowser
import platform
from datetime import datetime
from pathlib import Path
import json

class UltimateSystemLauncher:
    """مشغل النظام النهائي الشامل"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.system_info = self.get_system_info()
        self.requirements_checked = False
        self.enhanced_mode = False
        
    def get_system_info(self):
        """الحصول على معلومات النظام"""
        return {
            'platform': platform.system(),
            'python_version': platform.python_version(),
            'architecture': platform.architecture()[0],
            'processor': platform.processor(),
            'hostname': platform.node(),
            'timestamp': datetime.now().isoformat()
        }
    
    def print_banner(self):
        """طباعة شعار النظام"""
        banner = """
╔══════════════════════════════════════════════════════════════════════╗
║                                                                      ║
║    🌟 نظام التدريب والتأهيل - الإصدار النهائي الشامل 🌟           ║
║                                                                      ║
║    ✨ Ultimate Training & Qualification System ✨                   ║
║                                                                      ║
║    🚀 الميزات:                                                      ║
║    • نظام محمول بالكامل - لا يحتاج تثبيت                           ║
║    • يعمل على جميع أنظمة التشغيل                                   ║
║    • سرعة فائقة مع تحسينات الأداء                                   ║
║    • أمان متقدم وموثوقية عالية                                     ║
║    • شبكة متقدمة لجميع المستخدمين                                  ║
║    • اختبارات شاملة ومراقبة مستمرة                                 ║
║    • نسخ احتياطية ذكية                                             ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝
"""
        print(banner)
        
        print(f"🐍 Python: {self.system_info['python_version']}")
        print(f"💻 النظام: {self.system_info['platform']} {self.system_info['architecture']}")
        print(f"🖥️  المعالج: {self.system_info['processor']}")
        print(f"🌐 اسم الجهاز: {self.system_info['hostname']}")
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
    
    def check_python_version(self):
        """فحص إصدار Python"""
        print("🔍 فحص إصدار Python...")
        
        if sys.version_info < (3, 8):
            print("❌ يتطلب Python 3.8 أو أحدث")
            print(f"   الإصدار الحالي: {sys.version}")
            return False
        
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        return True
    
    def check_requirements(self):
        """فحص وتثبيت المتطلبات"""
        if self.requirements_checked:
            return True
        
        print("📦 فحص المتطلبات...")
        
        # قائمة المكتبات الأساسية
        basic_requirements = [
            'flask',
            'flask_sqlalchemy',
            'flask_login',
            'flask_wtf',
            'werkzeug'
        ]
        
        # قائمة المكتبات المحسنة
        enhanced_requirements = [
            'pandas',
            'openpyxl',
            'requests',
            'psutil'
        ]
        
        missing_basic = []
        missing_enhanced = []
        
        # فحص المكتبات الأساسية
        for lib in basic_requirements:
            try:
                __import__(lib)
                print(f"   ✅ {lib}")
            except ImportError:
                missing_basic.append(lib)
                print(f"   ❌ {lib}")
        
        # فحص المكتبات المحسنة
        for lib in enhanced_requirements:
            try:
                __import__(lib)
                print(f"   ✅ {lib}")
            except ImportError:
                missing_enhanced.append(lib)
                print(f"   ⚠️ {lib} (محسن)")
        
        # تثبيت المكتبات المفقودة
        if missing_basic:
            print(f"\n📥 تثبيت المكتبات الأساسية المفقودة: {missing_basic}")
            if not self.install_packages(missing_basic):
                return False
        
        if missing_enhanced:
            print(f"\n📥 تثبيت المكتبات المحسنة المفقودة: {missing_enhanced}")
            self.enhanced_mode = self.install_packages(missing_enhanced, optional=True)
        else:
            self.enhanced_mode = True
        
        self.requirements_checked = True
        return True
    
    def install_packages(self, packages, optional=False):
        """تثبيت الحزم"""
        try:
            for package in packages:
                print(f"   📦 تثبيت {package}...")
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", package],
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 دقائق لكل حزمة
                )
                
                if result.returncode == 0:
                    print(f"   ✅ تم تثبيت {package}")
                else:
                    print(f"   ❌ فشل في تثبيت {package}")
                    if not optional:
                        return False
            
            return True
            
        except subprocess.TimeoutExpired:
            print("   ⏰ انتهت مهلة التثبيت")
            return optional
        except Exception as e:
            print(f"   ❌ خطأ في التثبيت: {e}")
            return optional
    
    def check_database(self):
        """فحص قاعدة البيانات"""
        print("💾 فحص قاعدة البيانات...")
        
        db_files = [
            'training_system.db',
            'instance/training_system.db'
        ]
        
        for db_file in db_files:
            if os.path.exists(db_file):
                size = os.path.getsize(db_file) / 1024  # KB
                print(f"   ✅ {db_file} ({size:.1f} KB)")
                return True
        
        print("   ⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها")
        return True
    
    def check_files(self):
        """فحص الملفات الأساسية"""
        print("📁 فحص الملفات الأساسية...")
        
        essential_files = [
            'app.py',
            'templates',
            'static'
        ]
        
        all_exist = True
        for file_path in essential_files:
            if os.path.exists(file_path):
                print(f"   ✅ {file_path}")
            else:
                print(f"   ❌ {file_path}")
                all_exist = False
        
        return all_exist
    
    def run_system_test(self):
        """تشغيل اختبار سريع للنظام"""
        print("🧪 تشغيل اختبار سريع...")
        
        try:
            if self.enhanced_mode:
                from testing_system import run_quick_test
                results = run_quick_test()
                
                passed = results.get('passed', 0)
                failed = results.get('failed', 0)
                
                print(f"   ✅ نجح: {passed}")
                print(f"   ❌ فشل: {failed}")
                
                if failed == 0:
                    print("   🎉 جميع الاختبارات نجحت!")
                    return True
                else:
                    print("   ⚠️ بعض الاختبارات فشلت")
                    return True  # نستمر حتى لو فشلت بعض الاختبارات
            else:
                print("   ⚠️ الاختبارات المحسنة غير متاحة")
                return True
                
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار: {e}")
            return True  # نستمر رغم فشل الاختبار
    
    def launch_system(self):
        """تشغيل النظام"""
        print("🚀 تشغيل النظام...")
        
        try:
            if self.enhanced_mode and os.path.exists('enhanced_app.py'):
                print("   ⚡ تشغيل النظام المحسن...")
                from enhanced_app import main as enhanced_main
                enhanced_main()
            else:
                print("   🔧 تشغيل النظام الأساسي...")
                from app import app
                
                print("✅ النظام جاهز!")
                print("🌐 http://localhost:5000")
                print("🔑 <EMAIL> / admin123")
                print("=" * 50)
                
                # فتح المتصفح بعد 3 ثوان
                def open_browser():
                    time.sleep(3)
                    try:
                        webbrowser.open('http://localhost:5000')
                    except:
                        pass
                
                import threading
                threading.Thread(target=open_browser, daemon=True).start()
                
                app.run(
                    host='0.0.0.0',
                    port=5000,
                    debug=False,
                    threaded=True,
                    use_reloader=False
                )
                
        except KeyboardInterrupt:
            print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل النظام: {e}")
            print("🔄 محاولة التشغيل الطارئ...")
            self.emergency_launch()
    
    def emergency_launch(self):
        """تشغيل طارئ"""
        try:
            print("🆘 تشغيل طارئ للنظام الأساسي...")
            
            # تشغيل مباشر لـ app.py
            subprocess.run([sys.executable, "app.py"], check=True)
            
        except Exception as e:
            print(f"❌ فشل في التشغيل الطارئ: {e}")
            print("\n📞 للدعم الفني:")
            print("   1. تأكد من تثبيت Python 3.8+")
            print("   2. شغل: pip install flask flask-sqlalchemy flask-login")
            print("   3. شغل: python app.py")
    
    def run(self):
        """تشغيل المشغل الشامل"""
        self.print_banner()
        
        print("🔄 بدء فحص النظام الشامل...")
        print("=" * 50)
        
        # 1. فحص Python
        if not self.check_python_version():
            input("اضغط Enter للخروج...")
            return
        
        # 2. فحص المتطلبات
        if not self.check_requirements():
            print("❌ فشل في تثبيت المتطلبات الأساسية")
            input("اضغط Enter للخروج...")
            return
        
        # 3. فحص الملفات
        if not self.check_files():
            print("❌ ملفات أساسية مفقودة")
            input("اضغط Enter للخروج...")
            return
        
        # 4. فحص قاعدة البيانات
        self.check_database()
        
        # 5. اختبار سريع
        self.run_system_test()
        
        print("\n" + "=" * 50)
        print("✅ جميع الفحوصات اكتملت بنجاح!")
        
        if self.enhanced_mode:
            print("⚡ الوضع المحسن مفعل - جميع الميزات متاحة")
        else:
            print("🔧 الوضع الأساسي - الميزات الأساسية متاحة")
        
        print("=" * 50)
        
        # 6. تشغيل النظام
        self.launch_system()

def main():
    """الدالة الرئيسية"""
    try:
        launcher = UltimateSystemLauncher()
        launcher.run()
    except Exception as e:
        print(f"❌ خطأ في المشغل: {e}")
        print("🔄 محاولة التشغيل المباشر...")
        
        try:
            from app import app
            app.run(host='0.0.0.0', port=5000, debug=False)
        except Exception as direct_error:
            print(f"❌ فشل في التشغيل المباشر: {direct_error}")
            input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
