{% extends "layout.html" %}

{% block styles %}
<style>
    .course-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .course-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        color: #ffcc00;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    }

    .course-title {
        font-size: 1.8rem;
        margin-bottom: 10px;
    }

    .course-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-top: 15px;
    }

    .course-info-item {
        background-color: rgba(255, 255, 255, 0.2);
        padding: 8px 15px;
        border-radius: 10px;
        font-size: 0.9rem;
    }

    .course-info-item i {
        margin-left: 5px;
    }

    .form-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }

    .form-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: bold;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4a6bff;
        box-shadow: 0 0 0 0.25rem rgba(74, 107, 255, 0.25);
    }

    .form-text {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .btn-submit {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
    }

    .btn-cancel {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-cancel:hover {
        transform: translateY(-2px);
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
    }

    .form-section-title {
        font-weight: bold;
        margin-bottom: 15px;
        color: #4a6bff;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 10px;
    }

    .form-check-input {
        margin-left: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="course-header">
        <div class="course-number">{{ course.course_number }}</div>
        <div class="course-title">{{ course.title }}</div>
        <div class="course-info">
            <div class="course-info-item"><i class="fas fa-building"></i> {{ course.agency or 'غير محدد' }}</div>
            <div class="course-info-item"><i class="fas fa-map-marker-alt"></i> {{ course.center_name or 'غير محدد' }}</div>
            <div class="course-info-item"><i class="fas fa-users"></i> {{ course.participant_type or 'غير محدد' }}</div>
            <div class="course-info-item"><i class="fas fa-calendar-alt"></i> {{ course.start_date.strftime('%Y-%m-%d') }} إلى {{ course.end_date.strftime('%Y-%m-%d') }}</div>
            <div class="course-info-item"><i class="fas fa-clock"></i> {{ course.duration_days }} يوم</div>
        </div>
    </div>

    <div class="form-card">
        <div class="form-header">
            <h4><i class="fas fa-plus-circle me-2"></i> إضافة مشارك جديد</h4>
            <p class="text-muted">أدخل بيانات المشارك في الدورة</p>
        </div>

        <form method="POST">
            {{ form.hidden_tag() }}

            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-user me-2"></i> بيانات المشارك
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            {{ form.personal_data_id.label(class="form-label") }}
                            {% if form.personal_data_id.errors %}
                                {{ form.personal_data_id(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.personal_data_id.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.personal_data_id(class="form-select") }}
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.entry_date.label(class="form-label") }}
                            {% if form.entry_date.errors %}
                                {{ form.entry_date(class="form-control is-invalid", type="date") }}
                                <div class="invalid-feedback">
                                    {% for error in form.entry_date.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.entry_date(class="form-control", type="date") }}
                            {% endif %}
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.status.label(class="form-label") }}
                            {% if form.status.errors %}
                                {{ form.status(class="form-select is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.status.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.status(class="form-select") }}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-sticky-note me-2"></i> ملاحظات
                </div>

                <div class="form-group">
                    {{ form.notes.label(class="form-label") }}
                    {% if form.notes.errors %}
                        {{ form.notes(class="form-control is-invalid", rows=5) }}
                        <div class="invalid-feedback">
                            {% for error in form.notes.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.notes(class="form-control", rows=5, placeholder="أدخل أي ملاحظات إضافية") }}
                    {% endif %}
                </div>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <a href="{{ url_for('course_participants', course_id=course.id) }}" class="btn btn-secondary btn-cancel">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
                {{ form.submit(class="btn btn-primary btn-submit") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
