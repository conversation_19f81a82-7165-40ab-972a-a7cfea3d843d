{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap" rel="stylesheet">
<style>
    body, h1, h2, h3, h4, h5, h6, p, span, div, small, strong, .card-body, .stat-label, .stat-number, .btn, .badge, .alert {
        font-family: 'Cairo', sans-serif !important;
    }
    .stats-card {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 25px 20px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 900;
        margin: 15px 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        color: #ffffff;
        line-height: 1;
    }

    .stat-label {
        font-size: 1.1rem;
        font-weight: 600;
        opacity: 0.95;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        color: #f8f9fa;
        line-height: 1.3;
    }

    .result-section {
        background: white;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .section-header {
        background: #6c757d;
        color: white;
        padding: 15px 20px;
        margin-bottom: 0;
        font-weight: 600;
    }

    .name-item {
        background: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 10px;
        border-right: 4px solid #28a745;
    }

    .corrected-name {
        border-right-color: #ffc107;
        background: #fff3cd;
    }

    .similar-name {
        border-right-color: #17a2b8;
        background: #d1ecf1;
    }

    .new-name {
        border-right-color: #dc3545;
        background: #f8d7da;
    }

    .btn-export {
        background: #28a745;
        border: none;
        padding: 10px 20px;
        color: white;
        border-radius: 5px;
    }

    .btn-export:hover {
        background: #218838;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="text-primary">
                        <i class="fas fa-database"></i> نتائج تحليل البيانات الشخصية
                    </h1>
                    <p class="text-muted">
                        <i class="fas fa-file-excel"></i> الملف: {{ excel_filename }}
                    </p>
                </div>
                    <div>
                        <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-export me-2">
                            <i class="fas fa-download"></i> تصدير النتائج الكاملة
                        </a>
                        <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-success me-2">
                            <i class="fas fa-file-excel"></i> تصدير الأسماء الجديدة فقط
                        </a>
                        {% if results.new_records or results.corrected_names %}
                        <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-warning me-2"
                                    onclick="return confirm('هل تريد استيراد البيانات الجديدة إلى قاعدة البيانات؟')">
                                <i class="fas fa-database"></i> إضافة للقاعدة
                            </button>
                        </form>
                        {% endif %}
                        <a href="{{ url_for('person_data.name_analysis') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> تحليل جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number">{{ results.statistics.get('total_processed', 0) }}</div>
                    <div class="stat-label">إجمالي السجلات المعالجة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number">{{ results.statistics.get('corrected_count', 0) }}</div>
                    <div class="stat-label">الأسماء المصححة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number">{{ results.statistics.get('exact_matches_count', 0) }}</div>
                    <div class="stat-label">موجود في قاعدة البيانات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number">{{ results.statistics.get('new_records_count', 0) }}</div>
                    <div class="stat-label">غير موجود في قاعدة البيانات</div>
                </div>
            </div>
        </div>

        <!-- Advanced Duplicate Detection Cards -->
        {% if results.statistics.get('has_national_id_column', False) or results.statistics.get('has_phone_column', False) or results.statistics.get('has_military_id_column', False) %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-search"></i> نتائج فحص التطابق المتقدم
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="card text-center border-danger shadow-sm" style="background: linear-gradient(135deg, #fff5f5, #ffe6e6);">
                                    <div class="card-body py-3">
                                        <h3 class="text-danger fw-bold mb-2" style="font-size: 2.2rem; text-shadow: 1px 1px 2px rgba(220, 53, 69, 0.2);">{{ results.statistics.get('blocked_duplicates_count', 0) }}</h3>
                                        <small class="text-dark fw-semibold d-block" style="font-size: 0.85rem;">سجلات مرفوضة</small>
                                        <small class="text-danger fw-bold" style="font-size: 0.75rem;">تطابق في البيانات</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card text-center border-success shadow-sm" style="background: linear-gradient(135deg, #f0fff4, #e6ffe6);">
                                    <div class="card-body py-3">
                                        <h3 class="text-success fw-bold mb-2" style="font-size: 2.2rem; text-shadow: 1px 1px 2px rgba(40, 167, 69, 0.2);">{{ results.statistics.get('allowed_duplicates_count', 0) }}</h3>
                                        <small class="text-dark fw-semibold d-block" style="font-size: 0.85rem;">أسماء مكررة مسموحة</small>
                                        <small class="text-success fw-bold" style="font-size: 0.75rem;">بيانات مختلفة</small>
                                    </div>
                                </div>
                            </div>
                            {% if results.statistics.get('has_national_id_column', False) %}
                            <div class="col-md-2">
                                <div class="card text-center border-info shadow-sm" style="background: linear-gradient(135deg, #f0f8ff, #e6f3ff);">
                                    <div class="card-body py-3">
                                        <h3 class="text-info fw-bold mb-2" style="font-size: 2.2rem; text-shadow: 1px 1px 2px rgba(23, 162, 184, 0.2);">{{ results.statistics.get('name_national_id_matches', 0) + results.statistics.get('national_id_only_matches', 0) }}</h3>
                                        <small class="text-dark fw-semibold" style="font-size: 0.85rem;">تطابق رقم وطني</small>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            {% if results.statistics.get('has_phone_column', False) %}
                            <div class="col-md-2">
                                <div class="card text-center border-info shadow-sm" style="background: linear-gradient(135deg, #f0f8ff, #e6f3ff);">
                                    <div class="card-body py-3">
                                        <h3 class="text-info fw-bold mb-2" style="font-size: 2.2rem; text-shadow: 1px 1px 2px rgba(23, 162, 184, 0.2);">{{ results.statistics.get('name_phone_matches', 0) + results.statistics.get('phone_only_matches', 0) }}</h3>
                                        <small class="text-dark fw-semibold" style="font-size: 0.85rem;">تطابق رقم هاتف</small>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            {% if results.statistics.get('has_military_id_column', False) %}
                            <div class="col-md-2">
                                <div class="card text-center border-info shadow-sm" style="background: linear-gradient(135deg, #f0f8ff, #e6f3ff);">
                                    <div class="card-body py-3">
                                        <h3 class="text-info fw-bold mb-2" style="font-size: 2.2rem; text-shadow: 1px 1px 2px rgba(23, 162, 184, 0.2);">{{ results.statistics.get('name_military_id_matches', 0) + results.statistics.get('military_id_only_matches', 0) }}</h3>
                                        <small class="text-dark fw-semibold" style="font-size: 0.85rem;">تطابق رقم عسكري</small>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Similarity Statistics -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-center shadow-sm" style="background: linear-gradient(135deg, #e8f4fd, #d1ecf1); border: 2px solid #17a2b8;">
                    <div class="card-body py-3">
                        <h3 class="text-info fw-bold mb-2" style="font-size: 2.2rem; text-shadow: 1px 1px 2px rgba(23, 162, 184, 0.2);">{{ results.statistics.get('triple_similarity_count', 0) }}</h3>
                        <small class="text-dark fw-semibold" style="font-size: 0.85rem;">تشابه ثلاثي</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center shadow-sm" style="background: linear-gradient(135deg, #e8f4fd, #d1ecf1); border: 2px solid #17a2b8;">
                    <div class="card-body py-3">
                        <h3 class="text-info fw-bold mb-2" style="font-size: 2.2rem; text-shadow: 1px 1px 2px rgba(23, 162, 184, 0.2);">{{ results.statistics.get('quadruple_similarity_count', 0) }}</h3>
                        <small class="text-dark fw-semibold" style="font-size: 0.85rem;">تشابه رباعي</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center shadow-sm" style="background: linear-gradient(135deg, #e8f4fd, #d1ecf1); border: 2px solid #17a2b8;">
                    <div class="card-body py-3">
                        <h3 class="text-info fw-bold mb-2" style="font-size: 2.2rem; text-shadow: 1px 1px 2px rgba(23, 162, 184, 0.2);">{{ results.statistics.get('quintuple_similarity_count', 0) }}</h3>
                        <small class="text-dark fw-semibold" style="font-size: 0.85rem;">تشابه خماسي</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center shadow-sm" style="background: linear-gradient(135deg, #e8f4fd, #d1ecf1); border: 2px solid #17a2b8;">
                    <div class="card-body py-3">
                        <h3 class="text-info fw-bold mb-2" style="font-size: 2.2rem; text-shadow: 1px 1px 2px rgba(23, 162, 184, 0.2);">{{ results.statistics.get('full_with_title_count', 0) }}</h3>
                        <small class="text-dark fw-semibold" style="font-size: 0.85rem;">تشابه كامل</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center shadow-sm" style="background: linear-gradient(135deg, #e8f4fd, #d1ecf1); border: 2px solid #17a2b8;">
                    <div class="card-body py-3">
                        <h3 class="text-info fw-bold mb-2" style="font-size: 2.2rem; text-shadow: 1px 1px 2px rgba(23, 162, 184, 0.2);">{{ results.statistics.get('six_plus_count', 0) }}</h3>
                        <small class="text-dark fw-semibold" style="font-size: 0.85rem;">أسماء طويلة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center shadow-sm" style="background: linear-gradient(135deg, #d4edda, #c3e6cb); border: 2px solid #28a745;">
                    <div class="card-body py-3">
                        <h3 class="text-success fw-bold mb-2" style="font-size: 2.2rem; text-shadow: 1px 1px 2px rgba(40, 167, 69, 0.2);">{{ results.total_db_names }}</h3>
                        <small class="text-dark fw-semibold" style="font-size: 0.85rem;">في قاعدة البيانات</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Corrected Names Section -->
        {% if results.corrected_names %}
        <div class="result-section">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-spell-check"></i> الأسماء المصححة
                    <span class="badge bg-warning">{{ results.corrected_names|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="row">
                    {% for correction in results.corrected_names[:10] %}
                    <div class="col-md-6 mb-2">
                        <div class="name-item corrected-name">
                            <strong>الأصلي:</strong> {{ correction.original }}<br>
                            <strong>المصحح:</strong> <span class="text-success">{{ correction.corrected }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if results.corrected_names|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 أسماء فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Exact Matches Section -->
        {% if results.exact_matches %}
        <div class="result-section">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-check-circle"></i> موجود في قاعدة البيانات
                    <span class="badge bg-success">{{ results.exact_matches|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="row">
                    {% for match in results.exact_matches[:10] %}
                    <div class="col-md-12 mb-3">
                        <div class="card border-success">
                            <div class="card-body p-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-success mb-2">
                                            <i class="fas fa-check-circle"></i> من ملف Excel:
                                        </h6>
                                        <p class="mb-1"><strong>{{ match.excel_name }}</strong></p>
                                        <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                                        {% if match.excel_record.national_id %}
                                        <br><small class="badge bg-info">رقم وطني: {{ match.excel_record.national_id }}</small>
                                        {% endif %}
                                        {% if match.excel_record.phone %}
                                        <br><small class="badge bg-success">هاتف: {{ match.excel_record.phone }}</small>
                                        {% endif %}
                                        {% if match.excel_record.military_id %}
                                        <br><small class="badge bg-warning">عسكري: {{ match.excel_record.military_id }}</small>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-primary mb-2">
                                            <i class="fas fa-database"></i> من قاعدة البيانات:
                                        </h6>
                                        <p class="mb-1"><strong>{{ match.db_name }}</strong></p>
                                        <small class="text-muted">ID: {{ match.db_record.id }}</small>
                                        {% if match.db_record.national_id %}
                                        <br><small class="badge bg-info">رقم وطني: {{ match.db_record.national_id }}</small>
                                        {% endif %}
                                        {% if match.db_record.phone %}
                                        <br><small class="badge bg-success">هاتف: {{ match.db_record.phone }}</small>
                                        {% endif %}
                                        {% if match.db_record.military_id %}
                                        <br><small class="badge bg-warning">عسكري: {{ match.db_record.military_id }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if results.exact_matches|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 مطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Blocked Duplicates Section -->
        {% if results.blocked_duplicates %}
        <div class="result-section">
            <div class="section-header" style="background: linear-gradient(45deg, #dc3545, #c82333);">
                <h4 class="mb-0">
                    <i class="fas fa-ban"></i> السجلات المرفوضة (تطابق في البيانات الشخصية)
                    <span class="badge bg-light text-dark">{{ results.blocked_duplicates|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تحذير:</strong> هذه السجلات لن يتم استيرادها بسبب التطابق في البيانات الشخصية
                </div>
                {% for blocked in results.blocked_duplicates[:10] %}
                <div class="card mb-3 border-danger">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">السجل من Excel:</h6>
                                <p><strong>الاسم:</strong> {{ blocked.corrected_name }}</p>
                                {% if blocked.excel_record.national_id %}
                                <p><strong>الرقم الوطني:</strong> {{ blocked.excel_record.national_id }}</p>
                                {% endif %}
                                {% if blocked.excel_record.phone %}
                                <p><strong>الهاتف:</strong> {{ blocked.excel_record.phone }}</p>
                                {% endif %}
                                {% if blocked.excel_record.military_id %}
                                <p><strong>الرقم العسكري:</strong> {{ blocked.excel_record.military_id }}</p>
                                {% endif %}
                                <small class="text-muted">الصف: {{ blocked.excel_record.row_index }}</small>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-warning">السجل المطابق في قاعدة البيانات:</h6>
                                <p><strong>الاسم:</strong> {{ blocked.db_match.name }}</p>
                                {% if blocked.db_match.national_id %}
                                <p><strong>الرقم الوطني:</strong> {{ blocked.db_match.national_id }}</p>
                                {% endif %}
                                {% if blocked.db_match.phone %}
                                <p><strong>الهاتف:</strong> {{ blocked.db_match.phone }}</p>
                                {% endif %}
                                {% if blocked.db_match.military_id %}
                                <p><strong>الرقم العسكري:</strong> {{ blocked.db_match.military_id }}</p>
                                {% endif %}
                                <small class="text-muted">ID: {{ blocked.db_match.id }}</small>
                            </div>
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-danger">{{ blocked.reason }}</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% if results.blocked_duplicates|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 سجلات مرفوضة فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- New Records Section -->
        {% if results.new_records %}
        <div class="result-section">
            <div class="section-header" style="background: linear-gradient(45deg, #28a745, #20c997);">
                <h4 class="mb-0">
                    <i class="fas fa-plus-circle"></i> غير موجود في قاعدة البيانات - سيتم إضافته
                    <span class="badge bg-light text-dark">{{ results.new_records|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="alert alert-success">
                    <i class="fas fa-info-circle"></i>
                    <strong>إجراء:</strong> هذه السجلات جديدة وسيتم إضافتها إلى قاعدة البيانات
                </div>
                {% for record in results.new_records[:10] %}
                <div class="card mb-2 border-success">
                    <div class="card-body p-2">
                        <div class="row">
                            <div class="col-md-8">
                                <strong class="text-success">{{ record.corrected_name }}</strong>
                                <small class="text-muted d-block">الصف: {{ record.row_index }}</small>
                                {% if record.national_id %}
                                <small class="badge bg-info me-1">رقم وطني: {{ record.national_id }}</small>
                                {% endif %}
                                {% if record.phone %}
                                <small class="badge bg-success me-1">هاتف: {{ record.phone }}</small>
                                {% endif %}
                                {% if record.military_id %}
                                <small class="badge bg-warning me-1">عسكري: {{ record.military_id }}</small>
                                {% endif %}
                            </div>
                            <div class="col-md-4 text-end">
                                <span class="badge bg-success">سجل جديد</span>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% if results.new_records|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 سجلات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Similar Names Section -->
        {% if results.similar_names %}
        <div class="result-section">
            <div class="section-header" style="background: linear-gradient(45deg, #17a2b8, #6c757d);">
                <h4 class="mb-0">
                    <i class="fas fa-search"></i> أسماء متشابهة
                    <span class="badge bg-light text-dark">{{ results.similar_names|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> هذه الأسماء متشابهة مع أسماء موجودة في قاعدة البيانات
                </div>
                {% for similar in results.similar_names[:10] %}
                <div class="card mb-3 border-info">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-info">من ملف Excel:</h6>
                                <p><strong>{{ similar.excel_name }}</strong></p>
                                <small class="text-muted">الصف: {{ similar.excel_record.row_index }}</small>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">أسماء متشابهة في قاعدة البيانات:</h6>
                                {% for match in similar.similar_matches[:3] %}
                                <p class="mb-1">
                                    <strong>{{ match.name }}</strong>
                                    <small class="text-muted">(ID: {{ match.id }})</small>
                                    <span class="badge bg-info">{{ similar.similarity_type }}</span>
                                </p>
                                {% endfor %}
                                {% if similar.similar_matches|length > 3 %}
                                <small class="text-muted">و {{ similar.similar_matches|length - 3 }} أسماء أخرى...</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% if results.similar_names|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 أسماء متشابهة فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Advanced Duplicate Matches Sections -->
        {% for match_type, matches in results.duplicate_matches.items() %}
        {% if matches %}
        <div class="result-section">
            <div class="section-header" style="background: linear-gradient(45deg, #fd7e14, #e55a4e);">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    {% if match_type == 'name_national_id' %}
                    تطابق في الاسم والرقم الوطني
                    {% elif match_type == 'name_phone' %}
                    تطابق في الاسم ورقم الهاتف
                    {% elif match_type == 'name_military_id' %}
                    تطابق في الاسم والرقم العسكري
                    {% elif match_type == 'national_id_only' %}
                    تطابق في الرقم الوطني فقط (أسماء مختلفة)
                    {% elif match_type == 'phone_only' %}
                    تطابق في رقم الهاتف فقط (أسماء مختلفة)
                    {% elif match_type == 'military_id_only' %}
                    تطابق في الرقم العسكري فقط (أسماء مختلفة)
                    {% endif %}
                    <span class="badge bg-light text-dark">{{ matches|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i>
                    <strong>تنبيه:</strong> {{ matches[0].match_details if matches }}
                </div>
                {% for match in matches[:10] %}
                <div class="card mb-3 border-warning">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">السجل من Excel:</h6>
                                <p><strong>الاسم:</strong> {{ match.excel_record.corrected_name }}</p>
                                {% if match.excel_record.national_id %}
                                <p><strong>الرقم الوطني:</strong> {{ match.excel_record.national_id }}</p>
                                {% endif %}
                                {% if match.excel_record.phone %}
                                <p><strong>الهاتف:</strong> {{ match.excel_record.phone }}</p>
                                {% endif %}
                                {% if match.excel_record.military_id %}
                                <p><strong>الرقم العسكري:</strong> {{ match.excel_record.military_id }}</p>
                                {% endif %}
                                <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-warning">السجل المطابق في قاعدة البيانات:</h6>
                                <p><strong>الاسم:</strong> {{ match.db_record.name }}</p>
                                {% if match.db_record.national_id %}
                                <p><strong>الرقم الوطني:</strong> {{ match.db_record.national_id }}</p>
                                {% endif %}
                                {% if match.db_record.phone %}
                                <p><strong>الهاتف:</strong> {{ match.db_record.phone }}</p>
                                {% endif %}
                                {% if match.db_record.military_id %}
                                <p><strong>الرقم العسكري:</strong> {{ match.db_record.military_id }}</p>
                                {% endif %}
                                <small class="text-muted">ID: {{ match.db_record.id }}</small>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% if matches|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 مطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}
        {% endfor %}

        <!-- Similarity Sections -->
        {% for similarity_type, similarity_data in results.similarity_matches.items() %}
        {% if similarity_data and similarity_type != 'six_plus' %}
        <div class="result-section">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-layer-group"></i>
                    {% if similarity_type == 'triple' %}التشابه الثلاثي{% endif %}
                    {% if similarity_type == 'quadruple' %}التشابه الرباعي{% endif %}
                    {% if similarity_type == 'quintuple' %}التشابه الخماسي{% endif %}
                    {% if similarity_type == 'full_with_title' %}التشابه الكامل مع اللقب{% endif %}
                    <span class="badge bg-info">{{ similarity_data|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                {% for match in similarity_data[:5] %}
                <div class="name-item similar-name mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>من Excel:</strong> {{ match.excel_name }}<br>
                            <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                        </div>
                        <div class="col-md-6">
                            <strong>متشابه مع:</strong>
                            {% for similar in match.similar_names[:3] %}
                            <br>• {{ similar.name }} <small class="text-muted">(ID: {{ similar.id }})</small>
                            {% endfor %}
                            {% if match.similar_names|length > 3 %}
                            <br><small class="text-info">و {{ match.similar_names|length - 3 }} أسماء أخرى...</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% if similarity_data|length > 5 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 5 مطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}
        {% endfor %}

        <!-- Export and Import Options Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-gradient-primary text-white text-center">
                        <h5 class="mb-0">
                            <i class="fas fa-tools"></i> خيارات التصدير والاستيراد للبيانات الشخصية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <!-- إضافة البيانات الجديدة -->
                            <div class="col-lg-4">
                                <div class="text-center p-4 border rounded-3 h-100" style="background: linear-gradient(135deg, #ffeaa7, #fdcb6e);">
                                    <div class="mb-3">
                                        <i class="fas fa-database fa-3x text-warning"></i>
                                    </div>
                                    <h6 class="fw-bold">إضافة البيانات الجديدة</h6>
                                    <p class="small text-muted mb-3">
                                        إضافة الأشخاص الجدد مع بياناتهم
                                        إلى قاعدة البيانات الرئيسية
                                    </p>
                                    {% if results.new_records %}
                                    <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-warning fw-bold"
                                                onclick="return confirm('هل تريد إضافة {{ results.new_records|length }} شخص جديد إلى قاعدة البيانات؟')">
                                            <i class="fas fa-plus-circle"></i> إضافة الأشخاص والبيانات
                                        </button>
                                    </form>
                                    {% else %}
                                    <button class="btn btn-secondary" disabled>
                                        <i class="fas fa-ban"></i> لا توجد بيانات جديدة
                                    </button>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- التقرير الكامل للبيانات -->
                            <div class="col-lg-4">
                                <div class="text-center p-4 border rounded-3 h-100" style="background: linear-gradient(135deg, #a8e6cf, #81c784);">
                                    <div class="mb-3">
                                        <i class="fas fa-file-excel fa-3x text-success"></i>
                                    </div>
                                    <h6 class="fw-bold">التقرير الكامل للبيانات</h6>
                                    <p class="small text-muted mb-3">
                                        تصدير تقرير شامل يحتوي على جميع
                                        النتائج والإحصائيات والتفاصيل
                                    </p>
                                    <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-success fw-bold">
                                        <i class="fas fa-download"></i> تحميل
                                    </a>
                                </div>
                            </div>

                            <!-- التقرير المختصر للبيانات الجديدة -->
                            <div class="col-lg-4">
                                <div class="text-center p-4 border rounded-3 h-100" style="background: linear-gradient(135deg, #fab1a0, #e17055);">
                                    <div class="mb-3">
                                        <i class="fas fa-file-download fa-3x text-danger"></i>
                                    </div>
                                    <h6 class="fw-bold">التقرير المختصر للبيانات الجديدة</h6>
                                    <p class="small text-muted mb-3">
                                        تصدير ملف يحتوي على البيانات الجديدة
                                        فقط مع جميع الإحصائيات والتفاصيل
                                    </p>
                                    <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-danger fw-bold">
                                        <i class="fas fa-file-excel"></i> تحميل
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Bar for Data Analysis -->
                        <div class="mt-4">
                            <h6 class="text-center mb-3">
                                <i class="fas fa-chart-pie"></i> ملخص تحليل البيانات بالنسب المئوية
                            </h6>
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="progress mb-2" style="height: 20px;">
                                        {% set found_percentage = (results.statistics.exact_matches_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                        <div class="progress-bar bg-info" style="width: {{ found_percentage }}%"></div>
                                    </div>
                                    <small class="fw-bold text-info">{{ "%.1f"|format(found_percentage) }}%</small><br>
                                    <small class="text-muted">بيانات موجودة</small>
                                </div>
                                <div class="col-3">
                                    <div class="progress mb-2" style="height: 20px;">
                                        {% set new_percentage = (results.statistics.new_records_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                        <div class="progress-bar bg-success" style="width: {{ new_percentage }}%"></div>
                                    </div>
                                    <small class="fw-bold text-success">{{ "%.1f"|format(new_percentage) }}%</small><br>
                                    <small class="text-muted">بيانات جديدة جاهزة</small>
                                </div>
                                <div class="col-3">
                                    <div class="progress mb-2" style="height: 20px;">
                                        {% set corrected_percentage = (results.statistics.corrected_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                        <div class="progress-bar bg-warning" style="width: {{ corrected_percentage }}%"></div>
                                    </div>
                                    <small class="fw-bold text-warning">{{ "%.1f"|format(corrected_percentage) }}%</small><br>
                                    <small class="text-muted">أسماء تم تصحيحها</small>
                                </div>
                                <div class="col-3">
                                    <div class="progress mb-2" style="height: 20px;">
                                        {% set blocked_percentage = (results.statistics.blocked_duplicates_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                        <div class="progress-bar bg-danger" style="width: {{ blocked_percentage }}%"></div>
                                    </div>
                                    <small class="fw-bold text-danger">{{ "%.1f"|format(blocked_percentage) }}%</small><br>
                                    <small class="text-muted">بيانات مكررة مرفوضة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div id="chartsSection" class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="card-header text-white text-center border-0" style="background: transparent;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div></div>
                            <div class="text-center">
                                <h3 class="mb-0 fw-bold">
                                    <i class="fas fa-chart-pie me-2"></i> التقرير الكامل للبيانات - الرسوم البيانية
                                </h3>
                                <p class="mb-0 mt-2 opacity-75">تحليل مرئي شامل لنتائج معالجة البيانات الشخصية</p>
                            </div>
                            <div class="dropdown export-controls">
                                <button class="btn btn-light btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-download me-1"></i> تصدير الرسوم
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                                    <li><a class="dropdown-item" href="#" onclick="exportChartsToExcel()">
                                        <i class="fas fa-file-excel text-success me-2"></i> تصدير إلى Excel + صورة
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportChartsToWord()">
                                        <i class="fas fa-file-word text-primary me-2"></i> تصدير إلى Word
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportChartsAsPDF()">
                                        <i class="fas fa-file-pdf text-danger me-2"></i> تصدير إلى PDF
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportAllChartsAsImages()">
                                        <i class="fas fa-images text-info me-2"></i> تصدير كصورة PNG
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">

                        <!-- Main Statistics Chart -->
                        <div class="row mb-5">
                            <div class="col-lg-6 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-header bg-gradient-primary text-white text-center">
                                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>توزيع البيانات الرئيسية</h5>
                                    </div>
                                    <div class="card-body d-flex align-items-center justify-content-center" style="height: 400px;">
                                        <canvas id="mainStatsChart" style="max-width: 350px; max-height: 350px;"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-header bg-gradient-success text-white text-center">
                                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات التشابه</h5>
                                    </div>
                                    <div class="card-body d-flex align-items-center justify-content-center" style="height: 400px;">
                                        <canvas id="similarityChart" style="max-width: 350px; max-height: 350px;"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Bars Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-gradient-info text-white text-center">
                                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>تقدم المعالجة بالنسب المئوية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="fw-bold text-success mb-2">
                                                    <i class="fas fa-check-circle me-1"></i>
                                                    موجود في قاعدة البيانات ({{ results.statistics.exact_matches_count }})
                                                </label>
                                                {% set found_percentage = (results.statistics.exact_matches_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                                <div class="progress mb-2" style="height: 25px;">
                                                    <div class="progress-bar bg-success progress-bar-striped progress-bar-animated"
                                                         style="width: {{ found_percentage }}%">
                                                        {{ "%.1f"|format(found_percentage) }}%
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="fw-bold text-primary mb-2">
                                                    <i class="fas fa-plus-circle me-1"></i>
                                                    بيانات جديدة ({{ results.statistics.new_records_count }})
                                                </label>
                                                {% set new_percentage = (results.statistics.new_records_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                                <div class="progress mb-2" style="height: 25px;">
                                                    <div class="progress-bar bg-primary progress-bar-striped progress-bar-animated"
                                                         style="width: {{ new_percentage }}%">
                                                        {{ "%.1f"|format(new_percentage) }}%
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="fw-bold text-warning mb-2">
                                                    <i class="fas fa-edit me-1"></i>
                                                    أسماء مصححة ({{ results.statistics.corrected_count }})
                                                </label>
                                                {% set corrected_percentage = (results.statistics.corrected_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                                <div class="progress mb-2" style="height: 25px;">
                                                    <div class="progress-bar bg-warning progress-bar-striped progress-bar-animated"
                                                         style="width: {{ corrected_percentage }}%">
                                                        {{ "%.1f"|format(corrected_percentage) }}%
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="fw-bold text-info mb-2">
                                                    <i class="fas fa-copy me-1"></i>
                                                    أسماء مكررة مسموحة ({{ results.statistics.get('allowed_duplicates_count', 0) }})
                                                </label>
                                                {% set allowed_percentage = (results.statistics.get('allowed_duplicates_count', 0) / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                                <div class="progress mb-2" style="height: 25px;">
                                                    <div class="progress-bar bg-info progress-bar-striped progress-bar-animated"
                                                         style="width: {{ allowed_percentage }}%">
                                                        {{ "%.1f"|format(allowed_percentage) }}%
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="fw-bold text-danger mb-2">
                                                    <i class="fas fa-ban me-1"></i>
                                                    بيانات مرفوضة ({{ results.statistics.blocked_duplicates_count }})
                                                </label>
                                                {% set blocked_percentage = (results.statistics.blocked_duplicates_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                                <div class="progress mb-2" style="height: 25px;">
                                                    <div class="progress-bar bg-danger progress-bar-striped progress-bar-animated"
                                                         style="width: {{ blocked_percentage }}%">
                                                        {{ "%.1f"|format(blocked_percentage) }}%
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Comparison Chart -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-gradient-warning text-dark text-center">
                                        <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>مقارنة شاملة للنتائج</h5>
                                    </div>
                                    <div class="card-body" style="height: 300px;">
                                        <canvas id="comparisonChart" style="max-height: 250px;"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Export Libraries -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
// تحضير البيانات
const totalProcessed = {{ results.statistics.total_processed }};
const exactMatches = {{ results.statistics.exact_matches_count }};
const newRecords = {{ results.statistics.new_records_count }};
const correctedCount = {{ results.statistics.corrected_count }};
const blockedDuplicates = {{ results.statistics.blocked_duplicates_count }};
const allowedDuplicates = {{ results.statistics.get('allowed_duplicates_count', 0) }};

// الألوان
const colors = {
    primary: '#007bff',
    success: '#28a745',
    warning: '#ffc107',
    danger: '#dc3545',
    info: '#17a2b8',
    purple: '#6f42c1',
    pink: '#e83e8c',
    orange: '#fd7e14'
};

// الرسم البياني الدائري الرئيسي
const mainStatsCtx = document.getElementById('mainStatsChart').getContext('2d');
new Chart(mainStatsCtx, {
    type: 'doughnut',
    data: {
        labels: ['موجود في قاعدة البيانات', 'بيانات جديدة', 'أسماء مصححة', 'أسماء مكررة مسموحة', 'بيانات مرفوضة'],
        datasets: [{
            data: [exactMatches, newRecords, correctedCount, allowedDuplicates, blockedDuplicates],
            backgroundColor: [colors.success, colors.primary, colors.warning, colors.info, colors.danger],
            borderWidth: 3,
            borderColor: '#fff',
            hoverBorderWidth: 5
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: 1,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 15,
                    font: {
                        family: 'Cairo',
                        size: 11
                    }
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const percentage = ((context.parsed / totalProcessed) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                    }
                }
            }
        }
    }
});

// رسم بياني للتشابه
const similarityCtx = document.getElementById('similarityChart').getContext('2d');
new Chart(similarityCtx, {
    type: 'bar',
    data: {
        labels: ['تشابه ثلاثي', 'تشابه رباعي', 'تشابه خماسي', 'تشابه كامل', 'أسماء طويلة'],
        datasets: [{
            label: 'عدد الحالات',
            data: [
                {{ results.statistics.get('triple_similarity_count', 0) }},
                {{ results.statistics.get('quadruple_similarity_count', 0) }},
                {{ results.statistics.get('quintuple_similarity_count', 0) }},
                {{ results.statistics.get('full_with_title_count', 0) }},
                {{ results.statistics.get('six_plus_count', 0) }}
            ],
            backgroundColor: [colors.info, colors.purple, colors.pink, colors.orange, colors.success],
            borderColor: [colors.info, colors.purple, colors.pink, colors.orange, colors.success],
            borderWidth: 2,
            borderRadius: 5
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: 1,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// رسم بياني للمقارنة
const comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
new Chart(comparisonCtx, {
    type: 'line',
    data: {
        labels: ['إجمالي المعالجة', 'موجود في قاعدة البيانات', 'بيانات جديدة', 'أسماء مصححة', 'أسماء مكررة مسموحة', 'بيانات مرفوضة'],
        datasets: [{
            label: 'العدد',
            data: [totalProcessed, exactMatches, newRecords, correctedCount, allowedDuplicates, blockedDuplicates],
            borderColor: colors.primary,
            backgroundColor: colors.primary + '20',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: colors.primary,
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: 3,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// دوال التصدير
function exportChartsToExcel() {
    captureChartsSection().then(canvas => {
        // تحويل الصورة إلى base64
        const imageData = canvas.toDataURL('image/png');

        // تحميل الصورة مباشرة (لأن Excel لا يدعم الصور المدمجة بسهولة)
        downloadImage(imageData, 'تقرير_الرسوم_البيانية_' + new Date().toISOString().split('T')[0] + '.png');

        // إنشاء workbook مع البيانات
        const wb = XLSX.utils.book_new();

        // بيانات الإحصائيات
        const data = [
            ['تقرير تحليل البيانات الشخصية - الرسوم البيانية'],
            ['تاريخ التقرير: ' + new Date().toLocaleDateString('ar-SA')],
            [''],
            ['الإحصائيات الرئيسية:'],
            ['نوع البيانات', 'العدد', 'النسبة المئوية'],
            ['موجود في قاعدة البيانات', exactMatches, ((exactMatches / totalProcessed) * 100).toFixed(1) + '%'],
            ['بيانات جديدة', newRecords, ((newRecords / totalProcessed) * 100).toFixed(1) + '%'],
            ['أسماء مصححة', correctedCount, ((correctedCount / totalProcessed) * 100).toFixed(1) + '%'],
            ['أسماء مكررة مسموحة', allowedDuplicates, ((allowedDuplicates / totalProcessed) * 100).toFixed(1) + '%'],
            ['بيانات مرفوضة', blockedDuplicates, ((blockedDuplicates / totalProcessed) * 100).toFixed(1) + '%'],
            ['إجمالي المعالجة', totalProcessed, '100%'],
            [''],
            ['إحصائيات التشابه:'],
            ['نوع التشابه', 'العدد'],
            ['تشابه ثلاثي', {{ results.statistics.get('triple_similarity_count', 0) }}],
            ['تشابه رباعي', {{ results.statistics.get('quadruple_similarity_count', 0) }}],
            ['تشابه خماسي', {{ results.statistics.get('quintuple_similarity_count', 0) }}],
            ['تشابه كامل', {{ results.statistics.get('full_with_title_count', 0) }}],
            ['أسماء طويلة', {{ results.statistics.get('six_plus_count', 0) }}],
            [''],
            ['ملاحظة: تم تحميل الرسوم البيانية كصورة منفصلة']
        ];

        const ws = XLSX.utils.aoa_to_sheet(data);

        // تنسيق الأعمدة
        ws['!cols'] = [{wch: 30}, {wch: 15}, {wch: 20}];

        // تنسيق الخلايا
        ws['A1'].s = { font: { bold: true, sz: 14 }, alignment: { horizontal: 'center' } };
        ws['A4'].s = { font: { bold: true, sz: 12 } };
        ws['A12'].s = { font: { bold: true, sz: 12 } };

        XLSX.utils.book_append_sheet(wb, ws, 'تقرير الرسوم البيانية');

        // تصدير Excel
        XLSX.writeFile(wb, 'تقرير_الرسوم_البيانية_بيانات_' + new Date().toISOString().split('T')[0] + '.xlsx');

        showExportSuccess('Excel + صورة PNG');
    });
}

function exportChartsToWord() {
    captureChartsSection().then(canvas => {
        const imageData = canvas.toDataURL('image/png');

        // إنشاء محتوى HTML مع الصورة
        let htmlContent = `
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <style>
                    body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; text-align: center; }
                    .header { color: #007bff; margin-bottom: 30px; }
                    .chart-image { max-width: 100%; height: auto; border: 1px solid #ddd; margin: 20px 0; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>تقرير تحليل البيانات الشخصية - الرسوم البيانية</h1>
                    <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>

                <div>
                    <img src="${imageData}" class="chart-image" alt="الرسوم البيانية">
                </div>
            </body>
            </html>
        `;

        // إنشاء blob وتحميله
        const blob = new Blob([htmlContent], { type: 'application/msword' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'تقرير_الرسوم_البيانية_' + new Date().toISOString().split('T')[0] + '.doc';
        a.click();
        URL.revokeObjectURL(url);

        showExportSuccess('Word');
    });
}

function exportChartsAsPDF() {
    captureChartsSection().then(canvas => {
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('l', 'mm', 'a4'); // landscape للحصول على مساحة أكبر

        // إضافة العنوان
        pdf.setFontSize(16);
        pdf.text('تقرير تحليل البيانات الشخصية - الرسوم البيانية', 148, 20, { align: 'center' });

        // إضافة التاريخ
        pdf.setFontSize(12);
        pdf.text('تاريخ التقرير: ' + new Date().toLocaleDateString('ar-SA'), 148, 30, { align: 'center' });

        // إضافة الصورة
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 250; // عرض الصورة في PDF
        const imgHeight = (canvas.height * imgWidth) / canvas.width; // حساب الارتفاع بنسبة

        // التأكد من أن الصورة تدخل في الصفحة
        const maxHeight = 180; // أقصى ارتفاع متاح
        let finalWidth = imgWidth;
        let finalHeight = imgHeight;

        if (imgHeight > maxHeight) {
            finalHeight = maxHeight;
            finalWidth = (canvas.width * maxHeight) / canvas.height;
        }

        const x = (297 - finalWidth) / 2; // توسيط الصورة (297 هو عرض A4 landscape)
        const y = 40;

        pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);

        // حفظ الملف
        pdf.save('تقرير_الرسوم_البيانية_' + new Date().toISOString().split('T')[0] + '.pdf');

        showExportSuccess('PDF');
    });
}

function exportAllChartsAsImages() {
    // تصدير القسم كاملاً كصورة واحدة
    captureChartsSection().then(canvas => {
        const imageData = canvas.toDataURL('image/png');
        downloadImage(imageData, 'تقرير_الرسوم_البيانية_كامل_' + new Date().toISOString().split('T')[0] + '.png');
        showExportSuccess('صورة كاملة للتقرير');
    });
}

// دالة التقاط قسم الرسوم البيانية كاملاً
function captureChartsSection() {
    const element = document.getElementById('chartsSection');
    const exportControls = element.querySelector('.export-controls');

    // إخفاء أدوات التصدير مؤقتاً
    if (exportControls) {
        exportControls.style.visibility = 'hidden';
    }

    // انتظار قصير للتأكد من إخفاء العناصر
    return new Promise(resolve => {
        setTimeout(() => {
            html2canvas(element, {
                useCORS: true,
                allowTaint: true,
                scale: 2, // جودة عالية
                backgroundColor: null, // الحفاظ على الخلفية الأصلية
                width: element.scrollWidth,
                height: element.scrollHeight,
                scrollX: 0,
                scrollY: 0,
                ignoreElements: function(el) {
                    // تجاهل عناصر التصدير
                    return el.classList.contains('export-controls') ||
                           el.classList.contains('dropdown') ||
                           el.classList.contains('dropdown-menu') ||
                           el.id === 'exportDropdown';
                }
            }).then(canvas => {
                // إعادة إظهار أدوات التصدير
                if (exportControls) {
                    exportControls.style.visibility = 'visible';
                }
                resolve(canvas);
            });
        }, 100);
    });
}

// دالة تحميل الصورة
function downloadImage(imageData, filename) {
    const a = document.createElement('a');
    a.href = imageData;
    a.download = filename;
    a.click();
}

function showExportSuccess(format) {
    // إنشاء تنبيه نجاح
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        <strong>تم التصدير بنجاح!</strong><br>
        تم تصدير التقرير إلى ${format}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>

{% endblock %}
