#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار API التقرير مباشرة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User
import json

def test_report_api():
    """اختبار API التقرير"""
    
    with app.test_client() as client:
        with app.app_context():
            # البحث عن مستخدم مدير
            admin_user = User.query.filter_by(role='admin').first()
            
            if not admin_user:
                print("❌ لا يوجد مستخدم مدير في قاعدة البيانات!")
                return False
            
            print(f"👤 استخدام المستخدم المدير: {admin_user.username}")
            
            # تسجيل الدخول
            with client.session_transaction() as sess:
                sess['_user_id'] = str(admin_user.id)
                sess['_fresh'] = True
            
            # إرسال طلب التقرير
            data = {
                'start_date': '2020-01-01',
                'end_date': '2025-12-31'
            }
            
            print(f"📤 إرسال طلب التقرير: {data}")
            
            response = client.post('/generate_report', data=data)
            
            print(f"📥 رمز الاستجابة: {response.status_code}")
            print(f"📄 نوع المحتوى: {response.content_type}")
            
            if response.status_code == 200:
                try:
                    result = response.get_json()
                    
                    if result and result.get('success'):
                        print("✅ نجح إنشاء التقرير!")
                        print(f"📊 إجمالي الدورات: {result.get('totalCourses', 0)}")
                        print(f"👥 إجمالي المشاركين: {result.get('totalParticipants', 0)}")
                        print(f"🎓 إجمالي الخريجين: {result.get('totalGraduates', 0)}")
                        print(f"🏢 إجمالي المراكز: {result.get('totalCenters', 0)}")
                        
                        # فحص بيانات الرسوم البيانية
                        chart_data = result.get('chartData', {})
                        print(f"\n📈 بيانات الرسوم البيانية:")
                        print(f"   المستوى الأول: {chart_data.get('level1', 0)}")
                        print(f"   المستوى الثاني: {chart_data.get('level2', 0)}")
                        print(f"   المستوى الثالث: {chart_data.get('level3', 0)}")
                        
                        center_names = chart_data.get('centerNames', [])
                        center_participants = chart_data.get('centerParticipants', [])
                        print(f"   المراكز: {len(center_names)} مركز")
                        
                        if center_names:
                            print("   أهم المراكز:")
                            for i, (name, count) in enumerate(zip(center_names[:5], center_participants[:5])):
                                print(f"     {i+1}. {name}: {count} مشارك")
                        
                        # فحص بيانات الجداول
                        table_data = result.get('tableData', {})
                        level_data = table_data.get('levelData', [])
                        participants_data = table_data.get('participantsData', [])
                        
                        print(f"\n📋 بيانات الجداول:")
                        print(f"   جدول المستويات: {len(level_data)} صف")
                        print(f"   جدول المشاركين: {len(participants_data)} صف")
                        
                        return True
                    else:
                        print(f"❌ فشل إنشاء التقرير: {result.get('message', 'خطأ غير معروف')}")
                        return False
                        
                except Exception as e:
                    print(f"❌ خطأ في تحليل الاستجابة: {str(e)}")
                    print(f"📄 محتوى الاستجابة: {response.get_data(as_text=True)[:500]}...")
                    return False
            else:
                print(f"❌ فشل الطلب: {response.status_code}")
                print(f"📄 محتوى الخطأ: {response.get_data(as_text=True)}")
                return False

if __name__ == '__main__':
    print("🚀 بدء اختبار API التقرير...")
    
    success = test_report_api()
    
    if success:
        print("\n✅ اختبار API مكتمل بنجاح!")
    else:
        print("\n❌ فشل اختبار API!")
