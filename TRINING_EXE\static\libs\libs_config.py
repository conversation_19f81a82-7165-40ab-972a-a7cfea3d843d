#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تكوين المكتبات المحلية - إدارة مركزية للمكتبات
Local Libraries Configuration - Central management for libraries
"""

from flask import url_for

class LibrariesConfig:
    """فئة إدارة المكتبات المحلية"""
    
    @staticmethod
    def get_bootstrap_css():
        """الحصول على رابط Bootstrap CSS المحلي"""
        return url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css')
    
    @staticmethod
    def get_bootstrap_js():
        """الحصول على رابط Bootstrap JS المحلي"""
        return url_for('static', filename='libs/bootstrap/bootstrap.bundle.min.js')
    
    @staticmethod
    def get_fontawesome_css():
        """الحصول على رابط Font Awesome CSS المحلي"""
        return url_for('static', filename='libs/fontawesome/all.min.css')
    
    @staticmethod
    def get_jquery_js():
        """الحصول على رابط jQuery JS المحلي"""
        return url_for('static', filename='libs/jquery/jquery-3.6.0.min.js')
    
    @staticmethod
    def get_chartjs_js():
        """الحصول على رابط Chart.js JS المحلي"""
        return url_for('static', filename='libs/chartjs/chart.min.js')
    
    @staticmethod
    def get_datatables_css():
        """الحصول على روابط DataTables CSS المحلية"""
        return [
            url_for('static', filename='libs/datatables/dataTables.bootstrap5.min.css'),
            url_for('static', filename='libs/datatables/responsive.bootstrap5.min.css'),
            url_for('static', filename='libs/datatables/buttons.bootstrap5.min.css')
        ]
    
    @staticmethod
    def get_datatables_js():
        """الحصول على روابط DataTables JS المحلية"""
        return [
            url_for('static', filename='libs/datatables/jquery.dataTables.min.js'),
            url_for('static', filename='libs/datatables/dataTables.bootstrap5.min.js'),
            url_for('static', filename='libs/datatables/dataTables.responsive.min.js'),
            url_for('static', filename='libs/datatables/responsive.bootstrap5.min.js'),
            url_for('static', filename='libs/datatables/dataTables.buttons.min.js'),
            url_for('static', filename='libs/datatables/buttons.bootstrap5.min.js'),
            url_for('static', filename='libs/datatables/buttons.html5.min.js'),
            url_for('static', filename='libs/datatables/buttons.print.min.js')
        ]
    
    @staticmethod
    def get_select2_css():
        """الحصول على روابط Select2 CSS المحلية"""
        return [
            url_for('static', filename='libs/select2/select2.min.css'),
            url_for('static', filename='libs/select2/select2-bootstrap-5-theme.min.css')
        ]
    
    @staticmethod
    def get_select2_js():
        """الحصول على رابط Select2 JS المحلي"""
        return url_for('static', filename='libs/select2/select2.min.js')
    
    @staticmethod
    def get_devextreme_css():
        """الحصول على رابط DevExtreme CSS المحلي"""
        return url_for('static', filename='libs/devextreme/dx.light.css')
    
    @staticmethod
    def get_devextreme_js():
        """الحصول على رابط DevExtreme JS المحلي"""
        return url_for('static', filename='libs/devextreme/dx.all.js')
    
    @staticmethod
    def get_jspdf_js():
        """الحصول على رابط jsPDF JS المحلي"""
        return url_for('static', filename='libs/other/jspdf.umd.min.js')
    
    @staticmethod
    def get_xlsx_js():
        """الحصول على رابط XLSX JS المحلي"""
        return url_for('static', filename='libs/other/xlsx.full.min.js')
    
    @staticmethod
    def get_jszip_js():
        """الحصول على رابط JSZip JS المحلي"""
        return url_for('static', filename='libs/other/jszip.min.js')
    
    @staticmethod
    def get_all_common_css():
        """الحصول على جميع ملفات CSS الأساسية"""
        css_files = []
        css_files.append(LibrariesConfig.get_bootstrap_css())
        css_files.append(LibrariesConfig.get_fontawesome_css())
        return css_files
    
    @staticmethod
    def get_all_common_js():
        """الحصول على جميع ملفات JS الأساسية"""
        js_files = []
        js_files.append(LibrariesConfig.get_jquery_js())
        js_files.append(LibrariesConfig.get_bootstrap_js())
        return js_files

# دالة مساعدة لاستخدامها في القوالب
def inject_libraries_config():
    """حقن تكوين المكتبات في القوالب"""
    return dict(libs=LibrariesConfig)
