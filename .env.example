# نظام تحليل الأسماء - متغيرات البيئة
# Training System - Environment Variables

# إعدادات Flask
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-change-in-production

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///training_system.db
# للإنتاج مع PostgreSQL:
# DATABASE_URL=postgresql://username:password@localhost:5432/training_system

# إعدادات الخادم
HOST=0.0.0.0
PORT=5000
DEBUG=True

# إعدادات الأمان
WTF_CSRF_ENABLED=True
WTF_CSRF_TIME_LIMIT=3600

# إعدادات الملفات
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=uploads
BACKUP_FOLDER=backups

# إعدادات البريد الإلكتروني (للمستقبل)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# إعدادات النسخ الاحتياطي
BACKUP_ENABLED=True
BACKUP_INTERVAL=24  # ساعات
MAX_BACKUPS=30

# إعدادات التطوير
LOG_LEVEL=INFO
LOG_FILE=logs/training_system.log
