{% extends "layout.html" %}

{% block styles %}
<style>
    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
    }
    
    .form-section h5 {
        color: #495057;
        margin-bottom: 15px;
        font-weight: 600;
        border-bottom: 2px solid #007bff;
        padding-bottom: 5px;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 5px;
    }
    
    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #ced4da;
        padding: 10px 12px;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .btn-submit {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        padding: 12px 30px;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        font-weight: 600;
        border-radius: 8px;
        color: white;
        text-decoration: none;
        transition: all 0.3s;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }
    
    .current-image {
        max-width: 200px;
        border-radius: 8px;
        border: 2px solid #e9ecef;
        padding: 5px;
    }
    
    .hijri-date-group {
        display: flex;
        gap: 10px;
        align-items: end;
    }
    
    .hijri-date-group .form-group {
        flex: 1;
        margin-bottom: 0;
    }
    
    .hijri-date-group small {
        display: block;
        text-align: center;
        margin-top: 5px;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link active">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_excel') }}" class="sidebar-link">
                <i class="fas fa-id-card"></i> إدارة البيانات بالإكسل
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('reference_tables') }}" class="sidebar-link">
                <i class="fas fa-table"></i> الجداول الترميزية
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
            {% endif %}
        </div>
    </div>

    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-edit me-2"></i>تعديل الدورة: {{ course.title }}</h2>
            <a href="{{ url_for('course_details', course_id=course.id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى تفاصيل الدورة
            </a>
        </div>

        <form method="POST" enctype="multipart/form-data">
            {{ form.hidden_tag() }}
            
            <!-- معلومات أساسية -->
            <div class="form-section">
                <h5><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.course_number.label(class="form-label") }}
                            {% if form.course_number.errors %}
                                {{ form.course_number(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.course_number.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.course_number(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.agency_course_number.label(class="form-label") }}
                            {{ form.agency_course_number(class="form-control") }}
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.place_course_number.label(class="form-label") }}
                            {{ form.place_course_number(class="form-control") }}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    {{ form.title.label(class="form-label") }}
                    {% if form.title.errors %}
                        {{ form.title(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.title.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.title(class="form-control") }}
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form.description.label(class="form-label") }}
                    {{ form.description(class="form-control", rows=4) }}
                </div>
            </div>

            <!-- التصنيف والمستوى -->
            <div class="form-section">
                <h5><i class="fas fa-tags me-2"></i>التصنيف والمستوى</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.category_id.label(class="form-label") }}
                            {{ form.category_id(class="form-select") }}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.level_id.label(class="form-label") }}
                            {{ form.level_id(class="form-select") }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- الصورة -->
            <div class="form-section">
                <h5><i class="fas fa-image me-2"></i>صورة الدورة</h5>
                
                {% if course.image %}
                <div class="mb-3">
                    <label class="form-label">الصورة الحالية:</label><br>
                    <img src="{{ url_for('static', filename='uploads/courses/' + course.image) }}" alt="صورة الدورة" class="current-image">
                </div>
                {% endif %}
                
                <div class="form-group">
                    {{ form.image.label(class="form-label") }}
                    {{ form.image(class="form-control") }}
                    <small class="form-text text-muted">اتركه فارغاً للاحتفاظ بالصورة الحالية</small>
                </div>
            </div>

            <!-- التواريخ -->
            <div class="form-section">
                <h5><i class="fas fa-calendar-alt me-2"></i>التواريخ والمدة</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.start_date.label(class="form-label") }}
                            {{ form.start_date(class="form-control") }}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.duration_days.label(class="form-label") }}
                            {{ form.duration_days(class="form-control") }}
                        </div>
                    </div>
                </div>
                
                <!-- التواريخ الهجرية -->
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">تاريخ البدء (هجري)</label>
                        <div class="hijri-date-group">
                            <div class="form-group">
                                {{ form.start_date_hijri_day(class="form-select") }}
                                <small>اليوم</small>
                            </div>
                            <div class="form-group">
                                {{ form.start_date_hijri_month(class="form-select") }}
                                <small>الشهر</small>
                            </div>
                            <div class="form-group">
                                {{ form.start_date_hijri_year(class="form-select") }}
                                <small>السنة</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label">تاريخ النهاية (هجري)</label>
                        <div class="hijri-date-group">
                            <div class="form-group">
                                {{ form.end_date_hijri_day(class="form-select") }}
                                <small>اليوم</small>
                            </div>
                            <div class="form-group">
                                {{ form.end_date_hijri_month(class="form-select") }}
                                <small>الشهر</small>
                            </div>
                            <div class="form-group">
                                {{ form.end_date_hijri_year(class="form-select") }}
                                <small>السنة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between mt-4">
                <a href="{{ url_for('course_details', course_id=course.id) }}" class="btn-cancel">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
                {{ form.submit(class="btn btn-primary btn-submit", value="تحديث الدورة") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
