from flask import Blueprint, jsonify, request, render_template, flash, redirect, url_for, send_file, session
from flask_login import login_required, current_user
import pandas as pd
import io
import re
from datetime import datetime
from sqlalchemy import or_, func, text

# سيتم استيراد النماذج من app.py
db = None
PersonData = None
Course = None
CourseParticipant = None

person_data_bp = Blueprint('person_data', __name__)

def init_person_data_routes(app_db, person_data_model, flask_app, course_model=None, course_participant_model=None):
    global db, PersonData, Course, CourseParticipant
    db = app_db
    PersonData = person_data_model

    # استخدام النماذج الممررة أو استيرادها
    if course_model and course_participant_model:
        Course = course_model
        CourseParticipant = course_participant_model
    else:
        # استيراد النماذج الأخرى من app (للتوافق مع النسخة القديمة)
        from app import Course as CourseModel, CourseParticipant as CourseParticipantModel
        Course = CourseModel
        CourseParticipant = CourseParticipantModel

    # تسجيل Blueprint مع prefix
    flask_app.register_blueprint(person_data_bp, url_prefix='/person_data')

# صفحة جدول بيانات الأشخاص
@person_data_bp.route('/person_data_table')
@login_required
def person_data_table():
    """
    صفحة بيانات الأشخاص
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب جميع البيانات من جدول person_data
    try:
        # معاملات البحث والتصفية
        search = request.args.get('search', '')
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # بناء الاستعلام
        query = PersonData.query

        if search:
            query = query.filter(
                db.or_(
                    PersonData.full_name.contains(search),
                    PersonData.phone.contains(search),
                    PersonData.national_number.contains(search),
                    PersonData.governorate.contains(search)
                )
            )

        # تطبيق التصفح
        persons = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        return render_template('person_data_table.html',
                             title='جدول الملتحقين',
                             persons=persons,
                             search=search)
    except Exception as e:
        flash(f'حدث خطأ في جلب البيانات: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

# الحصول على بيانات الأشخاص بتنسيق JSON
@person_data_bp.route('/person_data_json')
@login_required
def person_data_json():
    """
    الحصول على بيانات الأشخاص بتنسيق JSON
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه البيانات'}), 403

    try:
        # جلب بيانات الأشخاص
        persons = PersonData.query.all()

        print(f"تم جلب {len(persons)} شخص من قاعدة البيانات")

        # تحويل البيانات إلى قائمة من القواميس
        data = []
        for person in persons:
            try:
                person_data = {
                    'id': person.id,
                    'full_name': person.full_name if person.full_name else '',
                    'nickname': person.nickname if person.nickname else '',
                    'age': person.age if person.age else '',
                    'governorate': person.governorate if person.governorate else '',
                    'directorate': person.directorate if person.directorate else '',
                    'uzla': person.uzla if person.uzla else '',
                    'village': person.village if person.village else '',
                    'qualification': person.qualification if person.qualification else '',
                    'marital_status': person.marital_status if person.marital_status else '',
                    'job': person.job if person.job else '',
                    'agency': person.agency if person.agency else '',
                    'work_place': person.work_place if person.work_place else '',
                    'national_number': person.national_number if person.national_number else '',
                    'military_number': person.military_number if person.military_number else '',
                    'phone': person.phone if person.phone else ''
                }
                data.append(person_data)
            except Exception as person_error:
                print(f"خطأ في معالجة الشخص {person.id}: {str(person_error)}")
                continue

        print(f"تم تحويل {len(data)} سجل بنجاح")
        return jsonify(data)

    except Exception as e:
        print(f"خطأ في person_data_json: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'حدث خطأ في الخادم: {str(e)}'}), 500

# الحصول على بيانات شخص محدد بتنسيق JSON
@person_data_bp.route('/person_data_get/<int:person_id>')
@login_required
def person_data_get(person_id):
    """
    الحصول على بيانات شخص محدد بتنسيق JSON
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه البيانات'}), 403

    try:
        # البحث عن الشخص
        person = PersonData.query.get_or_404(person_id)

        # تحويل البيانات إلى قاموس
        person_data = {
            'id': person.id,
            'full_name': person.full_name,
            'nickname': person.nickname if person.nickname else '',
            'age': person.age if person.age else '',
            'governorate': person.governorate if person.governorate else '',
            'directorate': person.directorate if person.directorate else '',
            'uzla': person.uzla if person.uzla else '',
            'village': person.village if person.village else '',
            'qualification': person.qualification if person.qualification else '',
            'marital_status': person.marital_status if person.marital_status else '',
            'job': person.job if person.job else '',
            'agency': person.agency if person.agency else '',
            'work_place': person.work_place if person.work_place else '',
            'national_number': person.national_number if person.national_number else '',
            'military_number': person.military_number if person.military_number else '',
            'phone': person.phone if person.phone else ''
        }

        return jsonify(person_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# إضافة شخص جديد
@person_data_bp.route('/person_data_add', methods=['POST'])
@login_required
def person_data_add():
    """
    إضافة شخص جديد
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'}), 403

    try:
        # الحصول على البيانات من الطلب
        data = request.json

        # إنشاء كائن شخص جديد
        person = PersonData(
            full_name=data['full_name'],
            nickname=data.get('nickname'),
            age=int(data.get('age')) if data.get('age') else None,
            governorate=data.get('governorate'),
            directorate=data.get('directorate'),
            uzla=data.get('uzla'),
            village=data.get('village'),
            qualification=data.get('qualification'),
            marital_status=data.get('marital_status'),
            job=data.get('job'),
            agency=data.get('agency'),
            work_place=data.get('work_place'),
            national_number=data.get('national_number'),
            military_number=data.get('military_number'),
            phone=data.get('phone')
        )

        # إضافة الشخص إلى قاعدة البيانات
        db.session.add(person)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تمت إضافة الشخص بنجاح', 'id': person.id})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

# تحديث بيانات شخص
@person_data_bp.route('/person_data_update/<int:person_id>', methods=['POST'])
@login_required
def person_data_update(person_id):
    """
    تحديث بيانات شخص
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'}), 403

    try:
        # البحث عن الشخص
        person = PersonData.query.get_or_404(person_id)

        # الحصول على البيانات من الطلب
        data = request.json

        # تحديث بيانات الشخص
        person.full_name = data['full_name']
        person.nickname = data.get('nickname')
        person.age = int(data.get('age')) if data.get('age') else None
        person.governorate = data.get('governorate')
        person.directorate = data.get('directorate')
        person.uzla = data.get('uzla')
        person.village = data.get('village')
        person.qualification = data.get('qualification')
        person.marital_status = data.get('marital_status')
        person.job = data.get('job')
        person.agency = data.get('agency')
        person.work_place = data.get('work_place')
        person.national_number = data.get('national_number')
        person.military_number = data.get('military_number')
        person.phone = data.get('phone')

        # حفظ التغييرات
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تحديث بيانات الشخص بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

# حذف شخص
@person_data_bp.route('/person_data_delete/<int:person_id>', methods=['POST'])
@login_required
def person_data_delete(person_id):
    """
    حذف شخص
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'}), 403

    try:
        # البحث عن الشخص
        person = PersonData.query.get_or_404(person_id)

        # حذف الشخص
        db.session.delete(person)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حذف الشخص بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

# تصدير بيانات الأشخاص إلى إكسل
@person_data_bp.route('/person_data_export')
@login_required
def person_data_export():
    """
    تصدير بيانات الأشخاص إلى ملف إكسل
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # جلب بيانات الأشخاص
        persons = PersonData.query.all()

        # إنشاء DataFrame
        data = []
        for person in persons:
            person_data = {
                'الاسم الشخصي': person.full_name,
                'الاسم المستعار': person.nickname if person.nickname else '',
                'العمر': person.age if person.age else '',
                'المحافظة': person.governorate if person.governorate else '',
                'المديرية': person.directorate if person.directorate else '',
                'العزلة': person.uzla if person.uzla else '',
                'الحي/القرية': person.village if person.village else '',
                'المؤهل العلمي': person.qualification if person.qualification else '',
                'الحالة الاجتماعية': person.marital_status if person.marital_status else '',
                'العمل': person.job if person.job else '',
                'الإدارة': person.agency if person.agency else '',
                'مكان العمل': person.work_place if person.work_place else '',
                'الرقم الوطني': person.national_number if person.national_number else '',
                'الرقم العسكري': person.military_number if person.military_number else '',
                'رقم التلفون': person.phone if person.phone else ''
            }
            data.append(person_data)

        # إنشاء DataFrame
        df = pd.DataFrame(data)

        # إنشاء ملف إكسل في الذاكرة
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='بيانات الأشخاص', index=False)

            # تنسيق ورقة العمل
            workbook = writer.book
            worksheet = writer.sheets['بيانات الأشخاص']

            # تنسيق العناوين
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1,
                'align': 'right'
            })

            # تطبيق التنسيق على العناوين
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)

            # تعيين عرض الأعمدة
            for i, column in enumerate(df.columns):
                column_width = max(df[column].astype(str).map(len).max(), len(column) + 2)
                worksheet.set_column(i, i, column_width)

        # إعادة المؤشر إلى بداية الملف
        output.seek(0)

        # إنشاء اسم الملف
        filename = f"بيانات_الأشخاص_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # إرسال الملف
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير البيانات: {str(e)}', 'danger')
        return redirect(url_for('person_data.person_data_table'))

# تنزيل قالب إكسل
@person_data_bp.route('/person_data_template')
@login_required
def person_data_template():
    """
    تنزيل قالب إكسل لبيانات الأشخاص
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # إنشاء DataFrame فارغ بالأعمدة المطلوبة
        columns = [
            'الاسم الشخصي', 'الاسم المستعار', 'العمر', 'المحافظة', 'المديرية',
            'العزلة', 'الحي/القرية', 'المؤهل العلمي', 'الحالة الاجتماعية',
            'العمل', 'الإدارة', 'مكان العمل', 'الرقم الوطني', 'الرقم العسكري', 'رقم التلفون'
        ]
        df = pd.DataFrame(columns=columns)

        # إنشاء ملف إكسل في الذاكرة
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='بيانات الأشخاص', index=False)

            # تنسيق ورقة العمل
            workbook = writer.book
            worksheet = writer.sheets['بيانات الأشخاص']

            # تنسيق العناوين
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1,
                'align': 'right'
            })

            # تطبيق التنسيق على العناوين
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)

            # تعيين عرض الأعمدة
            for i, column in enumerate(df.columns):
                worksheet.set_column(i, i, len(column) + 5)

            # تعيين اتجاه الورقة من اليمين إلى اليسار
            worksheet.right_to_left()

        # إعادة المؤشر إلى بداية الملف
        output.seek(0)

        # إنشاء اسم الملف
        filename = f"قالب_بيانات_الأشخاص.xlsx"

        # إرسال الملف
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f'حدث خطأ أثناء تنزيل القالب: {str(e)}', 'danger')
        return redirect(url_for('person_data.person_data_table'))

# استيراد بيانات الأشخاص من إكسل
@person_data_bp.route('/person_data_import', methods=['POST'])
@login_required
def person_data_import():
    """
    استيراد بيانات الأشخاص من ملف إكسل
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # التحقق من وجود ملف
        if 'excel_file' not in request.files:
            flash('لم يتم تحديد ملف', 'danger')
            return redirect(url_for('person_data.person_data_table'))

        file = request.files['excel_file']

        # التحقق من أن الملف ليس فارغًا
        if file.filename == '':
            flash('لم يتم تحديد ملف', 'danger')
            return redirect(url_for('person_data.person_data_table'))

        # التحقق من امتداد الملف
        if not file.filename.endswith(('.xlsx', '.xls')):
            flash('يجب أن يكون الملف بتنسيق Excel (.xlsx, .xls)', 'danger')
            return redirect(url_for('person_data.person_data_table'))

        # قراءة ملف Excel
        df = pd.read_excel(file)

        # تنظيف أسماء الأعمدة (إزالة المسافات الزائدة)
        df.columns = df.columns.str.strip()

        # التحقق من البيانات الموجودة مسبقاً في قاعدة البيانات
        existing_persons = PersonData.query.all()
        existing_names = [person.full_name for person in existing_persons]

        print(f"=== معلومات قاعدة البيانات الحالية ===")
        print(f"عدد الأشخاص الموجودين مسبقاً: {len(existing_persons)}")
        print(f"الأسماء الموجودة مسبقاً: {existing_names}")

        # طباعة معلومات التشخيص
        print(f"\n=== معلومات ملف الاستيراد ===")
        print(f"عدد الصفوف: {len(df)}")
        print(f"عدد الأعمدة: {len(df.columns)}")
        print("\n=== أسماء الأعمدة في الملف (بعد التنظيف) ===")
        for i, col in enumerate(df.columns):
            print(f"{i+1:2d}. '{col}'")
        print("=" * 50)
        print(f"أول صف من البيانات: {df.iloc[0].to_dict() if len(df) > 0 else 'لا توجد بيانات'}")

        # إنشاء خريطة للأعمدة المرنة
        column_mapping = {}

        # قائمة الأعمدة المطلوبة مع البدائل المحتملة
        column_alternatives = {
            'full_name': ['الاسم الشخصي', 'الاسم', 'اسم', 'الاسم الكامل'],
            'nickname': ['الاسم المستعار', 'اللقب', 'الكنية'],
            'age': ['العمر', 'السن'],
            'governorate': ['المحافظة', 'محافظة'],
            'directorate': ['المديرية', 'مديرية'],
            'uzla': ['العزلة', 'عزلة'],
            'village': ['الحي/القرية', 'الحي', 'القرية', 'حي/قرية', 'الحي / القرية'],
            'qualification': ['المؤهل العلمي', 'المؤهل', 'التعليم'],
            'marital_status': ['الحالة الاجتماعية', 'الحالة', 'حالة اجتماعية'],
            'job': ['العمل', 'الوظيفة', 'المهنة'],
            'agency': ['الإدارة', 'الجهة', 'القسم'],
            'work_place': ['مكان العمل', 'جهة العمل', 'العمل'],
            'national_number': ['الرقم الوطني', 'رقم وطني', 'الهوية'],
            'military_number': ['الرقم العسكري', 'رقم عسكري'],
            'phone': ['رقم التلفون', 'التلفون', 'الهاتف', 'رقم الهاتف', 'موبايل']
        }

        # البحث عن الأعمدة المطابقة
        for field, alternatives in column_alternatives.items():
            for alt in alternatives:
                if alt in df.columns:
                    column_mapping[field] = alt
                    print(f"✅ تم العثور على '{field}' في العمود: '{alt}'")
                    break
            if field not in column_mapping:
                print(f"⚠️ لم يتم العثور على عمود لـ '{field}'")

        # التحقق من وجود العمود الأساسي
        if 'full_name' not in column_mapping:
            # إنشاء رسالة مفصلة عن الأعمدة المطلوبة
            required_columns_msg = """
            الأعمدة المطلوبة في ملف Excel:
            1. الاسم الشخصي (مطلوب)
            2. الاسم المستعار
            3. العمر
            4. المحافظة
            5. المديرية
            6. العزلة
            7. الحي/القرية
            8. المؤهل العلمي
            9. الحالة الاجتماعية
            10. العمل
            11. الإدارة
            12. مكان العمل
            13. الرقم الوطني
            14. الرقم العسكري
            15. رقم التلفون
            """
            flash('لم يتم العثور على عمود الاسم الشخصي في الملف', 'danger')
            flash(required_columns_msg, 'info')
            return redirect(url_for('person_data.person_data_table'))

        # استيراد البيانات
        success_count = 0
        error_count = 0
        skipped_count = 0
        error_messages = []
        skipped_names = []

        # دالة مساعدة للحصول على قيمة من الصف باستخدام الخريطة
        def get_value_from_row(row, field_name, column_mapping):
            if field_name in column_mapping:
                column_name = column_mapping[field_name]
                if column_name in row and not pd.isna(row[column_name]):
                    return str(row[column_name]).strip()
            return None

        for index, row in df.iterrows():
            try:
                # طباعة بيانات الصف الحالي للتشخيص
                print(f"\n--- الصف {index + 1} ---")

                # استخدام الخريطة للحصول على القيم
                full_name_raw = get_value_from_row(row, 'full_name', column_mapping)
                governorate_raw = get_value_from_row(row, 'governorate', column_mapping)
                directorate_raw = get_value_from_row(row, 'directorate', column_mapping)
                village_raw = get_value_from_row(row, 'village', column_mapping)

                print(f"الاسم الشخصي: {full_name_raw or 'غير موجود'}")
                print(f"المحافظة: {governorate_raw or 'غير موجود'}")
                print(f"المديرية: {directorate_raw or 'غير موجود'}")
                print(f"الحي/القرية: {village_raw or 'غير موجود'}")

                # التحقق من أن الاسم الشخصي غير فارغ
                if not full_name_raw:
                    error_count += 1
                    error_messages.append(f'الصف {index + 2}: الاسم الشخصي مطلوب')
                    continue

                # التحقق من عدم وجود الاسم مسبقاً
                full_name = full_name_raw
                if full_name in existing_names:
                    print(f"تم تخطي الاسم '{full_name}' - موجود مسبقاً في قاعدة البيانات")
                    skipped_names.append(full_name)
                    skipped_count += 1
                    continue

                # معالجة القيم العددية باستخدام الخريطة
                age = None
                age_raw = get_value_from_row(row, 'age', column_mapping)
                if age_raw:
                    try:
                        age = int(float(age_raw))
                    except:
                        age = None

                # معالجة الأرقام باستخدام الخريطة
                national_number = get_value_from_row(row, 'national_number', column_mapping)
                military_number = get_value_from_row(row, 'military_number', column_mapping)
                phone = get_value_from_row(row, 'phone', column_mapping)

                # معالجة البيانات النصية باستخدام الخريطة المرنة
                governorate = governorate_raw  # تم الحصول عليها مسبقاً
                directorate = directorate_raw  # تم الحصول عليها مسبقاً
                uzla = get_value_from_row(row, 'uzla', column_mapping)
                village = village_raw  # تم الحصول عليها مسبقاً
                qualification = get_value_from_row(row, 'qualification', column_mapping)
                marital_status = get_value_from_row(row, 'marital_status', column_mapping)
                job = get_value_from_row(row, 'job', column_mapping)
                agency = get_value_from_row(row, 'agency', column_mapping)
                work_place = get_value_from_row(row, 'work_place', column_mapping)
                nickname = get_value_from_row(row, 'nickname', column_mapping)

                # طباعة القيم المعالجة للتشخيص
                print(f"القيم المعالجة:")
                print(f"  المحافظة: '{governorate}'")
                print(f"  المديرية: '{directorate}'")
                print(f"  العزلة: '{uzla}'")
                print(f"  الحي/القرية: '{village}'")
                print(f"  المؤهل العلمي: '{qualification}'")
                print(f"  الحالة الاجتماعية: '{marital_status}'")
                print(f"  العمل: '{job}'")
                print(f"  الإدارة: '{agency}'")
                print(f"  مكان العمل: '{work_place}'")

                # إنشاء كائن شخص جديد
                person = PersonData(
                    full_name=full_name,
                    nickname=nickname,
                    age=age,
                    governorate=governorate,
                    directorate=directorate,
                    uzla=uzla,
                    village=village,
                    qualification=qualification,
                    marital_status=marital_status,
                    job=job,
                    agency=agency,
                    work_place=work_place,
                    national_number=national_number,
                    military_number=military_number,
                    phone=phone
                )

                print(f"الكائن المُنشأ:")
                print(f"  الاسم الشخصي: '{person.full_name}'")
                print(f"  الاسم المستعار: '{person.nickname}'")
                print(f"  العمر: {person.age}")
                print(f"  المحافظة: '{person.governorate}'")
                print(f"  المديرية: '{person.directorate}'")
                print(f"  العزلة: '{person.uzla}'")
                print(f"  الحي/القرية: '{person.village}'")
                print(f"  المؤهل العلمي: '{person.qualification}'")
                print(f"  الحالة الاجتماعية: '{person.marital_status}'")
                print(f"  العمل: '{person.job}'")
                print(f"  الإدارة: '{person.agency}'")
                print(f"  مكان العمل: '{person.work_place}'")
                print(f"  الرقم الوطني: '{person.national_number}'")
                print(f"  الرقم العسكري: '{person.military_number}'")
                print(f"  رقم التلفون: '{person.phone}'")

                # إضافة الشخص إلى قاعدة البيانات
                db.session.add(person)
                db.session.commit()

                # إضافة الاسم إلى قائمة الأسماء الموجودة لتجنب المكررات في نفس الملف
                existing_names.append(full_name)

                success_count += 1
                print(f"✅ تم حفظ: {full_name}")
            except Exception as e:
                db.session.rollback()
                error_count += 1
                error_messages.append(f'الصف {index + 2}: {str(e)}')

        # عرض رسائل مفصلة
        if success_count > 0:
            flash(f'✅ تم استيراد {success_count} سجل بنجاح', 'success')

        if skipped_count > 0:
            flash(f'⏭️ تم تخطي {skipped_count} سجل (أسماء موجودة مسبقاً)', 'info')
            if skipped_names:
                flash(f'الأسماء المتخطاة: {", ".join(skipped_names[:5])}{"..." if len(skipped_names) > 5 else ""}', 'info')

        if error_count > 0:
            flash(f'⚠️ فشل استيراد {error_count} سجل', 'warning')
            for message in error_messages[:5]:  # عرض أول 5 أخطاء فقط
                flash(f'❌ {message}', 'warning')
            if len(error_messages) > 5:
                flash(f'... و {len(error_messages) - 5} أخطاء أخرى', 'warning')

        # رسالة تفصيلية عن البيانات المستوردة
        if success_count > 0:
            flash(f'📊 تم استيراد البيانات التالية بنجاح: الأسماء، الأعمار، المحافظات، المديريات، الأحياء/القرى، الأرقام الوطنية، الأرقام العسكرية، أرقام التلفون', 'info')

        return redirect(url_for('person_data.person_data_table'))
    except Exception as e:
        flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')
        return redirect(url_for('person_data.person_data_table'))

@person_data_bp.route('/duplicate_names')
def duplicate_names():
    """عرض الأسماء المكررة"""
    try:
        # البحث عن الأسماء المكررة
        duplicates = db.session.query(
            PersonData.full_name,
            func.count(PersonData.id).label('count')
        ).group_by(PersonData.full_name).having(func.count(PersonData.id) > 1).all()

        # جلب تفاصيل الأسماء المكررة
        duplicate_details = []
        for duplicate in duplicates:
            persons = PersonData.query.filter_by(full_name=duplicate.full_name).all()
            duplicate_details.append({
                'name': duplicate.full_name,
                'count': duplicate.count,
                'persons': persons
            })

        return render_template('person_data/duplicate_names.html',
                             duplicates=duplicate_details,
                             total_duplicates=len(duplicate_details))
    except Exception as e:
        flash(f'حدث خطأ أثناء جلب الأسماء المكررة: {str(e)}', 'danger')
        return redirect(url_for('person_data.person_data_table'))

@person_data_bp.route('/export_duplicates')
def export_duplicates():
    """تصدير الأسماء المكررة إلى Excel"""
    try:
        # البحث عن الأسماء المكررة
        duplicates = db.session.query(
            PersonData.full_name,
            func.count(PersonData.id).label('count')
        ).group_by(PersonData.full_name).having(func.count(PersonData.id) > 1).all()

        # إنشاء قائمة البيانات للتصدير
        export_data = []
        for duplicate in duplicates:
            persons = PersonData.query.filter_by(full_name=duplicate.full_name).all()
            for person in persons:
                export_data.append({
                    'الاسم الشخصي': person.full_name,
                    'الاسم المستعار': person.nickname or '',
                    'العمر': person.age or '',
                    'المحافظة': person.governorate or '',
                    'المديرية': person.directorate or '',
                    'العزلة': person.uzla or '',
                    'الحي/القرية': person.village or '',
                    'المؤهل العلمي': person.qualification or '',
                    'الحالة الاجتماعية': person.marital_status or '',
                    'العمل': person.job or '',
                    'الإدارة': person.agency or '',
                    'مكان العمل': person.work_place or '',
                    'الرقم الوطني': person.national_number or '',
                    'الرقم العسكري': person.military_number or '',
                    'رقم التلفون': person.phone or '',
                    'عدد التكرارات': duplicate.count
                })

        if not export_data:
            flash('لا توجد أسماء مكررة للتصدير', 'info')
            return redirect(url_for('person_data.duplicate_names'))

        # إنشاء DataFrame
        df = pd.DataFrame(export_data)

        # إنشاء ملف Excel
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='الأسماء المكررة', index=False)

            # تنسيق الورقة
            worksheet = writer.sheets['الأسماء المكررة']

            # تعديل عرض الأعمدة
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        output.seek(0)

        # إنشاء اسم الملف مع التاريخ
        filename = f'الأسماء_المكررة_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير الأسماء المكررة: {str(e)}', 'danger')
        return redirect(url_for('person_data.duplicate_names'))

@person_data_bp.route('/create_sample_excel')
def create_sample_excel():
    """إنشاء ملف Excel نموذجي بالترتيب الصحيح"""
    try:
        # البيانات النموذجية بالترتيب الصحيح
        sample_data = {
            'الاسم الشخصي': [
                'علي صالح محمد الحميري',
                'فاطمة أحمد علي المقطري',
                'محمد حسن يحيى الزبيدي'
            ],
            'الاسم المستعار': ['أبو محمد', 'أم أحمد', 'أبو علي'],
            'العمر': [30, 25, 35],
            'المحافظة': ['صنعاء', 'تعز', 'الحديدة'],
            'المديرية': ['الثورة', 'صالة', 'الحوك'],
            'العزلة': ['شعوب', 'الجند', 'الزهرة'],
            'الحي/القرية': ['حي السبعين', 'حي الجمهورية', 'حي الثورة'],
            'المؤهل العلمي': ['بكالوريوس', 'ثانوية', 'دبلوم'],
            'الحالة الاجتماعية': ['متزوج', 'عزباء', 'متزوج'],
            'العمل': ['مهندس', 'معلمة', 'طبيب'],
            'الإدارة': ['الهندسة', 'التعليم', 'الصحة'],
            'مكان العمل': ['وزارة الأشغال', 'مدرسة الأمل', 'مستشفى الثورة'],
            'الرقم الوطني': ['12345678', '87654321', '11223344'],
            'الرقم العسكري': ['M123456', 'M654321', 'M112233'],
            'رقم التلفون': ['777123456', '733987654', '770112233']
        }

        # إنشاء DataFrame
        df = pd.DataFrame(sample_data)

        # إنشاء ملف Excel
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='نموذج البيانات', index=False)

            # تنسيق الورقة
            worksheet = writer.sheets['نموذج البيانات']

            # تعديل عرض الأعمدة
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 30)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        output.seek(0)

        # إنشاء اسم الملف
        filename = f'نموذج_استيراد_البيانات_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء الملف النموذجي: {str(e)}', 'danger')
        return redirect(url_for('person_data.person_data_table'))

@person_data_bp.route('/person_data')
@login_required
def person_data():
    """
    صفحة بيانات الأشخاص المحسنة مع DevExpress
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('person_data.html', title='بيانات الأفراد')

@person_data_bp.route('/person_data/data')
@login_required
def person_data_data():
    """
    الحصول على بيانات الأشخاص للـ DevExpress DataGrid
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه البيانات'}), 403

    try:
        # جلب بيانات الأشخاص
        persons = PersonData.query.all()

        # تحويل البيانات إلى قائمة من القواميس
        data = []
        for person in persons:
            person_data = {
                'id': person.id,
                'full_name': person.full_name,
                'nickname': person.nickname if person.nickname else '',
                'age': person.age if person.age else '',
                'governorate': person.governorate if person.governorate else '',
                'directorate': person.directorate if person.directorate else '',
                'uzla': person.uzla if person.uzla else '',
                'village': person.village if person.village else '',
                'qualification': person.qualification if person.qualification else '',
                'marital_status': person.marital_status if person.marital_status else '',
                'job': person.job if person.job else '',
                'agency': person.agency if person.agency else '',
                'work_place': person.work_place if person.work_place else '',
                'national_number': person.national_number if person.national_number else '',
                'military_number': person.military_number if person.military_number else '',
                'phone': person.phone if person.phone else ''
            }
            data.append(person_data)

        return jsonify(data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@person_data_bp.route('/person_data/add', methods=['POST'])
@login_required
def person_data_add_devexpress():
    """
    إضافة شخص جديد من DevExpress
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'}), 403

    try:
        # الحصول على البيانات من الطلب
        data = request.json

        # إنشاء كائن شخص جديد
        person = PersonData(
            full_name=data['full_name'],
            nickname=data.get('nickname'),
            age=int(data.get('age')) if data.get('age') else None,
            governorate=data.get('governorate'),
            directorate=data.get('directorate'),
            uzla=data.get('uzla'),
            village=data.get('village'),
            qualification=data.get('qualification'),
            marital_status=data.get('marital_status'),
            job=data.get('job'),
            agency=data.get('agency'),
            work_place=data.get('work_place'),
            national_number=data.get('national_number'),
            military_number=data.get('military_number'),
            phone=data.get('phone')
        )

        # إضافة الشخص إلى قاعدة البيانات
        db.session.add(person)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تمت إضافة الشخص بنجاح', 'id': person.id})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@person_data_bp.route('/person_data/update/<int:person_id>', methods=['POST'])
@login_required
def person_data_update_devexpress(person_id):
    """
    تحديث بيانات شخص من DevExpress
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'}), 403

    try:
        # البحث عن الشخص
        person = PersonData.query.get_or_404(person_id)

        # الحصول على البيانات من الطلب
        data = request.json

        # تحديث بيانات الشخص
        person.full_name = data['full_name']
        person.nickname = data.get('nickname')
        person.age = int(data.get('age')) if data.get('age') else None
        person.governorate = data.get('governorate')
        person.directorate = data.get('directorate')
        person.uzla = data.get('uzla')
        person.village = data.get('village')
        person.qualification = data.get('qualification')
        person.marital_status = data.get('marital_status')
        person.job = data.get('job')
        person.agency = data.get('agency')
        person.work_place = data.get('work_place')
        person.national_number = data.get('national_number')
        person.military_number = data.get('military_number')
        person.phone = data.get('phone')

        # حفظ التغييرات
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تحديث بيانات الشخص بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@person_data_bp.route('/person_data/delete/<int:person_id>', methods=['POST'])
@login_required
def person_data_delete_devexpress(person_id):
    """
    حذف شخص من DevExpress
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'}), 403

    try:
        # البحث عن الشخص
        person = PersonData.query.get_or_404(person_id)

        # حذف الشخص
        db.session.delete(person)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حذف الشخص بنجاح'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@person_data_bp.route('/person_data/export')
@login_required
def person_data_export_devexpress():
    """
    تصدير بيانات الأشخاص إلى ملف إكسل من DevExpress
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # جلب بيانات الأشخاص
        persons = PersonData.query.all()

        # إنشاء DataFrame
        data = []
        for person in persons:
            person_data = {
                'الاسم الشخصي': person.full_name,
                'الاسم المستعار': person.nickname if person.nickname else '',
                'العمر': person.age if person.age else '',
                'المحافظة': person.governorate if person.governorate else '',
                'المديرية': person.directorate if person.directorate else '',
                'العزلة': person.uzla if person.uzla else '',
                'الحي/القرية': person.village if person.village else '',
                'المؤهل العلمي': person.qualification if person.qualification else '',
                'الحالة الاجتماعية': person.marital_status if person.marital_status else '',
                'العمل': person.job if person.job else '',
                'الإدارة': person.agency if person.agency else '',
                'مكان العمل': person.work_place if person.work_place else '',
                'الرقم الوطني': person.national_number if person.national_number else '',
                'الرقم العسكري': person.military_number if person.military_number else '',
                'رقم التلفون': person.phone if person.phone else ''
            }
            data.append(person_data)

        # إنشاء DataFrame
        df = pd.DataFrame(data)

        # إنشاء ملف إكسل في الذاكرة
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='بيانات الأشخاص', index=False)

            # تنسيق ورقة العمل
            worksheet = writer.sheets['بيانات الأشخاص']

            # تعيين عرض الأعمدة
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        # إعادة المؤشر إلى بداية الملف
        output.seek(0)

        # إنشاء اسم الملف
        filename = f"بيانات_الأشخاص_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # إرسال الملف
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير البيانات: {str(e)}', 'danger')
        return redirect(url_for('person_data.person_data'))

@person_data_bp.route('/person_data/template')
@login_required
def person_data_template_devexpress():
    """
    تنزيل قالب إكسل لبيانات الأشخاص من DevExpress
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # إنشاء DataFrame فارغ بالأعمدة المطلوبة
        columns = [
            'الاسم الشخصي', 'الاسم المستعار', 'العمر', 'المحافظة', 'المديرية',
            'العزلة', 'الحي/القرية', 'المؤهل العلمي', 'الحالة الاجتماعية',
            'العمل', 'الإدارة', 'مكان العمل', 'الرقم الوطني', 'الرقم العسكري', 'رقم التلفون'
        ]
        df = pd.DataFrame(columns=columns)

        # إنشاء ملف إكسل في الذاكرة
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='بيانات الأشخاص', index=False)

            # تنسيق ورقة العمل
            worksheet = writer.sheets['بيانات الأشخاص']

            # تعيين عرض الأعمدة
            for i, column in enumerate(df.columns):
                worksheet.column_dimensions[chr(65 + i)].width = len(column) + 5

            # تعيين اتجاه الورقة من اليمين إلى اليسار
            worksheet.sheet_view.rightToLeft = True

        # إعادة المؤشر إلى بداية الملف
        output.seek(0)

        # إنشاء اسم الملف
        filename = f"قالب_بيانات_الأشخاص.xlsx"

        # إرسال الملف
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f'حدث خطأ أثناء تنزيل القالب: {str(e)}', 'danger')
        return redirect(url_for('person_data.person_data'))

@person_data_bp.route('/person_data/import', methods=['POST'])
@login_required
def person_data_import_devexpress():
    """
    استيراد بيانات الأشخاص من ملف إكسل من DevExpress
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # استخدام نفس منطق الاستيراد من person_data_import
    return person_data_import()

@person_data_bp.route('/test_form', methods=['GET'])
@login_required
def test_form():
    """صفحة اختبار النموذج"""
    courses = Course.query.all()
    return render_template('person_data/test_form.html', courses=courses)

@person_data_bp.route('/name_analysis', methods=['GET', 'POST'])
@login_required
def name_analysis():
    """
    تحليل ومقايسة الأسماء من ملف Excel مع قاعدة البيانات
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب قائمة الدورات للعرض في القائمة المنسدلة
    courses = Course.query.all()

    if request.method == 'POST':
        print(f"🔥 تم استلام طلب POST")
        print(f"📋 الملفات المرسلة: {list(request.files.keys())}")
        print(f"📋 البيانات المرسلة: {list(request.form.keys())}")

        # تنظيف الجلسة من التحليلات السابقة
        print("🧹 تنظيف الجلسة من التحليلات السابقة...")
        session.pop('analysis_results', None)
        session.pop('excel_filename', None)
        session.pop('selected_course_id', None)
        session.pop('analysis_type', None)
        session.pop('selected_course_info', None)

        try:
            # التحقق من وجود ملف
            if 'excel_file' not in request.files:
                print(f"❌ لا يوجد ملف excel_file في الطلب")
                flash('لم يتم اختيار ملف', 'danger')
                return redirect(url_for('person_data.name_analysis'))

            file = request.files['excel_file']
            if file.filename == '':
                flash('لم يتم اختيار ملف', 'danger')
                return redirect(url_for('person_data.name_analysis'))

            # جلب نوع التحليل
            analysis_type = request.form.get('analysis_type')
            print(f"🎯 نوع التحليل من النموذج: {analysis_type}")

            # التحقق من صحة نوع التحليل
            if not analysis_type or analysis_type not in ['course', 'evaluation', 'data']:
                print(f"⚠️ نوع تحليل غير صحيح: {analysis_type}, سيتم استخدام 'course' كافتراضي")
                analysis_type = 'course'

            print(f"✅ نوع التحليل النهائي: {analysis_type}")

            # جلب معرف الدورة المختارة (إن وجد)
            selected_course_id = request.form.get('course_id')
            selected_course = None
            if selected_course_id:
                try:
                    selected_course = Course.query.get(int(selected_course_id))
                except (ValueError, TypeError):
                    selected_course = None

            # التحقق من اختيار الدورة حسب نوع التحليل
            if analysis_type in ['course', 'evaluation'] and not selected_course:
                flash('يجب اختيار دورة لهذا النوع من الفحص', 'danger')
                return redirect(url_for('person_data.name_analysis'))

            if not file.filename.lower().endswith(('.xlsx', '.xls')):
                flash('يجب أن يكون الملف من نوع Excel (.xlsx أو .xls)', 'danger')
                return redirect(url_for('person_data.name_analysis'))

            # قراءة ملف Excel
            df = pd.read_excel(file)

            # التحقق من وجود الأعمدة المطلوبة حسب نوع التحليل
            name_column = None
            if analysis_type == 'evaluation':
                # للتقييمات: البحث في أعمدة التقييم المختلفة
                possible_name_columns = [
                    'الاسم الشخصي', 'الاسم', 'اسم', 'الاسم الكامل', 'الاسم الخماسي', 'اسم المتدرب',
                    'اسم الطالب', 'Name', 'name', 'الاسم الرباعي', 'Student Name',
                    'Trainee Name', 'اسم المشارك', 'المشارك', 'اسم المتقدم'
                ]
            else:
                # للدورات والبيانات العادية
                possible_name_columns = ['الاسم الشخصي', 'الاسم', 'الاسم الكامل', 'Name', 'Full Name']

            print(f"🔍 البحث عن عمود الاسم في الأعمدة: {list(df.columns)}")
            print(f"🎯 نوع التحليل: {analysis_type}")
            print(f"📝 الأعمدة المحتملة للاسم: {possible_name_columns}")

            for col in possible_name_columns:
                if col in df.columns:
                    name_column = col
                    print(f"✅ تم العثور على عمود الاسم: {name_column}")
                    break

            if not name_column:
                if analysis_type == 'evaluation':
                    flash(f'لم يتم العثور على عمود الاسم في ملف التقييمات. الأعمدة المتوفرة: {", ".join(df.columns)}. يجب أن يحتوي على عمود "الاسم الشخصي" أو "اسم المتدرب"', 'danger')
                else:
                    flash(f'لم يتم العثور على عمود الاسم في الملف. الأعمدة المتوفرة: {", ".join(df.columns)}. يجب أن يحتوي على عمود "الاسم الشخصي"', 'danger')
                return redirect(url_for('person_data.name_analysis'))

            # البحث عن الأعمدة الإضافية للتحقق من التطابق
            national_id_column = None
            phone_column = None
            military_id_column = None

            # أعمدة الرقم الوطني المحتملة
            possible_national_columns = ['الرقم الوطني', 'رقم الهوية', 'National ID', 'ID Number', 'الهوية الوطنية']
            for col in possible_national_columns:
                if col in df.columns:
                    national_id_column = col
                    break

            # أعمدة رقم الهاتف المحتملة
            possible_phone_columns = ['رقم الهاتف', 'الهاتف', 'التلفون', 'Phone', 'Mobile', 'رقم التلفون']
            for col in possible_phone_columns:
                if col in df.columns:
                    phone_column = col
                    break

            # أعمدة الرقم العسكري المحتملة
            possible_military_columns = ['الرقم العسكري', 'Military ID', 'Military Number', 'رقم عسكري']
            for col in possible_military_columns:
                if col in df.columns:
                    military_id_column = col
                    break

            # استخراج البيانات من الملف
            excel_data = []
            for index, row in df.iterrows():
                name = str(row[name_column]).strip() if pd.notna(row[name_column]) else ''
                if name:  # فقط إذا كان الاسم موجود
                    person_data = {
                        'name': name,
                        'national_id': str(row[national_id_column]).strip() if national_id_column and pd.notna(row[national_id_column]) else '',
                        'phone': str(row[phone_column]).strip() if phone_column and pd.notna(row[phone_column]) else '',
                        'military_id': str(row[military_id_column]).strip() if military_id_column and pd.notna(row[military_id_column]) else '',
                        'row_index': index + 2  # +2 لأن Excel يبدأ من 1 وهناك header
                    }
                    excel_data.append(person_data)

            if not excel_data:
                flash('لا توجد بيانات صالحة في الملف', 'danger')
                return redirect(url_for('person_data.name_analysis'))

            # جلب جميع البيانات من قاعدة البيانات
            db_persons = PersonData.query.all()

            # تحضير بيانات قاعدة البيانات للمقارنة
            db_data = []
            for person in db_persons:
                if person.full_name:
                    db_person_data = {
                        'name': person.full_name,
                        'national_id': person.national_number or '',
                        'phone': person.phone or '',
                        'military_id': person.military_number or '',
                        'id': person.id
                    }
                    db_data.append(db_person_data)

            # تحليل البيانات حسب نوع التحليل
            if analysis_type == 'evaluation':
                # تحليل خاص بالتقييمات
                print(f"🔍 بدء تحليل التقييمات...")
                analysis_results = analyze_names_for_evaluation(excel_data, db_data, {
                    'name_column': name_column,
                    'national_id_column': national_id_column,
                    'phone_column': phone_column,
                    'military_id_column': military_id_column
                }, selected_course)

                # حفظ النتائج في الجلسة
                session['analysis_results'] = analysis_results
                session['excel_filename'] = file.filename
                session['selected_course_id'] = selected_course_id
                session['analysis_type'] = 'evaluation'  # تأكيد صريح لنوع التحليل
                session['selected_course_info'] = {
                    'id': selected_course.id,
                    'course_number': selected_course.course_number,
                    'title': selected_course.title
                } if selected_course else None

                print(f"💾 تم حفظ نوع التحليل في الجلسة: {session.get('analysis_type')}")
                print(f"💾 تم حفظ اسم الملف: {session.get('excel_filename')}")
                print(f"💾 تم حفظ معرف الدورة: {session.get('selected_course_id')}")

                # تأكيد إضافي لحفظ نوع التحليل
                session['analysis_type'] = 'evaluation'
                session.permanent = True  # جعل الجلسة دائمة

                print(f"🔒 تأكيد نهائي - نوع التحليل المحفوظ: {session.get('analysis_type')}")

                return render_template('person_data/name_analysis_evaluation_results.html',
                                     title='نتائج تحليل كشف التقييمات',
                                     results=analysis_results,
                                     excel_filename=file.filename,
                                     selected_course=selected_course,
                                     analysis_type='evaluation')
            else:
                # التحليل العادي للدورة أو البيانات
                print(f"🔍 بدء التحليل المتقدم...")
                print(f"📊 عدد سجلات Excel: {len(excel_data)}")
                print(f"📊 عدد سجلات قاعدة البيانات: {len(db_data)}")
                print(f"📋 الأعمدة المتوفرة: name={name_column}, national_id={national_id_column}, phone={phone_column}, military_id={military_id_column}")

                # استخدام النظام الذكي المتقدم
                analysis_results = analyze_names_smart_advanced(excel_data, db_data, {
                    'name_column': name_column,
                    'national_id_column': national_id_column,
                    'phone_column': phone_column,
                    'military_id_column': military_id_column
                }, selected_course, analysis_type)

                print(f"✅ انتهى التحليل المتقدم")
                print(f"📊 النتائج: {list(analysis_results.keys())}")

                # حفظ النتائج في الجلسة للاستخدام في التصدير
                session['analysis_results'] = analysis_results
                session['excel_filename'] = file.filename
                session['selected_course_id'] = selected_course_id
                session['analysis_type'] = analysis_type
                session['selected_course_info'] = {
                    'id': selected_course.id,
                    'course_number': selected_course.course_number,
                    'title': selected_course.title
                } if selected_course else None

                # تحديد template حسب نوع التحليل
                if analysis_type == 'data':
                    # تحليل البيانات الشخصية
                    return render_template('person_data/name_analysis_data_results.html',
                                         title='نتائج تحليل البيانات الشخصية',
                                         results=analysis_results,
                                         excel_filename=file.filename,
                                         selected_course=selected_course,
                                         analysis_type=analysis_type)
                else:
                    # تحليل الدورات
                    return render_template('person_data/name_analysis_results.html',
                                         title='نتائج تحليل كشف الدورة',
                                         results=analysis_results,
                                         excel_filename=file.filename,
                                         selected_course=selected_course,
                                         analysis_type=analysis_type)

        except Exception as e:
            flash(f'حدث خطأ أثناء تحليل الملف: {str(e)}', 'danger')
            return redirect(url_for('person_data.name_analysis'))

    return render_template('person_data/name_analysis.html',
                         title='تحليل ومقايسة الأسماء',
                         courses=courses)

# تم حذف دالة analyze_names القديمة واستبدالها بـ analyze_names_advanced

# دوال التصحيح العربي (خارج دالة analyze_names)
def correct_hamza(text):
    """تصحيح الهمزات في النص"""
    hamza_corrections = {
        # همزة القطع في بداية الكلمة - أسماء الذكور
        r'\bاحمد\b': 'أحمد',
        r'\bابراهيم\b': 'إبراهيم',
        r'\bاسماعيل\b': 'إسماعيل',
        r'\bاسحاق\b': 'إسحاق',
        r'\bاسلام\b': 'إسلام',
        r'\bايمن\b': 'أيمن',
        r'\bايوب\b': 'أيوب',
        r'\bانس\b': 'أنس',
        r'\bاسامة\b': 'أسامة',
        r'\bاشرف\b': 'أشرف',
        r'\bامين\b': 'أمين',
        r'\bامجد\b': 'أمجد',
        r'\bانور\b': 'أنور',
        r'\bايهاب\b': 'إيهاب',
        r'\bالياس\b': 'إلياس',
        r'\bاكرم\b': 'أكرم',
        r'\bادم\b': 'آدم',
        r'\bاحسان\b': 'إحسان',
        r'\bاخلاص\b': 'إخلاص',
        r'\bادريس\b': 'إدريس',
        r'\bاسعد\b': 'أسعد',
        r'\bاشرق\b': 'أشرق',
        r'\bاصيل\b': 'أصيل',
        r'\bاكثم\b': 'أكثم',
        r'\bالهام\b': 'إلهام',
        r'\bامير\b': 'أمير',
        r'\bانيس\b': 'أنيس',
        r'\bاوس\b': 'أوس',
        r'\bايسر\b': 'أيسر',
        r'\bايمن\b': 'أيمن',
        r'\bاياد\b': 'إياد',
        r'\bايلاف\b': 'إيلاف',
        r'\bايناس\b': 'إيناس',
        r'\bايهم\b': 'إيهم',
        r'\bاحمد\b': 'أحمد',
        r'\bاكرام\b': 'إكرام',
        r'\bاكمال\b': 'إكمال',
        r'\bالاء\b': 'آلاء',
        r'\bامان\b': 'أمان',
        r'\bامانة\b': 'أمانة',
        r'\bاماني\b': 'أماني',
        r'\bامتثال\b': 'امتثال',
        r'\bامجاد\b': 'أمجاد',
        r'\bامة\b': 'أمة',
        r'\bانتصار\b': 'انتصار',
        r'\bانجي\b': 'أنجي',
        r'\bانصاف\b': 'إنصاف',
        r'\bانعام\b': 'إنعام',
        r'\bانوار\b': 'أنوار',
        r'\bاهداء\b': 'إهداء',
        r'\bاوراق\b': 'أوراق',
        r'\bايات\b': 'آيات',

        # همزة القطع في بداية الكلمة - أسماء الإناث
        r'\bايمان\b': 'إيمان',
        r'\bاسراء\b': 'إسراء',
        r'\bامل\b': 'أمل',
        r'\bاميرة\b': 'أميرة',
        r'\bاسماء\b': 'أسماء',
        r'\bاحلام\b': 'أحلام',
        r'\bاريج\b': 'أريج',
        r'\bاسيل\b': 'أسيل',
        r'\bاثير\b': 'أثير',
        r'\bازهار\b': 'أزهار',
        r'\bاشواق\b': 'أشواق',
        r'\bاصالة\b': 'أصالة',
        r'\bافراح\b': 'أفراح',
        r'\bافكار\b': 'أفكار',
        r'\bاقبال\b': 'إقبال',
        r'\bالفت\b': 'ألفت',
        r'\bالماس\b': 'ألماس',
        r'\bامينة\b': 'أمينة',
        r'\bانغام\b': 'أنغام',
        r'\bانيسة\b': 'أنيسة',
        r'\bاوصاف\b': 'أوصاف',
        r'\bايلين\b': 'إيلين',

        # الكنى والألقاب
        r'\bابو\b': 'أبو',
        r'\bام\b': 'أم',
        r'\bابن\b': 'ابن',
        r'\bابنة\b': 'ابنة',

        # الألقاب المهنية والاجتماعية
        r'\bالاستاذ\b': 'الأستاذ',
        r'\bالاستاذة\b': 'الأستاذة',
        r'\bالاخ\b': 'الأخ',
        r'\bالاخت\b': 'الأخت',
        r'\bالامير\b': 'الأمير',
        r'\bالاميرة\b': 'الأميرة',
    }

    import re
    for wrong, correct in hamza_corrections.items():
        text = re.sub(wrong, correct, text, flags=re.IGNORECASE)

    return text

def correct_alif_maqsura(text):
    """تصحيح الألف المقصورة والياء"""
    alif_corrections = {
        # أسماء الذكور بالألف المقصورة
        r'\bعيسي\b': 'عيسى',
        r'\bموسي\b': 'موسى',
        r'\bيحيي\b': 'يحيى',
        r'\bمصطفي\b': 'مصطفى',
        r'\bمرتضي\b': 'مرتضى',
        r'\bيوسف\b': 'يوسف',  # صحيح بالفعل
        r'\bعلي\b': 'علي',    # صحيح بالفعل
        r'\bعلى\b': 'علي',    # تصحيح خطأ شائع
        r'\bمحي\b': 'محيي',
        r'\bزكي\b': 'زكي',    # صحيح بالفعل
        r'\bزكى\b': 'زكي',    # تصحيح
        r'\bتقي\b': 'تقي',    # صحيح بالفعل
        r'\bتقى\b': 'تقي',    # تصحيح
        r'\bعدي\b': 'عدي',    # صحيح بالفعل
        r'\bعدى\b': 'عدي',    # تصحيح
        r'\bهادي\b': 'هادي',  # صحيح بالفعل
        r'\bهادى\b': 'هادي',  # تصحيح
        r'\bراضي\b': 'راضي',  # صحيح بالفعل
        r'\bراضى\b': 'راضي',  # تصحيح
        r'\bساري\b': 'ساري',  # صحيح بالفعل
        r'\bسارى\b': 'ساري',  # تصحيح
        r'\bباسم\b': 'باسم',  # صحيح بالفعل
        r'\bحسني\b': 'حسني',  # صحيح بالفعل
        r'\bحسنى\b': 'حسني',  # تصحيح
        r'\bاسري\b': 'أسري',
        r'\bاسرى\b': 'أسرى',
        r'\bمعتز\b': 'معتز',  # صحيح بالفعل
        r'\bمعتزى\b': 'معتز',  # تصحيح

        # أسماء الإناث بالألف المقصورة
        r'\bهدي\b': 'هدى',
        r'\bسلمي\b': 'سلمى',
        r'\bليلي\b': 'ليلى',
        r'\bنجوي\b': 'نجوى',
        r'\bسهي\b': 'سهى',
        r'\bرني\b': 'رنا',     # خطأ شائع
        r'\bدني\b': 'دنيا',    # خطأ شائع
        r'\bمني\b': 'منى',
        r'\bلبني\b': 'لبنى',
        r'\bبشري\b': 'بشرى',
        r'\bذكري\b': 'ذكرى',
        r'\bسري\b': 'سرى',
        r'\bثري\b': 'ثرى',
        r'\bدعي\b': 'دعاء',    # خطأ شائع
        r'\bرجي\b': 'رجاء',    # خطأ شائع
        r'\bصفي\b': 'صفاء',    # خطأ شائع
        r'\bوفي\b': 'وفاء',    # خطأ شائع
        r'\bشيمي\b': 'شيماء',  # خطأ شائع
        r'\bاسمي\b': 'أسماء',  # خطأ شائع
        r'\bنادي\b': 'نادية',  # خطأ شائع - فقط للإناث
        r'\bراني\b': 'رانية',  # خطأ شائع
        r'\bماني\b': 'مانية',  # خطأ شائع
        r'\bتاني\b': 'تانية',  # خطأ شائع
        r'\bجني\b': 'جنى',
        r'\bرني\b': 'رنى',
        r'\bدني\b': 'دنى',
        r'\bمني\b': 'منى',
        r'\bسني\b': 'سنى',
        r'\bهني\b': 'هنى',

        # أسماء عائلات وألقاب
        r'\bالحوثي\b': 'الحوثي',  # صحيح بالفعل
        r'\bالحوثى\b': 'الحوثي',  # تصحيح
        r'\bالمرتضي\b': 'المرتضى',
        r'\bالمصطفي\b': 'المصطفى',
        r'\bالهادي\b': 'الهادي',   # صحيح بالفعل
        r'\bالهادى\b': 'الهادي',   # تصحيح
    }

    import re
    for wrong, correct in alif_corrections.items():
        text = re.sub(wrong, correct, text, flags=re.IGNORECASE)

    return text

def correct_taa_marbouta(text):
    """تصحيح التاء المربوطة والهاء"""
    # لا نغير التاء المربوطة إلى هاء أو العكس لأن هذا قد يغير المعنى
    # فقط نتأكد من الاستخدام الصحيح في الحالات الواضحة
    return text

def correct_compound_names(text):
    """تصحيح الأسماء المركبة مثل عبدالله"""
    compound_corrections = {
        # أسماء عبد + اسم الله الحسنى
        r'\bعبد\s+الله\b': 'عبدالله',
        r'\bعبد\s+الرحمن\b': 'عبدالرحمن',
        r'\bعبد\s+العزيز\b': 'عبدالعزيز',
        r'\bعبد\s+الكريم\b': 'عبدالكريم',
        r'\bعبد\s+الرحيم\b': 'عبدالرحيم',
        r'\bعبد\s+الحميد\b': 'عبدالحميد',
        r'\bعبد\s+القادر\b': 'عبدالقادر',
        r'\bعبد\s+الناصر\b': 'عبدالناصر',
        r'\bعبد\s+المجيد\b': 'عبدالمجيد',
        r'\bعبد\s+الحكيم\b': 'عبدالحكيم',
        r'\bعبد\s+الحليم\b': 'عبدالحليم',
        r'\bعبد\s+الرؤوف\b': 'عبدالرؤوف',
        r'\bعبد\s+الغني\b': 'عبدالغني',
        r'\bعبد\s+الباسط\b': 'عبدالباسط',
        r'\bعبد\s+الفتاح\b': 'عبدالفتاح',
        r'\bعبد\s+الوهاب\b': 'عبدالوهاب',
        r'\bعبد\s+الصمد\b': 'عبدالصمد',
        r'\bعبد\s+الجليل\b': 'عبدالجليل',
        r'\bعبد\s+الجبار\b': 'عبدالجبار',
        r'\bعبد\s+السلام\b': 'عبدالسلام',
        r'\bعبد\s+الملك\b': 'عبدالملك',
        r'\bعبد\s+الحق\b': 'عبدالحق',
        r'\bعبد\s+الخالق\b': 'عبدالخالق',
        r'\bعبد\s+الرزاق\b': 'عبدالرزاق',
        r'\bعبد\s+الحافظ\b': 'عبدالحافظ',
        r'\bعبد\s+الحكم\b': 'عبدالحكم',
        r'\bعبد\s+الحليم\b': 'عبدالحليم',
        r'\bعبد\s+الحمد\b': 'عبدالحمد',
        r'\bعبد\s+الحميد\b': 'عبدالحميد',
        r'\bعبد\s+الحنان\b': 'عبدالحنان',
        r'\bعبد\s+الحي\b': 'عبدالحي',
        r'\bعبد\s+الخبير\b': 'عبدالخبير',
        r'\bعبد\s+الرحيم\b': 'عبدالرحيم',
        r'\bعبد\s+الرشيد\b': 'عبدالرشيد',
        r'\bعبد\s+الرقيب\b': 'عبدالرقيب',
        r'\bعبد\s+السبحان\b': 'عبدالسبحان',
        r'\bعبد\s+السلام\b': 'عبدالسلام',
        r'\bعبد\s+السميع\b': 'عبدالسميع',
        r'\bعبد\s+الشافي\b': 'عبدالشافي',
        r'\bعبد\s+الشكور\b': 'عبدالشكور',
        r'\bعبد\s+الصبور\b': 'عبدالصبور',
        r'\bعبد\s+الصمد\b': 'عبدالصمد',
        r'\bعبد\s+الضار\b': 'عبدالضار',
        r'\bعبد\s+العالم\b': 'عبدالعالم',
        r'\bعبد\s+العاطي\b': 'عبدالعاطي',
        r'\bعبد\s+العظيم\b': 'عبدالعظيم',
        r'\bعبد\s+العليم\b': 'عبدالعليم',
        r'\bعبد\s+الغفار\b': 'عبدالغفار',
        r'\bعبد\s+الغفور\b': 'عبدالغفور',
        r'\bعبد\s+الفتاح\b': 'عبدالفتاح',
        r'\bعبد\s+القادر\b': 'عبدالقادر',
        r'\bعبد\s+القاهر\b': 'عبدالقاهر',
        r'\bعبد\s+القدوس\b': 'عبدالقدوس',
        r'\bعبد\s+القوي\b': 'عبدالقوي',
        r'\bعبد\s+الكريم\b': 'عبدالكريم',
        r'\bعبد\s+اللطيف\b': 'عبداللطيف',
        r'\bعبد\s+المؤمن\b': 'عبدالمؤمن',
        r'\bعبد\s+المالك\b': 'عبدالمالك',
        r'\bعبد\s+المتعال\b': 'عبدالمتعال',
        r'\bعبد\s+المجيب\b': 'عبدالمجيب',
        r'\bعبد\s+المحسن\b': 'عبدالمحسن',
        r'\bعبد\s+المصور\b': 'عبدالمصور',
        r'\bعبد\s+المعطي\b': 'عبدالمعطي',
        r'\bعبد\s+المنان\b': 'عبدالمنان',
        r'\bعبد\s+المنعم\b': 'عبدالمنعم',
        r'\bعبد\s+الهادي\b': 'عبدالهادي',
        r'\bعبد\s+الواحد\b': 'عبدالواحد',
        r'\bعبد\s+الوارث\b': 'عبدالوارث',
        r'\bعبد\s+الودود\b': 'عبدالودود',

        # تصحيحات أخطاء شائعة في عبدالله
        r'\bعبداللة\b': 'عبدالله',
        r'\bعبدالة\b': 'عبدالله',
        r'\bعبد_الله\b': 'عبدالله',
        r'\bعبدالاه\b': 'عبدالله',
        r'\bعبدالاة\b': 'عبدالله',

        # أسماء مركبة أخرى شائعة
        r'\bمحمد\s+على\b': 'محمد علي',
        r'\bمحمد\s+علي\b': 'محمد علي',
        r'\bاحمد\s+علي\b': 'أحمد علي',
        r'\bعلي\s+محمد\b': 'علي محمد',
        r'\bحسن\s+علي\b': 'حسن علي',
        r'\bعلي\s+حسن\b': 'علي حسن',
        r'\bمحمد\s+حسن\b': 'محمد حسن',
        r'\bحسن\s+محمد\b': 'حسن محمد',
        r'\bاحمد\s+محمد\b': 'أحمد محمد',
        r'\bمحمد\s+احمد\b': 'محمد أحمد',

        # أسماء مع أبو وأم
        r'\bابو\s+([^\s]+)\b': r'أبو \1',
        r'\bام\s+([^\s]+)\b': r'أم \1',
        r'\bابو_([^\s]+)\b': r'أبو \1',
        r'\bام_([^\s]+)\b': r'أم \1',

        # تصحيحات أخرى شائعة
        r'\bعلى\b': 'علي',  # فقط إذا كان اسم منفرد
        r'\bمحمد_علي\b': 'محمد علي',
        r'\bاحمد_علي\b': 'أحمد علي',
        r'\bعلي_محمد\b': 'علي محمد',

        # أسماء مع بن وبنت
        r'\bبن\s+([^\s]+)\b': r'بن \1',
        r'\bبنت\s+([^\s]+)\b': r'بنت \1',
        r'\bبن_([^\s]+)\b': r'بن \1',
        r'\bبنت_([^\s]+)\b': r'بنت \1',
    }

    import re
    for wrong, correct in compound_corrections.items():
        text = re.sub(wrong, correct, text, flags=re.IGNORECASE)

    return text

def correct_titles_and_kunya(text):
    """تصحيح الألقاب والكنى"""
    title_corrections = {
        r'\bال\s+': 'آل ',  # آل العائلة
        r'\bابو\s+': 'أبو ',
        r'\bام\s+': 'أم ',
        r'\bبن\s+': 'بن ',
        r'\bبنت\s+': 'بنت ',
    }

    import re
    for wrong, correct in title_corrections.items():
        text = re.sub(wrong, correct, text, flags=re.IGNORECASE)

    return text

def clean_unwanted_chars(text):
    """إزالة الأرقام والرموز غير المرغوبة من الأسماء"""
    import re
    # إزالة الأرقام من نهاية الاسم (مثل علي صالح محمد الحميري1)
    text = re.sub(r'\d+$', '', text)

    # إزالة الرموز الغريبة
    text = re.sub(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]', '', text)

    return text.strip()

def get_custom_corrections():
    """
    جلب التصحيحات المخصصة من قاعدة البيانات
    """
    try:
        # استيراد محلي لتجنب مشاكل الاستيراد الدائري
        from sqlalchemy import text
        custom_corrections = {}

        # استعلام مباشر لتجنب مشاكل الاستيراد
        query = text("SELECT wrong_name, correct_name FROM name_correction WHERE is_active = 1")
        result = db.session.execute(query)

        for row in result:
            # إنشاء نمط regex للاسم الخطأ
            pattern = r'\b' + re.escape(row.wrong_name) + r'\b'
            custom_corrections[pattern] = row.correct_name

        return custom_corrections
    except Exception:
        # في حالة عدم وجود الجدول أو خطأ، إرجاع قاموس فارغ
        return {}

def get_all_corrections_guide():
    """
    إنشاء دليل شامل لجميع التصحيحات المتاحة (الثابتة والمخصصة)
    """
    guide = {
        'hamza_corrections': [],
        'alif_maqsura_corrections': [],
        'compound_names_corrections': [],
        'titles_corrections': [],
        'common_mistakes': [],
        'custom_corrections': []
    }

    # تصحيحات الهمزات
    hamza_corrections = {
        # همزة القطع في بداية الكلمة - أسماء الذكور
        'احمد': 'أحمد',
        'ابراهيم': 'إبراهيم',
        'اسماعيل': 'إسماعيل',
        'اسحاق': 'إسحاق',
        'اسلام': 'إسلام',
        'ايمن': 'أيمن',
        'ايوب': 'أيوب',
        'انس': 'أنس',
        'اسامة': 'أسامة',
        'اشرف': 'أشرف',
        'امين': 'أمين',
        'امجد': 'أمجد',
        'انور': 'أنور',
        'ايهاب': 'إيهاب',
        'الياس': 'إلياس',
        'اكرم': 'أكرم',
        'ادم': 'آدم',
        'احسان': 'إحسان',
        'اخلاص': 'إخلاص',
        'ادريس': 'إدريس',
        'اسعد': 'أسعد',
        'اشرق': 'أشرق',
        'اصيل': 'أصيل',
        'اكثم': 'أكثم',
        'الهام': 'إلهام',
        'امير': 'أمير',
        'انيس': 'أنيس',
        'اوس': 'أوس',
        'ايسر': 'أيسر',
        'اياد': 'إياد',
        'ايلاف': 'إيلاف',
        'ايناس': 'إيناس',
        'ايهم': 'إيهم',
        'اكرام': 'إكرام',
        'اكمال': 'إكمال',
        'الاء': 'آلاء',
        'امان': 'أمان',
        'امانة': 'أمانة',
        'اماني': 'أماني',
        'امجاد': 'أمجاد',
        'امة': 'أمة',
        'انجي': 'أنجي',
        'انصاف': 'إنصاف',
        'انعام': 'إنعام',
        'انوار': 'أنوار',
        'اهداء': 'إهداء',
        'اوراق': 'أوراق',
        'ايات': 'آيات',

        # همزة القطع في بداية الكلمة - أسماء الإناث
        'ايمان': 'إيمان',
        'اسراء': 'إسراء',
        'امل': 'أمل',
        'اميرة': 'أميرة',
        'اسماء': 'أسماء',
        'احلام': 'أحلام',
        'اريج': 'أريج',
        'اسيل': 'أسيل',
        'اثير': 'أثير',
        'ازهار': 'أزهار',
        'اشواق': 'أشواق',
        'اصالة': 'أصالة',
        'افراح': 'أفراح',
        'افكار': 'أفكار',
        'اقبال': 'إقبال',
        'الفت': 'ألفت',
        'الماس': 'ألماس',
        'امينة': 'أمينة',
        'انغام': 'أنغام',
        'انيسة': 'أنيسة',
        'اوصاف': 'أوصاف',
        'ايلين': 'إيلين',

        # الكنى والألقاب
        'ابو': 'أبو',
        'ام': 'أم',
        'ابن': 'ابن',
        'ابنة': 'ابنة',

        # الألقاب المهنية والاجتماعية
        'الاستاذ': 'الأستاذ',
        'الاستاذة': 'الأستاذة',
        'الاخ': 'الأخ',
        'الاخت': 'الأخت',
        'الامير': 'الأمير',
        'الاميرة': 'الأميرة',
    }

    for wrong, correct in hamza_corrections.items():
        guide['hamza_corrections'].append({
            'الاسم الخطأ': wrong,
            'الاسم الصحيح': correct,
            'نوع الخطأ': 'همزة القطع'
        })

    # تصحيحات الألف المقصورة
    alif_corrections = {
        # أسماء الذكور بالألف المقصورة
        'عيسي': 'عيسى',
        'موسي': 'موسى',
        'يحيي': 'يحيى',
        'مصطفي': 'مصطفى',
        'مرتضي': 'مرتضى',
        'على': 'علي',
        'محي': 'محيي',
        'زكى': 'زكي',
        'تقى': 'تقي',
        'عدى': 'عدي',
        'هادى': 'هادي',
        'راضى': 'راضي',
        'سارى': 'ساري',
        'حسنى': 'حسني',
        'معتزى': 'معتز',

        # أسماء الإناث بالألف المقصورة
        'هدي': 'هدى',
        'سلمي': 'سلمى',
        'ليلي': 'ليلى',
        'نجوي': 'نجوى',
        'سهي': 'سهى',
        'رني': 'رنا',
        'دني': 'دنيا',
        'مني': 'منى',
        'لبني': 'لبنى',
        'بشري': 'بشرى',
        'ذكري': 'ذكرى',
        'سري': 'سرى',
        'ثري': 'ثرى',
        'دعي': 'دعاء',
        'رجي': 'رجاء',
        'صفي': 'صفاء',
        'وفي': 'وفاء',
        'شيمي': 'شيماء',
        'اسمي': 'أسماء',
        'نادي': 'نادية',
        'راني': 'رانية',
        'ماني': 'مانية',
        'تاني': 'تانية',
        'جني': 'جنى',
        'رني': 'رنى',
        'دني': 'دنى',
        'مني': 'منى',
        'سني': 'سنى',
        'هني': 'هنى',

        # أسماء عائلات وألقاب
        'الحوثى': 'الحوثي',
        'المرتضي': 'المرتضى',
        'المصطفي': 'المصطفى',
        'الهادى': 'الهادي',
    }

    for wrong, correct in alif_corrections.items():
        guide['alif_maqsura_corrections'].append({
            'الاسم الخطأ': wrong,
            'الاسم الصحيح': correct,
            'نوع الخطأ': 'الألف المقصورة'
        })

    # تصحيحات الأسماء المركبة
    compound_corrections = {
        # أسماء عبد + اسم الله الحسنى
        'عبد الله': 'عبدالله',
        'عبد الرحمن': 'عبدالرحمن',
        'عبد العزيز': 'عبدالعزيز',
        'عبد الكريم': 'عبدالكريم',
        'عبد الرحيم': 'عبدالرحيم',
        'عبد الحميد': 'عبدالحميد',
        'عبد القادر': 'عبدالقادر',
        'عبد الناصر': 'عبدالناصر',
        'عبد المجيد': 'عبدالمجيد',
        'عبد الحكيم': 'عبدالحكيم',
        'عبد الحليم': 'عبدالحليم',
        'عبد الرؤوف': 'عبدالرؤوف',
        'عبد الغني': 'عبدالغني',
        'عبد الباسط': 'عبدالباسط',
        'عبد الفتاح': 'عبدالفتاح',
        'عبد الوهاب': 'عبدالوهاب',
        'عبد الصمد': 'عبدالصمد',
        'عبد الجليل': 'عبدالجليل',
        'عبد الجبار': 'عبدالجبار',
        'عبد السلام': 'عبدالسلام',
        'عبد الملك': 'عبدالملك',
        'عبد الحق': 'عبدالحق',
        'عبد الخالق': 'عبدالخالق',
        'عبد الرزاق': 'عبدالرزاق',
        'عبد اللطيف': 'عبداللطيف',

        # تصحيحات أخطاء شائعة في عبدالله
        'عبداللة': 'عبدالله',
        'عبدالة': 'عبدالله',
        'عبد_الله': 'عبدالله',
        'عبدالاه': 'عبدالله',
        'عبدالاة': 'عبدالله',

        # أسماء مركبة أخرى شائعة
        'محمد على': 'محمد علي',
        'احمد علي': 'أحمد علي',
        'علي محمد': 'علي محمد',
        'حسن علي': 'حسن علي',
        'علي حسن': 'علي حسن',
        'محمد حسن': 'محمد حسن',
        'حسن محمد': 'حسن محمد',
        'احمد محمد': 'أحمد محمد',
        'محمد احمد': 'محمد أحمد',

        # أسماء مع أبو وأم
        'ابو الدين': 'أبو الدين',
        'ابو علي': 'أبو علي',
        'ابو محمد': 'أبو محمد',
        'ام علي': 'أم علي',
        'ام محمد': 'أم محمد',
        'ابو_الدين': 'أبو الدين',
        'ام_علي': 'أم علي',

        # تصحيحات أخرى شائعة
        'محمد_علي': 'محمد علي',
        'احمد_علي': 'أحمد علي',
        'علي_محمد': 'علي محمد',

        # أسماء مع بن وبنت
        'بن علي': 'بن علي',
        'بنت محمد': 'بنت محمد',
        'بن_علي': 'بن علي',
        'بنت_محمد': 'بنت محمد',
    }

    for wrong, correct in compound_corrections.items():
        guide['compound_names_corrections'].append({
            'الاسم الخطأ': wrong,
            'الاسم الصحيح': correct,
            'نوع الخطأ': 'اسم مركب'
        })

    # تصحيحات الألقاب والكنى
    titles_corrections = {
        'ال العائلة': 'آل العائلة',
        'ابو الدين': 'أبو الدين',
        'ام الخير': 'أم الخير',
        'بن علي': 'بن علي',
        'بنت محمد': 'بنت محمد',
    }

    for wrong, correct in titles_corrections.items():
        guide['titles_corrections'].append({
            'الاسم الخطأ': wrong,
            'الاسم الصحيح': correct,
            'نوع الخطأ': 'لقب أو كنية'
        })

    # أخطاء شائعة أخرى
    common_mistakes = {
        'علي صالح محمد الحميري1': 'علي صالح محمد الحميري',
        'احمد@محمد': 'أحمد محمد',
        'علي#حسن': 'علي حسن',
        'محمد123': 'محمد',
        'فاطمة456': 'فاطمة',
    }

    for wrong, correct in common_mistakes.items():
        guide['common_mistakes'].append({
            'الاسم الخطأ': wrong,
            'الاسم الصحيح': correct,
            'نوع الخطأ': 'رموز أو أرقام غير مرغوبة'
        })

    # إضافة التصحيحات المخصصة من قاعدة البيانات
    try:
        from sqlalchemy import text
        query = text("""
            SELECT nc.wrong_name, nc.correct_name, nc.correction_type,
                   nc.created_at, nc.usage_count, u.username
            FROM name_correction nc
            LEFT JOIN user u ON nc.created_by = u.id
            WHERE nc.is_active = 1
        """)
        result = db.session.execute(query)

        for row in result:
            guide['custom_corrections'].append({
                'الاسم الخطأ': row.wrong_name,
                'الاسم الصحيح': row.correct_name,
                'نوع الخطأ': get_correction_type_name(row.correction_type),
                'تاريخ الإضافة': str(row.created_at)[:10] if row.created_at else 'غير معروف',
                'عدد مرات الاستخدام': row.usage_count or 0,
                'أضافه': row.username or 'غير معروف'
            })
    except Exception:
        # في حالة عدم وجود الجدول أو خطأ، تجاهل التصحيحات المخصصة
        pass

    return guide

def get_correction_type_name(correction_type):
    """
    ترجمة نوع التصحيح إلى العربية
    """
    type_names = {
        'hamza': 'تصحيح الهمزات',
        'alif_maqsura': 'تصحيح الألف المقصورة',
        'compound_names': 'الأسماء المركبة',
        'titles': 'الألقاب والكنى',
        'symbols': 'إزالة الرموز والأرقام',
        'other': 'أخرى'
    }
    return type_names.get(correction_type, correction_type)

def analyze_names_smart_advanced(excel_data, db_data, column_info, selected_course=None, analysis_type='course'):
    """
    نظام تحليل ذكي شامل يغطي جميع الاحتمالات الممكنة
    """
    print(f"🧠 دخول النظام الذكي المتقدم")
    print(f"📊 excel_data: {len(excel_data)} سجل")
    print(f"📊 db_data: {len(db_data)} سجل")
    print(f"🎯 analysis_type: {analysis_type}")
    print(f"🎓 selected_course: {selected_course.course_number if selected_course else 'لا توجد دورة محددة'}")

    # جلب بيانات الدورة المحددة إذا كانت موجودة
    course_participants = []
    course_evaluations = []

    if selected_course:
        try:
            # جلب المشاركين في الدورة
            course_participants_query = db.session.execute(text("""
                SELECT pd.id, pd.full_name, pd.national_number, pd.phone, pd.military_number,
                       cp.id as participant_id, cp.status
                FROM course_participant cp
                JOIN person_data pd ON cp.personal_data_id = pd.id
                WHERE cp.course_id = :course_id
            """), {'course_id': selected_course.id})

            course_participants = [dict(row._mapping) for row in course_participants_query]
            print(f"👥 المشاركين في الدورة: {len(course_participants)}")

            # جلب التقييمات في الدورة (اختياري)
            course_evaluations = []
            try:
                course_evaluations_query = db.session.execute(text("""
                    SELECT pe.*, pd.full_name, pd.national_number, pd.phone, pd.military_number,
                           cp.id as participant_id
                    FROM participant_evaluations pe
                    JOIN course_participant cp ON pe.participant_id = cp.id
                    JOIN person_data pd ON cp.personal_data_id = pd.id
                    WHERE pe.course_id = :course_id
                """), {'course_id': selected_course.id})

                course_evaluations = [dict(row._mapping) for row in course_evaluations_query]
                print(f"📊 التقييمات في الدورة: {len(course_evaluations)}")
            except Exception as eval_error:
                print(f"⚠️ لا يمكن جلب التقييمات (جدول غير موجود): {eval_error}")
                course_evaluations = []

        except Exception as e:
            print(f"⚠️ خطأ في جلب بيانات الدورة: {str(e)}")
            course_participants = []
            course_evaluations = []

    # دالة تطبيع الأسماء للمقارنة
    def normalize_name(name):
        if not name:
            return ""
        return ' '.join(name.strip().split()).lower()

    # إنشاء فهارس للبحث السريع
    db_name_index = {}
    for record in db_data:
        normalized = normalize_name(record['name'])
        if normalized:  # تأكد من أن الاسم ليس فارغاً
            db_name_index[normalized] = record

    course_participants_index = {}
    course_participants_by_id = {}
    for p in course_participants:
        normalized = normalize_name(p['full_name'])
        if normalized:
            course_participants_index[normalized] = p
        # إنشاء فهرس بناءً على ID أيضاً
        if p.get('id'):
            course_participants_by_id[p['id']] = p

    course_evaluations_index = {}
    for e in course_evaluations:
        normalized = normalize_name(e['full_name'])
        if normalized:
            course_evaluations_index[normalized] = e

    print(f"📋 فهارس البحث: db={len(db_name_index)}, course={len(course_participants_index)}, evaluations={len(course_evaluations_index)}")

    # عرض عينة من البيانات للتشخيص
    if db_data:
        print(f"📋 عينة من قاعدة البيانات: {db_data[0]}")
    if excel_data:
        print(f"📋 عينة من Excel: {excel_data[0]}")
        print(f"📋 أعمدة Excel: {list(excel_data[0].keys())}")

    # نتائج التحليل الذكي
    smart_results = {
        # الفئات الذكية الجديدة
        'in_db_in_course': [],           # موجود في قاعدة البيانات + موجود في الدورة
        'in_db_not_in_course': [],       # موجود في قاعدة البيانات + غير موجود في الدورة
        'in_db_has_evaluation': [],      # موجود في قاعدة البيانات + لديه تقييم
        'in_db_no_evaluation': [],       # موجود في قاعدة البيانات + ليس لديه تقييم
        'not_in_db_new_person': [],      # غير موجود في قاعدة البيانات - شخص جديد
        'duplicate_in_course': [],       # مكرر في الدورة
        'duplicate_evaluation': [],      # تقييم مكرر
        'needs_update': [],              # يحتاج تحديث
        'ready_to_add': [],              # جاهز للإضافة

        # الفئات التقليدية (للتوافق)
        'exact_matches': [],
        'new_records': [],
        'corrected_names': [],
        'blocked_duplicates': [],
        'allowed_duplicates': [],
        'similarity_matches': {
            'triple': [],
            'quadruple': [],
            'quintuple': [],
            'full_with_title': [],
            'six_plus': []
        },
        'duplicate_matches': {},

        # إحصائيات ذكية
        'smart_statistics': {
            'total_processed': len(excel_data),
            'in_db_count': 0,
            'not_in_db_count': 0,
            'in_course_count': 0,
            'not_in_course_count': 0,
            'has_evaluation_count': 0,
            'no_evaluation_count': 0,
            'duplicate_course_count': 0,
            'duplicate_evaluation_count': 0,
            'ready_to_add_count': 0,
            'needs_update_count': 0,
            # إحصائيات جديدة للتقسيم
            'duplicate_in_course_count': 0,
            'in_db_not_in_course_count': 0
        }
    }

    # دالة التصحيح المحسنة
    def correct_arabic_text(text):
        if not isinstance(text, str) or not text.strip():
            return text
        text = ' '.join(text.split())
        custom_corrections = get_custom_corrections()
        import re
        for pattern, correct in custom_corrections.items():
            text = re.sub(pattern, correct, text, flags=re.IGNORECASE)
        text = correct_hamza(text)
        text = correct_alif_maqsura(text)
        text = correct_taa_marbouta(text)
        text = correct_compound_names(text)
        text = correct_titles_and_kunya(text)
        text = clean_unwanted_chars(text)
        return text.strip()

    # معالجة كل سجل من Excel بالنظام الذكي
    print(f"🔄 بدء معالجة {len(excel_data)} سجل من Excel...")

    for index, excel_record in enumerate(excel_data):
        try:
            original_name = excel_record.get('name', '').strip()
            if not original_name:
                print(f"⚠️ السجل {index + 1}: اسم فارغ - تم تخطيه")
                continue

            # تصحيح الاسم
            corrected_name = correct_arabic_text(original_name)
            normalized_name = normalize_name(corrected_name)

            print(f"🔍 معالجة السجل {index + 1}: '{original_name}' -> '{corrected_name}' -> '{normalized_name}'")

            # تحديد حالة السجل الذكية
            record_status = {
                'original_name': original_name,
                'corrected_name': corrected_name,
                'normalized_name': normalized_name,
                'excel_record': excel_record,
                'row_index': index + 2,
                'in_database': False,
                'in_course': False,
                'has_evaluation': False,
                'is_duplicate': False,
                'action_needed': '',
                'category': '',
                'db_match': None,
                'course_match': None,
                'evaluation_match': None
            }

            # فحص وجود الشخص في قاعدة البيانات
            db_match = db_name_index.get(normalized_name)
            print(f"   🔍 البحث في قاعدة البيانات: '{normalized_name}' -> {'موجود' if db_match else 'غير موجود'}")

            if db_match:
                record_status['in_database'] = True
                record_status['db_match'] = db_match
                smart_results['smart_statistics']['in_db_count'] += 1
                print(f"   ✅ تم العثور على تطابق: {db_match['name']}")

                # فحص وجود الشخص في الدورة المحددة
                if selected_course:
                    # البحث بناءً على ID الشخص في قاعدة البيانات
                    person_id = db_match.get('id')
                    course_match = course_participants_by_id.get(person_id)

                    print(f"   🔍 فحص وجود الشخص في الدورة: ID={person_id} -> {'موجود' if course_match else 'غير موجود'}")

                    if course_match:
                        record_status['in_course'] = True
                        record_status['course_match'] = course_match
                        smart_results['smart_statistics']['in_course_count'] += 1

                        # تحديد الفئة: موجود في قاعدة البيانات + موجود في الدورة
                        if analysis_type == 'course':
                            record_status['category'] = 'duplicate_in_course'
                            record_status['action_needed'] = 'مكرر في الدورة - لن يتم إضافته'
                            smart_results['duplicate_in_course'].append(record_status)
                            smart_results['smart_statistics']['duplicate_course_count'] += 1
                            smart_results['smart_statistics']['duplicate_in_course_count'] += 1
                        elif analysis_type == 'evaluation':
                            # فحص وجود تقييم للشخص
                            evaluation_match = course_evaluations_index.get(normalized_name)
                            if evaluation_match:
                                record_status['has_evaluation'] = True
                                record_status['evaluation_match'] = evaluation_match
                                record_status['category'] = 'has_evaluation_needs_update'
                                record_status['action_needed'] = 'لديه تقييم - يحتاج تحديث'
                                smart_results['in_db_has_evaluation'].append(record_status)
                                smart_results['smart_statistics']['has_evaluation_count'] += 1
                            else:
                                record_status['category'] = 'no_evaluation_add_new'
                                record_status['action_needed'] = 'ليس لديه تقييم - سيتم إضافة تقييم'
                                smart_results['in_db_no_evaluation'].append(record_status)
                                smart_results['smart_statistics']['no_evaluation_count'] += 1
                    else:
                        record_status['category'] = 'in_db_not_in_course'
                        record_status['action_needed'] = 'موجود في قاعدة البيانات - سيتم إضافته للدورة'
                        smart_results['in_db_not_in_course'].append(record_status)
                        smart_results['smart_statistics']['not_in_course_count'] += 1
                        smart_results['smart_statistics']['in_db_not_in_course_count'] += 1
                else:
                    # لا توجد دورة محددة - فقط تحديث البيانات
                    record_status['category'] = 'in_db_update_data'
                    record_status['action_needed'] = 'موجود في قاعدة البيانات - يمكن تحديث البيانات'
                    smart_results['exact_matches'].append({
                        'excel_name': corrected_name,
                        'db_name': db_match['name'],
                        'excel_record': excel_record,
                        'db_record': db_match
                    })
            else:
                # غير موجود في قاعدة البيانات
                print(f"   ❌ غير موجود في قاعدة البيانات - سيتم إضافته كسجل جديد")
                record_status['category'] = 'not_in_db_new_person'
                record_status['action_needed'] = 'غير موجود - سيتم إنشاء شخص جديد'
                smart_results['not_in_db_new_person'].append(record_status)
                smart_results['smart_statistics']['not_in_db_count'] += 1

                # إضافة للسجلات الجديدة (للتوافق)
                smart_results['new_records'].append({
                    'corrected_name': corrected_name,
                    'original_name': original_name,
                    'excel_record': excel_record,
                    'row_index': index + 2,
                    'national_id': excel_record.get('national_id', ''),
                    'phone': excel_record.get('phone', ''),
                    'military_id': excel_record.get('military_id', ''),
                    'was_corrected': original_name != corrected_name
                })

            # تسجيل التصحيحات
            if original_name != corrected_name:
                smart_results['corrected_names'].append({
                    'original': original_name,
                    'corrected': corrected_name,
                    'row_index': index + 2
                })

        except Exception as e:
            print(f"⚠️ خطأ في معالجة السجل {index + 1}: {str(e)}")
            continue

    # حساب الإحصائيات النهائية
    smart_results['smart_statistics']['ready_to_add_count'] = len(smart_results['not_in_db_new_person'])
    smart_results['smart_statistics']['needs_update_count'] = len(smart_results.get('in_db_has_evaluation', []))

    # نسخ النتائج للتوافق مع النظام القديم
    smart_results['statistics'] = {
        'total_processed': smart_results['smart_statistics']['total_processed'],
        'exact_matches_count': smart_results['smart_statistics']['in_db_count'],
        'new_records_count': smart_results['smart_statistics']['not_in_db_count'],
        'corrected_count': len(smart_results['corrected_names']),
        'blocked_duplicates_count': smart_results['smart_statistics']['duplicate_course_count'],
        'allowed_duplicates_count': 0
    }

    # نسخ البيانات للتوافق مع الصفحة
    smart_results['new_records'] = []
    for person in smart_results['not_in_db_new_person']:
        smart_results['new_records'].append({
            'corrected_name': person['corrected_name'],
            'original_name': person['original_name'],
            'excel_record': person['excel_record'],
            'row_index': person['excel_record']['row_index'],
            'national_id': person['excel_record']['national_id'],
            'phone': person['excel_record']['phone'],
            'military_id': person['excel_record']['military_id'],
            'was_corrected': person['original_name'] != person['corrected_name']
        })

    print(f"✅ انتهى النظام الذكي المتقدم")
    print(f"📊 الإحصائيات: {smart_results['smart_statistics']}")
    print(f"📊 duplicate_in_course count: {len(smart_results['duplicate_in_course'])}")
    print(f"📊 in_db_not_in_course count: {len(smart_results['in_db_not_in_course'])}")

    return smart_results

def analyze_names_advanced(excel_data, db_data, column_info, selected_course=None):
    """
    تحليل ومقايسة الأسماء والبيانات الشخصية مع التحقق من التطابق المتقدم
    """
    print(f"🔧 دخول دالة analyze_names_advanced")
    print(f"📊 excel_data: {len(excel_data)} سجل")
    print(f"📊 db_data: {len(db_data)} سجل")
    print(f"📋 column_info: {column_info}")
    print(f"🎓 selected_course: {selected_course.course_number if selected_course else 'لا توجد دورة محددة'}")

    results = {
        'total_excel_records': len(excel_data),
        'total_db_records': len(db_data),
        'corrected_names': [],
        'exact_matches': [],
        'similarity_matches': {
            'triple': [],      # تشابه ثلاثي
            'quadruple': [],   # تشابه رباعي
            'quintuple': [],   # تشابه خماسي
            'full_with_title': [], # تشابه خماسي مع اللقب
            'six_plus': []     # أسماء أكثر من ستة أجزاء
        },
        'duplicate_matches': {
            'name_national_id': [],    # تطابق الاسم والرقم الوطني
            'name_phone': [],          # تطابق الاسم ورقم الهاتف
            'name_military_id': [],    # تطابق الاسم والرقم العسكري
            'national_id_only': [],    # تطابق الرقم الوطني فقط
            'phone_only': [],          # تطابق رقم الهاتف فقط
            'military_id_only': [],    # تطابق الرقم العسكري فقط
        },
        'allowed_duplicates': [],      # أسماء مكررة مسموحة (مختلفة في البيانات الأخرى)
        'blocked_duplicates': [],      # أسماء مكررة مرفوضة (متطابقة في البيانات)
        'new_records': [],
        'statistics': {},
        'column_info': column_info,
        'course_analysis': {           # تحليل خاص بالدورة المختارة
            'selected_course': {
                'id': selected_course.id,
                'course_number': selected_course.course_number,
                'title': selected_course.title,
                'agency': selected_course.agency.name if selected_course.agency else '',
                'center_name': selected_course.center.name if selected_course.center else '',
                'total_participants': selected_course.total_participants or 0
            } if selected_course else None,
            'existing_participants': [],    # المشاركين الموجودين في الدورة
            'new_participants': [],         # المشاركين الجدد الذين سيتم إضافتهم
            'duplicate_participants': [],   # المشاركين المكررين في الدورة
            'participants_summary': {}      # ملخص المشاركين
        }
    }

    print(f"✅ تم إنشاء هيكل النتائج")

    # جلب المشاركين الحاليين في الدورة إذا تم اختيار دورة
    current_course_participants = []
    if selected_course:
        print(f"🔍 جلب المشاركين الحاليين في الدورة {selected_course.course_number}")
        participants = CourseParticipant.query.filter_by(course_id=selected_course.id).all()

        for participant in participants:
            if participant.personal_data:
                current_course_participants.append({
                    'id': participant.personal_data.id,
                    'name': participant.personal_data.full_name,
                    'national_id': participant.personal_data.national_number or '',
                    'phone': participant.personal_data.phone or '',
                    'military_id': participant.personal_data.military_number or '',
                    'status': participant.status,
                    'entry_date': participant.entry_date.strftime('%Y-%m-%d') if participant.entry_date else ''
                })

        print(f"📊 تم العثور على {len(current_course_participants)} مشارك حالي في الدورة")
        results['course_analysis']['existing_participants'] = current_course_participants

    # دالة التصحيح المحسنة
    def correct_arabic_text(text):
        """
        دالة تصحيح شاملة للنصوص العربية (تشمل التصحيحات المخصصة والثابتة)
        """
        if not isinstance(text, str) or not text.strip():
            return text

        # إزالة الفراغات الزائدة
        text = ' '.join(text.split())

        # تطبيق التصحيحات المخصصة أولاً
        custom_corrections = get_custom_corrections()
        import re
        for pattern, correct in custom_corrections.items():
            text = re.sub(pattern, correct, text, flags=re.IGNORECASE)

        # تطبيق التصحيحات الثابتة
        text = correct_hamza(text)
        text = correct_alif_maqsura(text)
        text = correct_taa_marbouta(text)
        text = correct_compound_names(text)
        text = correct_titles_and_kunya(text)

        # إزالة الأرقام والرموز غير المرغوبة
        text = clean_unwanted_chars(text)

        return text.strip()

    def normalize_identifier(identifier):
        """تنظيف وتوحيد المعرفات (أرقام الهوية، الهاتف، إلخ)"""
        if not identifier or identifier == 'nan':
            return ''

        # إزالة الفراغات والرموز غير المرغوبة
        import re
        identifier = re.sub(r'[^\d]', '', str(identifier))
        return identifier.strip()

    def check_duplicate_match(excel_record, db_record):
        """
        فحص التطابق بين سجل من Excel وسجل من قاعدة البيانات
        إرجاع نوع التطابق أو None إذا لم يكن هناك تطابق
        """
        excel_name = excel_record['name'].lower().strip()
        db_name = db_record['name'].lower().strip()

        excel_national = normalize_identifier(excel_record['national_id'])
        db_national = normalize_identifier(db_record['national_id'])

        excel_phone = normalize_identifier(excel_record['phone'])
        db_phone = normalize_identifier(db_record['phone'])

        excel_military = normalize_identifier(excel_record['military_id'])
        db_military = normalize_identifier(db_record['military_id'])

        # فحص التطابقات المختلفة
        matches = []

        # تطابق الاسم والرقم الوطني
        if excel_name == db_name and excel_national and db_national and excel_national == db_national:
            matches.append('name_national_id')

        # تطابق الاسم ورقم الهاتف
        if excel_name == db_name and excel_phone and db_phone and excel_phone == db_phone:
            matches.append('name_phone')

        # تطابق الاسم والرقم العسكري
        if excel_name == db_name and excel_military and db_military and excel_military == db_military:
            matches.append('name_military_id')

        # تطابق الرقم الوطني فقط (بدون الاسم)
        if excel_national and db_national and excel_national == db_national and excel_name != db_name:
            matches.append('national_id_only')

        # تطابق رقم الهاتف فقط (بدون الاسم)
        if excel_phone and db_phone and excel_phone == db_phone and excel_name != db_name:
            matches.append('phone_only')

        # تطابق الرقم العسكري فقط (بدون الاسم)
        if excel_military and db_military and excel_military == db_military and excel_name != db_name:
            matches.append('military_id_only')

        return matches if matches else None

    def get_match_details(match_type):
        """إرجاع تفاصيل نوع التطابق"""
        details = {
            'name_national_id': 'تطابق في الاسم والرقم الوطني',
            'name_phone': 'تطابق في الاسم ورقم الهاتف',
            'name_military_id': 'تطابق في الاسم والرقم العسكري',
            'national_id_only': 'تطابق في الرقم الوطني فقط (أسماء مختلفة)',
            'phone_only': 'تطابق في رقم الهاتف فقط (أسماء مختلفة)',
            'military_id_only': 'تطابق في الرقم العسكري فقط (أسماء مختلفة)'
        }
        return details.get(match_type, 'نوع تطابق غير معروف')

    # معالجة كل سجل من ملف Excel
    for excel_record in excel_data:
        original_name = excel_record['name']

        # تطبيق التصحيح المحسن على الاسم
        corrected_name = correct_arabic_text(original_name)

        # تحديث السجل بالاسم المصحح
        excel_record['corrected_name'] = corrected_name

        # التحقق من حدوث تصحيح
        was_corrected = (original_name != corrected_name)

        # إضافة الاسم المصحح إذا تم تصحيحه
        if was_corrected:
            results['corrected_names'].append({
                'original': original_name,
                'corrected': corrected_name,
                'row_index': excel_record['row_index'],
                'national_id': excel_record['national_id'],
                'phone': excel_record['phone'],
                'military_id': excel_record['military_id']
            })

        # فحص التطابقات مع قاعدة البيانات
        found_duplicates = []
        exact_match_found = False
        similarity_match_found = False

        for db_record in db_data:
            # فحص التطابق المتقدم
            duplicate_types = check_duplicate_match(
                {'name': corrected_name, 'national_id': excel_record['national_id'],
                 'phone': excel_record['phone'], 'military_id': excel_record['military_id']},
                db_record
            )

            if duplicate_types:
                for dup_type in duplicate_types:
                    excel_record_copy = excel_record.copy()
                    excel_record_copy['corrected_name'] = corrected_name
                    duplicate_info = {
                        'excel_record': excel_record_copy,
                        'db_record': db_record,
                        'duplicate_type': dup_type,
                        'match_details': get_match_details(dup_type)
                    }
                    results['duplicate_matches'][dup_type].append(duplicate_info)
                    found_duplicates.append(dup_type)

                # إذا كان هناك تطابق كامل في الاسم والبيانات، فهو مطابقة تامة
                if any(dt in ['name_national_id', 'name_phone', 'name_military_id'] for dt in duplicate_types):
                    exact_match_found = True

            # فحص التشابه في الأسماء (إذا لم يكن هناك تطابق تام)
            if not exact_match_found and corrected_name.lower().strip() == db_record['name'].lower().strip():
                results['exact_matches'].append({
                    'excel_name': corrected_name,
                    'db_name': db_record['name'],
                    'excel_record': excel_record,
                    'db_record': db_record
                })
                exact_match_found = True

        # تحديد حالة السجل
        if found_duplicates:
            # فحص ما إذا كان التطابق يمنع الإضافة أم لا
            blocking_duplicates = [dt for dt in found_duplicates if dt in ['name_national_id', 'name_phone', 'name_military_id', 'national_id_only', 'phone_only', 'military_id_only']]

            if blocking_duplicates:
                # هذا السجل مرفوض بسبب التطابق
                results['blocked_duplicates'].append({
                    'excel_record': excel_record,
                    'corrected_name': corrected_name,
                    'duplicate_types': blocking_duplicates,
                    'reason': 'تطابق في البيانات الشخصية يمنع الإضافة'
                })
            else:
                # يمكن إضافة هذا السجل (اسم مكرر لكن بيانات مختلفة)
                results['allowed_duplicates'].append({
                    'excel_record': excel_record,
                    'corrected_name': corrected_name,
                    'reason': 'اسم مكرر لكن البيانات الشخصية مختلفة'
                })
        elif not exact_match_found:
            # فحص التشابه في الأسماء
            name_parts = corrected_name.strip().split()
            similarity_found = False

            for db_record in db_data:
                db_parts = db_record['name'].strip().split()

                # حساب نسبة التشابه
                common_parts = set(name_parts) & set(db_parts)

                # تشابه ثلاثي (3 أجزاء متطابقة)
                if len(name_parts) >= 3 and len(db_parts) >= 3:
                    common_first_three = set(name_parts[:3]) & set(db_parts[:3])
                    if len(common_first_three) >= 3:
                        results['similarity_matches']['triple'].append({
                            'excel_name': corrected_name,
                            'db_name': db_record['name'],
                            'excel_record': excel_record,
                            'db_record': db_record,
                            'common_parts': list(common_first_three)
                        })
                        similarity_found = True
                        continue

                # تشابه رباعي (4 أجزاء متطابقة)
                if len(name_parts) >= 4 and len(db_parts) >= 4:
                    common_first_four = set(name_parts[:4]) & set(db_parts[:4])
                    if len(common_first_four) >= 4:
                        results['similarity_matches']['quadruple'].append({
                            'excel_name': corrected_name,
                            'db_name': db_record['name'],
                            'excel_record': excel_record,
                            'db_record': db_record,
                            'common_parts': list(common_first_four)
                        })
                        similarity_found = True
                        continue

                # تشابه خماسي (5 أجزاء متطابقة)
                if len(name_parts) >= 5 and len(db_parts) >= 5:
                    common_first_five = set(name_parts[:5]) & set(db_parts[:5])
                    if len(common_first_five) >= 5:
                        results['similarity_matches']['quintuple'].append({
                            'excel_name': corrected_name,
                            'db_name': db_record['name'],
                            'excel_record': excel_record,
                            'db_record': db_record,
                            'common_parts': list(common_first_five)
                        })
                        similarity_found = True
                        continue

            # أسماء أكثر من ستة أجزاء
            if len(name_parts) > 6:
                results['similarity_matches']['six_plus'].append({
                    'name': corrected_name,
                    'parts_count': len(name_parts),
                    'parts': name_parts,
                    'excel_record': excel_record
                })

            # إذا لم يتم العثور على تشابه، فهو سجل جديد
            if not similarity_found:
                results['new_records'].append({
                    'excel_record': excel_record,
                    'corrected_name': corrected_name
                })

    # تحليل المشاركين في الدورة إذا تم اختيار دورة
    if selected_course:
        print(f"🔍 تحليل المشاركين للدورة {selected_course.course_number}")

        # جمع الأسماء الجديدة التي ستتم إضافتها للدورة
        new_participants_for_course = []
        duplicate_participants_for_course = []

        # فحص السجلات الجديدة
        for record in results['new_records']:
            corrected_name = record['corrected_name']
            excel_record = record['excel_record']

            # التحقق من عدم وجود الاسم في المشاركين الحاليين
            is_already_participant = False
            for current_participant in current_course_participants:
                if corrected_name.lower().strip() == current_participant['name'].lower().strip():
                    duplicate_participants_for_course.append({
                        'name': corrected_name,
                        'excel_record': excel_record,
                        'existing_participant': current_participant,
                        'reason': 'موجود مسبقاً في كشف الدورة'
                    })
                    is_already_participant = True
                    break

            if not is_already_participant:
                new_participants_for_course.append({
                    'name': corrected_name,
                    'excel_record': excel_record,
                    'type': 'سجل جديد'
                })

        # فحص الأسماء المكررة المسموحة
        for allowed in results['allowed_duplicates']:
            corrected_name = allowed['corrected_name']
            excel_record = allowed['excel_record']

            # التحقق من عدم وجود الاسم في المشاركين الحاليين
            is_already_participant = False
            for current_participant in current_course_participants:
                if corrected_name.lower().strip() == current_participant['name'].lower().strip():
                    duplicate_participants_for_course.append({
                        'name': corrected_name,
                        'excel_record': excel_record,
                        'existing_participant': current_participant,
                        'reason': 'موجود مسبقاً في كشف الدورة'
                    })
                    is_already_participant = True
                    break

            if not is_already_participant:
                new_participants_for_course.append({
                    'name': corrected_name,
                    'excel_record': excel_record,
                    'type': 'اسم مكرر مسموح'
                })

        # حفظ نتائج تحليل الدورة
        results['course_analysis']['new_participants'] = new_participants_for_course
        results['course_analysis']['duplicate_participants'] = duplicate_participants_for_course
        results['course_analysis']['participants_summary'] = {
            'current_participants_count': len(current_course_participants),
            'new_participants_count': len(new_participants_for_course),
            'duplicate_participants_count': len(duplicate_participants_for_course),
            'total_after_import': len(current_course_participants) + len(new_participants_for_course)
        }

        print(f"📊 ملخص المشاركين في الدورة:")
        print(f"   المشاركين الحاليين: {len(current_course_participants)}")
        print(f"   المشاركين الجدد: {len(new_participants_for_course)}")
        print(f"   المشاركين المكررين: {len(duplicate_participants_for_course)}")
        print(f"   إجمالي بعد الاستيراد: {len(current_course_participants) + len(new_participants_for_course)}")

    # حساب الإحصائيات المتقدمة
    results['statistics'] = {
        'total_processed': len(excel_data),
        'corrected_count': len(results['corrected_names']),
        'exact_matches_count': len(results['exact_matches']),
        'triple_similarity_count': len(results['similarity_matches']['triple']),
        'quadruple_similarity_count': len(results['similarity_matches']['quadruple']),
        'quintuple_similarity_count': len(results['similarity_matches']['quintuple']),
        'full_with_title_count': len(results['similarity_matches']['full_with_title']),
        'six_plus_count': len(results['similarity_matches']['six_plus']),
        'new_records_count': len(results['new_records']),
        'allowed_duplicates_count': len(results['allowed_duplicates']),
        'blocked_duplicates_count': len(results['blocked_duplicates']),

        # إحصائيات التطابق المتقدم
        'name_national_id_matches': len(results['duplicate_matches']['name_national_id']),
        'name_phone_matches': len(results['duplicate_matches']['name_phone']),
        'name_military_id_matches': len(results['duplicate_matches']['name_military_id']),
        'national_id_only_matches': len(results['duplicate_matches']['national_id_only']),
        'phone_only_matches': len(results['duplicate_matches']['phone_only']),
        'military_id_only_matches': len(results['duplicate_matches']['military_id_only']),

        # إحصائيات الأعمدة المتوفرة
        'has_national_id_column': bool(column_info['national_id_column']),
        'has_phone_column': bool(column_info['phone_column']),
        'has_military_id_column': bool(column_info['military_id_column'])
    }

    return results

def analyze_names_for_evaluation(excel_data, db_data, column_info, selected_course=None):
    """
    تحليل ومقايسة الأسماء للتقييمات - مع التصنيف الدقيق للفئات الأربعة

    الفئات الأربعة الواضحة:
    1. category_1: غير موجودين في قاعدة البيانات - سيتم إدخالهم للقاعدة والدورة والتقييم
    2. category_2: موجودين في القاعدة والدورة وغير مدخل لهم تقييم - سيتم إضافة التقييم فقط
    3. category_3: موجودين في القاعدة وغير موجودين في الدورة - سيتم إضافتهم للدورة والتقييم
    4. category_4: موجودين في القاعدة والدورة ولديهم تقييم - سيتم تحديث التقييم
    """
    print(f"🔧 بدء تحليل التقييمات الجديد والمحسن")
    print(f"📊 excel_data: {len(excel_data)} سجل")
    print(f"📊 db_data: {len(db_data)} سجل")
    print(f"🎓 selected_course: {selected_course.course_number if selected_course else 'لا توجد دورة محددة'}")

    # هيكل النتائج الجديد والواضح
    results = {
        'total_excel_records': len(excel_data),
        'total_db_records': len(db_data),
        'corrected_names': [],

        # الفئات الأربعة الرئيسية للتقييمات
        'category_1': [],  # غير موجود في القاعدة - سيتم إنشاؤه + إضافته للدورة + إدخال تقييمه
        'category_2': [],  # موجود في القاعدة والدورة بدون تقييم - سيتم إضافة التقييم فقط
        'category_3': [],  # موجود في القاعدة وليس في الدورة - سيتم إضافته للدورة + إدخال تقييمه
        'category_4': [],  # موجود في القاعدة والدورة ولديه تقييم - سيتم تحديث تقييمه

        # سجلات مرفوضة
        'blocked_duplicates': [],  # أسماء مكررة مرفوضة (تطابق كامل في البيانات)

        'statistics': {},
        'column_info': column_info,
        'selected_course': {
            'id': selected_course.id,
            'course_number': selected_course.course_number,
            'title': selected_course.title,
            'agency': selected_course.agency.name if selected_course.agency else '',
            'center_name': selected_course.center.name if selected_course.center else '',
            'total_participants': selected_course.total_participants or 0
        } if selected_course else None
    }

    print(f"✅ تم إنشاء هيكل النتائج الجديد")

    # جلب بيانات الدورة المحددة
    if not selected_course:
        print("❌ لا يمكن تحليل التقييمات بدون تحديد دورة")
        return results

    print(f"🔍 جلب بيانات الدورة {selected_course.course_number}")

    # جلب المشاركين في الدورة
    course_participants = []
    try:
        participants_query = db.session.execute(text("""
            SELECT pd.id, pd.full_name, pd.national_number, pd.phone, pd.military_number,
                   cp.id as participant_id, cp.status
            FROM course_participant cp
            JOIN person_data pd ON cp.personal_data_id = pd.id
            WHERE cp.course_id = :course_id
        """), {'course_id': selected_course.id})

        course_participants = [dict(row._mapping) for row in participants_query]
        print(f"📊 المشاركين في الدورة: {len(course_participants)}")
    except Exception as e:
        print(f"⚠️ خطأ في جلب المشاركين: {e}")

    # جلب التقييمات في الدورة
    course_evaluations = []
    try:
        evaluations_query = db.session.execute(text("""
            SELECT pe.*, pd.full_name, pd.national_number, pd.phone, pd.military_number,
                   cp.id as participant_id
            FROM participant_evaluations pe
            JOIN course_participant cp ON pe.participant_id = cp.id
            JOIN person_data pd ON cp.personal_data_id = pd.id
            WHERE pe.course_id = :course_id
        """), {'course_id': selected_course.id})

        course_evaluations = [dict(row._mapping) for row in evaluations_query]
        print(f"📊 التقييمات في الدورة: {len(course_evaluations)}")
    except Exception as e:
        print(f"⚠️ خطأ في جلب التقييمات: {e}")

    # إنشاء فهارس للبحث السريع
    participants_by_name = {p['full_name'].lower().strip(): p for p in course_participants}
    participants_by_id = {p['id']: p for p in course_participants}
    evaluations_by_participant = {e['participant_id']: e for e in course_evaluations}
    db_by_name = {d['name'].lower().strip(): d for d in db_data}

    print(f"📋 تم إنشاء الفهارس للبحث السريع")

    # دوال مساعدة للتحليل
    def correct_arabic_text(text):
        """تصحيح النصوص العربية"""
        if not isinstance(text, str) or not text.strip():
            return text
        text = ' '.join(text.split())
        custom_corrections = get_custom_corrections()
        import re
        for pattern, correct in custom_corrections.items():
            text = re.sub(pattern, correct, text, flags=re.IGNORECASE)
        text = correct_hamza(text)
        text = correct_alif_maqsura(text)
        text = correct_taa_marbouta(text)
        text = correct_compound_names(text)
        text = correct_titles_and_kunya(text)
        text = clean_unwanted_chars(text)
        return text.strip()

    def normalize_identifier(identifier):
        """تنظيف المعرفات الرقمية"""
        if not identifier or identifier == 'nan':
            return ''
        import re
        identifier = re.sub(r'[^\d]', '', str(identifier))
        return identifier.strip()

    def is_duplicate_person(excel_record, db_record):
        """فحص التطابق الكامل بين شخصين لمنع التكرار"""
        excel_name = excel_record['name'].lower().strip()
        db_name = db_record['name'].lower().strip()
        excel_national = normalize_identifier(excel_record.get('national_id', ''))
        db_national = normalize_identifier(db_record.get('national_id', ''))
        excel_phone = normalize_identifier(excel_record.get('phone', ''))
        db_phone = normalize_identifier(db_record.get('phone', ''))
        excel_military = normalize_identifier(excel_record.get('military_id', ''))
        db_military = normalize_identifier(db_record.get('military_id', ''))

        # تطابق كامل في الاسم + أي من المعرفات
        if excel_name == db_name:
            if (excel_national and db_national and excel_national == db_national) or \
               (excel_phone and db_phone and excel_phone == db_phone) or \
               (excel_military and db_military and excel_military == db_military):
                return True

        # تطابق في المعرفات حتى لو اختلف الاسم (نفس الشخص)
        if (excel_national and db_national and excel_national == db_national) or \
           (excel_phone and db_phone and excel_phone == db_phone) or \
           (excel_military and db_military and excel_military == db_military):
            return True

        return False

    # معالجة كل سجل من ملف Excel وتصنيفه حسب الفئات الأربعة
    print(f"🔄 بدء تصنيف {len(excel_data)} سجل من ملف Excel")

    for i, excel_record in enumerate(excel_data):
        original_name = excel_record['name']
        corrected_name = correct_arabic_text(original_name)
        was_corrected = (original_name != corrected_name)

        # حفظ الاسم المصحح
        excel_record['corrected_name'] = corrected_name
        if was_corrected:
            results['corrected_names'].append({
                'original': original_name,
                'corrected': corrected_name,
                'row_index': excel_record.get('row_index', i+1),
                'national_id': excel_record.get('national_id', ''),
                'phone': excel_record.get('phone', ''),
                'military_id': excel_record.get('military_id', '')
            })

        # البحث عن تطابق في قاعدة البيانات
        db_match = None
        is_duplicate = False

        for db_record in db_data:
            if is_duplicate_person(
                {'name': corrected_name, 'national_id': excel_record.get('national_id', ''),
                 'phone': excel_record.get('phone', ''), 'military_id': excel_record.get('military_id', '')},
                db_record
            ):
                # وجد تطابق - فحص إذا كان تكرار مرفوض
                if corrected_name.lower().strip() == db_record['name'].lower().strip():
                    db_match = db_record
                    break
                else:
                    # تطابق في البيانات لكن أسماء مختلفة - مرفوض
                    is_duplicate = True
                    results['blocked_duplicates'].append({
                        'excel_record': excel_record,
                        'corrected_name': corrected_name,
                        'db_record': db_record,
                        'reason': 'تطابق في البيانات الشخصية مع اسم مختلف - مرفوض'
                    })
                    break

        if is_duplicate:
            continue  # تخطي هذا السجل لأنه مرفوض

        if not db_match:
            # البحث بالاسم فقط
            db_match = db_by_name.get(corrected_name.lower().strip())

        # تصنيف السجل حسب الفئات الأربعة
        if not db_match:
            # الفئة 1: غير موجود في قاعدة البيانات
            results['category_1'].append({
                'name': corrected_name,
                'original_name': original_name,
                'excel_record': excel_record,
                'action': 'إنشاء شخص جديد + إضافة للدورة + إدخال تقييم',
                'was_corrected': was_corrected
            })
        else:
            # موجود في قاعدة البيانات - فحص حالته في الدورة
            participant = participants_by_id.get(db_match['id'])

            if participant:
                # موجود في الدورة - فحص التقييم
                evaluation = evaluations_by_participant.get(participant['participant_id'])

                if evaluation:
                    # الفئة 4: موجود في القاعدة والدورة ولديه تقييم
                    results['category_4'].append({
                        'name': corrected_name,
                        'original_name': original_name,
                        'excel_record': excel_record,
                        'db_record': db_match,
                        'participant_record': participant,
                        'evaluation_record': evaluation,
                        'action': 'تحديث التقييم الموجود',
                        'was_corrected': was_corrected
                    })
                else:
                    # الفئة 2: موجود في القاعدة والدورة بدون تقييم
                    results['category_2'].append({
                        'name': corrected_name,
                        'original_name': original_name,
                        'excel_record': excel_record,
                        'db_record': db_match,
                        'participant_record': participant,
                        'action': 'إضافة تقييم فقط',
                        'was_corrected': was_corrected
                    })
            else:
                # الفئة 3: موجود في القاعدة وليس في الدورة
                results['category_3'].append({
                    'name': corrected_name,
                    'original_name': original_name,
                    'excel_record': excel_record,
                    'db_record': db_match,
                    'action': 'إضافة للدورة + إدخال تقييم',
                    'was_corrected': was_corrected
                })

    # طباعة نتائج التصنيف
    print(f"📊 نتائج التصنيف النهائية:")
    print(f"   الفئة 1 (غير موجود في القاعدة): {len(results['category_1'])}")
    print(f"   الفئة 2 (في القاعدة والدورة بدون تقييم): {len(results['category_2'])}")
    print(f"   الفئة 3 (في القاعدة وليس في الدورة): {len(results['category_3'])}")
    print(f"   الفئة 4 (في القاعدة والدورة ولديه تقييم): {len(results['category_4'])}")
    print(f"   سجلات مرفوضة: {len(results['blocked_duplicates'])}")
    print(f"   أسماء مصححة: {len(results['corrected_names'])}")

    # حساب الإحصائيات الدقيقة
    total_processed = len(excel_data)
    category_1_count = len(results['category_1'])
    category_2_count = len(results['category_2'])
    category_3_count = len(results['category_3'])
    category_4_count = len(results['category_4'])
    blocked_count = len(results['blocked_duplicates'])
    corrected_count = len(results['corrected_names'])

    # التحقق من صحة الحسابات
    total_categorized = category_1_count + category_2_count + category_3_count + category_4_count + blocked_count
    if total_categorized != total_processed:
        print(f"⚠️ تحذير: مجموع الفئات ({total_categorized}) لا يساوي إجمالي المعالجة ({total_processed})")

    # إحصائيات إضافية
    success_rate = ((category_1_count + category_2_count + category_3_count + category_4_count) / total_processed * 100) if total_processed > 0 else 0
    blocked_rate = (blocked_count / total_processed * 100) if total_processed > 0 else 0
    correction_rate = (corrected_count / total_processed * 100) if total_processed > 0 else 0

    # بناء الإحصائيات النهائية
    results['statistics'] = {
        'total_processed': total_processed,
        'corrected_count': corrected_count,

        # الفئات الأربعة الجديدة
        'category_1_count': category_1_count,  # غير موجود في القاعدة
        'category_2_count': category_2_count,  # في القاعدة والدورة بدون تقييم
        'category_3_count': category_3_count,  # في القاعدة وليس في الدورة
        'category_4_count': category_4_count,  # في القاعدة والدورة ولديه تقييم

        'blocked_duplicates_count': blocked_count,

        # إحصائيات إضافية للتوافق مع النظام القديم
        'exact_matches_count': category_2_count + category_4_count,  # الموجودين في القاعدة والدورة
        'new_records_count': category_1_count,  # غير موجود في القاعدة
        'allowed_duplicates_count': 0,  # لا نستخدمها في النظام الجديد

        # معلومات الأعمدة
        'has_national_id_column': bool(column_info.get('national_id_column')),
        'has_phone_column': bool(column_info.get('phone_column')),
        'has_military_id_column': bool(column_info.get('military_id_column')),

        # نسب مئوية
        'success_rate': success_rate,
        'blocked_rate': blocked_rate,
        'correction_rate': correction_rate,

        # تفاصيل إضافية
        'total_categorized': total_categorized,
        'validation_passed': total_categorized == total_processed
    }

    print(f"✅ انتهى تحليل التقييمات بنجاح")
    print(f"📊 الإحصائيات النهائية:")
    print(f"   إجمالي المعالجة: {total_processed}")
    print(f"   معدل النجاح: {success_rate:.1f}%")
    print(f"   معدل الرفض: {blocked_rate:.1f}%")
    print(f"   معدل التصحيح: {correction_rate:.1f}%")

    return results

@person_data_bp.route('/export_analysis_results')
@login_required
def export_analysis_results():
    """
    تصدير نتائج تحليل الأسماء إلى ملف Excel
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب النتائج من الجلسة
    analysis_results = session.get('analysis_results')
    excel_filename = session.get('excel_filename', 'unknown_file.xlsx')
    selected_course_id = session.get('selected_course_id')
    analysis_type = session.get('analysis_type')

    # فحص مباشر: إذا كان هناك دورة مختارة وبيانات تحليل، فهو تقييمات
    if selected_course_id and analysis_results and not analysis_type:
        analysis_type = 'evaluation'
        print("🎯 تم تحديد نوع التحليل كـ evaluation بناءً على وجود دورة مختارة")

    # طباعة للتشخيص
    print(f"🔍 تشخيص التصدير:")
    print(f"   - نوع التحليل من الجلسة: {analysis_type}")
    print(f"   - اسم الملف: {excel_filename}")
    print(f"   - معرف الدورة: {selected_course_id}")
    print(f"   - جميع مفاتيح الجلسة: {list(session.keys())}")
    print(f"   - محتوى analysis_type في الجلسة: {session.get('analysis_type')}")

    # التحقق من صحة نوع التحليل
    if not analysis_type:
        print("⚠️ نوع التحليل غير محدد، سيتم تحديده من محتوى النتائج")
        # محاولة تحديد نوع التحليل من النتائج
        if analysis_results and 'course_analysis' in analysis_results:
            # إذا كان هناك تحليل دورة، فهو إما course أو evaluation
            if any('evaluation' in str(key).lower() for key in analysis_results.keys()):
                analysis_type = 'evaluation'
                print("🔍 تم تحديد نوع التحليل كـ: evaluation (من محتوى النتائج)")
            else:
                analysis_type = 'course'
                print("🔍 تم تحديد نوع التحليل كـ: course (من محتوى النتائج)")
        else:
            analysis_type = 'data'
            print("🔍 تم تحديد نوع التحليل كـ: data (افتراضي)")

    # فحص إضافي: إذا كان selected_course_id موجود وهناك تقييمات، فهو evaluation
    if selected_course_id and analysis_results:
        # البحث عن مؤشرات التقييمات في النتائج
        evaluation_indicators = [
            'evaluation_grade', 'grade', 'percentage', 'notes',
            'تقييم', 'درجة', 'نسبة', 'ملاحظات'
        ]

        # فحص البيانات للبحث عن مؤشرات التقييمات
        for key, value in analysis_results.items():
            if isinstance(value, list) and value:
                first_item = value[0] if value else {}
                if isinstance(first_item, dict):
                    excel_record = first_item.get('excel_record', {})
                    if any(indicator in str(excel_record) for indicator in evaluation_indicators):
                        analysis_type = 'evaluation'
                        print("🎯 تم تحديد نوع التحليل كـ: evaluation (من مؤشرات التقييمات في البيانات)")
                        break

    # إصلاح نهائي: إذا كان هناك دورة مختارة، فهو تقييمات
    if selected_course_id and not analysis_type:
        analysis_type = 'evaluation'
        print("🔧 إصلاح نهائي: تم تحديد نوع التحليل كـ evaluation بسبب وجود دورة مختارة")

    # إصلاح إضافي: فحص URL المرجع
    referrer = request.referrer or ''
    if 'evaluation' in referrer.lower() and not analysis_type:
        analysis_type = 'evaluation'
        print("🔧 تم تحديد نوع التحليل كـ evaluation من URL المرجع")

    if not analysis_results:
        flash('لا توجد نتائج تحليل للتصدير', 'danger')
        return redirect(url_for('person_data.name_analysis'))

    try:
        # إنشاء ملف Excel في الذاكرة
        output = io.BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # ورقة معلومات التحليل (ورقة جديدة في المقدمة)
            analysis_info_data = [
                {'البيان': 'نوع التحليل', 'القيمة': 'تحليل كشف التقييمات' if analysis_type == 'evaluation' else 'تحليل كشف الدورة' if analysis_type == 'course' else 'تحليل كشف البيانات'},
                {'البيان': 'اسم الملف المحلل', 'القيمة': excel_filename},
                {'البيان': 'تاريخ التحليل', 'القيمة': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
                {'البيان': 'إجمالي السجلات المعالجة', 'القيمة': analysis_results['statistics'].get('total_processed', 0)},
                {'البيان': 'الأسماء المصححة', 'القيمة': analysis_results['statistics'].get('corrected_count', 0)}
            ]

            # إضافة بيانات خاصة بتحليل الدورة
            if analysis_type == 'course':
                duplicate_in_course = len(analysis_results.get('duplicate_in_course', []))
                in_db_not_in_course = len(analysis_results.get('in_db_not_in_course', []))
                not_in_db_count = analysis_results.get('smart_statistics', {}).get('not_in_db_count', 0) if analysis_results.get('smart_statistics') else analysis_results['statistics'].get('new_records_count', 0)

                analysis_info_data.extend([
                    {'البيان': '', 'القيمة': ''},  # فاصل
                    {'البيان': '=== بيانات تحليل الدورة ===', 'القيمة': ''},
                    {'البيان': 'موجود في القاعدة + موجود في الدورة', 'القيمة': duplicate_in_course},
                    {'البيان': 'موجود في القاعدة + غير مضاف في الدورة', 'القيمة': in_db_not_in_course},
                    {'البيان': 'غير موجود في قاعدة البيانات', 'القيمة': not_in_db_count},
                    {'البيان': '', 'القيمة': ''},  # فاصل
                    {'البيان': '=== النسب المئوية ===', 'القيمة': ''},
                    {'البيان': 'معدل النجاح (%)', 'القيمة': f"{((in_db_not_in_course + not_in_db_count) / analysis_results['statistics'].get('total_processed', 1) * 100):.1f}%"},
                    {'البيان': 'معدل التكرار (%)', 'القيمة': f"{(duplicate_in_course / analysis_results['statistics'].get('total_processed', 1) * 100):.1f}%"},
                    {'البيان': 'معدل التصحيح (%)', 'القيمة': f"{(analysis_results['statistics'].get('corrected_count', 0) / analysis_results['statistics'].get('total_processed', 1) * 100):.1f}%"}
                ])

            if analysis_type == 'evaluation':
                # للتقييمات: تفصيل واضح للحالات
                analysis_info_data.extend([
                    {'البيان': '--- تفصيل التقييمات ---', 'القيمة': '---'},
                    {'البيان': 'موجود في قاعدة البيانات (إجمالي)', 'القيمة': analysis_results['statistics'].get('exact_matches_count', 0)},
                    {'البيان': '  ↳ لديهم تقييم (سيتم التحديث)', 'القيمة': 'سيتم حسابه'},
                    {'البيان': '  ↳ ليس لديهم تقييم (سيتم الإضافة)', 'القيمة': 'سيتم حسابه'},
                    {'البيان': 'غير موجود (سيتم إنشاؤهم + تقييمهم)', 'القيمة': analysis_results['statistics'].get('new_records_count', 0)},
                    {'البيان': 'سجلات مرفوضة (تطابق)', 'القيمة': analysis_results['statistics'].get('blocked_duplicates_count', 0)},
                    {'البيان': 'أسماء مكررة مسموحة', 'القيمة': analysis_results['statistics'].get('allowed_duplicates_count', 0)}
                ])
            else:
                # للدورات العادية: التفصيل التقليدي
                analysis_info_data.extend([
                    {'البيان': 'موجود في قاعدة البيانات', 'القيمة': analysis_results['statistics'].get('exact_matches_count', 0)},
                    {'البيان': 'غير موجود في قاعدة البيانات', 'القيمة': analysis_results['statistics'].get('new_records_count', 0)},
                    {'البيان': 'سجلات مرفوضة (تطابق)', 'القيمة': analysis_results['statistics'].get('blocked_duplicates_count', 0)},
                    {'البيان': 'أسماء مكررة مسموحة', 'القيمة': analysis_results['statistics'].get('allowed_duplicates_count', 0)}
                ])

            # إضافة معلومات الدورة إذا كانت متوفرة
            if selected_course_id and 'course_analysis' in analysis_results:
                course_info = analysis_results['course_analysis'].get('selected_course', {})
                if course_info:
                    analysis_info_data.extend([
                        {'البيان': '--- معلومات الدورة ---', 'القيمة': '---'},
                        {'البيان': 'رقم الدورة', 'القيمة': course_info.get('course_number', '')},
                        {'البيان': 'اسم الدورة', 'القيمة': course_info.get('title', '')},
                        {'البيان': 'الجهة', 'القيمة': course_info.get('agency', '')},
                        {'البيان': 'المركز', 'القيمة': course_info.get('center_name', '')}
                    ])

                    # إضافة إحصائيات الدورة/التقييمات
                    participants_summary = analysis_results['course_analysis'].get('participants_summary', {})
                    if participants_summary:
                        if analysis_type == 'evaluation':
                            analysis_info_data.extend([
                                {'البيان': '--- إحصائيات التقييمات ---', 'القيمة': '---'},
                                {'البيان': 'التقييمات الحالية في الدورة', 'القيمة': participants_summary.get('current_participants_count', 0)},
                                {'البيان': 'التقييمات الجديدة للإضافة', 'القيمة': participants_summary.get('new_participants_count', 0)},
                                {'البيان': 'التقييمات المكررة', 'القيمة': participants_summary.get('duplicate_participants_count', 0)},
                                {'البيان': 'إجمالي التقييمات بعد الاستيراد', 'القيمة': participants_summary.get('total_after_import', 0)}
                            ])
                        else:
                            analysis_info_data.extend([
                                {'البيان': '--- إحصائيات المشاركين ---', 'القيمة': '---'},
                                {'البيان': 'المشاركين الحاليين في الدورة', 'القيمة': participants_summary.get('current_participants_count', 0)},
                                {'البيان': 'المشاركين الجدد للإضافة', 'القيمة': participants_summary.get('new_participants_count', 0)},
                                {'البيان': 'المشاركين المكررين', 'القيمة': participants_summary.get('duplicate_participants_count', 0)},
                                {'البيان': 'إجمالي المشاركين بعد الاستيراد', 'القيمة': participants_summary.get('total_after_import', 0)}
                            ])

            analysis_info_df = pd.DataFrame(analysis_info_data)
            analysis_info_df.to_excel(writer, sheet_name='معلومات التحليل', index=False)

            # تنسيق ورقة معلومات التحليل
            info_worksheet = writer.sheets['معلومات التحليل']
            info_worksheet.sheet_view.rightToLeft = True
            # ورقة الإحصائيات
            stats_data = []
            for key, value in analysis_results['statistics'].items():
                stats_data.append({
                    'النوع': translate_stat_key(key),
                    'العدد': value
                })

            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='الإحصائيات', index=False)

            # تعيين اتجاه ورقة الإحصائيات من اليمين إلى اليسار
            stats_worksheet = writer.sheets['الإحصائيات']
            stats_worksheet.sheet_view.rightToLeft = True

            # ورقة الأسماء المصححة
            if analysis_results['corrected_names']:
                # إنشاء البيانات بالترتيب الصحيح
                corrected_data = []
                for item in analysis_results['corrected_names']:
                    corrected_data.append({
                        'الاسم الأصلي': item['original'],
                        'الاسم المصحح': item['corrected']
                    })
                corrected_df = pd.DataFrame(corrected_data)
                corrected_df.to_excel(writer, sheet_name='الأسماء المصححة', index=False)

                # تعيين اتجاه ورقة الأسماء المصححة من اليمين إلى اليسار
                corrected_worksheet = writer.sheets['الأسماء المصححة']
                corrected_worksheet.sheet_view.rightToLeft = True

            # ورقة موجود في قاعدة البيانات (المطابقات التامة) - مع تمييز التقييمات
            if analysis_results['exact_matches']:
                if analysis_type == 'evaluation':
                    # للتقييمات: تمييز بين من لديه تقييم ومن ليس لديه تقييم
                    has_evaluation_data = []
                    no_evaluation_data = []

                    for match in analysis_results['exact_matches']:
                        # فحص وجود تقييم للشخص في الدورة المحددة
                        person_id = match['db_record'].get('id')
                        has_evaluation = False
                        current_evaluation = None

                        if selected_course_id and person_id:
                            try:
                                # البحث عن تقييم للشخص في الدورة
                                evaluation_query = db.session.execute(text("""
                                    SELECT pe.*, cp.id as participant_id
                                    FROM participant_evaluations pe
                                    JOIN course_participant cp ON pe.participant_id = cp.id
                                    WHERE cp.personal_data_id = :person_id AND pe.course_id = :course_id
                                """), {'person_id': person_id, 'course_id': selected_course_id})

                                evaluation_result = evaluation_query.fetchone()
                                if evaluation_result:
                                    has_evaluation = True
                                    current_evaluation = dict(evaluation_result._mapping)
                            except Exception as e:
                                print(f"خطأ في فحص التقييم: {str(e)}")

                        base_data = {
                            'الاسم من ملف التقييمات': match['excel_name'],
                            'الاسم في قاعدة البيانات': match['db_name'],
                            'الرقم الوطني (ملف)': match['excel_record'].get('national_id', ''),
                            'الرقم الوطني (قاعدة البيانات)': match['db_record'].get('national_id', ''),
                            'رقم الهاتف (ملف)': match['excel_record'].get('phone', ''),
                            'رقم الهاتف (قاعدة البيانات)': match['db_record'].get('phone', ''),
                            'الرقم العسكري (ملف)': match['excel_record'].get('military_id', ''),
                            'الرقم العسكري (قاعدة البيانات)': match['db_record'].get('military_id', ''),
                            'رقم الصف في Excel': match['excel_record'].get('row_index', ''),
                            'ID في قاعدة البيانات': match['db_record'].get('id', ''),
                            'الدرجة الجديدة': match['excel_record'].get('grade', ''),
                            'النسبة الجديدة': match['excel_record'].get('percentage', ''),
                            'التقدير الجديد': match['excel_record'].get('evaluation_grade', ''),
                            'ملاحظات جديدة': match['excel_record'].get('notes', '')
                        }

                        if has_evaluation:
                            # لديه تقييم - سيتم تحديث تقييمه
                            has_evaluation_data.append({
                                **base_data,
                                'حالة التقييم': 'لديه تقييم مدخل مسبقاً',
                                'الدرجة الحالية': current_evaluation.get('grade', '') if current_evaluation else '',
                                'النسبة الحالية': current_evaluation.get('percentage', '') if current_evaluation else '',
                                'التقدير الحالي': current_evaluation.get('evaluation_grade', '') if current_evaluation else '',
                                'تاريخ التقييم الحالي': current_evaluation.get('created_at', '') if current_evaluation else '',
                                'إجراء مطلوب': 'تحديث التقييم الموجود',
                                'نوع العملية': 'تحديث تقييم'
                            })
                        else:
                            # ليس لديه تقييم - سيتم إضافة تقييم له
                            no_evaluation_data.append({
                                **base_data,
                                'حالة التقييم': 'ليس لديه تقييم مدخل',
                                'الدرجة الحالية': 'لا يوجد',
                                'النسبة الحالية': 'لا يوجد',
                                'التقدير الحالي': 'لا يوجد',
                                'تاريخ التقييم الحالي': 'لا يوجد',
                                'إجراء مطلوب': 'إضافة تقييم جديد للشخص',
                                'نوع العملية': 'إضافة تقييم'
                            })

                    # إنشاء ورقة للذين لديهم تقييم (سيتم تحديث تقييمهم)
                    if has_evaluation_data:
                        has_eval_df = pd.DataFrame(has_evaluation_data)
                        has_eval_df.to_excel(writer, sheet_name='لديهم تقييم - سيتم التحديث', index=False)
                        has_eval_worksheet = writer.sheets['لديهم تقييم - سيتم التحديث']
                        has_eval_worksheet.sheet_view.rightToLeft = True

                    # إنشاء ورقة للذين ليس لديهم تقييم (سيتم إضافة تقييم لهم)
                    if no_evaluation_data:
                        no_eval_df = pd.DataFrame(no_evaluation_data)
                        no_eval_df.to_excel(writer, sheet_name='ليس لديهم تقييم - سيتم الإضافة', index=False)
                        no_eval_worksheet = writer.sheets['ليس لديهم تقييم - سيتم الإضافة']
                        no_eval_worksheet.sheet_view.rightToLeft = True

                else:
                    # للدورات العادية: الورقة التقليدية
                    exact_data = []
                    for match in analysis_results['exact_matches']:
                        exact_data.append({
                            'الاسم من الملف': match['excel_name'],
                            'الاسم في قاعدة البيانات': match['db_name'],
                            'الرقم الوطني (ملف)': match['excel_record'].get('national_id', ''),
                            'الرقم الوطني (قاعدة البيانات)': match['db_record'].get('national_id', ''),
                            'رقم الهاتف (ملف)': match['excel_record'].get('phone', ''),
                            'رقم الهاتف (قاعدة البيانات)': match['db_record'].get('phone', ''),
                            'الرقم العسكري (ملف)': match['excel_record'].get('military_id', ''),
                            'الرقم العسكري (قاعدة البيانات)': match['db_record'].get('military_id', ''),
                            'رقم الصف في Excel': match['excel_record'].get('row_index', ''),
                            'ID في قاعدة البيانات': match['db_record'].get('id', ''),
                            'حالة التطابق': 'مطابقة تامة',
                            'إجراء مقترح': 'تحديث البيانات أو إضافة للدورة'
                        })
                    exact_df = pd.DataFrame(exact_data)
                    sheet_name = 'موجود في قاعدة البيانات'
                    exact_df.to_excel(writer, sheet_name=sheet_name, index=False)

                    # تعيين اتجاه ورقة المطابقات التامة من اليمين إلى اليسار
                    exact_worksheet = writer.sheets[sheet_name]
                    exact_worksheet.sheet_view.rightToLeft = True

            # ورقة التشابه الثلاثي
            if analysis_results['similarity_matches']['triple']:
                triple_data = []
                for match in analysis_results['similarity_matches']['triple']:
                    triple_data.append({
                        'الاسم من Excel': match['excel_name'],
                        'الاسم من قاعدة البيانات': match['db_name'],
                        'الأجزاء المتطابقة': ', '.join(match['common_parts'])
                    })
                triple_df = pd.DataFrame(triple_data)
                triple_df.to_excel(writer, sheet_name='التشابه الثلاثي', index=False)

                # تعيين اتجاه ورقة التشابه الثلاثي من اليمين إلى اليسار
                triple_worksheet = writer.sheets['التشابه الثلاثي']
                triple_worksheet.sheet_view.rightToLeft = True

            # ورقة التشابه الرباعي
            if analysis_results['similarity_matches']['quadruple']:
                quad_data = []
                for match in analysis_results['similarity_matches']['quadruple']:
                    quad_data.append({
                        'الاسم من Excel': match['excel_name'],
                        'الاسم من قاعدة البيانات': match['db_name'],
                        'الأجزاء المتطابقة': ', '.join(match['common_parts'])
                    })
                quad_df = pd.DataFrame(quad_data)
                quad_df.to_excel(writer, sheet_name='التشابه الرباعي', index=False)

                # تعيين اتجاه ورقة التشابه الرباعي من اليمين إلى اليسار
                quad_worksheet = writer.sheets['التشابه الرباعي']
                quad_worksheet.sheet_view.rightToLeft = True

            # ورقة التشابه الخماسي
            if analysis_results['similarity_matches']['quintuple']:
                quint_data = []
                for match in analysis_results['similarity_matches']['quintuple']:
                    quint_data.append({
                        'الاسم من Excel': match['excel_name'],
                        'الاسم من قاعدة البيانات': match['db_name'],
                        'الأجزاء المتطابقة': ', '.join(match['common_parts'])
                    })
                quint_df = pd.DataFrame(quint_data)
                quint_df.to_excel(writer, sheet_name='التشابه الخماسي', index=False)

                # تعيين اتجاه ورقة التشابه الخماسي من اليمين إلى اليسار
                quint_worksheet = writer.sheets['التشابه الخماسي']
                quint_worksheet.sheet_view.rightToLeft = True

            # ورقة التشابه الكامل مع اللقب
            if analysis_results['similarity_matches']['full_with_title']:
                full_data = []
                for match in analysis_results['similarity_matches']['full_with_title']:
                    full_data.append({
                        'الاسم من Excel': match['excel_name'],
                        'الاسم من قاعدة البيانات': match['db_name'],
                        'الأجزاء المتطابقة': ', '.join(match['common_parts'])
                    })
                full_df = pd.DataFrame(full_data)
                full_df.to_excel(writer, sheet_name='التشابه الكامل', index=False)

                # تعيين اتجاه ورقة التشابه الكامل من اليمين إلى اليسار
                full_worksheet = writer.sheets['التشابه الكامل']
                full_worksheet.sheet_view.rightToLeft = True

            # ورقة الأسماء الطويلة (أكثر من 6 أجزاء)
            if analysis_results['similarity_matches']['six_plus']:
                long_data = []
                for item in analysis_results['similarity_matches']['six_plus']:
                    long_data.append({
                        'الاسم': item['name'],
                        'عدد الأجزاء': item['parts_count'],
                        'الأجزاء': ', '.join(item['parts'])
                    })
                long_df = pd.DataFrame(long_data)
                long_df.to_excel(writer, sheet_name='الأسماء الطويلة', index=False)

                # تعيين اتجاه ورقة الأسماء الطويلة من اليمين إلى اليسار
                long_worksheet = writer.sheets['الأسماء الطويلة']
                long_worksheet.sheet_view.rightToLeft = True

            # ورقة غير موجود في قاعدة البيانات (السجلات الجديدة)
            if 'new_records' in analysis_results and analysis_results['new_records']:
                new_records_data = []
                for record in analysis_results['new_records']:
                    base_data = {
                        'الاسم الشخصي': record['corrected_name'],
                        'الاسم الأصلي في الملف': record.get('original_name', record['corrected_name']),
                        'الرقم الوطني': record['excel_record'].get('national_id', ''),
                        'رقم الهاتف': record['excel_record'].get('phone', ''),
                        'الرقم العسكري': record['excel_record'].get('military_id', ''),
                        'رقم الصف في Excel': record['excel_record'].get('row_index', ''),
                        'حالة الاسم': 'مصحح' if record.get('was_corrected', False) else 'أصلي',
                        'نوع السجل': 'جديد - غير موجود في قاعدة البيانات',
                        'أولوية الإضافة': 'عالية' if record['excel_record'].get('national_id') else 'متوسطة',
                        'ملاحظات': 'سيتم إنشاء ملف شخصي جديد'
                    }

                    if analysis_type == 'evaluation':
                        # للتقييمات: إضافة تفاصيل التقييم
                        new_records_data.append({
                            **base_data,
                            'الدرجة المدخلة': record['excel_record'].get('grade', ''),
                            'النسبة المدخلة': record['excel_record'].get('percentage', ''),
                            'التقدير المدخل': record['excel_record'].get('evaluation_grade', ''),
                            'ملاحظات التقييم': record['excel_record'].get('notes', ''),
                            'إجراء مطلوب': 'إنشاء شخص جديد + إدخال تقييم له',
                            'نوع العملية': 'إنشاء + تقييم',
                            'حالة التقييم': 'تقييم جديد - سيتم إدخاله',
                            'ملاحظات إضافية': 'سيتم إنشاء الشخص وإضافة تقييمه في نفس الوقت'
                        })
                    else:
                        # للدورات العادية
                        new_records_data.append({
                            **base_data,
                            'إجراء مطلوب': 'إضافة إلى قاعدة البيانات' + (' والدورة' if analysis_type == 'course' else ''),
                            'نوع العملية': 'إنشاء شخص جديد',
                            'ملاحظات إضافية': 'يحتاج مراجعة البيانات الشخصية قبل الإضافة'
                        })

                new_records_df = pd.DataFrame(new_records_data)
                if analysis_type == 'evaluation':
                    sheet_name = 'غير موجود - سيتم إدخال تقييم لهم'
                else:
                    sheet_name = 'غير موجود في قاعدة البيانات'
                new_records_df.to_excel(writer, sheet_name=sheet_name, index=False)

                # تعيين اتجاه ورقة السجلات الجديدة من اليمين إلى اليسار
                new_records_worksheet = writer.sheets[sheet_name]
                new_records_worksheet.sheet_view.rightToLeft = True

            # ورقة الأسماء المكررة المسموحة
            if 'allowed_duplicates' in analysis_results and analysis_results['allowed_duplicates']:
                allowed_data = []
                for allowed in analysis_results['allowed_duplicates']:
                    allowed_data.append({
                        'الاسم الشخصي': allowed['corrected_name'],
                        'الرقم الوطني': allowed['excel_record'].get('national_id', ''),
                        'رقم الهاتف': allowed['excel_record'].get('phone', ''),
                        'الرقم العسكري': allowed['excel_record'].get('military_id', ''),
                        'رقم الصف': allowed['excel_record'].get('row_index', ''),
                        'السبب': allowed['reason']
                    })
                allowed_df = pd.DataFrame(allowed_data)
                allowed_df.to_excel(writer, sheet_name='الأسماء المكررة المسموحة', index=False)

                # تعيين اتجاه ورقة الأسماء المكررة المسموحة من اليمين إلى اليسار
                allowed_worksheet = writer.sheets['الأسماء المكررة المسموحة']
                allowed_worksheet.sheet_view.rightToLeft = True

            # ورقة السجلات المرفوضة
            if 'blocked_duplicates' in analysis_results and analysis_results['blocked_duplicates']:
                blocked_data = []
                for blocked in analysis_results['blocked_duplicates']:
                    blocked_data.append({
                        'الاسم الشخصي': blocked['corrected_name'],
                        'الرقم الوطني': blocked['excel_record'].get('national_id', ''),
                        'رقم الهاتف': blocked['excel_record'].get('phone', ''),
                        'الرقم العسكري': blocked['excel_record'].get('military_id', ''),
                        'رقم الصف': blocked['excel_record'].get('row_index', ''),
                        'سبب الرفض': blocked['reason'],
                        'أنواع التطابق': ', '.join(blocked['duplicate_types'])
                    })
                blocked_df = pd.DataFrame(blocked_data)
                blocked_df.to_excel(writer, sheet_name='السجلات المرفوضة', index=False)

                # تعيين اتجاه ورقة السجلات المرفوضة من اليمين إلى اليسار
                blocked_worksheet = writer.sheets['السجلات المرفوضة']
                blocked_worksheet.sheet_view.rightToLeft = True

            # ورقة التطابقات المتقدمة
            if 'duplicate_matches' in analysis_results:
                for match_type, matches in analysis_results['duplicate_matches'].items():
                    if matches:
                        match_data = []
                        for match in matches:
                            match_data.append({
                                'الاسم من Excel': match['excel_record']['corrected_name'],
                                'الرقم الوطني Excel': match['excel_record'].get('national_id', ''),
                                'رقم الهاتف Excel': match['excel_record'].get('phone', ''),
                                'الرقم العسكري Excel': match['excel_record'].get('military_id', ''),
                                'رقم الصف Excel': match['excel_record'].get('row_index', ''),
                                'الاسم من قاعدة البيانات': match['db_record']['name'],
                                'الرقم الوطني قاعدة البيانات': match['db_record'].get('national_id', ''),
                                'رقم الهاتف قاعدة البيانات': match['db_record'].get('phone', ''),
                                'الرقم العسكري قاعدة البيانات': match['db_record'].get('military_id', ''),
                                'نوع التطابق': match['match_details']
                            })

                        match_df = pd.DataFrame(match_data)
                        sheet_name = f'تطابق_{match_type}'
                        match_df.to_excel(writer, sheet_name=sheet_name, index=False)

                        # تعيين اتجاه ورقة التطابقات من اليمين إلى اليسار
                        match_worksheet = writer.sheets[sheet_name]
                        match_worksheet.sheet_view.rightToLeft = True

            # ورقة الأسماء الجديدة (للتوافق مع النظام القديم)
            if 'new_names' in analysis_results and analysis_results['new_names']:
                new_df = pd.DataFrame({'الأسماء الجديدة': analysis_results['new_names']})
                new_df.to_excel(writer, sheet_name='الأسماء الجديدة القديمة', index=False)

                # تعيين اتجاه ورقة الأسماء الجديدة من اليمين إلى اليسار
                new_worksheet = writer.sheets['الأسماء الجديدة القديمة']
                new_worksheet.sheet_view.rightToLeft = True

            # أوراق تحليل الدورة/التقييمات (إذا تم اختيار دورة)
            if 'course_analysis' in analysis_results and analysis_results['course_analysis']['selected_course']:
                course_analysis = analysis_results['course_analysis']

                if analysis_type == 'evaluation':
                    # أوراق خاصة بالتقييمات

                    # ورقة التقييمات الحالية في الدورة
                    if course_analysis['existing_participants']:
                        existing_data = []
                        for participant in course_analysis['existing_participants']:
                            existing_data.append({
                                'الاسم': participant['name'],
                                'الرقم الوطني': participant['national_id'],
                                'رقم الهاتف': participant['phone'],
                                'الرقم العسكري': participant['military_id'],
                                'حالة التقييم': participant['status'],
                                'تاريخ إدخال التقييم': participant['entry_date'],
                                'آخر تحديث': participant.get('last_update', ''),
                                'الدرجة الحالية': participant.get('current_grade', ''),
                                'النسبة المئوية': participant.get('percentage', ''),
                                'التقدير': participant.get('evaluation_grade', '')
                            })
                        existing_df = pd.DataFrame(existing_data)
                        existing_df.to_excel(writer, sheet_name='التقييمات الحالية في الدورة', index=False)

                        # تعيين اتجاه الورقة من اليمين إلى اليسار
                        existing_worksheet = writer.sheets['التقييمات الحالية في الدورة']
                        existing_worksheet.sheet_view.rightToLeft = True

                    # ورقة التقييمات الجديدة للإضافة
                    if course_analysis['new_participants']:
                        new_course_data = []
                        for participant in course_analysis['new_participants']:
                            new_course_data.append({
                                'الاسم': participant['name'],
                                'الرقم الوطني': participant['excel_record'].get('national_id', ''),
                                'رقم الهاتف': participant['excel_record'].get('phone', ''),
                                'الرقم العسكري': participant['excel_record'].get('military_id', ''),
                                'رقم الصف في Excel': participant['excel_record'].get('row_index', ''),
                                'نوع الإضافة': participant['type'],
                                'الدرجة المدخلة': participant['excel_record'].get('grade', ''),
                                'النسبة المدخلة': participant['excel_record'].get('percentage', ''),
                                'التقدير المدخل': participant['excel_record'].get('evaluation_grade', ''),
                                'ملاحظات التقييم': participant['excel_record'].get('notes', ''),
                                'حالة الإضافة': 'جاهز للإضافة'
                            })
                        new_course_df = pd.DataFrame(new_course_data)
                        new_course_df.to_excel(writer, sheet_name='التقييمات الجديدة للإضافة', index=False)

                        # تعيين اتجاه الورقة من اليمين إلى اليسار
                        new_course_worksheet = writer.sheets['التقييمات الجديدة للإضافة']
                        new_course_worksheet.sheet_view.rightToLeft = True

                    # ورقة التقييمات المكررة (لن يتم إضافتها)
                    if course_analysis['duplicate_participants']:
                        duplicate_course_data = []
                        for participant in course_analysis['duplicate_participants']:
                            duplicate_course_data.append({
                                'الاسم': participant['name'],
                                'الرقم الوطني': participant['excel_record'].get('national_id', ''),
                                'رقم الهاتف': participant['excel_record'].get('phone', ''),
                                'الرقم العسكري': participant['excel_record'].get('military_id', ''),
                                'رقم الصف في Excel': participant['excel_record'].get('row_index', ''),
                                'سبب الرفض': participant['reason'],
                                'التقييم الموجود مسبقاً': participant['existing_participant']['name'],
                                'تاريخ التقييم الموجود': participant['existing_participant'].get('entry_date', ''),
                                'الدرجة الموجودة': participant['existing_participant'].get('current_grade', ''),
                                'الدرجة الجديدة المرفوضة': participant['excel_record'].get('grade', ''),
                                'إجراء مقترح': 'تحديث التقييم الموجود أو تجاهل'
                            })
                        duplicate_course_df = pd.DataFrame(duplicate_course_data)
                        duplicate_course_df.to_excel(writer, sheet_name='التقييمات المكررة المرفوضة', index=False)

                        # تعيين اتجاه الورقة من اليمين إلى اليسار
                        duplicate_course_worksheet = writer.sheets['التقييمات المكررة المرفوضة']
                        duplicate_course_worksheet.sheet_view.rightToLeft = True

                    # ورقة التقييمات القابلة للتحديث
                    if analysis_results.get('exact_matches'):
                        update_data = []
                        for match in analysis_results['exact_matches']:
                            update_data.append({
                                'الاسم في قاعدة البيانات': match['db_name'],
                                'الاسم في ملف التقييمات': match['excel_name'],
                                'الرقم الوطني': match['db_record'].get('national_id', ''),
                                'رقم الهاتف': match['db_record'].get('phone', ''),
                                'الرقم العسكري': match['db_record'].get('military_id', ''),
                                'رقم الصف في Excel': match['excel_record'].get('row_index', ''),
                                'التقييم الحالي': match['db_record'].get('current_grade', 'غير متوفر'),
                                'التقييم الجديد': match['excel_record'].get('grade', ''),
                                'النسبة الحالية': match['db_record'].get('percentage', 'غير متوفر'),
                                'النسبة الجديدة': match['excel_record'].get('percentage', ''),
                                'التقدير الحالي': match['db_record'].get('evaluation_grade', 'غير متوفر'),
                                'التقدير الجديد': match['excel_record'].get('evaluation_grade', ''),
                                'إجراء مقترح': 'تحديث التقييم أو إضافة تقييم جديد',
                                'حالة التطابق': 'مطابقة تامة - قابل للتحديث'
                            })
                        update_df = pd.DataFrame(update_data)
                        update_df.to_excel(writer, sheet_name='التقييمات القابلة للتحديث', index=False)

                        # تعيين اتجاه الورقة من اليمين إلى اليسار
                        update_worksheet = writer.sheets['التقييمات القابلة للتحديث']
                        update_worksheet.sheet_view.rightToLeft = True

                else:
                    # أوراق خاصة بالدورات العادية

                    # ورقة المشاركين الحاليين في الدورة
                    if course_analysis['existing_participants']:
                        existing_data = []
                        for participant in course_analysis['existing_participants']:
                            existing_data.append({
                                'الاسم': participant['name'],
                                'الرقم الوطني': participant['national_id'],
                                'رقم الهاتف': participant['phone'],
                                'الرقم العسكري': participant['military_id'],
                                'الحالة': participant['status'],
                                'تاريخ الدخول': participant['entry_date']
                            })
                        existing_df = pd.DataFrame(existing_data)
                        existing_df.to_excel(writer, sheet_name='المشاركين الحاليين', index=False)

                        # تعيين اتجاه الورقة من اليمين إلى اليسار
                        existing_worksheet = writer.sheets['المشاركين الحاليين']
                        existing_worksheet.sheet_view.rightToLeft = True

                    # ورقة المشاركين الجدد للدورة
                    if course_analysis['new_participants']:
                        new_course_data = []
                        for participant in course_analysis['new_participants']:
                            new_course_data.append({
                                'الاسم': participant['name'],
                                'الرقم الوطني': participant['excel_record'].get('national_id', ''),
                                'رقم الهاتف': participant['excel_record'].get('phone', ''),
                                'الرقم العسكري': participant['excel_record'].get('military_id', ''),
                                'رقم الصف': participant['excel_record'].get('row_index', ''),
                                'النوع': participant['type']
                            })
                        new_course_df = pd.DataFrame(new_course_data)
                        new_course_df.to_excel(writer, sheet_name='المشاركين الجدد للدورة', index=False)

                        # تعيين اتجاه الورقة من اليمين إلى اليسار
                        new_course_worksheet = writer.sheets['المشاركين الجدد للدورة']
                        new_course_worksheet.sheet_view.rightToLeft = True

                    # ورقة المشاركين المكررين في الدورة
                    if course_analysis['duplicate_participants']:
                        duplicate_course_data = []
                        for participant in course_analysis['duplicate_participants']:
                            duplicate_course_data.append({
                                'الاسم': participant['name'],
                                'الرقم الوطني': participant['excel_record'].get('national_id', ''),
                                'رقم الهاتف': participant['excel_record'].get('phone', ''),
                                'الرقم العسكري': participant['excel_record'].get('military_id', ''),
                                'رقم الصف': participant['excel_record'].get('row_index', ''),
                                'السبب': participant['reason'],
                                'المشارك الموجود': participant['existing_participant']['name']
                            })
                        duplicate_course_df = pd.DataFrame(duplicate_course_data)
                        duplicate_course_df.to_excel(writer, sheet_name='المشاركين المكررين بالدورة', index=False)

                        # تعيين اتجاه الورقة من اليمين إلى اليسار
                        duplicate_course_worksheet = writer.sheets['المشاركين المكررين بالدورة']
                        duplicate_course_worksheet.sheet_view.rightToLeft = True

                # ورقة ملخص الدورة
                if course_analysis['participants_summary']:
                    summary = course_analysis['participants_summary']
                    selected_course = course_analysis['selected_course']
                    summary_data = [
                        {'البيان': 'رقم الدورة', 'القيمة': selected_course['course_number']},
                        {'البيان': 'اسم الدورة', 'القيمة': selected_course['title']},
                        {'البيان': 'الجهة', 'القيمة': selected_course['agency']},
                        {'البيان': 'المركز', 'القيمة': selected_course['center_name']},
                        {'البيان': 'المشاركين الحاليين', 'القيمة': summary['current_participants_count']},
                        {'البيان': 'المشاركين الجدد', 'القيمة': summary['new_participants_count']},
                        {'البيان': 'المشاركين المكررين', 'القيمة': summary['duplicate_participants_count']},
                        {'البيان': 'إجمالي بعد الاستيراد', 'القيمة': summary['total_after_import']}
                    ]
                    summary_df = pd.DataFrame(summary_data)
                    summary_df.to_excel(writer, sheet_name='ملخص الدورة', index=False)

                    # تعيين اتجاه الورقة من اليمين إلى اليسار
                    summary_worksheet = writer.sheets['ملخص الدورة']
                    summary_worksheet.sheet_view.rightToLeft = True

            # أوراق التصنيفات الأربعة لتحليل الدورة (تفصيلية)
            if analysis_type == 'course':

                # 1. ورقة: موجود في القاعدة + مكرر في الدورة (تفصيلي)
                if 'duplicate_in_course' in analysis_results and analysis_results['duplicate_in_course']:
                    duplicate_data = []
                    for record in analysis_results['duplicate_in_course']:
                        duplicate_data.append({
                            'الاسم': record.get('corrected_name', ''),
                            'الرقم الوطني': record.get('excel_record', {}).get('national_id', ''),
                            'رقم الهاتف': record.get('excel_record', {}).get('phone', ''),
                            'الرقم العسكري': record.get('excel_record', {}).get('military_id', ''),
                            'رقم الصف في Excel': record.get('row_index', ''),
                            'سبب التكرار': record.get('action_needed', 'مكرر في الدورة'),
                            'ID في قاعدة البيانات': record.get('db_match', {}).get('id', '') if record.get('db_match') else '',
                            'تاريخ الدخول للدورة': record.get('course_match', {}).get('created_at', '') if record.get('course_match') else '',
                            'الحالة': 'لن يتم إضافته - مكرر'
                        })
                    duplicate_df = pd.DataFrame(duplicate_data)
                    duplicate_df.to_excel(writer, sheet_name='مكرر في الدورة', index=False)

                    # تنسيق الورقة
                    duplicate_worksheet = writer.sheets['مكرر في الدورة']
                    duplicate_worksheet.sheet_view.rightToLeft = True

                # 2. ورقة: موجود في القاعدة + سيتم إضافته للدورة (تفصيلي)
                if 'in_db_not_in_course' in analysis_results and analysis_results['in_db_not_in_course']:
                    will_add_data = []
                    for record in analysis_results['in_db_not_in_course']:
                        will_add_data.append({
                            'الاسم': record.get('corrected_name', ''),
                            'الرقم الوطني': record.get('excel_record', {}).get('national_id', ''),
                            'رقم الهاتف': record.get('excel_record', {}).get('phone', ''),
                            'الرقم العسكري': record.get('excel_record', {}).get('military_id', ''),
                            'رقم الصف في Excel': record.get('row_index', ''),
                            'ID في قاعدة البيانات': record.get('db_match', {}).get('id', '') if record.get('db_match') else '',
                            'تاريخ الإنشاء في القاعدة': record.get('db_match', {}).get('created_at', '') if record.get('db_match') else '',
                            'الإجراء': record.get('action_needed', 'سيتم إضافته للدورة'),
                            'الحالة': 'سيتم إضافته للدورة'
                        })
                    will_add_df = pd.DataFrame(will_add_data)
                    will_add_df.to_excel(writer, sheet_name='سيتم إضافته للدورة', index=False)

                    # تنسيق الورقة
                    will_add_worksheet = writer.sheets['سيتم إضافته للدورة']
                    will_add_worksheet.sheet_view.rightToLeft = True

                # 3. ورقة: غير موجود في قاعدة البيانات (تفصيلي)
                new_records_data = []

                # من new_records إذا كان موجود
                if 'new_records' in analysis_results and analysis_results['new_records']:
                    for record in analysis_results['new_records']:
                        new_records_data.append({
                            'الاسم': record.get('corrected_name', ''),
                            'الرقم الوطني': record.get('excel_record', {}).get('national_id', ''),
                            'رقم الهاتف': record.get('excel_record', {}).get('phone', ''),
                            'الرقم العسكري': record.get('excel_record', {}).get('military_id', ''),
                            'رقم الصف في Excel': record.get('row_index', ''),
                            'النوع': 'جديد تماماً',
                            'الإجراء': 'سيتم إضافته للقاعدة والدورة',
                            'الحالة': 'جديد - سيتم إضافته'
                        })

                if new_records_data:
                    new_records_df = pd.DataFrame(new_records_data)
                    new_records_df.to_excel(writer, sheet_name='غير موجود في القاعدة', index=False)

                    # تنسيق الورقة
                    new_records_worksheet = writer.sheets['غير موجود في القاعدة']
                    new_records_worksheet.sheet_view.rightToLeft = True

                # 4. ورقة: المطابقات التامة (للمراجعة)
                if 'exact_matches' in analysis_results and analysis_results['exact_matches']:
                    exact_matches_data = []
                    for match in analysis_results['exact_matches']:
                        exact_matches_data.append({
                            'الاسم من Excel': match.get('excel_name', ''),
                            'الاسم من القاعدة': match.get('db_name', ''),
                            'الرقم الوطني': match.get('excel_record', {}).get('national_id', ''),
                            'رقم الهاتف': match.get('excel_record', {}).get('phone', ''),
                            'الرقم العسكري': match.get('excel_record', {}).get('military_id', ''),
                            'رقم الصف في Excel': match.get('excel_record', {}).get('row_index', ''),
                            'ID في قاعدة البيانات': match.get('db_record', {}).get('id', ''),
                            'نوع المطابقة': match.get('match_type', 'مطابقة تامة'),
                            'الحالة': 'موجود في القاعدة'
                        })

                    if exact_matches_data:
                        exact_matches_df = pd.DataFrame(exact_matches_data)
                        exact_matches_df.to_excel(writer, sheet_name='المطابقات التامة', index=False)

                        # تنسيق الورقة
                        exact_matches_worksheet = writer.sheets['المطابقات التامة']
                        exact_matches_worksheet.sheet_view.rightToLeft = True

            # أوراق التصنيفات الأربعة لتحليل الدورة (تفصيلية)
            if analysis_type == 'course':

                # 1. ورقة: موجود في القاعدة + مكرر في الدورة (تفصيلي)
                if 'duplicate_in_course' in analysis_results and analysis_results['duplicate_in_course']:
                    duplicate_data = []
                    for record in analysis_results['duplicate_in_course']:
                        duplicate_data.append({
                            'الاسم': record.get('corrected_name', ''),
                            'الرقم الوطني': record.get('excel_record', {}).get('national_id', ''),
                            'رقم الهاتف': record.get('excel_record', {}).get('phone', ''),
                            'الرقم العسكري': record.get('excel_record', {}).get('military_id', ''),
                            'رقم الصف في Excel': record.get('row_index', ''),
                            'سبب التكرار': record.get('action_needed', 'مكرر في الدورة'),
                            'ID في قاعدة البيانات': record.get('db_match', {}).get('id', '') if record.get('db_match') else '',
                            'تاريخ الدخول للدورة': record.get('course_match', {}).get('created_at', '') if record.get('course_match') else '',
                            'الحالة': 'لن يتم إضافته - مكرر'
                        })
                    duplicate_df = pd.DataFrame(duplicate_data)
                    duplicate_df.to_excel(writer, sheet_name='مكرر في الدورة', index=False)

                    # تنسيق الورقة
                    duplicate_worksheet = writer.sheets['مكرر في الدورة']
                    duplicate_worksheet.sheet_view.rightToLeft = True

                # 2. ورقة: موجود في القاعدة + سيتم إضافته للدورة (تفصيلي)
                if 'in_db_not_in_course' in analysis_results and analysis_results['in_db_not_in_course']:
                    will_add_data = []
                    for record in analysis_results['in_db_not_in_course']:
                        will_add_data.append({
                            'الاسم': record.get('corrected_name', ''),
                            'الرقم الوطني': record.get('excel_record', {}).get('national_id', ''),
                            'رقم الهاتف': record.get('excel_record', {}).get('phone', ''),
                            'الرقم العسكري': record.get('excel_record', {}).get('military_id', ''),
                            'رقم الصف في Excel': record.get('row_index', ''),
                            'ID في قاعدة البيانات': record.get('db_match', {}).get('id', '') if record.get('db_match') else '',
                            'تاريخ الإنشاء في القاعدة': record.get('db_match', {}).get('created_at', '') if record.get('db_match') else '',
                            'الإجراء': record.get('action_needed', 'سيتم إضافته للدورة'),
                            'الحالة': 'سيتم إضافته للدورة'
                        })
                    will_add_df = pd.DataFrame(will_add_data)
                    will_add_df.to_excel(writer, sheet_name='سيتم إضافته للدورة', index=False)

                    # تنسيق الورقة
                    will_add_worksheet = writer.sheets['سيتم إضافته للدورة']
                    will_add_worksheet.sheet_view.rightToLeft = True

                # 3. ورقة: غير موجود في قاعدة البيانات (تفصيلي)
                new_records_data = []

                # من new_records إذا كان موجود
                if 'new_records' in analysis_results and analysis_results['new_records']:
                    for record in analysis_results['new_records']:
                        new_records_data.append({
                            'الاسم': record.get('corrected_name', ''),
                            'الرقم الوطني': record.get('excel_record', {}).get('national_id', ''),
                            'رقم الهاتف': record.get('excel_record', {}).get('phone', ''),
                            'الرقم العسكري': record.get('excel_record', {}).get('military_id', ''),
                            'رقم الصف في Excel': record.get('row_index', ''),
                            'النوع': 'جديد تماماً',
                            'الإجراء': 'سيتم إضافته للقاعدة والدورة',
                            'الحالة': 'جديد - سيتم إضافته'
                        })

                if new_records_data:
                    new_records_df = pd.DataFrame(new_records_data)
                    new_records_df.to_excel(writer, sheet_name='غير موجود في القاعدة', index=False)

                    # تنسيق الورقة
                    new_records_worksheet = writer.sheets['غير موجود في القاعدة']
                    new_records_worksheet.sheet_view.rightToLeft = True

                # 4. ورقة: المطابقات التامة (للمراجعة)
                if 'exact_matches' in analysis_results and analysis_results['exact_matches']:
                    exact_matches_data = []
                    for match in analysis_results['exact_matches']:
                        exact_matches_data.append({
                            'الاسم من Excel': match.get('excel_name', ''),
                            'الاسم من القاعدة': match.get('db_name', ''),
                            'الرقم الوطني': match.get('excel_record', {}).get('national_id', ''),
                            'رقم الهاتف': match.get('excel_record', {}).get('phone', ''),
                            'الرقم العسكري': match.get('excel_record', {}).get('military_id', ''),
                            'رقم الصف في Excel': match.get('excel_record', {}).get('row_index', ''),
                            'ID في قاعدة البيانات': match.get('db_record', {}).get('id', ''),
                            'نوع المطابقة': match.get('match_type', 'مطابقة تامة'),
                            'الحالة': 'موجود في القاعدة'
                        })

                    if exact_matches_data:
                        exact_matches_df = pd.DataFrame(exact_matches_data)
                        exact_matches_df.to_excel(writer, sheet_name='المطابقات التامة', index=False)

                        # تنسيق الورقة
                        exact_matches_worksheet = writer.sheets['المطابقات التامة']
                        exact_matches_worksheet.sheet_view.rightToLeft = True

            # ورقة بيانات الرسوم البيانية (خاصة بتحليل الدورة)
            if analysis_type == 'course':
                duplicate_in_course = len(analysis_results.get('duplicate_in_course', []))
                in_db_not_in_course = len(analysis_results.get('in_db_not_in_course', []))
                not_in_db_count = analysis_results.get('smart_statistics', {}).get('not_in_db_count', 0) if analysis_results.get('smart_statistics') else analysis_results['statistics'].get('new_records_count', 0)
                corrected_count = analysis_results['statistics'].get('corrected_count', 0)
                total_processed = analysis_results['statistics'].get('total_processed', 0)

                charts_data = [
                    {'الفئة': 'موجود في القاعدة + موجود في الدورة', 'العدد': duplicate_in_course, 'النسبة المئوية': f"{(duplicate_in_course / total_processed * 100):.1f}%" if total_processed > 0 else "0%", 'الإجراء': 'لن يتم إضافته', 'اللون': 'أحمر'},
                    {'الفئة': 'موجود في القاعدة + غير مضاف في الدورة', 'العدد': in_db_not_in_course, 'النسبة المئوية': f"{(in_db_not_in_course / total_processed * 100):.1f}%" if total_processed > 0 else "0%", 'الإجراء': 'سيتم إضافته للدورة', 'اللون': 'أخضر'},
                    {'الفئة': 'غير موجود في قاعدة البيانات', 'العدد': not_in_db_count, 'النسبة المئوية': f"{(not_in_db_count / total_processed * 100):.1f}%" if total_processed > 0 else "0%", 'الإجراء': 'سيتم إضافته للقاعدة والدورة', 'اللون': 'أزرق فاتح'},
                    {'الفئة': 'أسماء مصححة', 'العدد': corrected_count, 'النسبة المئوية': f"{(corrected_count / total_processed * 100):.1f}%" if total_processed > 0 else "0%", 'الإجراء': 'تم تصحيحها', 'اللون': 'أصفر'},
                    {'الفئة': '', 'العدد': '', 'النسبة المئوية': '', 'الإجراء': '', 'اللون': ''},  # فاصل
                    {'الفئة': '=== مؤشرات الأداء ===', 'العدد': '', 'النسبة المئوية': '', 'الإجراء': '', 'اللون': ''},
                    {'الفئة': 'معدل النجاح', 'العدد': in_db_not_in_course + not_in_db_count, 'النسبة المئوية': f"{((in_db_not_in_course + not_in_db_count) / total_processed * 100):.1f}%" if total_processed > 0 else "0%", 'الإجراء': 'السجلات المقبولة', 'اللون': 'أخضر'},
                    {'الفئة': 'معدل التكرار', 'العدد': duplicate_in_course, 'النسبة المئوية': f"{(duplicate_in_course / total_processed * 100):.1f}%" if total_processed > 0 else "0%", 'الإجراء': 'السجلات المكررة', 'اللون': 'أحمر'},
                    {'الفئة': 'معدل التصحيح', 'العدد': corrected_count, 'النسبة المئوية': f"{(corrected_count / total_processed * 100):.1f}%" if total_processed > 0 else "0%", 'الإجراء': 'السجلات المصححة', 'اللون': 'أصفر'},
                    {'الفئة': '', 'العدد': '', 'النسبة المئوية': '', 'الإجراء': '', 'اللون': ''},  # فاصل
                    {'الفئة': '=== مسار معالجة البيانات ===', 'العدد': '', 'النسبة المئوية': '', 'الإجراء': '', 'اللون': ''},
                    {'الفئة': 'بداية التحليل', 'العدد': total_processed, 'النسبة المئوية': '100%', 'الإجراء': 'إجمالي السجلات', 'اللون': 'رمادي'},
                    {'الفئة': 'موجود في قاعدة البيانات', 'العدد': duplicate_in_course + in_db_not_in_course, 'النسبة المئوية': f"{((duplicate_in_course + in_db_not_in_course) / total_processed * 100):.1f}%" if total_processed > 0 else "0%", 'الإجراء': 'تم العثور عليه', 'اللون': 'أزرق'},
                    {'الفئة': 'النتائج النهائية', 'العدد': in_db_not_in_course + not_in_db_count, 'النسبة المئوية': f"{((in_db_not_in_course + not_in_db_count) / total_processed * 100):.1f}%" if total_processed > 0 else "0%", 'الإجراء': 'سيتم إضافتها', 'اللون': 'أزرق فاتح'}
                ]

                charts_df = pd.DataFrame(charts_data)
                charts_df.to_excel(writer, sheet_name='بيانات الرسوم البيانية', index=False)

                # تعيين اتجاه الورقة من اليمين إلى اليسار
                charts_worksheet = writer.sheets['بيانات الرسوم البيانية']
                charts_worksheet.sheet_view.rightToLeft = True

                # تنسيق خاص للورقة
                from openpyxl.styles import Font, PatternFill, Alignment

                # تنسيق العناوين
                for cell in charts_worksheet[1]:
                    cell.font = Font(bold=True, size=12)
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")

                # تنسيق الفواصل والعناوين الفرعية
                for row in charts_worksheet.iter_rows(min_row=2):
                    if row[0].value and str(row[0].value).startswith('==='):
                        for cell in row:
                            cell.font = Font(bold=True, size=11, color="FF0000")
                            cell.fill = PatternFill(start_color="F0F0F0", end_color="F0F0F0", fill_type="solid")

        # إعادة المؤشر إلى بداية الملف
        output.seek(0)

        # إنشاء اسم الملف حسب نوع التحليل
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        print(f"🏷️ إنشاء اسم الملف - نوع التحليل: {analysis_type}")

        if analysis_type == 'evaluation':
            if selected_course_id and 'course_analysis' in analysis_results:
                course_info = analysis_results['course_analysis'].get('selected_course', {})
                course_number = course_info.get('course_number', 'غير_محدد')
                filename = f"تقرير_تحليل_التقييمات_الدورة_{course_number}_{timestamp}.xlsx"
            else:
                filename = f"تقرير_تحليل_التقييمات_{timestamp}.xlsx"
        elif analysis_type == 'course':
            if selected_course_id and 'course_analysis' in analysis_results:
                course_info = analysis_results['course_analysis'].get('selected_course', {})
                course_number = course_info.get('course_number', 'غير_محدد')
                filename = f"تقرير_تحليل_الدورة_{course_number}_{timestamp}.xlsx"
            else:
                filename = f"تقرير_تحليل_الدورة_{timestamp}.xlsx"
        else:  # data analysis
            filename = f"تقرير_تحليل_البيانات_{timestamp}.xlsx"

        # إرسال الملف
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير النتائج: {str(e)}', 'danger')
        return redirect(url_for('person_data.name_analysis'))

def translate_stat_key(key):
    """
    ترجمة مفاتيح الإحصائيات إلى العربية
    """
    translations = {
        'total_processed': 'إجمالي السجلات المعالجة',
        'corrected_count': 'عدد الأسماء المصححة',
        'exact_matches_count': 'عدد المطابقات التامة',
        'triple_similarity_count': 'عدد التشابه الثلاثي',
        'quadruple_similarity_count': 'عدد التشابه الرباعي',
        'quintuple_similarity_count': 'عدد التشابه الخماسي',
        'full_with_title_count': 'عدد التشابه الكامل مع اللقب',
        'six_plus_count': 'عدد الأسماء الطويلة (أكثر من 6 أجزاء)',
        'new_records_count': 'عدد السجلات الجديدة',
        'allowed_duplicates_count': 'عدد الأسماء المكررة المسموحة',
        'blocked_duplicates_count': 'عدد السجلات المرفوضة',
        'name_national_id_matches': 'تطابق الاسم والرقم الوطني',
        'name_phone_matches': 'تطابق الاسم ورقم الهاتف',
        'name_military_id_matches': 'تطابق الاسم والرقم العسكري',
        'national_id_only_matches': 'تطابق الرقم الوطني فقط',
        'phone_only_matches': 'تطابق رقم الهاتف فقط',
        'military_id_only_matches': 'تطابق الرقم العسكري فقط',
        'has_national_id_column': 'يحتوي على عمود الرقم الوطني',
        'has_phone_column': 'يحتوي على عمود رقم الهاتف',
        'has_military_id_column': 'يحتوي على عمود الرقم العسكري'
    }
    return translations.get(key, key)

@person_data_bp.route('/export_new_names_only')
@login_required
def export_new_names_only():
    """
    تصدير الأسماء الجديدة والمصححة فقط (غير الموجودة في قاعدة البيانات)
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب النتائج من الجلسة
    analysis_results = session.get('analysis_results')
    excel_filename = session.get('excel_filename', 'unknown_file.xlsx')

    if not analysis_results:
        flash('لا توجد نتائج تحليل للتصدير', 'danger')
        return redirect(url_for('person_data.name_analysis'))

    try:
        # إنشاء قائمة السجلات الجديدة والمسموحة
        new_and_allowed_records = []

        # إضافة السجلات الجديدة
        if 'new_records' in analysis_results:
            for record in analysis_results['new_records']:
                new_and_allowed_records.append({
                    'الاسم الشخصي': record['corrected_name'],
                    'الرقم الوطني': record['excel_record'].get('national_id', ''),
                    'رقم الهاتف': record['excel_record'].get('phone', ''),
                    'الرقم العسكري': record['excel_record'].get('military_id', ''),
                    'رقم الصف': record['excel_record'].get('row_index', ''),
                    'النوع': 'سجل جديد',
                    'الملاحظات': 'غير موجود في قاعدة البيانات'
                })

        # إضافة الأسماء المكررة المسموحة
        if 'allowed_duplicates' in analysis_results:
            for allowed in analysis_results['allowed_duplicates']:
                new_and_allowed_records.append({
                    'الاسم الشخصي': allowed['corrected_name'],
                    'الرقم الوطني': allowed['excel_record'].get('national_id', ''),
                    'رقم الهاتف': allowed['excel_record'].get('phone', ''),
                    'الرقم العسكري': allowed['excel_record'].get('military_id', ''),
                    'رقم الصف': allowed['excel_record'].get('row_index', ''),
                    'النوع': 'اسم مكرر مسموح',
                    'الملاحظات': allowed['reason']
                })

        # إضافة الأسماء المصححة الجديدة (للتوافق مع النظام القديم)
        if 'corrected_names' in analysis_results:
            for correction in analysis_results['corrected_names']:
                # التحقق من أن الاسم المصحح ليس موجوداً في المطابقات التامة
                is_existing = False
                if 'exact_matches' in analysis_results:
                    for match in analysis_results['exact_matches']:
                        if correction['corrected'].lower() == match.get('excel_name', '').lower():
                            is_existing = True
                            break

                if not is_existing:
                    new_and_allowed_records.append({
                        'الاسم الشخصي': correction['corrected'],
                        'الرقم الوطني': correction.get('national_id', ''),
                        'رقم الهاتف': correction.get('phone', ''),
                        'الرقم العسكري': correction.get('military_id', ''),
                        'رقم الصف': correction.get('row_index', ''),
                        'النوع': 'مصحح وجديد',
                        'الملاحظات': f"تم تصحيحه من: {correction['original']}"
                    })

        if not new_and_allowed_records:
            flash('لا توجد سجلات جديدة للتصدير', 'info')
            return redirect(url_for('person_data.name_analysis'))

        # إنشاء DataFrame
        df = pd.DataFrame(new_and_allowed_records)

        # إنشاء ملف Excel في الذاكرة
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='الأسماء الجديدة', index=False)

            # تنسيق ورقة العمل
            worksheet = writer.sheets['الأسماء الجديدة']

            # تعيين عرض الأعمدة
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # تعيين اتجاه الورقة من اليمين إلى اليسار
            worksheet.sheet_view.rightToLeft = True

        # إعادة المؤشر إلى بداية الملف
        output.seek(0)

        # إنشاء اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"الأسماء_الجديدة_فقط_{timestamp}.xlsx"

        # إرسال الملف
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير الأسماء الجديدة: {str(e)}', 'danger')
        return redirect(url_for('person_data.name_analysis'))

@person_data_bp.route('/import_analyzed_names', methods=['POST', 'GET'])
@login_required
def import_analyzed_names():
    """
    استيراد الأسماء الجديدة المحللة مباشرة إلى قاعدة البيانات وإلى الدورة إن وجدت
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # جلب النتائج من الجلسة
    analysis_results = session.get('analysis_results')
    selected_course_id = session.get('selected_course_id')
    selected_course_info = session.get('selected_course_info')
    analysis_type = session.get('analysis_type', 'course')

    if not analysis_results:
        flash('لا توجد نتائج تحليل للاستيراد', 'danger')
        return redirect(url_for('person_data.name_analysis'))

    # التحقق من نوع الاستيراد من URL parameter
    import_type = request.args.get('type', 'normal')

    print(f"🔄 نوع الاستيراد: {import_type}")
    print(f"🎯 نوع التحليل: {analysis_type}")

    try:
        imported_count = 0
        course_participants_count = 0
        evaluations_count = 0

        # جلب الدورة إن وجدت
        selected_course = None
        if selected_course_id:
            try:
                selected_course = Course.query.get(int(selected_course_id))
            except (ValueError, TypeError):
                selected_course = None

        # معالجة استيراد التقييمات
        if import_type == 'evaluation' and analysis_type == 'evaluation':
            print(f"🔄 بدء استيراد التقييمات...")

            if not selected_course:
                flash('يجب اختيار دورة لاستيراد التقييمات', 'danger')
                return redirect(url_for('person_data.name_analysis'))

            # استيراد التقييمات الجديدة
            if 'evaluation_analysis' in analysis_results and 'new_evaluations' in analysis_results['evaluation_analysis']:
                for evaluation_record in analysis_results['evaluation_analysis']['new_evaluations']:
                    name = evaluation_record['name']
                    excel_record = evaluation_record['excel_record']

                    # البحث عن الشخص في قاعدة البيانات أو إنشاؤه
                    person = PersonData.query.filter_by(full_name=name).first()
                    if not person:
                        # إنشاء شخص جديد
                        person = PersonData(
                            full_name=name,
                            national_number=excel_record.get('national_id', ''),
                            phone=excel_record.get('phone', ''),
                            military_number=excel_record.get('military_id', '')
                        )
                        db.session.add(person)
                        db.session.flush()  # للحصول على ID
                        imported_count += 1

                    # إضافة كمشارك في الدورة إذا لم يكن موجود
                    existing_participant = CourseParticipant.query.filter_by(
                        course_id=selected_course.id,
                        personal_data_id=person.id
                    ).first()

                    if not existing_participant:
                        new_participant = CourseParticipant(
                            course_id=selected_course.id,
                            personal_data_id=person.id,
                            status='active'
                        )
                        db.session.add(new_participant)
                        db.session.flush()
                        course_participants_count += 1
                        participant_id = new_participant.id
                    else:
                        participant_id = existing_participant.id

                    # إضافة تقييم في جدول participant_evaluations
                    try:
                        from datetime import datetime
                        db.engine.execute("""
                            INSERT INTO participant_evaluations
                            (course_id, participant_id, evaluator_id, total_score, percentage, grade, notes, is_final, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            selected_course.id,
                            participant_id,
                            current_user.id,
                            0.0,  # درجة افتراضية
                            0.0,  # نسبة افتراضية
                            '',   # تقدير افتراضي
                            f'تم إضافة التقييم من ملف Excel: {session.get("excel_filename", "")}',
                            0,    # ليس نهائي
                            datetime.now()
                        ))
                        evaluations_count += 1
                    except Exception as e:
                        print(f"خطأ في إضافة التقييم: {str(e)}")

            # حفظ التغييرات
            db.session.commit()

            # رسالة النجاح للتقييمات
            flash(f'تم استيراد {imported_count} اسم جديد و {course_participants_count} مشارك و {evaluations_count} تقييم للدورة {selected_course.course_number}', 'success')

            # مسح النتائج من الجلسة
            session.pop('analysis_results', None)
            session.pop('excel_filename', None)
            session.pop('selected_course_id', None)
            session.pop('selected_course_info', None)
            session.pop('analysis_type', None)

            return redirect(url_for('course_participants', course_id=selected_course.id))

        # المعالجة العادية للدورات والبيانات
        else:
            # استيراد السجلات الجديدة
            if 'new_records' in analysis_results:
                for record in analysis_results['new_records']:
                    name = record['corrected_name']
                    excel_record = record['excel_record']

                    # التحقق من عدم وجود الاسم مسبقاً
                    existing = PersonData.query.filter_by(full_name=name).first()
                    if not existing:
                        person = PersonData(
                            full_name=name,
                            national_number=excel_record.get('national_id', ''),
                            phone=excel_record.get('phone', ''),
                            military_number=excel_record.get('military_id', '')
                        )
                        db.session.add(person)
                        db.session.flush()  # للحصول على ID
                        imported_count += 1

                        # إضافة للدورة إن وجدت
                        if selected_course:
                            # التحقق من عدم وجود المشارك في الدورة مسبقاً
                            existing_participant = CourseParticipant.query.filter_by(
                                course_id=selected_course.id,
                                personal_data_id=person.id
                            ).first()

                            if not existing_participant:
                                new_participant = CourseParticipant(
                                    course_id=selected_course.id,
                                    personal_data_id=person.id,
                                    status='active'
                                )
                                db.session.add(new_participant)
                                course_participants_count += 1

            # استيراد الأسماء المكررة المسموحة
            if 'allowed_duplicates' in analysis_results:
                for allowed in analysis_results['allowed_duplicates']:
                    name = allowed['corrected_name']
                    excel_record = allowed['excel_record']

                    # التحقق من عدم وجود الاسم مسبقاً
                    existing = PersonData.query.filter_by(full_name=name).first()
                    if not existing:
                        person = PersonData(
                            full_name=name,
                            national_number=excel_record.get('national_id', ''),
                            phone=excel_record.get('phone', ''),
                            military_number=excel_record.get('military_id', '')
                        )
                        db.session.add(person)
                        db.session.flush()  # للحصول على ID
                        imported_count += 1

                        # إضافة للدورة إن وجدت
                        if selected_course:
                            # التحقق من عدم وجود المشارك في الدورة مسبقاً
                            existing_participant = CourseParticipant.query.filter_by(
                                course_id=selected_course.id,
                                personal_data_id=person.id
                            ).first()

                            if not existing_participant:
                                new_participant = CourseParticipant(
                                    course_id=selected_course.id,
                                    personal_data_id=person.id,
                                    status='active'
                                )
                                db.session.add(new_participant)
                                course_participants_count += 1

            # استيراد الأسماء المصححة الجديدة (للتوافق مع النظام القديم)
            if 'corrected_names' in analysis_results:
                for correction in analysis_results['corrected_names']:
                    corrected_name = correction['corrected']
                    # التحقق من عدم وجود الاسم المصحح مسبقاً
                    existing = PersonData.query.filter_by(full_name=corrected_name).first()
                    if not existing:
                        person = PersonData(
                            full_name=corrected_name,
                            national_number=correction.get('national_id', ''),
                            phone=correction.get('phone', ''),
                            military_number=correction.get('military_id', '')
                        )
                        db.session.add(person)
                        db.session.flush()  # للحصول على ID
                        imported_count += 1

                        # إضافة للدورة إن وجدت
                        if selected_course:
                            # التحقق من عدم وجود المشارك في الدورة مسبقاً
                            existing_participant = CourseParticipant.query.filter_by(
                                course_id=selected_course.id,
                                personal_data_id=person.id
                            ).first()

                            if not existing_participant:
                                new_participant = CourseParticipant(
                                    course_id=selected_course.id,
                                    personal_data_id=person.id,
                                    status='active'
                                )
                                db.session.add(new_participant)
                                course_participants_count += 1

            # حفظ التغييرات
            db.session.commit()

            # رسالة النجاح
            if selected_course:
                flash(f'تم استيراد {imported_count} اسم جديد إلى قاعدة البيانات و {course_participants_count} مشارك إلى الدورة {selected_course.course_number}', 'success')
            else:
                flash(f'تم استيراد {imported_count} اسم جديد بنجاح إلى قاعدة البيانات', 'success')

            # مسح النتائج من الجلسة
            session.pop('analysis_results', None)
            session.pop('excel_filename', None)
            session.pop('selected_course_id', None)
            session.pop('selected_course_info', None)
            session.pop('analysis_type', None)

            return redirect(url_for('person_data.person_data_table'))

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء استيراد الأسماء: {str(e)}', 'danger')
        return redirect(url_for('person_data.name_analysis'))

@person_data_bp.route('/person_data_excel')
@login_required
def person_data_excel():
    """
    صفحة إدارة البيانات بالإكسل
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # جلب الإحصائيات
        total_persons = PersonData.query.count()
        total_courses = Course.query.count()
        total_participants = CourseParticipant.query.count()

        # حساب الأسماء المكررة
        duplicate_names_count = 0
        try:
            # جلب جميع الأسماء
            all_names = [p.full_name for p in PersonData.query.all() if p.full_name]
            # حساب المكررات
            from collections import Counter
            name_counts = Counter(all_names)
            duplicate_names_count = sum(1 for count in name_counts.values() if count > 1)
        except:
            duplicate_names_count = 0

        return render_template('person_data/excel_management.html',
                             title='إدارة البيانات بالإكسل',
                             total_persons=total_persons,
                             total_courses=total_courses,
                             total_participants=total_participants,
                             duplicate_names_count=duplicate_names_count)
    except Exception as e:
        flash(f'حدث خطأ في تحميل الصفحة: {str(e)}', 'danger')
        return redirect(url_for('person_data.person_data_table'))

@person_data_bp.route('/download_evaluation_template')
def download_evaluation_template():
    """
    تحميل قالب Excel لكشف التقييمات
    """
    print("🔥 تم الوصول إلى route تحميل قالب التقييمات")
    try:
        print("📦 استيراد pandas...")
        import pandas as pd
        print("📦 استيراد BytesIO...")
        from io import BytesIO
        print("✅ تم استيراد المكتبات بنجاح")

        # إنشاء DataFrame مع جميع الأعمدة المطلوبة للتقييمات
        print("📋 إنشاء قالب التقييمات الكامل...")
        evaluation_columns = [
            'الاسم الشخصي',      # العمود الأساسي للاسم
            'الرقم الوطني',
            'رقم الهاتف',
            'الرقم العسكري',
            'الدرجة النهائية',
            'النسبة المئوية',
            'التقدير',
            'ملاحظات التقييم',
            'تاريخ التقييم',
            'حالة التقييم'
        ]

        df = pd.DataFrame(columns=evaluation_columns)

        # إضافة بيانات توضيحية
        sample_data = [
            ['أحمد محمد علي', '123456789', '777123456', 'M12345', '85', '85%', 'جيد جداً', 'اجتاز التقييم بنجاح', '2024-01-15', 'مكتمل'],
            ['فاطمة سالم أحمد', '987654321', '777987654', 'M67890', '92', '92%', 'ممتاز', 'أداء متميز', '2024-01-15', 'مكتمل'],
            ['محمد عبدالله حسن', '456789123', '777456789', 'M11111', '78', '78%', 'جيد', 'يحتاج تحسين', '2024-01-15', 'مكتمل']
        ]

        for i, row in enumerate(sample_data):
            df.loc[i] = row

        print("📁 إنشاء ملف Excel مع التنسيق...")

        # إنشاء ملف Excel في الذاكرة مع تنسيق متقدم
        output = BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='كشف التقييمات', index=False)

            # الحصول على workbook و worksheet للتنسيق
            workbook = writer.book
            worksheet = writer.sheets['كشف التقييمات']

            # تنسيق الرأس
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#4472C4',
                'font_color': 'white',
                'border': 1,
                'font_size': 12
            })

            # تنسيق البيانات التوضيحية
            sample_format = workbook.add_format({
                'fg_color': '#E7E6E6',
                'border': 1,
                'text_wrap': True,
                'font_size': 10
            })

            # تطبيق التنسيق على الرأس
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
                worksheet.set_column(col_num, col_num, 18)  # عرض العمود

            # تطبيق التنسيق على البيانات التوضيحية
            for row_num in range(1, len(sample_data) + 1):
                for col_num in range(len(evaluation_columns)):
                    worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], sample_format)

            # إضافة ملاحظات
            notes_format = workbook.add_format({
                'bold': True,
                'fg_color': '#FFE699',
                'border': 1,
                'font_size': 11
            })

            note_format = workbook.add_format({
                'fg_color': '#FFF2CC',
                'border': 1,
                'text_wrap': True,
                'font_size': 10
            })

            start_row = len(sample_data) + 2
            worksheet.write(start_row, 0, 'ملاحظات مهمة:', notes_format)
            worksheet.write(start_row + 1, 0, '1. احذف الصفوف التوضيحية (الرمادية) قبل رفع الملف', note_format)
            worksheet.write(start_row + 2, 0, '2. تأكد من وجود عمود "الاسم الشخصي" بهذا الاسم تماماً', note_format)
            worksheet.write(start_row + 3, 0, '3. يمكن ترك الحقول الاختيارية فارغة', note_format)
            worksheet.write(start_row + 4, 0, '4. استخدم هذا القالب لفحص كشف التقييمات فقط', note_format)
            worksheet.write(start_row + 5, 0, '5. الأعمدة المطلوبة: الاسم الشخصي (إجباري)', note_format)

            # توسيع الأعمدة للملاحظات
            worksheet.set_column(0, 0, 50)

        output.seek(0)

        print("✅ تم إنشاء الملف بنجاح")

        # إنشاء الاستجابة
        from flask import make_response
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = 'attachment; filename=قالب_كشف_التقييمات.xlsx'

        print("🎉 إرسال الملف للمستخدم...")
        return response

    except Exception as e:
        print(f"خطأ في تحميل قالب التقييمات: {str(e)}")
        import traceback
        traceback.print_exc()
        flash(f'حدث خطأ في تحميل القالب: {str(e)}', 'danger')
        return redirect(url_for('person_data.name_analysis'))

@person_data_bp.route('/update_evaluations', methods=['POST'])
def update_evaluations():
    """
    تحديث التقييمات المدخلة
    """
    try:
        selected_evaluations = request.form.getlist('selected_evaluations')

        if not selected_evaluations:
            flash('لم يتم اختيار أي تقييمات للتحديث', 'warning')
            return redirect(url_for('person_data.name_analysis'))

        updated_count = 0

        for person_id in selected_evaluations:
            action = request.form.get(f'action_{person_id}')

            if action == 'update_data':
                # تحديث البيانات الشخصية
                # يمكن إضافة المنطق هنا لاحقاً
                updated_count += 1
            elif action == 'add_evaluation':
                # إضافة تقييم جديد
                # يمكن إضافة المنطق هنا لاحقاً
                updated_count += 1
            elif action == 'update_evaluation':
                # تحديث التقييم الحالي
                # يمكن إضافة المنطق هنا لاحقاً
                updated_count += 1

        flash(f'تم تحديث {updated_count} تقييم بنجاح', 'success')
        return redirect(url_for('person_data.name_analysis'))

    except Exception as e:
        print(f"خطأ في تحديث التقييمات: {str(e)}")
        flash(f'حدث خطأ في تحديث التقييمات: {str(e)}', 'danger')
        return redirect(url_for('person_data.name_analysis'))

@person_data_bp.route('/export_corrections_guide')
def export_corrections_guide():
    """
    تصدير دليل التصحيحات الشامل إلى ملف Excel
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # الحصول على دليل التصحيحات
        guide = get_all_corrections_guide()

        # إنشاء ملف Excel في الذاكرة
        output = io.BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # ورقة تصحيحات الهمزات
            if guide['hamza_corrections']:
                hamza_df = pd.DataFrame(guide['hamza_corrections'])
                hamza_df.to_excel(writer, sheet_name='تصحيحات الهمزات', index=False)

                # تعيين اتجاه الورقة من اليمين إلى اليسار
                hamza_worksheet = writer.sheets['تصحيحات الهمزات']
                hamza_worksheet.sheet_view.rightToLeft = True

            # ورقة تصحيحات الألف المقصورة
            if guide['alif_maqsura_corrections']:
                alif_df = pd.DataFrame(guide['alif_maqsura_corrections'])
                alif_df.to_excel(writer, sheet_name='تصحيحات الألف المقصورة', index=False)

                # تعيين اتجاه الورقة من اليمين إلى اليسار
                alif_worksheet = writer.sheets['تصحيحات الألف المقصورة']
                alif_worksheet.sheet_view.rightToLeft = True

            # ورقة تصحيحات الأسماء المركبة
            if guide['compound_names_corrections']:
                compound_df = pd.DataFrame(guide['compound_names_corrections'])
                compound_df.to_excel(writer, sheet_name='تصحيحات الأسماء المركبة', index=False)

                # تعيين اتجاه الورقة من اليمين إلى اليسار
                compound_worksheet = writer.sheets['تصحيحات الأسماء المركبة']
                compound_worksheet.sheet_view.rightToLeft = True

            # ورقة تصحيحات الألقاب والكنى
            if guide['titles_corrections']:
                titles_df = pd.DataFrame(guide['titles_corrections'])
                titles_df.to_excel(writer, sheet_name='تصحيحات الألقاب والكنى', index=False)

                # تعيين اتجاه الورقة من اليمين إلى اليسار
                titles_worksheet = writer.sheets['تصحيحات الألقاب والكنى']
                titles_worksheet.sheet_view.rightToLeft = True

            # ورقة الأخطاء الشائعة
            if guide['common_mistakes']:
                mistakes_df = pd.DataFrame(guide['common_mistakes'])
                mistakes_df.to_excel(writer, sheet_name='الأخطاء الشائعة', index=False)

                # تعيين اتجاه الورقة من اليمين إلى اليسار
                mistakes_worksheet = writer.sheets['الأخطاء الشائعة']
                mistakes_worksheet.sheet_view.rightToLeft = True

            # ورقة التصحيحات المخصصة
            if guide['custom_corrections']:
                custom_df = pd.DataFrame(guide['custom_corrections'])
                custom_df.to_excel(writer, sheet_name='التصحيحات المخصصة', index=False)

                # تعيين اتجاه الورقة من اليمين إلى اليسار
                custom_worksheet = writer.sheets['التصحيحات المخصصة']
                custom_worksheet.sheet_view.rightToLeft = True

            # ورقة ملخص الدليل
            summary_data = [
                {'القسم': 'تصحيحات الهمزات', 'عدد التصحيحات': len(guide['hamza_corrections'])},
                {'القسم': 'تصحيحات الألف المقصورة', 'عدد التصحيحات': len(guide['alif_maqsura_corrections'])},
                {'القسم': 'تصحيحات الأسماء المركبة', 'عدد التصحيحات': len(guide['compound_names_corrections'])},
                {'القسم': 'تصحيحات الألقاب والكنى', 'عدد التصحيحات': len(guide['titles_corrections'])},
                {'القسم': 'الأخطاء الشائعة', 'عدد التصحيحات': len(guide['common_mistakes'])},
                {'القسم': 'التصحيحات المخصصة', 'عدد التصحيحات': len(guide['custom_corrections'])},
                {'القسم': 'المجموع الكلي', 'عدد التصحيحات':
                 len(guide['hamza_corrections']) +
                 len(guide['alif_maqsura_corrections']) +
                 len(guide['compound_names_corrections']) +
                 len(guide['titles_corrections']) +
                 len(guide['common_mistakes']) +
                 len(guide['custom_corrections'])}
            ]

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='ملخص الدليل', index=False)

            # تعيين اتجاه ورقة الملخص من اليمين إلى اليسار
            summary_worksheet = writer.sheets['ملخص الدليل']
            summary_worksheet.sheet_view.rightToLeft = True

        # إعادة المؤشر إلى بداية الملف
        output.seek(0)

        # إنشاء اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"دليل_تصحيح_الأسماء_العربية_{timestamp}.xlsx"

        # إرسال الملف
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير دليل التصحيحات: {str(e)}', 'danger')
        return redirect(url_for('person_data.name_analysis'))

@person_data_bp.route('/manage_corrections')
@login_required
def manage_corrections():
    """
    صفحة إدارة التصحيحات المخصصة
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        from sqlalchemy import text
        query = text("""
            SELECT nc.id, nc.wrong_name, nc.correct_name, nc.correction_type,
                   nc.created_at, nc.usage_count, nc.is_active, nc.created_by
            FROM name_correction nc
            ORDER BY nc.created_at DESC
        """)
        result = db.session.execute(query)

        corrections = []
        for row in result:
            corrections.append({
                'id': row.id,
                'wrong_name': row.wrong_name,
                'correct_name': row.correct_name,
                'correction_type': row.correction_type,
                'created_at': row.created_at,
                'usage_count': row.usage_count or 0,
                'is_active': bool(row.is_active),
                'creator': {'username': 'admin'}  # افتراضي
            })
    except Exception as e:
        corrections = []
        flash(f'حدث خطأ في جلب التصحيحات: {str(e)}', 'warning')

    # إضافة رسالة تصحيح
    flash(f'تم جلب {len(corrections)} تصحيح من قاعدة البيانات', 'info')

    return render_template('person_data/manage_corrections.html',
                         title='إدارة التصحيحات المخصصة',
                         corrections=corrections)

@person_data_bp.route('/add_correction', methods=['GET', 'POST'])
@login_required
def add_correction():
    """
    إضافة تصحيح مخصص جديد
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # إنشاء النموذج محلياً لتجنب مشاكل الاستيراد
    from flask_wtf import FlaskForm
    from wtforms import StringField, SelectField, SubmitField
    from wtforms.validators import DataRequired, Length

    class NameCorrectionForm(FlaskForm):
        wrong_name = StringField('الاسم الخطأ', validators=[DataRequired(), Length(max=200)],
                                render_kw={"placeholder": "مثال: عيسي عبداللة"})
        correct_name = StringField('الاسم الصحيح', validators=[DataRequired(), Length(max=200)],
                                  render_kw={"placeholder": "مثال: عيسى عبدالله"})
        correction_type = SelectField('نوع التصحيح', choices=[
            ('', 'اختر نوع التصحيح'),
            ('hamza', 'تصحيح الهمزات'),
            ('alif_maqsura', 'تصحيح الألف المقصورة'),
            ('compound_names', 'الأسماء المركبة'),
            ('titles', 'الألقاب والكنى'),
            ('symbols', 'إزالة الرموز والأرقام'),
            ('other', 'أخرى')
        ], validators=[DataRequired()])
        submit = SubmitField('إضافة التصحيح')

    form = NameCorrectionForm()

    if form.validate_on_submit():
        try:
            from sqlalchemy import text
            from datetime import datetime, timezone

            # التحقق من عدم وجود تصحيح مماثل
            check_query = text("SELECT id FROM name_correction WHERE wrong_name = :wrong AND correct_name = :correct")
            existing = db.session.execute(check_query, {
                'wrong': form.wrong_name.data,
                'correct': form.correct_name.data
            }).first()

            if existing:
                flash('هذا التصحيح موجود بالفعل!', 'warning')
                return redirect(url_for('person_data.manage_corrections'))

            # إضافة تصحيح جديد
            insert_query = text("""
                INSERT INTO name_correction (wrong_name, correct_name, correction_type, created_by, created_at, is_active, usage_count)
                VALUES (:wrong, :correct, :type, :user_id, :created, 1, 0)
            """)

            db.session.execute(insert_query, {
                'wrong': form.wrong_name.data,
                'correct': form.correct_name.data,
                'type': form.correction_type.data,
                'user_id': current_user.id,
                'created': datetime.now(timezone.utc)
            })

            db.session.commit()

            flash(f'تم إضافة التصحيح بنجاح: "{form.wrong_name.data}" → "{form.correct_name.data}"', 'success')
            return redirect(url_for('person_data.manage_corrections'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في إضافة التصحيح: {str(e)}', 'danger')

    return render_template('person_data/add_correction.html',
                         title='إضافة تصحيح مخصص',
                         form=form)

@person_data_bp.route('/toggle_correction/<int:correction_id>')
@login_required
def toggle_correction(correction_id):
    """
    تفعيل/إلغاء تفعيل تصحيح مخصص
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        from sqlalchemy import text

        # جلب التصحيح الحالي
        get_query = text("SELECT wrong_name, correct_name, is_active FROM name_correction WHERE id = :id")
        correction = db.session.execute(get_query, {'id': correction_id}).first()

        if not correction:
            flash('التصحيح غير موجود', 'danger')
            return redirect(url_for('person_data.manage_corrections'))

        # تبديل حالة التفعيل
        new_status = not bool(correction.is_active)
        update_query = text("UPDATE name_correction SET is_active = :status WHERE id = :id")
        db.session.execute(update_query, {'status': new_status, 'id': correction_id})
        db.session.commit()

        status = 'تم تفعيل' if new_status else 'تم إلغاء تفعيل'
        flash(f'{status} التصحيح: "{correction.wrong_name}" → "{correction.correct_name}"', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في تحديث التصحيح: {str(e)}', 'danger')

    return redirect(url_for('person_data.manage_corrections'))

@person_data_bp.route('/delete_correction/<int:correction_id>')
@login_required
def delete_correction(correction_id):
    """
    حذف تصحيح مخصص
    """
    # التحقق من أن المستخدم هو مدير
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    try:
        from sqlalchemy import text

        # جلب بيانات التصحيح قبل الحذف
        get_query = text("SELECT wrong_name, correct_name FROM name_correction WHERE id = :id")
        correction = db.session.execute(get_query, {'id': correction_id}).first()

        if not correction:
            flash('التصحيح غير موجود', 'danger')
            return redirect(url_for('person_data.manage_corrections'))

        # حذف التصحيح
        delete_query = text("DELETE FROM name_correction WHERE id = :id")
        db.session.execute(delete_query, {'id': correction_id})
        db.session.commit()

        flash(f'تم حذف التصحيح: "{correction.wrong_name}" → "{correction.correct_name}"', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في حذف التصحيح: {str(e)}', 'danger')

    return redirect(url_for('person_data.manage_corrections'))

