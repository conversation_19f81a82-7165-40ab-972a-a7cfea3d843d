#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ نظام الموثوقية والاعتمادية الشامل
Comprehensive Reliability and Robustness System
"""

import os
import sys
import logging
import traceback
import functools
import json
import sqlite3
import shutil
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Callable, Optional, Dict, List
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

class ReliabilitySystem:
    """نظام الموثوقية الشامل"""
    
    def __init__(self):
        self.error_count = {}
        self.system_health = {}
        self.backup_manager = BackupManager()
        self.logger = self.setup_logging()
        self.monitoring_active = True
        
        # بدء مراقبة النظام
        self.start_system_monitoring()
    
    def setup_logging(self):
        """إعداد نظام التسجيل المتقدم"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # إعداد logger رئيسي
        logger = logging.getLogger('reliability')
        logger.setLevel(logging.DEBUG)
        
        # تنسيق الرسائل
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        
        # معالج ملف للأخطاء
        error_handler = logging.FileHandler(
            log_dir / f"errors_{datetime.now().strftime('%Y%m%d')}.log",
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        
        # معالج ملف للمعلومات
        info_handler = logging.FileHandler(
            log_dir / f"system_{datetime.now().strftime('%Y%m%d')}.log",
            encoding='utf-8'
        )
        info_handler.setLevel(logging.INFO)
        info_handler.setFormatter(formatter)
        
        # معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(error_handler)
        logger.addHandler(info_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def error_handler(self, reraise=True, default_return=None, max_retries=3):
        """ديكوريتر معالجة الأخطاء المتقدم"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                func_name = f"{func.__module__}.{func.__name__}"
                
                for attempt in range(max_retries + 1):
                    try:
                        result = func(*args, **kwargs)
                        
                        # إعادة تعيين عداد الأخطاء عند النجاح
                        if func_name in self.error_count:
                            self.error_count[func_name] = 0
                        
                        return result
                        
                    except Exception as e:
                        # تسجيل الخطأ
                        error_info = {
                            'function': func_name,
                            'attempt': attempt + 1,
                            'max_retries': max_retries,
                            'error_type': type(e).__name__,
                            'error_message': str(e),
                            'traceback': traceback.format_exc(),
                            'args': str(args)[:200],
                            'kwargs': str(kwargs)[:200],
                            'timestamp': datetime.now().isoformat()
                        }
                        
                        self.logger.error(f"Error in {func_name}: {json.dumps(error_info, indent=2)}")
                        
                        # تحديث عداد الأخطاء
                        if func_name not in self.error_count:
                            self.error_count[func_name] = 0
                        self.error_count[func_name] += 1
                        
                        # إرسال تنبيه إذا تكررت الأخطاء
                        if self.error_count[func_name] >= 5:
                            self.send_alert(f"Multiple errors in {func_name}", error_info)
                        
                        # إعادة المحاولة أو إرجاع القيمة الافتراضية
                        if attempt < max_retries:
                            wait_time = 2 ** attempt  # exponential backoff
                            self.logger.warning(f"Retrying {func_name} in {wait_time}s (attempt {attempt + 2})")
                            time.sleep(wait_time)
                            continue
                        else:
                            if reraise:
                                raise
                            else:
                                self.logger.warning(f"Returning default value for {func_name}")
                                return default_return
            
            return wrapper
        return decorator
    
    def validate_input(self, validation_rules: Dict):
        """ديكوريتر التحقق من صحة المدخلات"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                func_name = f"{func.__module__}.{func.__name__}"
                
                try:
                    # التحقق من المعاملات
                    for param_name, rules in validation_rules.items():
                        if param_name in kwargs:
                            value = kwargs[param_name]
                        elif len(args) > rules.get('position', -1):
                            value = args[rules['position']]
                        else:
                            if rules.get('required', False):
                                raise ValueError(f"Required parameter '{param_name}' is missing")
                            continue
                        
                        # التحقق من النوع
                        if 'type' in rules and not isinstance(value, rules['type']):
                            raise TypeError(f"Parameter '{param_name}' must be of type {rules['type']}")
                        
                        # التحقق من القيم المسموحة
                        if 'allowed_values' in rules and value not in rules['allowed_values']:
                            raise ValueError(f"Parameter '{param_name}' must be one of {rules['allowed_values']}")
                        
                        # التحقق من النطاق
                        if 'min_value' in rules and value < rules['min_value']:
                            raise ValueError(f"Parameter '{param_name}' must be >= {rules['min_value']}")
                        
                        if 'max_value' in rules and value > rules['max_value']:
                            raise ValueError(f"Parameter '{param_name}' must be <= {rules['max_value']}")
                        
                        # التحقق من الطول
                        if 'min_length' in rules and len(value) < rules['min_length']:
                            raise ValueError(f"Parameter '{param_name}' must have length >= {rules['min_length']}")
                        
                        if 'max_length' in rules and len(value) > rules['max_length']:
                            raise ValueError(f"Parameter '{param_name}' must have length <= {rules['max_length']}")
                    
                    return func(*args, **kwargs)
                    
                except Exception as e:
                    self.logger.error(f"Input validation failed for {func_name}: {str(e)}")
                    raise
            
            return wrapper
        return decorator
    
    def circuit_breaker(self, failure_threshold=5, recovery_timeout=60):
        """نمط Circuit Breaker لمنع انتشار الأخطاء"""
        def decorator(func: Callable) -> Callable:
            func_name = f"{func.__module__}.{func.__name__}"
            state = {'failures': 0, 'last_failure': None, 'state': 'CLOSED'}
            
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                current_time = time.time()
                
                # فحص حالة Circuit Breaker
                if state['state'] == 'OPEN':
                    if current_time - state['last_failure'] > recovery_timeout:
                        state['state'] = 'HALF_OPEN'
                        self.logger.info(f"Circuit breaker for {func_name} is now HALF_OPEN")
                    else:
                        raise Exception(f"Circuit breaker is OPEN for {func_name}")
                
                try:
                    result = func(*args, **kwargs)
                    
                    # نجح التنفيذ - إعادة تعيين العداد
                    if state['state'] == 'HALF_OPEN':
                        state['state'] = 'CLOSED'
                        state['failures'] = 0
                        self.logger.info(f"Circuit breaker for {func_name} is now CLOSED")
                    
                    return result
                    
                except Exception as e:
                    state['failures'] += 1
                    state['last_failure'] = current_time
                    
                    if state['failures'] >= failure_threshold:
                        state['state'] = 'OPEN'
                        self.logger.error(f"Circuit breaker for {func_name} is now OPEN")
                    
                    raise
            
            return wrapper
        return decorator
    
    def health_check(self):
        """فحص صحة النظام"""
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'HEALTHY',
            'components': {}
        }
        
        # فحص قاعدة البيانات
        try:
            db_path = 'training_system.db'
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                conn.close()
                
                health_status['components']['database'] = {
                    'status': 'HEALTHY',
                    'tables': table_count,
                    'file_size': os.path.getsize(db_path)
                }
            else:
                health_status['components']['database'] = {
                    'status': 'ERROR',
                    'message': 'Database file not found'
                }
                health_status['overall_status'] = 'DEGRADED'
        except Exception as e:
            health_status['components']['database'] = {
                'status': 'ERROR',
                'message': str(e)
            }
            health_status['overall_status'] = 'DEGRADED'
        
        # فحص الملفات الأساسية
        essential_files = ['app.py', 'templates', 'static']
        missing_files = []
        
        for file_path in essential_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            health_status['components']['files'] = {
                'status': 'ERROR',
                'missing_files': missing_files
            }
            health_status['overall_status'] = 'DEGRADED'
        else:
            health_status['components']['files'] = {
                'status': 'HEALTHY'
            }
        
        # فحص الذاكرة والأداء
        try:
            import psutil
            memory_info = psutil.virtual_memory()
            disk_info = psutil.disk_usage('.')

            health_status['components']['system'] = {
                'status': 'HEALTHY',
                'memory_percent': memory_info.percent,
                'disk_percent': disk_info.percent,
                'cpu_count': psutil.cpu_count()
            }

            if memory_info.percent > 90 or disk_info.percent > 90:
                health_status['components']['system']['status'] = 'WARNING'
                health_status['overall_status'] = 'DEGRADED'

        except ImportError:
            health_status['components']['system'] = {
                'status': 'UNKNOWN',
                'message': 'psutil not available'
            }
        
        return health_status
    
    def send_alert(self, subject: str, details: Dict):
        """إرسال تنبيه (يمكن تخصيصه للبريد الإلكتروني أو أنظمة أخرى)"""
        alert = {
            'timestamp': datetime.now().isoformat(),
            'subject': subject,
            'details': details,
            'severity': 'HIGH' if 'error' in subject.lower() else 'MEDIUM'
        }
        
        # حفظ التنبيه في ملف
        alerts_dir = Path("alerts")
        alerts_dir.mkdir(exist_ok=True)
        
        alert_file = alerts_dir / f"alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(alert_file, 'w', encoding='utf-8') as f:
            json.dump(alert, f, indent=2, ensure_ascii=False)
        
        self.logger.critical(f"ALERT: {subject}")
    
    def start_system_monitoring(self):
        """بدء مراقبة النظام"""
        def monitor():
            while self.monitoring_active:
                try:
                    # فحص صحة النظام كل 5 دقائق
                    health = self.health_check()
                    
                    if health['overall_status'] != 'HEALTHY':
                        self.send_alert("System Health Degraded", health)
                    
                    # حفظ حالة النظام
                    self.system_health = health
                    
                    time.sleep(300)  # 5 دقائق
                    
                except Exception as e:
                    self.logger.error(f"Error in system monitoring: {str(e)}")
                    time.sleep(60)  # إعادة المحاولة بعد دقيقة
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def get_system_status(self):
        """الحصول على حالة النظام"""
        return {
            'health': self.system_health,
            'error_counts': self.error_count,
            'monitoring_active': self.monitoring_active
        }

class BackupManager:
    """مدير النسخ الاحتياطية"""
    
    def __init__(self):
        self.backup_dir = Path("backups")
        self.backup_dir.mkdir(exist_ok=True)
        self.max_backups = 10
    
    def create_backup(self, source_path: str, backup_name: Optional[str] = None):
        """إنشاء نسخة احتياطية"""
        if not backup_name:
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        source = Path(source_path)
        backup_path = self.backup_dir / f"{backup_name}_{source.name}"
        
        try:
            if source.is_file():
                shutil.copy2(source, backup_path)
            elif source.is_dir():
                shutil.copytree(source, backup_path)
            else:
                raise FileNotFoundError(f"Source path not found: {source}")
            
            # تنظيف النسخ القديمة
            self.cleanup_old_backups()
            
            return backup_path
            
        except Exception as e:
            raise Exception(f"Backup failed: {str(e)}")
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        backups = list(self.backup_dir.glob("backup_*"))
        backups.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        for old_backup in backups[self.max_backups:]:
            if old_backup.is_file():
                old_backup.unlink()
            elif old_backup.is_dir():
                shutil.rmtree(old_backup)

# إنشاء مثيل عام للنظام
reliability_system = ReliabilitySystem()

# ديكوريتر سهل الاستخدام
def reliable(reraise=True, default_return=None, max_retries=3):
    """ديكوريتر الموثوقية الشامل"""
    return reliability_system.error_handler(reraise, default_return, max_retries)

def validate(**validation_rules):
    """ديكوريتر التحقق من صحة المدخلات"""
    return reliability_system.validate_input(validation_rules)

def circuit_breaker(failure_threshold=5, recovery_timeout=60):
    """ديكوريتر Circuit Breaker"""
    return reliability_system.circuit_breaker(failure_threshold, recovery_timeout)

# دوال مساعدة
def get_system_health():
    """الحصول على صحة النظام"""
    return reliability_system.health_check()

def create_backup(source_path: str, backup_name: Optional[str] = None):
    """إنشاء نسخة احتياطية"""
    return reliability_system.backup_manager.create_backup(source_path, backup_name)

def get_system_status():
    """الحصول على حالة النظام"""
    return reliability_system.get_system_status()

# مثال على الاستخدام
if __name__ == "__main__":
    # اختبار النظام
    @reliable(max_retries=2)
    @validate(name={'type': str, 'required': True, 'min_length': 2})
    def test_function(name: str):
        """دالة اختبار"""
        if len(name) < 3:
            raise ValueError("Name too short")
        return f"Hello, {name}!"
    
    # اختبار الموثوقية
    print("🧪 اختبار نظام الموثوقية...")
    
    try:
        result = test_function("أحمد")
        print(f"النتيجة: {result}")
    except Exception as e:
        print(f"خطأ: {e}")
    
    # طباعة حالة النظام
    status = get_system_status()
    print("\n📊 حالة النظام:")
    print(json.dumps(status, indent=2, ensure_ascii=False))
