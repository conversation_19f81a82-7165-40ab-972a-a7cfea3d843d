<!-- نموذج تقييم مبسط للاختبار -->
<div class="container">
    <h5>نموذج التقييم - اختبار</h5>
    
    <div class="alert alert-info">
        <strong>معلومات الدورة:</strong> {{ course.title }}<br>
        <strong>عدد المشاركين:</strong> {{ participants|length }}<br>
        <strong>عدد المعايير:</strong> {{ criteria_data|length }}<br>
        <strong>الدرجة العظمى:</strong> {{ total_max_score }}
    </div>
    
    {% if participants %}
    <div class="card mb-3">
        <div class="card-header">المشاركين المحددين</div>
        <div class="card-body">
            {% for participant in participants %}
            <div class="mb-2">
                <i class="fas fa-user"></i>
                {{ participant.personal_data.full_name if participant.personal_data else 'غير محدد' }}
                (ID: {{ participant.id }})
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if criteria_data %}
    <div class="card">
        <div class="card-header">معايير التقييم</div>
        <div class="card-body">
            {% for criteria in criteria_data %}
            <div class="mb-3">
                <h6>{{ criteria.name }}</h6>
                {% if criteria.items %}
                <ul>
                    {% for item in criteria.items %}
                    <li>{{ item.name }} - {{ item.max_score }} درجة</li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-muted">لا توجد بنود</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <div class="alert alert-warning">
        لا توجد معايير تقييم لهذه الدورة
    </div>
    {% endif %}
    
    <div class="mt-3">
        <button class="btn btn-secondary" onclick="window.history.back()">العودة</button>
    </div>
</div>
