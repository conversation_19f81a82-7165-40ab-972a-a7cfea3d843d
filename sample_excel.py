import pandas as pd
import os

# إنشاء بيانات نموذجية للاختبار
data = {
    'name': [
        'علي أحمد محمد',
        'سارة محمد علي', 
        'خالد سعد أحمد',
        'نور فاطمة حسن',
        'محمد عبدالله حسن'  # هذا موجود بالفعل في القاعدة
    ],
    'national_id': [
        '9876543210',
        '9876543211', 
        '9876543212',
        '9876543213',
        '1234567892'  # هذا موجود بالفعل في القاعدة
    ],
    'military_number': [
        'M101',
        'M102',
        'M103', 
        'M104',
        'M003'  # هذا موجود بالفعل في القاعدة
    ]
}

# إنشاء DataFrame
df = pd.DataFrame(data)

# إنشاء مجلد static إذا لم يكن موجوداً
if not os.path.exists('static'):
    os.makedirs('static')

# حفظ الملف في مجلد static
try:
    file_path = 'static/participants_sample.xlsx'
    df.to_excel(file_path, index=False)
    print(f"تم إنشاء ملف {file_path} بنجاح!")
    print("الملف يحتوي على:")
    print("- 4 أشخاص جدد")
    print("- 1 شخص موجود بالفعل (للاختبار)")
    print("- أعمدة: name, national_id, military_number")
    print(f"يمكنك تحميل الملف من: http://localhost:5000/static/participants_sample.xlsx")
except Exception as e:
    print(f"خطأ في إنشاء الملف: {e}")

# عرض محتوى الملف
print("\nمحتوى الملف:")
print(df.to_string(index=False))
