{% extends "layout.html" %}

{% block styles %}
<style>
    .data-card {
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        border: none;
        margin-bottom: 20px;
    }

    .data-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    .data-card-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 15px 20px;
        font-weight: bold;
        border-radius: 15px 15px 0 0;
    }

    .data-table {
        width: 100%;
    }

    .data-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    .data-table th, .data-table td {
        padding: 12px 15px;
        text-align: right;
    }

    .data-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    .data-table tr:hover {
        background-color: #e9ecef;
    }

    .btn-add {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 10px 20px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
        color: white;
    }

    .btn-view {
        background-color: #28a745;
        color: white;
    }

    .btn-edit {
        background-color: #ffc107;
        color: white;
    }

    .btn-delete {
        background-color: #dc3545;
        color: white;
    }

    .btn-sm {
        padding: 5px 10px;
        font-size: 0.875rem;
        border-radius: 5px;
    }

    .sidebar {
        background-color: #343a40;
        color: white;
        min-height: calc(100vh - 56px);
        padding-top: 20px;
    }

    .sidebar-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 10px 15px;
        display: block;
        text-decoration: none;
        transition: all 0.3s;
        border-radius: 5px;
        margin: 5px 10px;
    }

    .sidebar-link:hover, .sidebar-link.active {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .sidebar-link i {
        margin-left: 10px;
        width: 20px;
        text-align: center;
    }

    .sidebar-dropdown-menu {
        display: none;
        padding-right: 20px;
    }

    .sidebar-dropdown-menu.show {
        display: block;
    }

    .dropdown-toggle::after {
        display: inline-block;
        margin-right: 5px;
        vertical-align: middle;
        content: "";
        border-top: 0.3em solid;
        border-left: 0.3em solid transparent;
        border-right: 0.3em solid transparent;
    }

    .search-box {
        margin-bottom: 20px;
    }

    .search-box .form-control {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
    }

    .search-box .btn {
        border-radius: 10px;
        padding: 12px;
    }

    .trainer-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        margin-bottom: 20px;
    }

    .trainer-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    .trainer-header {
        background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        color: white;
        padding: 15px;
        text-align: center;
    }

    .trainer-body {
        padding: 20px;
        background-color: white;
    }

    .trainer-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        margin: 0 auto 15px;
        display: block;
        border: 3px solid white;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .trainer-info {
        margin-bottom: 10px;
    }

    .trainer-info i {
        width: 20px;
        margin-left: 5px;
        color: #17a2b8;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="sidebar rounded">
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المستخدم" class="rounded-circle" width="100">
                <h5 class="mt-2">{{ current_user.username }}</h5>
                <p class="badge bg-primary">{{ current_user.role }}</p>
            </div>
            <hr>
            <a href="{{ url_for('dashboard') }}" class="sidebar-link">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
            <a href="{{ url_for('courses') }}" class="sidebar-link">
                <i class="fas fa-graduation-cap"></i> الدورات التدريبية
            </a>
            {% if current_user.role == 'admin' %}
            <a href="{{ url_for('users') }}" class="sidebar-link">
                <i class="fas fa-users"></i> إدارة المستخدمين
            </a>
            <a href="{{ url_for('personal_data_list') }}" class="sidebar-link">
                <i class="fas fa-id-card"></i> البيانات الشخصية
            </a>
            <a href="{{ url_for('trainers') }}" class="sidebar-link active">
                <i class="fas fa-chalkboard-teacher"></i> المدربين
            </a>
            <a href="{{ url_for('reference_tables') }}" class="sidebar-link">
                <i class="fas fa-table"></i> الجداول الترميزية
            </a>
            {% endif %}
            <a href="#" class="sidebar-link">
                <i class="fas fa-calendar-alt"></i> الجدول الزمني
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-certificate"></i> الشهادات
            </a>
            <a href="{{ url_for('reports') }}" class="sidebar-link">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
            <a href="#" class="sidebar-link">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </div>
    </div>

    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>المدربين</h2>
            <a href="{{ url_for('users') }}" class="btn btn-add">
                <i class="fas fa-plus-circle me-1"></i> إضافة مدرب جديد
            </a>
        </div>

        <div class="search-box">
            <div class="input-group">
                <input type="text" id="searchInput" class="form-control" placeholder="بحث عن مدرب...">
                <button class="btn btn-primary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>

        <div class="row">
            {% if trainers %}
                {% for trainer in trainers %}
                <div class="col-md-4 trainer-item">
                    <div class="trainer-card">
                        <div class="trainer-header">
                            <h5>{{ trainer.username }}</h5>
                        </div>
                        <div class="trainer-body">
                            <img src="{{ url_for('static', filename='img/user-avatar.png') }}" alt="صورة المدرب" class="trainer-avatar">
                            <div class="trainer-info">
                                <p><i class="fas fa-envelope"></i> {{ trainer.email }}</p>
                                <p><i class="fas fa-calendar-alt"></i> تاريخ التسجيل: {{ trainer.date_registered.strftime('%Y-%m-%d') }}</p>
                            </div>
                            <div class="d-flex justify-content-between mt-3">
                                <a href="{{ url_for('user', user_id=trainer.id) }}" class="btn btn-sm btn-view">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                <a href="{{ url_for('edit_user', user_id=trainer.id) }}" class="btn btn-sm btn-edit">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{{ url_for('delete_user', user_id=trainer.id) }}" class="btn btn-sm btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذا المدرب؟');">
                                    <i class="fas fa-trash-alt"></i> حذف
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا يوجد مدربين مسجلين حتى الآن.
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تفعيل البحث
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const trainerItems = document.querySelectorAll('.trainer-item');

        searchInput.addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();

            trainerItems.forEach(item => {
                const trainerName = item.querySelector('.trainer-header h5').textContent.toLowerCase();
                const trainerEmail = item.querySelector('.trainer-info p:first-child').textContent.toLowerCase();

                if (trainerName.includes(searchValue) || trainerEmail.includes(searchValue)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
</script>
{% endblock %}
