#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, CourseParticipant, PersonData

with app.app_context():
    course_id = 1
    print(f"🔍 تنفيذ الاستعلام للدورة رقم: {course_id}")
    
    # الكود المطلوب اختباره
    existing_participant_ids = [p.personal_data_id for p in CourseParticipant.query.filter_by(course_id=course_id).all()]
    print(f"المضافين مسبقاً: {existing_participant_ids}")
    
    available_people = PersonData.query.filter(~PersonData.id.in_(existing_participant_ids)).all()
    print(f"المتاحين للإضافة: {len(available_people)} شخص")
    
    choices = [(0, 'اختر مشارك')] + [(p.id, p.full_name) for p in available_people]
    print(f"خيارات القائمة: {len(choices)} خيار")
    
    print("\nأول 5 خيارات:")
    for i, choice in enumerate(choices[:5], 1):
        print(f"  {i}. ({choice[0]}, '{choice[1]}')")
    
    print("✅ تم تنفيذ الاستعلام بنجاح!")
