/* تحسينات إضافية للتقارير التفاعلية - بدون حركات */

/* العناصر بدون حركات */
.reports-header,
.date-filter-section,
.stats-grid .stat-card,
.charts-section,
.table-card,
.export-section {
    /* بدون حركات */
}

/* تحسينات إضافية للبطاقات */
.stat-card {
    position: relative;
    overflow: hidden;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s;
    opacity: 0;
}

.stat-card:hover::after {
    /* بدون حركة */
}

/* تحسينات للجداول */
.data-table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.data-table th {
    position: relative;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.data-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

.data-table tbody tr {
    transition: all 0.3s ease;
}

.data-table tbody tr:hover {
    background: linear-gradient(135deg, #f8f6f2, #ede4d3);
    transform: scale(1.01);
    box-shadow: 0 5px 15px rgba(141, 110, 99, 0.1);
}

/* تحسينات للأزرار */
.btn-export {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-export::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
    z-index: -1;
}

.btn-export:hover::before {
    left: 100%;
}

/* تحسينات للرسوم البيانية */
.chart-card {
    position: relative;
    background: linear-gradient(135deg, #ffffff, #f8f9ff);
}

.chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    /* بدون حركة */
}

/* تحسينات للتحميل */
.loading-spinner {
    position: relative;
}

.loading-spinner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    margin: -40px 0 0 -40px;
    border: 3px solid transparent;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    /* بدون حركة */
}

/* تأثيرات النص */
.reports-title {
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}



/* تحسينات للفلاتر */
.filter-card {
    position: relative;
    overflow: hidden;
}

.filter-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea, #764ba2);
    z-index: -1;
    border-radius: 17px;
}



/* تحسينات للأيقونات */
.stat-icon {
    position: relative;
    display: inline-block;
}

.stat-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    border: 2px solid transparent;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    /* بدون حركة */
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
    .reports-header {
        padding: 20px;
    }
    
    .reports-title {
        font-size: 2rem;
    }
    
    .stat-card {
        margin-bottom: 20px;
    }
    
    .chart-card {
        margin-bottom: 20px;
    }
}

/* تحسينات للطباعة */
@media print {
    .reports-header::before,
    .stat-card::after,
    .chart-card::before,
    .filter-card::before {
        display: none;
    }
    
    .stat-card,
    .chart-card,
    .table-card {
        break-inside: avoid;
        animation: none;
    }
}
