<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقييم المشاركين - {{ course.title }}</title>
    <link href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .page-break { page-break-before: always; }
            body { font-size: 12px; }
            .table { font-size: 11px; }
        }
        
        .evaluation-header {
            text-align: center;
            border: 2px solid #000;
            padding: 20px;
            margin-bottom: 30px;
            background-color: #f8f9fa;
        }
        
        .evaluation-form {
            border: 1px solid #000;
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        
        .criteria-table {
            border-collapse: collapse;
            width: 100%;
        }
        
        .criteria-table th,
        .criteria-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        
        .criteria-table th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        
        .participant-info {
            background-color: #f8f9fa;
            border: 1px solid #000;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .signature-section {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            border: 1px solid #000;
            padding: 20px;
            width: 200px;
            text-align: center;
        }
        
        .logo-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .grade-box {
            border: 2px solid #000;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <!-- أزرار التحكم -->
    <div class="no-print mb-3">
        <div class="d-flex gap-2">
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
            <button class="btn btn-secondary" onclick="window.close()">
                <i class="fas fa-times"></i> إغلاق
            </button>
        </div>
    </div>

    {% for evaluation in evaluations %}
    <div class="evaluation-form {% if not loop.first %}page-break{% endif %}">
        <!-- رأس النموذج -->
        <div class="evaluation-header">
            <div class="logo-section">
                <div>
                    <img src="/static/images/logo.png" alt="الشعار" style="height: 60px;" onerror="this.style.display='none'">
                </div>
                <div>
                    <h3>الجمهورية اليمنية</h3>
                    <h4>وزارة الدفاع الوطني</h4>
                    <h5>نموذج تقييم المتدربين</h5>
                </div>
                <div>
                    <img src="/static/images/ministry_logo.png" alt="شعار الوزارة" style="height: 60px;" onerror="this.style.display='none'">
                </div>
            </div>
        </div>

        <!-- معلومات الدورة -->
        <div class="row mb-3">
            <div class="col-md-6">
                <strong>اسم الدورة:</strong> {{ course.title }}
            </div>
            <div class="col-md-6">
                <strong>رقم الدورة:</strong> {{ course.course_number }}
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-md-6">
                <strong>تاريخ البداية:</strong> {{ course.start_date.strftime('%Y-%m-%d') if course.start_date else '-' }}
            </div>
            <div class="col-md-6">
                <strong>تاريخ النهاية:</strong> {{ course.end_date.strftime('%Y-%m-%d') if course.end_date else '-' }}
            </div>
        </div>

        <!-- معلومات المشارك -->
        <div class="participant-info">
            <h5 class="mb-3">بيانات المتدرب</h5>
            <div class="row">
                <div class="col-md-4">
                    <strong>الاسم الكامل:</strong><br>
                    {{ evaluation.full_name }}
                </div>
                <div class="col-md-4">
                    <strong>الرقم الوطني:</strong><br>
                    {{ evaluation.national_number or '-' }}
                </div>
                <div class="col-md-4">
                    <strong>الرقم العسكري:</strong><br>
                    {{ evaluation.military_number or '-' }}
                </div>
            </div>
        </div>

        <!-- جدول التقييم -->
        <table class="criteria-table">
            <thead>
                <tr>
                    <th width="40%">معايير التقييم</th>
                    <th width="15%">الدرجة العظمى</th>
                    <th width="15%">الدرجة المحققة</th>
                    <th width="15%">النسبة المئوية</th>
                    <th width="15%">ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="text-align: right;">الحضور والانضباط</td>
                    <td>20</td>
                    <td>{{ evaluation.attendance_score or 0 }}</td>
                    <td>{{ ((evaluation.attendance_score or 0) / 20 * 100)|round(1) }}%</td>
                    <td>{{ evaluation.attendance_notes or '-' }}</td>
                </tr>
                <tr>
                    <td style="text-align: right;">المشاركة والتفاعل</td>
                    <td>20</td>
                    <td>{{ evaluation.participation_score or 0 }}</td>
                    <td>{{ ((evaluation.participation_score or 0) / 20 * 100)|round(1) }}%</td>
                    <td>{{ evaluation.participation_notes or '-' }}</td>
                </tr>
                <tr>
                    <td style="text-align: right;">الاختبار النظري</td>
                    <td>30</td>
                    <td>{{ evaluation.theory_score or 0 }}</td>
                    <td>{{ ((evaluation.theory_score or 0) / 30 * 100)|round(1) }}%</td>
                    <td>{{ evaluation.theory_notes or '-' }}</td>
                </tr>
                <tr>
                    <td style="text-align: right;">الاختبار العملي</td>
                    <td>30</td>
                    <td>{{ evaluation.practical_score or 0 }}</td>
                    <td>{{ ((evaluation.practical_score or 0) / 30 * 100)|round(1) }}%</td>
                    <td>{{ evaluation.practical_notes or '-' }}</td>
                </tr>
                <tr>
                    <td style="text-align: right;">المهارات الشخصية</td>
                    <td>15</td>
                    <td>{{ evaluation.personal_score or 0 }}</td>
                    <td>{{ ((evaluation.personal_score or 0) / 15 * 100)|round(1) }}%</td>
                    <td>{{ evaluation.personal_notes or '-' }}</td>
                </tr>
                <tr style="background-color: #e9ecef; font-weight: bold;">
                    <td style="text-align: right;">الإجمالي</td>
                    <td>115</td>
                    <td>{{ evaluation.total_score }}</td>
                    <td>{{ evaluation.percentage|round(1) }}%</td>
                    <td>-</td>
                </tr>
            </tbody>
        </table>

        <!-- التقدير النهائي -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="grade-box">
                    <div>التقدير النهائي</div>
                    <div style="font-size: 20px; color: 
                        {% if evaluation.percentage >= 90 %}green
                        {% elif evaluation.percentage >= 80 %}blue
                        {% elif evaluation.percentage >= 70 %}orange
                        {% elif evaluation.percentage >= 60 %}brown
                        {% else %}red{% endif %};">
                        {{ evaluation.grade }}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="grade-box">
                    <div>النسبة المئوية</div>
                    <div style="font-size: 20px;">{{ evaluation.percentage|round(1) }}%</div>
                </div>
            </div>
        </div>

        <!-- ملاحظات عامة -->
        {% if evaluation.notes %}
        <div class="mt-4">
            <strong>ملاحظات عامة:</strong>
            <div style="border: 1px solid #000; padding: 10px; min-height: 60px;">
                {{ evaluation.notes }}
            </div>
        </div>
        {% endif %}

        <!-- التواقيع -->
        <div class="signature-section">
            <div class="signature-box">
                <div style="margin-bottom: 40px;">توقيع المدرب</div>
                <div>التاريخ: ___________</div>
            </div>
            <div class="signature-box">
                <div style="margin-bottom: 40px;">توقيع رئيس القسم</div>
                <div>التاريخ: ___________</div>
            </div>
            <div class="signature-box">
                <div style="margin-bottom: 40px;">توقيع المدير</div>
                <div>التاريخ: ___________</div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="mt-4" style="font-size: 10px; text-align: center; color: #666;">
            <div>تاريخ التقييم: {% if evaluation.evaluation_date %}{{ evaluation.evaluation_date.strftime('%Y-%m-%d %H:%M') }}{% else %}غير محدد{% endif %}</div>
            <div>المقيم: {{ evaluation.evaluator_name or 'غير محدد' }}</div>
        </div>
    </div>
    {% endfor %}

    <script>
        // طباعة تلقائية عند فتح الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
