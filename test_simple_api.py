#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار التطبيق البسيط بدون CSRF
"""

import requests
import json
import time

BASE_URL = "http://localhost:5002"
COURSE_ID = 1

def test_server_status():
    """فحص حالة الخادم"""
    print("🔍 فحص حالة الخادم البسيط...")
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print("✅ الخادم البسيط يعمل بشكل صحيح")
            print(f"📋 الرسالة: {result.get('message')}")
            return True
        else:
            print(f"⚠️ الخادم يستجيب برمز: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ لا يمكن الوصول للخادم: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n📊 اختبار قاعدة البيانات...")
    
    try:
        response = requests.get(f"{BASE_URL}/test_db", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ قاعدة البيانات متصلة")
                print(f"👥 عدد الأشخاص: {result.get('people_count')}")
                print(f"📚 عدد الدورات: {result.get('courses_count')}")
                print(f"🎯 عدد المشاركات: {result.get('participants_count')}")
                print(f"📋 الدورة رقم 1: {result.get('course_1_title')}")
                print(f"👥 مشاركي الدورة 1: {result.get('course_1_participants')}")
                return True
            else:
                print(f"❌ خطأ في قاعدة البيانات: {result.get('error')}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_add_existing_person():
    """اختبار إضافة شخص موجود"""
    print("\n🎯 اختبار إضافة شخص موجود للدورة...")
    
    # قائمة الأشخاص للاختبار
    test_people = [1, 2, 3]
    
    for person_id in test_people:
        print(f"\n🔄 اختبار إضافة الشخص ID: {person_id}")
        
        try:
            response = requests.post(
                f"{BASE_URL}/course/{COURSE_ID}/add_participant_api",
                json={"person_id": person_id},
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            print(f"📡 رمز الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ نجح: {result.get('message')}")
                    person_info = result.get('person', {})
                    print(f"   الاسم: {person_info.get('full_name')}")
                    print(f"   الرقم الوطني: {person_info.get('national_number')}")
                else:
                    print(f"⚠️ فشل: {result.get('message')}")
            else:
                print(f"❌ خطأ HTTP: {response.status_code}")
                print(f"📄 الاستجابة: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ خطأ في الطلب: {e}")
        
        time.sleep(0.5)

def test_add_new_person():
    """اختبار إضافة شخص جديد"""
    print("\n👤 اختبار إضافة شخص جديد...")
    
    new_person = {
        "full_name": "اختبار API البسيط الجديد",
        "national_number": "1111111111",
        "military_number": "SIMPLE001",
        "age": 28,
        "governorate": "صنعاء",
        "directorate": "شعوب",
        "job": "مختبر API بسيط",
        "qualification": "بكالوريوس حاسوب",
        "phone": "777888999"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/course/{COURSE_ID}/add_new_person_api",
            json=new_person,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📡 رمز الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ تم إنشاء الشخص: {result.get('message')}")
                person_info = result.get('person', {})
                print(f"   ID: {person_info.get('id')}")
                print(f"   الاسم: {person_info.get('full_name')}")
                print(f"   الرقم الوطني: {person_info.get('national_number')}")
            else:
                print(f"⚠️ فشل في الإنشاء: {result.get('message')}")
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            print(f"📄 الاستجابة: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ خطأ في الطلب: {e}")

def test_create_test_person():
    """اختبار إنشاء شخص تجريبي"""
    print("\n🧪 اختبار إنشاء شخص تجريبي...")
    
    try:
        response = requests.get(f"{BASE_URL}/test_add_person", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ تم إنشاء شخص تجريبي: {result.get('message')}")
                print(f"   ID: {result.get('person_id')}")
            else:
                print(f"⚠️ فشل: {result.get('error')}")
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في الطلب: {e}")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل للتطبيق البسيط بدون CSRF")
    print("=" * 60)
    
    # 1. فحص حالة الخادم
    if not test_server_status():
        print("❌ الخادم لا يعمل! تأكد من تشغيل python simple_api_test.py")
        return
    
    # 2. اختبار قاعدة البيانات
    if not test_database():
        print("❌ مشكلة في قاعدة البيانات!")
        return
    
    # 3. اختبار إنشاء شخص تجريبي
    test_create_test_person()
    
    # 4. اختبار إضافة الأشخاص الموجودين
    test_add_existing_person()
    
    # 5. اختبار إضافة شخص جديد
    test_add_new_person()
    
    # 6. اختبار قاعدة البيانات مرة أخرى
    print("\n📊 فحص نهائي لقاعدة البيانات...")
    test_database()
    
    print("\n" + "=" * 60)
    print("🎉 انتهى الاختبار الشامل!")
    print("✅ التطبيق البسيط يعمل بدون CSRF!")
    print("🌐 يمكنك الآن اختبار الواجهة من المتصفح:")
    print(f"   {BASE_URL}/")
    print(f"   {BASE_URL}/test_db")

if __name__ == "__main__":
    main()
