#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import zipfile
import tempfile
import sqlite3
import os

def check_zip_backup():
    zip_path = 'backups/نسخة مع البيانات.zip'
    
    if not os.path.exists(zip_path):
        print("❌ الملف المضغوط غير موجود")
        return
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as z:
            print(f"📦 محتويات الملف المضغوط:")
            for file in z.namelist():
                print(f"  - {file}")
            
            # البحث عن ملفات قواعد البيانات
            db_files = [f for f in z.namelist() if f.endswith('.db')]
            
            if not db_files:
                print("❌ لا توجد ملفات قواعد بيانات")
                return
            
            # استخراج مؤقت
            with tempfile.TemporaryDirectory() as temp_dir:
                z.extractall(temp_dir)
                
                for db_file in db_files:
                    db_path = os.path.join(temp_dir, db_file)
                    print(f"\n🔍 فحص: {db_file}")
                    
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # فحص الجداول
                    tables = ['user', 'course', 'personal_data', 'course_participant']
                    
                    for table in tables:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            print(f"  📋 {table}: {count} سجل")
                            
                            # عرض عينة
                            if table == 'personal_data' and count > 0:
                                cursor.execute("SELECT full_name FROM personal_data LIMIT 5")
                                persons = cursor.fetchall()
                                print("    عينة من الأشخاص:")
                                for person in persons:
                                    print(f"      - {person[0]}")
                                    
                        except Exception as e:
                            print(f"  ❌ خطأ في {table}: {e}")
                    
                    conn.close()
                    
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == '__main__':
    check_zip_backup()
