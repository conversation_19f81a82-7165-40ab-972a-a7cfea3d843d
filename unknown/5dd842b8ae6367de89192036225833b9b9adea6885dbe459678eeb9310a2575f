from flask import Flask, render_template, send_from_directory
import os

app = Flask(__name__, template_folder=os.path.abspath('templates'))

@app.route('/')
def home():
    return "مرحباً بك في تطبيق الترحيب النهائي 4!"

@app.route('/welcome-final')
def welcome_final():
    # طباعة مسار القوالب
    print("مسار القوالب:", app.template_folder)
    print("هل الملف موجود؟", os.path.exists(os.path.join(app.template_folder, 'welcome_final.html')))
    
    return render_template('welcome_final.html')

@app.route('/welcome-file')
def welcome_file():
    return send_from_directory('templates', 'welcome_final.html')

if __name__ == '__main__':
    app.run(debug=True, port=5008)
