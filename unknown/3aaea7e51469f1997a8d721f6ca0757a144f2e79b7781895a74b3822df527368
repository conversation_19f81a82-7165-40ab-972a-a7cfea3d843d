#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, CourseParticipant, Course, PersonData

def fix_course_3455667():
    with app.app_context():
        print("🔧 إصلاح الدورة رقم 3455667...")
        print("=" * 60)
        
        # البحث عن الدورة برقم 3455667
        course = Course.query.filter_by(course_number='3455667').first()
        
        if not course:
            print("❌ لم يتم العثور على الدورة رقم 3455667")
            return
        
        print(f"✅ تم العثور على الدورة:")
        print(f"   ID: {course.id}")
        print(f"   رقم الدورة: {course.course_number}")
        print(f"   اسم الدورة: {course.title}")
        print(f"   إجمالي المشاركين المسجل: {course.total_participants}")
        
        # حساب العدد الفعلي للمشاركين
        actual_participants = CourseParticipant.query.filter_by(course_id=course.id).all()
        actual_count = len(actual_participants)
        
        print(f"   العدد الفعلي للمشاركين: {actual_count}")
        
        # عرض قائمة المشاركين
        print(f"\nقائمة المشاركين:")
        for i, participant in enumerate(actual_participants, 1):
            person = participant.personal_data
            name = person.full_name if person else f"ID: {participant.personal_data_id}"
            print(f"   {i}. {name} (حالة: {participant.status})")
        
        # حساب الإحصائيات
        active_count = len([p for p in actual_participants if p.status == 'active'])
        completed_count = len([p for p in actual_participants if p.status == 'completed'])
        dropped_count = len([p for p in actual_participants if p.status == 'dropped'])
        
        print(f"\nالإحصائيات:")
        print(f"   النشطين: {active_count}")
        print(f"   الخريجين: {completed_count}")
        print(f"   المنسحبين: {dropped_count}")
        
        # تحديث الأرقام في الدورة
        course.total_participants = actual_count
        course.total_graduates = completed_count
        course.total_dropouts = dropped_count
        
        # حفظ التغييرات
        db.session.commit()
        
        print(f"\n✅ تم تحديث الدورة بنجاح!")
        print(f"   إجمالي المشاركين الجديد: {course.total_participants}")
        print(f"   إجمالي الخريجين: {course.total_graduates}")
        print(f"   إجمالي المنسحبين: {course.total_dropouts}")

if __name__ == '__main__':
    fix_course_3455667()
