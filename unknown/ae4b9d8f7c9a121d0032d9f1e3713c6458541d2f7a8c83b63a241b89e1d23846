@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🎓 إعداد نظام إدارة التدريب
echo    Training System Setup
echo ========================================
echo.

echo 🔧 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    echo يرجى تحميل وتثبيت Python من: https://python.org
    pause
    exit /b 1
)
echo ✅ Python متوفر

echo.
echo 📦 إنشاء البيئة الافتراضية...
if not exist ".venv" (
    python -m venv .venv
    echo ✅ تم إنشاء البيئة الافتراضية
) else (
    echo ✅ البيئة الافتراضية موجودة
)

echo.
echo 🔧 تفعيل البيئة الافتراضية...
call .venv\Scripts\activate.bat

echo.
echo 📦 تحديث pip...
python -m pip install --upgrade pip

echo.
echo 📦 تثبيت المكتبات المطلوبة...
pip install Flask==2.3.3 Flask-SQLAlchemy==3.0.5 Flask-Login==0.6.3 Flask-WTF==1.1.1 WTForms==3.0.1 pandas==2.1.1 openpyxl==3.1.2 xlsxwriter==3.1.9 SQLAlchemy==2.0.21 arabic-reshaper==3.0.0 python-bidi==0.4.2 tqdm==4.66.1 email-validator==2.0.0

echo.
echo 📁 إنشاء المجلدات المطلوبة...
if not exist "static" mkdir static
if not exist "static\css" mkdir static\css
if not exist "static\js" mkdir static\js
if not exist "static\libs" mkdir static\libs
if not exist "uploads" mkdir uploads
if not exist "exports" mkdir exports
echo ✅ تم إنشاء جميع المجلدات

echo.
echo 💾 فحص قاعدة البيانات...
if not exist "training_system.db" (
    echo 📊 إنشاء قاعدة البيانات...
    python -c "from app import app, db; app.app_context().push(); db.create_all(); print('✅ تم إنشاء قاعدة البيانات')" 2>nul
    if errorlevel 1 (
        echo ⚠️ سيتم إنشاء قاعدة البيانات عند أول تشغيل
    )
) else (
    echo ✅ قاعدة البيانات موجودة
)

echo.
echo ========================================
echo       ✅ تم إعداد النظام بنجاح!
echo ========================================
echo.
echo الآن يمكنك تشغيل النظام باستخدام:
echo.
echo   RUN.bat
echo.
echo أو:
echo.
echo   .\start_system.ps1 -Quick
echo.
pause
