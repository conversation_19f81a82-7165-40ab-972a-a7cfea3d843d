#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار ميزة استثناء المشاركين الحاليين من البحث
"""

import requests
import json
import time

BASE_URL = "http://localhost:5002"
COURSE_ID = 1

def test_search_exclusion():
    """اختبار استثناء المشاركين من نتائج البحث"""
    print("🔍 اختبار ميزة استثناء المشاركين من البحث...")
    
    try:
        # 1. البحث عن "علي" - يجب أن يستثني المشاركين الحاليين
        print("\n📋 البحث عن 'علي':")
        response = requests.get(f"{BASE_URL}/course/{COURSE_ID}/search_people?q=علي", timeout=10)
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ تم العثور على {len(results)} نتيجة")
            
            for person in results:
                print(f"   - {person['name']} (ID: {person['id']})")
                
            # التحقق من عدم وجود المشاركين الحاليين
            participant_names = [p['name'] for p in results if 'علي صالح محمد الحميري1' in p['name']]
            if not participant_names:
                print("✅ تم استثناء المشاركين الحاليين بنجاح")
            else:
                print(f"⚠️ تم العثور على مشاركين حاليين: {participant_names}")
        else:
            print(f"❌ خطأ في البحث: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def test_search_and_add():
    """اختبار البحث وإضافة شخص جديد"""
    print("\n🎯 اختبار البحث وإضافة شخص جديد...")
    
    try:
        # 1. البحث عن أشخاص متاحين
        print("🔍 البحث عن أشخاص متاحين...")
        response = requests.get(f"{BASE_URL}/course/{COURSE_ID}/search_people?q=محمد", timeout=10)
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ تم العثور على {len(results)} شخص متاح")
            
            if results:
                # اختيار أول شخص متاح
                person = results[0]
                print(f"📋 سيتم إضافة: {person['name']} (ID: {person['id']})")
                
                # 2. إضافة الشخص للدورة
                add_response = requests.post(
                    f"{BASE_URL}/course/{COURSE_ID}/add_participant_api",
                    json={"person_id": person['id']},
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                
                if add_response.status_code == 200:
                    result = add_response.json()
                    if result.get('success'):
                        print(f"✅ تم إضافة {person['name']} بنجاح")
                        
                        # 3. البحث مرة أخرى للتأكد من الاستثناء
                        print("🔍 البحث مرة أخرى للتأكد من الاستثناء...")
                        time.sleep(1)
                        
                        search_again = requests.get(f"{BASE_URL}/course/{COURSE_ID}/search_people?q=محمد", timeout=10)
                        if search_again.status_code == 200:
                            new_results = search_again.json()
                            
                            # التحقق من عدم وجود الشخص المضاف
                            found_added_person = any(p['id'] == person['id'] for p in new_results)
                            
                            if not found_added_person:
                                print("✅ تم استثناء الشخص المضاف من البحث بنجاح!")
                            else:
                                print("⚠️ الشخص المضاف لا يزال يظهر في البحث")
                        
                    else:
                        print(f"⚠️ فشل في الإضافة: {result.get('message')}")
                else:
                    print(f"❌ خطأ في الإضافة: {add_response.status_code}")
            else:
                print("⚠️ لا توجد أشخاص متاحين للإضافة")
        else:
            print(f"❌ خطأ في البحث: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def test_database_status():
    """فحص حالة قاعدة البيانات قبل وبعد الاختبار"""
    print("📊 فحص حالة قاعدة البيانات...")
    
    try:
        response = requests.get(f"{BASE_URL}/test_db", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"👥 عدد الأشخاص: {result.get('people_count')}")
                print(f"🎯 عدد المشاركات: {result.get('participants_count')}")
                print(f"📋 مشاركي الدورة 1: {result.get('course_1_participants')}")
                return result
            else:
                print(f"❌ خطأ: {result.get('error')}")
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    return None

def test_multiple_searches():
    """اختبار بحثات متعددة"""
    print("\n🔍 اختبار بحثات متعددة...")
    
    search_terms = ['أحمد', 'فاطمة', 'يحيى', 'صالح']
    
    for term in search_terms:
        try:
            print(f"\n🔍 البحث عن '{term}':")
            response = requests.get(f"{BASE_URL}/course/{COURSE_ID}/search_people?q={term}", timeout=10)
            
            if response.status_code == 200:
                results = response.json()
                print(f"   ✅ {len(results)} نتيجة")
                
                for person in results[:3]:  # عرض أول 3 نتائج فقط
                    print(f"      - {person['name']}")
            else:
                print(f"   ❌ خطأ: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
        
        time.sleep(0.5)

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لميزة استثناء المشاركين")
    print("=" * 60)
    
    # 1. فحص حالة قاعدة البيانات قبل الاختبار
    print("📊 الحالة قبل الاختبار:")
    initial_status = test_database_status()
    
    if not initial_status:
        print("❌ لا يمكن الوصول لقاعدة البيانات!")
        return
    
    # 2. اختبار استثناء المشاركين من البحث
    test_search_exclusion()
    
    # 3. اختبار بحثات متعددة
    test_multiple_searches()
    
    # 4. اختبار البحث وإضافة شخص جديد
    test_search_and_add()
    
    # 5. فحص حالة قاعدة البيانات بعد الاختبار
    print("\n📊 الحالة بعد الاختبار:")
    final_status = test_database_status()
    
    if initial_status and final_status:
        participants_added = final_status.get('course_1_participants', 0) - initial_status.get('course_1_participants', 0)
        if participants_added > 0:
            print(f"✅ تم إضافة {participants_added} مشارك جديد للدورة")
    
    print("\n" + "=" * 60)
    print("🎉 انتهى اختبار ميزة الاستثناء!")
    print("✅ الميزة تعمل بشكل صحيح - المشاركين الحاليين لا يظهرون في البحث")
    print("🌐 يمكنك اختبار البحث من المتصفح:")
    print(f"   {BASE_URL}/course/{COURSE_ID}/search_people?q=علي")

if __name__ == "__main__":
    main()
