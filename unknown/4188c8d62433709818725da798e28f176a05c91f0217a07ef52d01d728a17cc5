#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🔧 إنشاء مدير مباشرة...")

try:
    # استيراد التطبيق
    from app import app, db, User
    from werkzeug.security import generate_password_hash, check_password_hash
    
    with app.app_context():
        print(f"📁 مسار قاعدة البيانات: {app.config['SQLALCHEMY_DATABASE_URI']}")
        
        # إنشاء الجداول إذا لم تكن موجودة
        db.create_all()
        print("✅ تم التأكد من وجود الجداول")
        
        # حذف جميع المديرين الحاليين
        existing_admins = User.query.filter_by(role='admin').all()
        for admin in existing_admins:
            print(f"🗑️ حذف المدير: {admin.email}")
            db.session.delete(admin)
        
        # حذف المستخدم <EMAIL> إذا كان موجوداً
        existing_user = User.query.filter_by(email='<EMAIL>').first()
        if existing_user:
            print(f"🗑️ حذف المستخدم الموجود: {existing_user.email}")
            db.session.delete(existing_user)
        
        db.session.commit()
        
        # إنشاء مدير جديد
        password_hash = generate_password_hash('admin123')
        print(f"🔐 تم تشفير كلمة المرور: {password_hash[:50]}...")
        
        admin = User(
            username='admin',
            email='<EMAIL>',
            password=password_hash,
            role='admin'
        )
        
        db.session.add(admin)
        db.session.commit()
        
        print("✅ تم إنشاء المدير بنجاح!")
        
        # التحقق من إنشاء المدير
        check_admin = User.query.filter_by(email='<EMAIL>').first()
        if check_admin:
            print("✅ تم التحقق من وجود المدير:")
            print(f"  ID: {check_admin.id}")
            print(f"  اسم المستخدم: {check_admin.username}")
            print(f"  البريد: {check_admin.email}")
            print(f"  الدور: {check_admin.role}")
            print(f"  كلمة المرور المشفرة: {check_admin.password[:50]}...")
            
            # اختبار كلمة المرور
            if check_password_hash(check_admin.password, 'admin123'):
                print("✅ كلمة المرور صحيحة")
            else:
                print("❌ كلمة المرور غير صحيحة")
                
            # اختبار تسجيل الدخول
            print("\n🧪 اختبار تسجيل الدخول...")
            test_user = User.query.filter_by(email='<EMAIL>').first()
            if test_user and check_password_hash(test_user.password, 'admin123'):
                print("✅ اختبار تسجيل الدخول نجح!")
            else:
                print("❌ اختبار تسجيل الدخول فشل!")
        else:
            print("❌ فشل في إنشاء المدير")
        
        # عرض جميع المستخدمين
        all_users = User.query.all()
        print(f"\n👥 إجمالي المستخدمين: {len(all_users)}")
        for user in all_users:
            print(f"  - {user.username} ({user.email}) - {user.role}")
            
except Exception as e:
    print(f"❌ خطأ: {str(e)}")
    import traceback
    traceback.print_exc()

print("\n🌐 يمكنك الآن تسجيل الدخول على:")
print("http://127.0.0.1:5000/login")
print("📧 البريد: <EMAIL>")
print("🔑 كلمة المرور: admin123")
