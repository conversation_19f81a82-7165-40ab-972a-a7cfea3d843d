#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح جميع التواريخ المعطوبة في النظام
"""

import sqlite3
import sys
import os
from datetime import datetime

def fix_all_dates():
    """
    إصلاح جميع التواريخ المعطوبة في النظام
    """
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        print("🔍 فحص وإصلاح جميع التواريخ المعطوبة في النظام...")
        
        # قائمة الجداول والحقول التي تحتوي على تواريخ
        date_fields = [
            ('course', ['start_date', 'end_date', 'created_at', 'entry_date', 'exit_date']),
            ('course_participant', ['entry_date', 'exit_date', 'payment_date']),
            ('personal_data', ['birth_date', 'issue_date', 'qualification_date', 'assignment_date', 'rank_date', 'injury_date']),
            ('user', ['created_at']),
            ('batch', ['start_date', 'end_date']),
            ('batch_participant', ['registration_date']),
            ('name_correction', ['created_at']),
        ]
        
        total_fixed = 0
        
        for table_name, fields in date_fields:
            print(f"\n📋 فحص جدول: {table_name}")
            
            # التحقق من وجود الجدول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                print(f"   ⚠️ الجدول {table_name} غير موجود")
                continue
            
            # فحص كل حقل تاريخ
            for field in fields:
                try:
                    # التحقق من وجود الحقل
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    if field not in columns:
                        print(f"   ⚠️ الحقل {field} غير موجود في {table_name}")
                        continue
                    
                    # البحث عن القيم المعطوبة
                    cursor.execute(f"SELECT id FROM {table_name} WHERE {field} = '0' OR {field} = 0")
                    corrupted_rows = cursor.fetchall()
                    
                    if corrupted_rows:
                        print(f"   ❌ وجد {len(corrupted_rows)} صف معطوب في {table_name}.{field}")
                        
                        # إصلاح القيم المعطوبة
                        default_date = datetime(2024, 1, 1).strftime('%Y-%m-%d %H:%M:%S')
                        
                        for row in corrupted_rows:
                            row_id = row[0]
                            cursor.execute(f"UPDATE {table_name} SET {field} = ? WHERE id = ?", (default_date, row_id))
                            total_fixed += 1
                        
                        print(f"   ✅ تم إصلاح {len(corrupted_rows)} صف في {table_name}.{field}")
                    else:
                        print(f"   ✅ {table_name}.{field} سليم")
                        
                except Exception as e:
                    print(f"   ❌ خطأ في فحص {table_name}.{field}: {e}")
        
        # حفظ التغييرات
        conn.commit()
        print(f"\n✅ تم إصلاح {total_fixed} حقل تاريخ معطوب!")
        
        # فحص إضافي للحقول التي قد تحتوي على NULL في جداول تتطلب قيم
        print("\n🔍 فحص الحقول المطلوبة...")
        
        # إصلاح حقول course المطلوبة
        cursor.execute("SELECT id FROM course WHERE start_date IS NULL OR end_date IS NULL OR created_at IS NULL")
        null_courses = cursor.fetchall()
        
        if null_courses:
            print(f"❌ وجد {len(null_courses)} دورة تحتوي على تواريخ NULL")
            default_date = datetime(2024, 1, 1).strftime('%Y-%m-%d %H:%M:%S')
            
            for course in null_courses:
                course_id = course[0]
                cursor.execute("""
                    UPDATE course 
                    SET start_date = COALESCE(start_date, ?),
                        end_date = COALESCE(end_date, ?),
                        created_at = COALESCE(created_at, ?)
                    WHERE id = ?
                """, (default_date, default_date, default_date, course_id))
            
            conn.commit()
            print(f"✅ تم إصلاح {len(null_courses)} دورة تحتوي على تواريخ NULL")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح التواريخ: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("🧪 إصلاح جميع التواريخ المعطوبة في النظام...")
    success = fix_all_dates()
    if success:
        print("✅ تم الإصلاح بنجاح!")
    else:
        print("❌ فشل الإصلاح!")
