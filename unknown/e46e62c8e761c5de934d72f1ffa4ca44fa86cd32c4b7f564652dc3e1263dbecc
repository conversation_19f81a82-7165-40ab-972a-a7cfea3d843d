#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تحديث بيانات الدورات لربطها بالمراكز والجهات بشكل صحيح
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Course, TrainingCenter, Agency
import random

def update_course_assignments():
    """تحديث ربط الدورات بالمراكز والجهات"""
    
    with app.app_context():
        print("🔄 تحديث ربط الدورات بالمراكز والجهات...")
        
        # جلب جميع الدورات والمراكز والجهات
        courses = Course.query.all()
        centers = TrainingCenter.query.all()
        agencies = Agency.query.all()
        
        print(f"📊 البيانات المتوفرة:")
        print(f"   - الدورات: {len(courses)}")
        print(f"   - المراكز: {len(centers)}")
        print(f"   - الجهات: {len(agencies)}")
        
        if not centers or not agencies:
            print("❌ لا توجد مراكز أو جهات كافية")
            return
        
        # تحديث كل دورة
        updated_count = 0
        for course in courses:
            # اختيار مركز عشوائي
            center = random.choice(centers)
            
            # اختيار جهة عشوائية (يفضل أن تكون مرتبطة بالمركز)
            if center.agency_id:
                agency = Agency.query.get(center.agency_id)
            else:
                agency = random.choice(agencies)
            
            # تحديث الدورة
            course.center_id = center.id
            course.agency_id = agency.id
            
            # تحديث بيانات إضافية
            course.total_graduates = random.randint(
                int(course.total_participants * 0.7), 
                course.total_participants
            )
            course.total_dropouts = course.total_participants - course.total_graduates
            
            # تحديث البيانات المالية
            course.daily_allowance = random.choice([50, 75, 100, 125, 150])
            course.transportation_allowance = random.choice([25, 50, 75, 100])
            course.accommodation_allowance = random.choice([100, 150, 200, 250])
            course.total_allowance = (
                (course.daily_allowance * course.duration_days) +
                course.transportation_allowance +
                course.accommodation_allowance
            ) * course.total_participants
            
            updated_count += 1
            print(f"   ✅ تم تحديث: {course.title}")
        
        # حفظ التغييرات
        db.session.commit()
        
        print(f"\n🎉 تم تحديث {updated_count} دورة بنجاح!")
        
        # طباعة إحصائيات التوزيع
        print(f"\n📊 توزيع الدورات حسب الجهات:")
        for agency in agencies:
            agency_courses = Course.query.filter_by(agency_id=agency.id).count()
            if agency_courses > 0:
                print(f"   - {agency.name}: {agency_courses} دورة")
        
        print(f"\n🏢 توزيع الدورات حسب المراكز:")
        for center in centers:
            center_courses = Course.query.filter_by(center_id=center.id).count()
            if center_courses > 0:
                print(f"   - {center.name}: {center_courses} دورة")

def add_course_levels_and_paths():
    """إضافة مستويات ومسارات للدورات"""
    
    with app.app_context():
        from app import CourseLevel, CoursePath
        
        print("🔄 تحديث مستويات ومسارات الدورات...")
        
        courses = Course.query.all()
        levels = CourseLevel.query.all()
        paths = CoursePath.query.all()
        
        if not levels or not paths:
            print("❌ لا توجد مستويات أو مسارات كافية")
            return
        
        # تحديث كل دورة
        for course in courses:
            # اختيار مستوى ومسار عشوائي
            level = random.choice(levels)
            path = random.choice(paths)
            
            course.status_id = level.id
            course.path_id = path.id
            
        db.session.commit()
        print(f"✅ تم تحديث مستويات ومسارات {len(courses)} دورة")

def generate_summary_report():
    """إنشاء تقرير ملخص للبيانات"""
    
    with app.app_context():
        from app import CourseParticipant
        
        print("\n📋 تقرير ملخص البيانات:")
        print("=" * 50)
        
        # إحصائيات الدورات
        total_courses = Course.query.count()
        active_courses = Course.query.filter(Course.end_date > db.func.now()).count()
        completed_courses = total_courses - active_courses
        
        print(f"📚 الدورات:")
        print(f"   - إجمالي الدورات: {total_courses}")
        print(f"   - الدورات النشطة: {active_courses}")
        print(f"   - الدورات المكتملة: {completed_courses}")
        
        # إحصائيات المشاركين
        total_participants = CourseParticipant.query.count()
        active_participants = CourseParticipant.query.filter_by(status='نشط').count()
        completed_participants = CourseParticipant.query.filter_by(status='مكتمل').count()
        dropped_participants = CourseParticipant.query.filter_by(status='منسحب').count()
        
        print(f"\n👥 المشاركين:")
        print(f"   - إجمالي المشاركين: {total_participants}")
        print(f"   - المشاركين النشطين: {active_participants}")
        print(f"   - المشاركين المكتملين: {completed_participants}")
        print(f"   - المشاركين المنسحبين: {dropped_participants}")
        
        # إحصائيات الجهات
        from app import Agency
        agencies = Agency.query.all()
        print(f"\n🏛️ الجهات:")
        for agency in agencies:
            agency_courses = Course.query.filter_by(agency_id=agency.id).count()
            if agency_courses > 0:
                agency_participants = db.session.query(CourseParticipant).join(Course).filter(Course.agency_id == agency.id).count()
                print(f"   - {agency.name}: {agency_courses} دورة، {agency_participants} مشارك")
        
        # إحصائيات المراكز
        from app import TrainingCenter
        centers = TrainingCenter.query.all()
        print(f"\n🏢 المراكز التدريبية:")
        for center in centers:
            center_courses = Course.query.filter_by(center_id=center.id).count()
            if center_courses > 0:
                center_participants = db.session.query(CourseParticipant).join(Course).filter(Course.center_id == center.id).count()
                print(f"   - {center.name}: {center_courses} دورة، {center_participants} مشارك")
        
        # إحصائيات المستويات
        from app import CourseLevel
        levels = CourseLevel.query.all()
        print(f"\n📊 المستويات:")
        for level in levels:
            level_courses = Course.query.filter_by(status_id=level.id).count()
            if level_courses > 0:
                print(f"   - {level.name}: {level_courses} دورة")
        
        # إحصائيات المسارات
        from app import CoursePath
        paths = CoursePath.query.all()
        print(f"\n🛤️ المسارات:")
        for path in paths:
            path_courses = Course.query.filter_by(path_id=path.id).count()
            if path_courses > 0:
                print(f"   - {path.name}: {path_courses} دورة")
        
        print("\n" + "=" * 50)
        print("✅ تم إنشاء التقرير بنجاح!")

if __name__ == "__main__":
    update_course_assignments()
    add_course_levels_and_paths()
    generate_summary_report()
