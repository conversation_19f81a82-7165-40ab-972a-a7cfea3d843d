# شرح شامل لصفحة تحليل ومقايسة الأسماء
## `/person_data/name_analysis`

---

## 🎯 **الغرض من الصفحة**

هذه الصفحة هي نظام ذكي متطور لتحليل وتصحيح الأسماء العربية ومقايستها مع قاعدة البيانات. تهدف إلى:

1. **تحليل ملفات Excel** التي تحتوي على أسماء المتدربين
2. **مقايسة الأسماء** مع البيانات الموجودة في قاعدة البيانات
3. **اكتشاف التطابقات والاختلافات** بين الأسماء
4. **تصحيح الأخطاء الإملائية** في الأسماء العربية
5. **إدارة بيانات المشاركين** في الدورات التدريبية

---

## 🔐 **متطلبات الوصول**

- **تسجيل الدخول مطلوب**: `@login_required`
- **صلاحية المدير فقط**: `current_user.role == 'admin'`
- إذا لم تكن مديراً، ستتم إعادة توجيهك إلى لوحة التحكم

---

## 🗂️ **أنواع التحليل المتاحة**

### 1. **تحليل كشف الدورة** (`course`)
- **الهدف**: فحص كشف حضور دورة معينة
- **متطلبات**: اختيار دورة محددة (مطلوب)
- **الاستخدام**: للتحقق من صحة بيانات المشاركين في دورة

### 2. **تحليل البيانات الشخصية** (`data`)
- **الهدف**: فحص البيانات الشخصية العامة
- **متطلبات**: اختيار دورة (اختياري)
- **الاستخدام**: لإضافة أشخاص جدد إلى قاعدة البيانات

### 3. **تحليل التقييمات** (`evaluation`)
- **الهدف**: فحص ملفات تقييمات المتدربين
- **متطلبات**: اختيار دورة محددة (مطلوب)
- **الاستخدام**: لإدخال أو تحديث تقييمات المشاركين

---

## 🏗️ **هيكل قاعدة البيانات والجداول**

### 📊 **الجداول الرئيسية**

#### 1. **جدول البيانات الشخصية** (`person_data`)
```sql
CREATE TABLE person_data (
    id INTEGER PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL,        -- الاسم الشخصي
    nickname VARCHAR(50),                   -- الاسم المستعار
    age INTEGER,                           -- العمر
    governorate VARCHAR(100),              -- المحافظة
    directorate VARCHAR(100),              -- المديرية
    uzla VARCHAR(100),                     -- العزلة
    village VARCHAR(100),                  -- الحي/القرية
    qualification VARCHAR(100),            -- المؤهل العلمي
    marital_status VARCHAR(100),           -- الحالة الاجتماعية
    job VARCHAR(100),                      -- العمل
    agency VARCHAR(100),                   -- الإدارة
    work_place VARCHAR(100),               -- مكان العمل
    national_number VARCHAR(20),           -- الرقم الوطني
    military_number VARCHAR(20),           -- الرقم العسكري
    phone VARCHAR(20)                      -- رقم التلفون
);
```

#### 2. **جدول الدورات** (`course`)
```sql
CREATE TABLE course (
    id INTEGER PRIMARY KEY,
    course_number VARCHAR(20) UNIQUE NOT NULL,  -- رقم الدورة العام
    agency_course_number VARCHAR(20),           -- رقم الدورة في الجهة
    place_course_number VARCHAR(20),            -- رقم الدورة في المكان
    title VARCHAR(100) NOT NULL,                -- عنوان الدورة
    description TEXT NOT NULL,                  -- وصف الدورة
    category VARCHAR(50) NOT NULL,              -- فئة الدورة
    level VARCHAR(20) NOT NULL,                 -- مستوى الدورة
    start_date DATETIME NOT NULL,               -- تاريخ البدء
    end_date DATETIME NOT NULL,                 -- تاريخ النهاية
    duration_days INTEGER NOT NULL,             -- مدة الدورة بالأيام
    trainer_id INTEGER,                         -- معرف المدرب
    total_participants INTEGER DEFAULT 0,       -- إجمالي المشاركين
    total_graduates INTEGER DEFAULT 0,          -- إجمالي الخريجين
    total_dropouts INTEGER DEFAULT 0            -- إجمالي المنسحبين
);
```

#### 3. **جدول مشاركي الدورة** (`course_participant`)
```sql
CREATE TABLE course_participant (
    id INTEGER PRIMARY KEY,
    course_id INTEGER NOT NULL,                 -- معرف الدورة
    personal_data_id INTEGER NOT NULL,          -- معرف البيانات الشخصية
    entry_date DATETIME,                        -- تاريخ الدخول
    exit_date DATETIME,                         -- تاريخ الخروج
    status VARCHAR(20) DEFAULT 'active',        -- الحالة (active/completed/dropped)
    notes TEXT,                                 -- ملاحظات
    FOREIGN KEY (course_id) REFERENCES course (id),
    FOREIGN KEY (personal_data_id) REFERENCES person_data (id)
);
```

#### 4. **جدول التقييمات** (`participant_evaluations`)
```sql
CREATE TABLE participant_evaluations (
    id INTEGER PRIMARY KEY,
    course_id INTEGER NOT NULL,                 -- معرف الدورة
    participant_id INTEGER NOT NULL,            -- معرف المشارك
    evaluator_id INTEGER NOT NULL,              -- معرف المقيم
    evaluation_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    total_score FLOAT,                          -- الدرجة الإجمالية
    percentage FLOAT,                           -- النسبة المئوية
    grade VARCHAR(20),                          -- التقدير
    notes TEXT,                                 -- ملاحظات
    is_final BOOLEAN DEFAULT 0,                 -- تقييم نهائي؟
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔗 **العلاقات بين الجداول**

### 🔄 **العلاقات الأساسية**

1. **PersonData ↔ CourseParticipant** (One-to-Many)
   - شخص واحد يمكن أن يشارك في عدة دورات
   - `person_data.id` → `course_participant.personal_data_id`

2. **Course ↔ CourseParticipant** (One-to-Many)
   - دورة واحدة تحتوي على عدة مشاركين
   - `course.id` → `course_participant.course_id`

3. **CourseParticipant ↔ ParticipantEvaluations** (One-to-Many)
   - مشارك واحد يمكن أن يحصل على عدة تقييمات
   - `course_participant.id` → `participant_evaluations.participant_id`

4. **Course ↔ ParticipantEvaluations** (One-to-Many)
   - دورة واحدة تحتوي على عدة تقييمات
   - `course.id` → `participant_evaluations.course_id`

### 📊 **مخطط العلاقات**
```
PersonData (البيانات الشخصية)
    ↓ (One-to-Many)
CourseParticipant (مشاركي الدورة)
    ↓ (Many-to-One)        ↓ (One-to-Many)
Course (الدورات)    ParticipantEvaluations (التقييمات)
```

---

## ⚙️ **آلية عمل النظام**

### 📤 **1. رفع الملف**
- يقبل ملفات Excel (.xlsx, .xls)
- يتحقق من وجود الملف وصحته
- يقرأ البيانات باستخدام مكتبة pandas

### 🔍 **2. تحديد الأعمدة**
النظام يبحث عن الأعمدة التالية في ملف Excel:

#### **أعمدة الأسماء المحتملة:**
- `"الاسم الشخصي"` (الأساسي)
- `"اسم المتدرب"` (للتقييمات)
- `"الاسم"`
- `"Name"`

#### **أعمدة البيانات الأخرى:**
- `"الرقم الوطني"`, `"National ID"`
- `"رقم التلفون"`, `"Phone"`
- `"الرقم العسكري"`, `"Military ID"`

### 🧠 **3. النظام الذكي للتحليل**

#### **دالة `analyze_names_smart_advanced()`**
هذه هي الدالة الرئيسية التي تقوم بـ:

1. **تنظيف الأسماء**: إزالة المسافات الزائدة والرموز
2. **التطابق الدقيق**: البحث عن تطابق كامل
3. **التطابق الجزئي**: البحث عن تطابق في أجزاء من الاسم
4. **التطابق الصوتي**: استخدام خوارزميات التشابه الصوتي
5. **التحقق من البيانات الإضافية**: الرقم الوطني، الهاتف، الرقم العسكري

#### **فئات النتائج:**

##### **للدورات العادية:**
- `exact_matches`: تطابق دقيق موجود في الدورة
- `partial_matches`: تطابق جزئي
- `new_names`: أسماء جديدة غير موجودة
- `duplicate_in_excel`: أسماء مكررة في ملف Excel
- `duplicate_in_course`: أسماء مكررة في الدورة
- `in_db_not_in_course`: موجود في قاعدة البيانات لكن ليس في الدورة

##### **للتقييمات:**
- `category_1`: غير موجودين في قاعدة البيانات
- `category_2`: موجودين في القاعدة والدورة وغير مدخل لهم تقييم
- `category_3`: موجودين في القاعدة وغير موجودين في الدورة
- `category_4`: موجودين في القاعدة والدورة ولديهم تقييم

---

## 🎨 **واجهة المستخدم**

### 📱 **التصميم**
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **تأثيرات زجاجية**: استخدام `backdrop-filter: blur()`
- **ألوان متدرجة**: تدرجات زرقاء أنيقة
- **خطوط عربية**: Cairo و Tajawal
- **أيقونات Font Awesome**: لتحسين التجربة البصرية

### 🔘 **أزرار نوع التحليل**
```javascript
const analysisTypes = {
    course: {
        cardTitle: 'اختيار الدورة (مطلوب)',
        submitText: 'بدء تحليل كشف الدورة',
        required: true
    },
    data: {
        cardTitle: 'اختيار الدورة (اختياري)',
        submitText: 'بدء تحليل البيانات الشخصية',
        required: false
    },
    evaluation: {
        cardTitle: 'اختيار الدورة (مطلوب)',
        submitText: 'بدء تحليل التقييمات',
        required: true
    }
};
```

---

## 📊 **صفحات النتائج**

### 1. **نتائج تحليل الدورات** (`name_analysis_results.html`)
- عرض إحصائيات شاملة
- جداول تفاعلية للنتائج
- أزرار للاستيراد والتصدير

### 2. **نتائج تحليل البيانات** (`name_analysis_data_results.html`)
- تركيز على البيانات الشخصية
- خيارات إضافة أشخاص جدد

### 3. **نتائج تحليل التقييمات** (`name_analysis_evaluation_results.html`)
- تصنيف حسب فئات التقييم الأربعة
- خيارات إدخال وتحديث التقييمات

---

## 🔧 **الوظائف المساعدة**

### **تنظيف الأسماء**
```python
def clean_name(name):
    """تنظيف وتوحيد الأسماء العربية"""
    # إزالة المسافات الزائدة
    # توحيد الأحرف العربية
    # إزالة الرموز الخاصة
```

### **حساب التشابه**
```python
def calculate_similarity(name1, name2):
    """حساب نسبة التشابه بين اسمين"""
    # استخدام خوارزميات مختلفة
    # Levenshtein distance
    # Jaro-Winkler similarity
```

### **البحث الذكي**
```python
def smart_search(excel_name, db_names):
    """البحث الذكي عن التطابقات"""
    # البحث الدقيق
    # البحث الجزئي
    # البحث الصوتي
```

---

## 📈 **الإحصائيات والتقارير**

النظام يوفر إحصائيات شاملة تشمل:

- **إجمالي الأسماء في الملف**
- **عدد التطابقات الدقيقة**
- **عدد التطابقات الجزئية**
- **عدد الأسماء الجديدة**
- **عدد الأسماء المكررة**
- **نسب النجاح والدقة**

---

## 🚀 **ميزات متقدمة**

### 1. **النظام الذكي للتصحيح**
- تصحيح الأخطاء الإملائية تلقائياً
- اقتراح بدائل للأسماء المشكوك فيها

### 2. **التحقق المتعدد المستويات**
- التحقق بالاسم
- التحقق بالرقم الوطني
- التحقق برقم الهاتف
- التحقق بالرقم العسكري

### 3. **إدارة الجلسات**
- حفظ نتائج التحليل في الجلسة
- إمكانية الرجوع للنتائج السابقة
- تصدير النتائج بصيغ مختلفة

### 4. **التكامل مع النظام**
- ربط مباشر مع جداول قاعدة البيانات
- تحديث تلقائي للإحصائيات
- تسجيل العمليات والتغييرات

---

## 🛡️ **الأمان والحماية**

- **التحقق من الصلاحيات**: فقط المديرين يمكنهم الوصول
- **التحقق من الملفات**: فحص نوع وحجم الملفات
- **حماية من SQL Injection**: استخدام ORM آمن
- **تشفير الجلسات**: حماية بيانات المستخدم

---

## 📝 **سجل العمليات**

النظام يسجل جميع العمليات:
- تاريخ ووقت التحليل
- نوع التحليل المستخدم
- النتائج المحققة
- المستخدم الذي قام بالعملية

---

هذا شرح شامل لصفحة تحليل ومقايسة الأسماء. النظام مصمم ليكون ذكياً ومرناً ويتعامل مع تعقيدات الأسماء العربية بكفاءة عالية.
