#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 تطبيق بسيط لاختبار API إضافة المشاركين بدون CSRF
"""

from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
import sqlite3
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///training_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# نماذج قاعدة البيانات المبسطة
class PersonData(db.Model):
    __tablename__ = 'person_data'

    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(200), nullable=False)
    national_number = db.Column(db.String(20), nullable=True)
    military_number = db.Column(db.String(20), nullable=True)
    age = db.Column(db.Integer, nullable=True)
    governorate = db.Column(db.String(100), nullable=True)
    directorate = db.Column(db.String(100), nullable=True)
    job = db.Column(db.String(100), nullable=True)
    qualification = db.Column(db.String(100), nullable=True)
    phone = db.Column(db.String(20), nullable=True)

class Course(db.Model):
    __tablename__ = 'course'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    course_number = db.Column(db.String(20), nullable=False)

class CourseParticipant(db.Model):
    __tablename__ = 'course_participant'

    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.Integer, db.ForeignKey('course.id'), nullable=False)
    personal_data_id = db.Column(db.Integer, db.ForeignKey('person_data.id'), nullable=False)
    status = db.Column(db.String(20), nullable=False, default='active')

# المسارات

@app.route('/')
def home():
    """الصفحة الرئيسية"""
    return jsonify({
        'message': 'تطبيق اختبار API يعمل بنجاح!',
        'endpoints': [
            '/course/<int:course_id>/add_participant_api',
            '/course/<int:course_id>/add_new_person_api',
            '/test_db',
            '/test_add_person'
        ]
    })

@app.route('/test_db')
def test_db():
    """اختبار قاعدة البيانات"""
    try:
        # فحص الجداول
        people_count = PersonData.query.count()
        courses_count = Course.query.count()
        participants_count = CourseParticipant.query.count()

        # فحص الدورة رقم 1
        course_1 = Course.query.get(1)
        course_1_participants = 0
        if course_1:
            course_1_participants = CourseParticipant.query.filter_by(course_id=1).count()

        return jsonify({
            'success': True,
            'database_status': 'متصل',
            'people_count': people_count,
            'courses_count': courses_count,
            'participants_count': participants_count,
            'course_1_title': course_1.title if course_1 else 'غير موجودة',
            'course_1_participants': course_1_participants
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/course/<int:course_id>/search_people', methods=['GET'])
def search_people_for_course(course_id):
    """البحث عن الأشخاص لإضافتهم للدورة - مع استثناء المشاركين الحاليين"""
    try:
        query = request.args.get('q', '').strip()
        if not query or len(query) < 2:
            return jsonify([])

        print(f"🔍 البحث عن: {query} للدورة {course_id}")

        # البحث في قاعدة البيانات
        people = PersonData.query.filter(
            db.or_(
                PersonData.full_name.ilike(f'%{query}%'),
                PersonData.national_number.ilike(f'%{query}%'),
                PersonData.military_number.ilike(f'%{query}%')
            )
        ).limit(20).all()

        results = []
        excluded_count = 0

        for person in people:
            # التحقق من كونه مشارك في الدورة
            existing = CourseParticipant.query.filter_by(
                course_id=course_id,
                personal_data_id=person.id
            ).first()

            if existing:
                excluded_count += 1
                print(f"⏭️ تم استثناء {person.full_name} - مشارك بالفعل")
                continue

            results.append({
                'id': person.id,
                'name': person.full_name,
                'national_number': person.national_number,
                'military_number': person.military_number,
                'age': person.age,
                'governorate': person.governorate,
                'job': person.job
            })

        print(f"✅ تم العثور على {len(results)} شخص متاح، تم استثناء {excluded_count} مشارك")
        return jsonify(results)

    except Exception as e:
        print(f"❌ خطأ في البحث: {e}")
        return jsonify([])

@app.route('/course/<int:course_id>/add_participant_api', methods=['POST'])
def add_participant_api(course_id):
    """إضافة مشارك موجود للدورة - بدون CSRF"""
    try:
        print(f"🎯 طلب إضافة مشارك للدورة {course_id}")

        # التحقق من البيانات
        if not request.json:
            return jsonify({'success': False, 'message': 'لا توجد بيانات JSON'}), 400

        person_id = request.json.get('person_id')
        if not person_id:
            return jsonify({'success': False, 'message': 'معرف الشخص مطلوب'}), 400

        print(f"📋 معرف الشخص: {person_id}")

        # التحقق من وجود الدورة
        course = Course.query.get(course_id)
        if not course:
            return jsonify({'success': False, 'message': 'الدورة غير موجودة'}), 404

        # التحقق من وجود الشخص
        person = PersonData.query.get(person_id)
        if not person:
            return jsonify({'success': False, 'message': 'الشخص غير موجود'}), 404

        # التحقق من عدم وجود تسجيل مسبق
        existing = CourseParticipant.query.filter_by(
            course_id=course_id,
            personal_data_id=person_id
        ).first()

        if existing:
            return jsonify({
                'success': False,
                'message': f'{person.full_name} مسجل بالفعل في الدورة'
            })

        # إضافة المشارك
        participant = CourseParticipant(
            course_id=course_id,
            personal_data_id=person_id,
            status='active'
        )

        db.session.add(participant)
        db.session.commit()

        print(f"✅ تم إضافة {person.full_name} للدورة بنجاح")

        return jsonify({
            'success': True,
            'message': f'تم إضافة {person.full_name} للدورة بنجاح',
            'person': {
                'id': person.id,
                'full_name': person.full_name,
                'national_number': person.national_number,
                'military_number': person.military_number
            }
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ: {e}")
        return jsonify({'success': False, 'message': f'خطأ: {str(e)}'}), 500

@app.route('/course/<int:course_id>/add_new_person_api', methods=['POST'])
def add_new_person_api(course_id):
    """إضافة شخص جديد للدورة - بدون CSRF"""
    try:
        print(f"👤 طلب إضافة شخص جديد للدورة {course_id}")

        # التحقق من البيانات
        if not request.json:
            return jsonify({'success': False, 'message': 'لا توجد بيانات JSON'}), 400

        data = request.json
        full_name = data.get('full_name', '').strip()
        national_number = data.get('national_number', '').strip()

        if not full_name or not national_number:
            return jsonify({'success': False, 'message': 'الاسم والرقم الوطني مطلوبان'}), 400

        # التحقق من وجود الدورة
        course = Course.query.get(course_id)
        if not course:
            return jsonify({'success': False, 'message': 'الدورة غير موجودة'}), 404

        # التحقق من عدم وجود الشخص مسبقاً
        existing_person = PersonData.query.filter_by(national_number=national_number).first()
        if existing_person:
            return jsonify({
                'success': False,
                'message': f'شخص بالرقم الوطني {national_number} موجود مسبقاً'
            })

        # إنشاء الشخص الجديد
        new_person = PersonData(
            full_name=full_name,
            national_number=national_number,
            military_number=data.get('military_number', '').strip(),
            age=data.get('age'),
            governorate=data.get('governorate', '').strip(),
            directorate=data.get('directorate', '').strip(),
            job=data.get('job', '').strip(),
            qualification=data.get('qualification', '').strip(),
            phone=data.get('phone', '').strip()
        )

        db.session.add(new_person)
        db.session.flush()  # للحصول على ID

        # إضافته للدورة
        participant = CourseParticipant(
            course_id=course_id,
            personal_data_id=new_person.id,
            status='active'
        )

        db.session.add(participant)
        db.session.commit()

        print(f"✅ تم إنشاء وإضافة {new_person.full_name} للدورة بنجاح")

        return jsonify({
            'success': True,
            'message': f'تم إنشاء وإضافة {new_person.full_name} للدورة بنجاح',
            'person': {
                'id': new_person.id,
                'full_name': new_person.full_name,
                'national_number': new_person.national_number,
                'military_number': new_person.military_number
            }
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ: {e}")
        return jsonify({'success': False, 'message': f'خطأ: {str(e)}'}), 500

@app.route('/test_add_person')
def test_add_person():
    """اختبار إضافة شخص تجريبي"""
    try:
        # إنشاء شخص تجريبي
        test_person = PersonData(
            full_name='اختبار API البسيط',
            national_number='9999999999',
            military_number='TEST999',
            age=30,
            governorate='صنعاء',
            directorate='شعوب',
            job='مختبر API',
            qualification='بكالوريوس'
        )

        db.session.add(test_person)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إنشاء شخص تجريبي بنجاح',
            'person_id': test_person.id
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    print("🚀 بدء تشغيل تطبيق اختبار API البسيط...")
    print("🌐 الخادم سيعمل على: http://localhost:5002")
    print("✅ بدون CSRF - جاهز للاختبار!")

    app.run(debug=True, port=5002, host='0.0.0.0')
