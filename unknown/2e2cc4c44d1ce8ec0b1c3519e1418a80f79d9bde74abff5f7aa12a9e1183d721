#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة مشارك تجريبي للاختبار
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, CourseParticipant, PersonData, Course
from datetime import datetime

def add_test_participant():
    """
    إضافة مشارك تجريبي للاختبار
    """
    with app.app_context():
        try:
            course_id = 1
            
            # جلب شخص متاح للإضافة
            existing_participant_ids = [p.personal_data_id for p in CourseParticipant.query.filter_by(course_id=course_id).all()]
            available_people = PersonData.query.filter(~PersonData.id.in_(existing_participant_ids)).all()
            
            if not available_people:
                print("❌ لا يوجد أشخاص متاحين للإضافة")
                return False
            
            # اختيار أول شخص متاح
            person = available_people[0]
            print(f"👤 سيتم إضافة: {person.full_name} (ID: {person.id})")
            
            # إنشاء مشارك جديد
            participant = CourseParticipant(
                course_id=course_id,
                personal_data_id=person.id,
                entry_date=datetime.now(),
                status='active',
                notes='مشارك تجريبي للاختبار'
            )
            
            db.session.add(participant)
            db.session.commit()
            
            print(f"✅ تم إضافة المشارك بنجاح!")
            print(f"   - الاسم: {person.full_name}")
            print(f"   - معرف المشارك: {participant.id}")
            print(f"   - الحالة: {participant.status}")
            
            # عرض إحصائيات محدثة
            total_participants = CourseParticipant.query.filter_by(course_id=course_id).count()
            remaining_available = PersonData.query.filter(~PersonData.id.in_([p.personal_data_id for p in CourseParticipant.query.filter_by(course_id=course_id).all()])).count()
            
            print(f"\n📊 الإحصائيات المحدثة:")
            print(f"   - إجمالي المشاركين في الدورة: {total_participants}")
            print(f"   - الأشخاص المتاحين للإضافة: {remaining_available}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إضافة المشارك: {e}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    print("🧪 إضافة مشارك تجريبي للاختبار...")
    success = add_test_participant()
    if success:
        print("✅ تم الاختبار بنجاح!")
    else:
        print("❌ فشل الاختبار!")
