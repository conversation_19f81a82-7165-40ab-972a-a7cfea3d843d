@echo off
chcp 65001 >nul
echo ========================================
echo    إعداد نظام تحليل الأسماء
echo    Training System Setup
echo ========================================
echo.

cd /d "E:\app\TRINING"

echo 🔧 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python أولاً
    echo ❌ Python not installed! Please install Python first
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 🔧 إنشاء البيئة الافتراضية...
if not exist ".venv" (
    python -m venv .venv
    echo ✅ تم إنشاء البيئة الافتراضية
) else (
    echo ✅ البيئة الافتراضية موجودة مسبقاً
)

echo.
echo 🔧 تفعيل البيئة الافتراضية...
call .venv\Scripts\activate.bat

echo.
echo 📦 تثبيت المكتبات المطلوبة...
pip install --upgrade pip
pip install -r requirements.txt

echo.
echo 🔧 فحص قاعدة البيانات...
if not exist "training_system.db" (
    echo ⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها عند أول تشغيل
    echo ⚠️ Database not found - will be created on first run
)

echo.
echo 🔧 إنشاء اختصار سطح المكتب...
call create_desktop_shortcut.bat

echo.
echo ========================================
echo    ✅ تم إعداد النظام بنجاح!
echo    ✅ System setup completed!
echo ========================================
echo.
echo الآن يمكنك:
echo Now you can:
echo.
echo 1. تشغيل التطبيق من سطح المكتب
echo    Run application from desktop shortcut
echo.
echo 2. تشغيل start_app.bat مباشرة
echo    Run start_app.bat directly
echo.
echo 3. استخدام start_app.ps1 في PowerShell
echo    Use start_app.ps1 in PowerShell
echo.
pause
