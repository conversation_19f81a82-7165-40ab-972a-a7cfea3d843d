import sqlite3
from werkzeug.security import generate_password_hash

print("إنشاء مدير بكلمة مرور صحيحة...")

# الاتصال بقاعدة البيانات
conn = sqlite3.connect('training_system.db')
cursor = conn.cursor()

# حذف المستخدمين الحاليين
cursor.execute("DELETE FROM user")
print("تم حذف المستخدمين الحاليين")

# إنشاء كلمة مرور مشفرة
password_hash = generate_password_hash('admin123')
print(f"تم تشفير كلمة المرور: {password_hash[:50]}...")

# إنشاء مدير جديد
cursor.execute("""
    INSERT INTO user (username, email, password, role, created_at)
    VALUES (?, ?, ?, ?, datetime('now'))
""", ('admin', '<EMAIL>', password_hash, 'admin'))

conn.commit()
print("تم إنشاء المدير")

# التحقق
cursor.execute("SELECT id, username, email, role FROM user")
users = cursor.fetchall()
print(f"تم إنشاء {len(users)} مستخدم:")
for user in users:
    print(f"- ID: {user[0]}, اسم المستخدم: {user[1]}, البريد: {user[2]}, الدور: {user[3]}")

# اختبار كلمة المرور
cursor.execute("SELECT password FROM user WHERE email = ?", ('<EMAIL>',))
stored_password = cursor.fetchone()[0]

from werkzeug.security import check_password_hash
if check_password_hash(stored_password, 'admin123'):
    print("✅ كلمة المرور صحيحة")
else:
    print("❌ كلمة المرور خاطئة")

conn.close()

print("\n🌐 يمكنك الآن تسجيل الدخول:")
print("البريد: <EMAIL>")
print("كلمة المرور: admin123")
