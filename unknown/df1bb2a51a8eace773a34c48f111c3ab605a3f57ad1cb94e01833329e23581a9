#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة دورات تجريبية مع مشاركين لاختبار التقارير
"""

from app import app, db
from app import (
    Course, PersonData, CourseParticipant, Agency, TrainingCenter,
    CourseLevel, CoursePath, ParticipantType, Governorate, Directorate, User
)
from datetime import datetime, timedelta
import random

def create_sample_courses():
    """إنشاء دورات تجريبية مع مشاركين"""
    
    with app.app_context():
        print("🚀 بدء إنشاء الدورات التجريبية...")
        
        # جلب البيانات المرجعية
        agencies = Agency.query.all()
        centers = TrainingCenter.query.all()
        levels = CourseLevel.query.all()
        paths = CoursePath.query.all()
        participant_types = ParticipantType.query.all()
        governorates = Governorate.query.all()
        
        if not agencies or not centers or not levels or not paths:
            print("❌ البيانات المرجعية غير مكتملة. تأكد من تشغيل seed_data.py أولاً")
            return
        
        print(f"📊 البيانات المتوفرة:")
        print(f"   - الجهات: {len(agencies)}")
        print(f"   - المراكز: {len(centers)}")
        print(f"   - المستويات: {len(levels)}")
        print(f"   - المسارات: {len(paths)}")

        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        default_user = User.query.filter_by(id=1).first()
        if not default_user:
            default_user = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            default_user.set_password('admin123')
            db.session.add(default_user)
            db.session.commit()
            print("✅ تم إنشاء مستخدم افتراضي")

        # حذف الدورات الموجودة
        CourseParticipant.query.delete()
        Course.query.delete()
        PersonData.query.delete()
        db.session.commit()
        
        # إنشاء دورات متنوعة
        courses_data = [
            # دورات وزارة الدفاع
            {
                'title': 'دورة القيادة العسكرية المتقدمة',
                'course_number': 'MOD-2024-001',
                'agency_name': 'وزارة الدفاع',
                'center_pattern': 'العسكري',
                'level_name': 'متقدم',
                'path_name': 'القيادة والإدارة',
                'participants_count': 25
            },
            {
                'title': 'دورة التكتيك العسكري',
                'course_number': 'MOD-2024-002',
                'agency_name': 'وزارة الدفاع',
                'center_pattern': 'العسكري',
                'level_name': 'متوسط',
                'path_name': 'العلوم العسكرية',
                'participants_count': 30
            },
            {
                'title': 'دورة الأمن السيبراني العسكري',
                'course_number': 'MOD-2024-003',
                'agency_name': 'وزارة الدفاع',
                'center_pattern': 'التقني',
                'level_name': 'متقدم',
                'path_name': 'التكنولوجيا والاتصالات',
                'participants_count': 20
            },
            
            # دورات وزارة الداخلية
            {
                'title': 'دورة إدارة الأزمات الأمنية',
                'course_number': 'MOI-2024-001',
                'agency_name': 'وزارة الداخلية',
                'center_pattern': 'الأمني',
                'level_name': 'متقدم',
                'path_name': 'الأمن والسلامة',
                'participants_count': 22
            },
            {
                'title': 'دورة التحقيق الجنائي',
                'course_number': 'MOI-2024-002',
                'agency_name': 'وزارة الداخلية',
                'center_pattern': 'الأمني',
                'level_name': 'متوسط',
                'path_name': 'العدالة والقانون',
                'participants_count': 18
            },
            {
                'title': 'دورة مكافحة الجرائم الإلكترونية',
                'course_number': 'MOI-2024-003',
                'agency_name': 'وزارة الداخلية',
                'center_pattern': 'التقني',
                'level_name': 'متقدم',
                'path_name': 'التكنولوجيا والاتصالات',
                'participants_count': 15
            },
            
            # دورات وزارة التعليم
            {
                'title': 'دورة طرق التدريس الحديثة',
                'course_number': 'MOE-2024-001',
                'agency_name': 'وزارة التعليم',
                'center_pattern': 'التعليمي',
                'level_name': 'أساسي',
                'path_name': 'التعليم والتدريب',
                'participants_count': 35
            },
            {
                'title': 'دورة التعليم الإلكتروني',
                'course_number': 'MOE-2024-002',
                'agency_name': 'وزارة التعليم',
                'center_pattern': 'التقني',
                'level_name': 'متوسط',
                'path_name': 'التكنولوجيا والاتصالات',
                'participants_count': 28
            },
            {
                'title': 'دورة إدارة المؤسسات التعليمية',
                'course_number': 'MOE-2024-003',
                'agency_name': 'وزارة التعليم',
                'center_pattern': 'الإداري',
                'level_name': 'متقدم',
                'path_name': 'القيادة والإدارة',
                'participants_count': 20
            },
            
            # دورات وزارة الصحة
            {
                'title': 'دورة الطوارئ الطبية',
                'course_number': 'MOH-2024-001',
                'agency_name': 'وزارة الصحة',
                'center_pattern': 'الطبي',
                'level_name': 'متوسط',
                'path_name': 'الصحة والطب',
                'participants_count': 24
            },
            {
                'title': 'دورة إدارة المستشفيات',
                'course_number': 'MOH-2024-002',
                'agency_name': 'وزارة الصحة',
                'center_pattern': 'الإداري',
                'level_name': 'متقدم',
                'path_name': 'القيادة والإدارة',
                'participants_count': 16
            },
            
            # دورات وزارة المالية
            {
                'title': 'دورة المحاسبة الحكومية',
                'course_number': 'MOF-2024-001',
                'agency_name': 'وزارة المالية',
                'center_pattern': 'الإداري',
                'level_name': 'متوسط',
                'path_name': 'المالية والمحاسبة',
                'participants_count': 26
            },
            {
                'title': 'دورة التدقيق المالي',
                'course_number': 'MOF-2024-002',
                'agency_name': 'وزارة المالية',
                'center_pattern': 'الإداري',
                'level_name': 'متقدم',
                'path_name': 'المالية والمحاسبة',
                'participants_count': 18
            }
        ]
        
        # أسماء عربية للمشاركين
        arabic_names = [
            'أحمد محمد علي الزهراني', 'محمد عبدالله أحمد الغامدي', 'علي سعد محمد القحطاني',
            'عبدالرحمن خالد علي العتيبي', 'سعد عبدالعزيز محمد الدوسري', 'خالد أحمد سعد الشهري',
            'عبدالله محمد علي الحربي', 'فهد سعد أحمد المطيري', 'عبدالعزيز علي محمد العنزي',
            'محمد سعد عبدالله الرشيد', 'أحمد علي خالد السبيعي', 'سعد محمد عبدالرحمن الخالدي',
            'علي أحمد عبدالله الصالح', 'عبدالرحمن سعد محمد البقمي', 'خالد عبدالعزيز أحمد الفهد',
            'محمد علي سعد الناصر', 'أحمد عبدالله خالد الراشد', 'سعد علي محمد العبدالله',
            'عبدالعزيز أحمد سعد الملك', 'علي محمد عبدالرحمن الأمير', 'فهد خالد أحمد الوزير',
            'عبدالله سعد علي الشيخ', 'محمد عبدالعزيز خالد الدكتور', 'أحمد علي عبدالرحمن المهندس',
            'سعد محمد أحمد الأستاذ', 'علي عبدالله سعد الكابتن', 'خالد أحمد محمد الرائد',
            'عبدالرحمن علي سعد المقدم', 'محمد خالد أحمد العقيد', 'أحمد سعد علي العميد'
        ]
        
        created_courses = []
        total_participants = 0
        
        for course_data in courses_data:
            print(f"📚 إنشاء دورة: {course_data['title']}")
            
            # العثور على الجهة
            agency = next((a for a in agencies if a.name == course_data['agency_name']), None)
            if not agency:
                print(f"❌ لم يتم العثور على الجهة: {course_data['agency_name']}")
                continue
            
            # العثور على المركز
            center = next((c for c in centers if course_data['center_pattern'] in c.name), None)
            if not center:
                center = random.choice(centers)
            
            # العثور على المستوى
            level = next((l for l in levels if l.name == course_data['level_name']), None)
            if not level:
                level = random.choice(levels)
            
            # العثور على المسار
            path = next((p for p in paths if p.name == course_data['path_name']), None)
            if not path:
                path = random.choice(paths)
            
            # إنشاء الدورة
            course = Course(
                title=course_data['title'],
                course_number=course_data['course_number'],
                description='وصف تجريبي للدورة',
                category='تدريبية',
                level='متوسط',
                agency_id=agency.id,
                center_id=center.id,
                status_id=level.id,
                path_id=path.id,
                participant_type_id=participant_types[0].id if participant_types else None,
                start_date=datetime.now() - timedelta(days=random.randint(30, 180)),
                end_date=datetime.now() + timedelta(days=random.randint(30, 90)),
                start_date_hijri='1445/05/15',
                end_date_hijri='1445/06/15',
                duration_days=30,
                trainer_id=1,  # افتراض وجود مستخدم بـ ID=1
                total_participants=course_data['participants_count']
            )
            
            db.session.add(course)
            db.session.flush()  # للحصول على ID الدورة
            
            # إنشاء المشاركين
            participants_created = 0
            for i in range(course_data['participants_count']):
                if i < len(arabic_names):
                    name = arabic_names[i]
                else:
                    name = f"مشارك {i+1} في {course_data['course_number']}"
                
                # إنشاء بيانات شخصية
                person = PersonData(
                    full_name=name,
                    national_number=f"{random.randint(100000000, 999999999)}",
                    phone=f"05{random.randint(10000000, 99999999)}",
                    governorate_id=random.choice(governorates).id if governorates else None,
                    directorate_id=None,
                    village_id=None
                )
                
                db.session.add(person)
                db.session.flush()
                
                # إنشاء مشاركة في الدورة
                participation = CourseParticipant(
                    course_id=course.id,
                    personal_data_id=person.id,
                    status=random.choice(['مسجل', 'مكتمل', 'منسحب']),
                    entry_date=course.start_date + timedelta(days=random.randint(0, 5))
                )
                
                db.session.add(participation)
                participants_created += 1
            
            created_courses.append(course)
            total_participants += participants_created
            print(f"   ✅ تم إنشاء {participants_created} مشارك")
        
        # حفظ جميع التغييرات
        db.session.commit()
        
        print(f"\n🎉 تم إنشاء {len(created_courses)} دورة بنجاح!")
        print(f"👥 إجمالي المشاركين: {total_participants}")
        print(f"📊 التوزيع حسب الجهات:")
        
        # إحصائيات سريعة
        for agency in agencies:
            agency_courses = [c for c in created_courses if c.agency_id == agency.id]
            if agency_courses:
                print(f"   - {agency.name}: {len(agency_courses)} دورة")

if __name__ == "__main__":
    create_sample_courses()
