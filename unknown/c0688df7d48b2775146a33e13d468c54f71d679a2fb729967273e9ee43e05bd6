{% extends "layout.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-edit"></i> إدارة التصحيحات المخصصة
                </h1>
                <div>
                    <a href="{{ url_for('person_data.add_correction') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة تصحيح جديد
                    </a>
                    <a href="{{ url_for('person_data.name_analysis') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للتحليل
                    </a>
                </div>
            </div>

            <!-- معلومات توضيحية -->
            <div class="alert alert-info mb-4" role="alert">
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle"></i> نظام التصحيحات المخصصة
                        </h5>
                        <p class="mb-2">
                            <strong>يمكنك إضافة تصحيحات مخصصة للأسماء العربية حسب احتياجاتك.</strong>
                            هذه التصحيحات ستطبق تلقائياً عند تحليل الأسماء.
                        </p>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <small>
                                    <i class="fas fa-check text-success"></i> <strong>نشط:</strong> يطبق في التحليل<br>
                                    <i class="fas fa-pause text-warning"></i> <strong>معطل:</strong> لا يطبق في التحليل<br>
                                    <i class="fas fa-chart-line text-info"></i> <strong>الاستخدام:</strong> عدد مرات التطبيق
                                </small>
                            </div>
                            <div class="col-md-6">
                                <small>
                                    <i class="fas fa-font text-primary"></i> <strong>الهمزات:</strong> إ، أ، آ<br>
                                    <i class="fas fa-italic text-info"></i> <strong>الألف المقصورة:</strong> ى، ي<br>
                                    <i class="fas fa-link text-warning"></i> <strong>أسماء مركبة:</strong> أبو، أم، عبد
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="mt-2">
                            <h6 class="text-muted">أمثلة على التصحيحات:</h6>
                            <div class="badge badge-danger mr-1">مرتضي</div>
                            <i class="fas fa-arrow-left text-muted"></i>
                            <div class="badge badge-success ml-1">مرتضى</div>
                            <br><br>
                            <div class="badge badge-danger mr-1">ابو_الدين</div>
                            <i class="fas fa-arrow-left text-muted"></i>
                            <div class="badge badge-success ml-1">أبو الدين</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2 hover-card">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي التصحيحات
                                    </div>
                                    <div class="h4 mb-0 font-weight-bold text-gray-800" id="totalCorrections">
                                        {{ corrections|length }}
                                    </div>
                                    <div class="text-xs text-muted">تصحيح مخصص متاح</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-list fa-2x text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2 hover-card">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        التصحيحات النشطة
                                    </div>
                                    <div class="h4 mb-0 font-weight-bold text-gray-800" id="activeCorrections">
                                        {% set active_count = corrections|selectattr('is_active', 'equalto', true)|list|length %}
                                        {{ active_count }}
                                    </div>
                                    <div class="text-xs text-muted">جاهز للتطبيق فوراً</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2 hover-card">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        التصحيحات المعطلة
                                    </div>
                                    <div class="h4 mb-0 font-weight-bold text-gray-800" id="inactiveCorrections">
                                        {% set inactive_count = corrections|selectattr('is_active', 'equalto', false)|list|length %}
                                        {{ inactive_count }}
                                    </div>
                                    <div class="text-xs text-muted">متوقف مؤقتاً</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-pause-circle fa-2x text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2 hover-card">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        إجمالي الاستخدامات
                                    </div>
                                    <div class="h4 mb-0 font-weight-bold text-gray-800" id="totalUsage">
                                        {% set total_usage = 0 %}
                                        {% for correction in corrections %}
                                            {% set total_usage = total_usage + (correction.usage_count or 0) %}
                                        {% endfor %}
                                        {{ total_usage }}
                                    </div>
                                    <div class="text-xs text-muted">مرة تطبيق إجمالية</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-bar fa-2x text-info"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Corrections Table -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-table"></i> قائمة التصحيحات المخصصة
                    </h6>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportToExcel()">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="printTable()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-info" onclick="refreshTable()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if corrections %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-hover" id="correctionsTable" width="100%" cellspacing="0">
                                <thead style="background-color: #ecf0f1 !important;">
                                    <tr>
                                        <th style="min-width: 120px; color: #2c3e50 !important; background-color: #ecf0f1 !important; font-weight: bold;">الاسم الخطأ</th>
                                        <th style="min-width: 120px; color: #2c3e50 !important; background-color: #ecf0f1 !important; font-weight: bold;">الاسم الصحيح</th>
                                        <th style="min-width: 140px; color: #2c3e50 !important; background-color: #ecf0f1 !important; font-weight: bold;">نوع التصحيح</th>
                                        <th style="min-width: 80px; color: #2c3e50 !important; background-color: #ecf0f1 !important; font-weight: bold;">الحالة</th>
                                        <th style="min-width: 100px; color: #2c3e50 !important; background-color: #ecf0f1 !important; font-weight: bold;">عدد الاستخدامات</th>
                                        <th style="min-width: 120px; color: #2c3e50 !important; background-color: #ecf0f1 !important; font-weight: bold;">تاريخ الإضافة</th>
                                        <th style="min-width: 80px; color: #2c3e50 !important; background-color: #ecf0f1 !important; font-weight: bold;">أضافه</th>
                                        <th style="min-width: 140px; color: #2c3e50 !important; background-color: #ecf0f1 !important; font-weight: bold;">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for correction in corrections %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-danger" style="font-size: 14px; padding: 8px 12px;">
                                                <i class="fas fa-times-circle"></i> {{ correction['wrong_name'] }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-success" style="font-size: 14px; padding: 8px 12px;">
                                                <i class="fas fa-check-circle"></i> {{ correction['correct_name'] }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if correction['correction_type'] == 'hamza' %}
                                                <span class="badge badge-primary" style="font-size: 12px;">
                                                    <i class="fas fa-font"></i> تصحيح الهمزات
                                                </span>
                                            {% elif correction['correction_type'] == 'alif_maqsura' %}
                                                <span class="badge badge-info" style="font-size: 12px;">
                                                    <i class="fas fa-italic"></i> الألف المقصورة
                                                </span>
                                            {% elif correction['correction_type'] == 'compound_names' %}
                                                <span class="badge badge-warning" style="font-size: 12px;">
                                                    <i class="fas fa-link"></i> أسماء مركبة
                                                </span>
                                            {% elif correction['correction_type'] == 'titles' %}
                                                <span class="badge badge-secondary" style="font-size: 12px;">
                                                    <i class="fas fa-crown"></i> ألقاب وكنى
                                                </span>
                                            {% elif correction['correction_type'] == 'symbols' %}
                                                <span class="badge badge-dark" style="font-size: 12px;">
                                                    <i class="fas fa-eraser"></i> إزالة رموز
                                                </span>
                                            {% else %}
                                                <span class="badge badge-light text-dark" style="font-size: 12px;">
                                                    <i class="fas fa-question-circle"></i> أخرى
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if correction['is_active'] %}
                                                <span class="badge badge-success" style="font-size: 13px; padding: 6px 10px;">
                                                    <i class="fas fa-check"></i> نشط
                                                </span>
                                            {% else %}
                                                <span class="badge badge-warning" style="font-size: 13px; padding: 6px 10px;">
                                                    <i class="fas fa-pause"></i> معطل
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <span class="badge badge-info" style="font-size: 14px; padding: 8px 12px;">
                                                <i class="fas fa-chart-line"></i> {{ correction['usage_count'] or 0 }}
                                            </span>
                                            {% if (correction['usage_count'] or 0) > 0 %}
                                                <br><small class="text-muted">مرة</small>
                                            {% else %}
                                                <br><small class="text-muted">لم يُستخدم بعد</small>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div style="font-size: 13px;">
                                                {% if correction['created_at'] %}
                                                    {% if correction['created_at'].strftime is defined %}
                                                        <strong>{{ correction['created_at'].strftime('%Y-%m-%d') }}</strong><br>
                                                        <small class="text-muted">{{ correction['created_at'].strftime('%H:%M') }}</small>
                                                    {% else %}
                                                        <strong>{{ correction['created_at']|string|truncate(10, True, '') }}</strong><br>
                                                        <small class="text-muted">{{ correction['created_at']|string|slice(11, 16) }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="text-muted">غير معروف</span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge badge-secondary" style="font-size: 12px;">
                                                <i class="fas fa-user"></i>
                                                {{ correction['creator']['username'] if correction['creator'] and correction['creator']['username'] else 'غير معروف' }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('person_data.toggle_correction', correction_id=correction['id']) }}"
                                                   class="btn btn-sm {% if correction['is_active'] %}btn-warning{% else %}btn-success{% endif %}"
                                                   title="{% if correction['is_active'] %}إلغاء التفعيل{% else %}تفعيل{% endif %}"
                                                   style="margin: 2px;">
                                                    <i class="fas {% if correction['is_active'] %}fa-pause{% else %}fa-play{% endif %}"></i>
                                                    {% if correction['is_active'] %}إيقاف{% else %}تشغيل{% endif %}
                                                </a>
                                                <a href="{{ url_for('person_data.delete_correction', correction_id=correction['id']) }}"
                                                   class="btn btn-sm btn-danger"
                                                   onclick="return confirm('هل أنت متأكد من حذف هذا التصحيح؟\n\nالاسم الخطأ: {{ correction['wrong_name'] }}\nالاسم الصحيح: {{ correction['correct_name'] }}')"
                                                   title="حذف التصحيح"
                                                   style="margin: 2px;">
                                                    <i class="fas fa-trash"></i>
                                                    حذف
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-gray-500">لا توجد تصحيحات مخصصة</h5>
                            <p class="text-gray-400">ابدأ بإضافة تصحيحات جديدة لتحسين دقة النظام</p>
                            <a href="{{ url_for('person_data.add_correction') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة أول تصحيح
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- DataTables CSS and JS -->
<link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css" rel="stylesheet">
<script src="{{ url_for('static', filename='libs/datatables/jquery.dataTables.min.js') }}"></script>
<script src="{{ url_for('static', filename='libs/datatables/dataTables.bootstrap5.min.js') }}"></script>

<!-- تحسين ألوان الجدول -->
<style>
    /* تحسين رؤوس الجدول بألوان واضحة */
    table.dataTable thead th,
    table.dataTable thead td {
        color: #2c3e50 !important;
        background-color: #ecf0f1 !important;
        border-bottom: 2px solid #bdc3c7 !important;
        font-weight: bold !important;
    }

    /* تحسين أيقونات الترتيب */
    table.dataTable thead .sorting:before,
    table.dataTable thead .sorting:after,
    table.dataTable thead .sorting_asc:before,
    table.dataTable thead .sorting_asc:after,
    table.dataTable thead .sorting_desc:before,
    table.dataTable thead .sorting_desc:after {
        color: #2c3e50 !important;
    }

    /* تحسين شامل لجميع عناصر الرأس */
    #correctionsTable_wrapper table thead th,
    #correctionsTable_wrapper table thead td,
    .dataTables_wrapper table thead th,
    .dataTables_wrapper table thead td {
        color: #2c3e50 !important;
        background-color: #ecf0f1 !important;
        font-weight: bold !important;
        text-align: center !important;
        border: 1px solid #bdc3c7 !important;
    }

    /* تأكيد لون النص في الرؤوس */
    #correctionsTable thead th,
    #correctionsTable thead th * {
        color: #2c3e50 !important;
    }

    /* تحسين تأثيرات hover للرؤوس */
    #correctionsTable thead th:hover {
        background-color: #d5dbdb !important;
        transition: background-color 0.3s ease;
    }

    /* تحسين الحدود */
    #correctionsTable thead th {
        border-top: 3px solid #3498db !important;
        border-bottom: 2px solid #bdc3c7 !important;
    }
</style>

<!-- تحسينات CSS للجدول -->
<style>
    /* تحسين عرض الجدول */
    #correctionsTable {
        font-size: 14px;
    }

    #correctionsTable th {
        background-color: #ecf0f1 !important;
        color: #2c3e50 !important;
        text-align: center;
        vertical-align: middle;
        font-weight: bold;
        border: 1px solid #bdc3c7;
        font-size: 14px !important;
    }

    /* تحسين لون النص في رؤوس الجدول */
    #correctionsTable thead th {
        color: #2c3e50 !important;
        background-color: #ecf0f1 !important;
    }

    .thead-dark th {
        color: #2c3e50 !important;
        background-color: #ecf0f1 !important;
        border-color: #bdc3c7 !important;
    }

    /* تحسينات إضافية لضمان ظهور النص */
    table#correctionsTable thead tr th {
        color: #2c3e50 !important;
        background-color: #ecf0f1 !important;
        text-shadow: none !important;
        font-weight: bold !important;
    }

    /* إصلاح DataTables header */
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        color: #333 !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        color: #fff !important;
        background: #007bff !important;
    }

    /* تحسين ألوان النصوص في الجدول */
    #correctionsTable tbody td {
        color: #1a1a1a !important;
        background-color: #fff;
        font-weight: 500 !important;
        font-size: 14px !important;
    }

    #correctionsTable tbody tr:nth-child(even) td {
        background-color: #f8f9fa;
        color: #1a1a1a !important;
    }

    #correctionsTable tbody tr:hover td {
        background-color: #e3f2fd !important;
        color: #0d47a1 !important;
    }

    /* تحسين ألوان العناصر داخل الخلايا */
    #correctionsTable tbody td .badge {
        font-weight: bold !important;
        font-size: 12px !important;
    }

    #correctionsTable tbody td .btn {
        font-weight: 500 !important;
    }

    /* تحسين ألوان النصوص المختلفة */
    #correctionsTable tbody td strong {
        color: #000 !important;
        font-weight: bold !important;
    }

    #correctionsTable tbody td span {
        color: #1a1a1a !important;
    }

    #correctionsTable td {
        vertical-align: middle;
        border: 1px solid #dee2e6;
        padding: 10px 8px;
        color: #1a1a1a !important;
    }

    /* تحسين ألوان الـ badges */
    .badge-danger {
        background-color: #dc3545 !important;
        color: #fff !important;
        font-weight: bold !important;
    }

    .badge-success {
        background-color: #28a745 !important;
        color: #fff !important;
        font-weight: bold !important;
    }

    .badge-primary {
        background-color: #007bff !important;
        color: #fff !important;
        font-weight: bold !important;
    }

    .badge-info {
        background-color: #17a2b8 !important;
        color: #fff !important;
        font-weight: bold !important;
    }

    .badge-warning {
        background-color: #ffc107 !important;
        color: #212529 !important;
        font-weight: bold !important;
    }

    .badge-dark {
        background-color: #343a40 !important;
        color: #fff !important;
        font-weight: bold !important;
    }

    .badge-secondary {
        background-color: #6c757d !important;
        color: #fff !important;
        font-weight: bold !important;
    }

    /* تحسين ألوان الأزرار */
    .btn-sm {
        font-weight: 500 !important;
        font-size: 12px !important;
    }

    /* تحسين أزرار الإجراءات */
    .btn-group .btn {
        margin: 1px;
        font-size: 12px;
        padding: 4px 8px;
    }

    /* تحسين البحث والفلترة */
    .dataTables_filter input {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 6px 12px;
        margin-left: 8px;
    }

    .dataTables_length select {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 4px 8px;
        margin: 0 8px;
    }

    /* تحسين pagination */
    .dataTables_paginate .paginate_button {
        padding: 6px 12px !important;
        margin: 0 2px !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 4px !important;
    }

    .dataTables_paginate .paginate_button.current {
        background: #007bff !important;
        color: white !important;
        border-color: #007bff !important;
    }

    .dataTables_paginate .paginate_button:hover {
        background: #e9ecef !important;
        border-color: #adb5bd !important;
    }

    /* تحسين المعلومات */
    .dataTables_info {
        font-weight: 500;
        color: #6c757d;
    }

    /* تحسين التمرير الأفقي */
    .dataTables_scrollHead,
    .dataTables_scrollBody {
        border: 1px solid #dee2e6;
    }

    /* تحسين الـ badges */
    .badge {
        font-size: 12px;
        padding: 6px 10px;
        border-radius: 6px;
    }

    /* تحسين responsive */
    @media (max-width: 768px) {
        #correctionsTable {
            font-size: 12px;
        }

        .btn-group .btn {
            font-size: 10px;
            padding: 2px 6px;
        }

        .badge {
            font-size: 10px;
            padding: 4px 8px;
        }
    }

    /* تحسين بطاقات الإحصائيات */
    .hover-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .hover-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }

    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }

    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }

    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }

    /* تحسين أيقونات الإحصائيات */
    .card-body .fas {
        opacity: 0.8;
    }

    .hover-card:hover .fas {
        opacity: 1;
        transform: scale(1.1);
        transition: all 0.3s ease;
    }

    /* تحسين النصوص */
    .text-gray-800 {
        color: #5a5c69 !important;
    }

    .text-xs {
        font-size: 0.75rem;
    }

    /* تحسين الجدول للشاشات الصغيرة */
    @media (max-width: 992px) {
        .table-responsive {
            border: 1px solid #dee2e6;
            border-radius: 0.35rem;
        }
    }

    /* تحسينات إضافية لوضوح النص */
    table.dataTable tbody td {
        color: #1a1a1a !important;
        font-weight: 500 !important;
    }

    table.dataTable tbody tr {
        color: #1a1a1a !important;
    }

    /* تحسين ألوان DataTables */
    .dataTables_wrapper {
        color: #1a1a1a !important;
    }

    .dataTables_info {
        color: #1a1a1a !important;
        font-weight: 500 !important;
    }

    .dataTables_length label {
        color: #1a1a1a !important;
        font-weight: 500 !important;
    }

    .dataTables_filter label {
        color: #1a1a1a !important;
        font-weight: 500 !important;
    }

    /* تحسين النصوص العامة */
    .table td, .table th {
        color: #1a1a1a !important;
    }

    /* تأكيد ألوان الأيقونات */
    .fas, .fa {
        color: inherit !important;
    }

    /* تحسين شامل لجميع النصوص في الجدول */
    #correctionsTable * {
        color: #1a1a1a !important;
    }

    #correctionsTable thead * {
        color: #2c3e50 !important;
    }

    /* تحسين خاص للنصوص المهمة */
    #correctionsTable tbody td:first-child,
    #correctionsTable tbody td:nth-child(2) {
        font-weight: 600 !important;
        color: #000 !important;
    }

    /* تحسين تباين الألوان */
    #correctionsTable tbody td {
        text-shadow: none !important;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
</style>

<script>
$(document).ready(function() {
    $('#correctionsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json",
            "emptyTable": "لا توجد تصحيحات متاحة",
            "info": "عرض _START_ إلى _END_ من أصل _TOTAL_ تصحيح",
            "infoEmpty": "عرض 0 إلى 0 من أصل 0 تصحيح",
            "infoFiltered": "(مفلتر من _MAX_ إجمالي التصحيحات)",
            "lengthMenu": "عرض _MENU_ تصحيح",
            "loadingRecords": "جاري التحميل...",
            "processing": "جاري المعالجة...",
            "search": "البحث:",
            "zeroRecords": "لم يتم العثور على تصحيحات مطابقة",
            "paginate": {
                "first": "الأول",
                "last": "الأخير",
                "next": "التالي",
                "previous": "السابق"
            }
        },
        "order": [[ 5, "desc" ]], // ترتيب حسب تاريخ الإضافة
        "pageLength": 10, // عرض 10 صفوف في كل صفحة
        "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "الكل"]],
        "responsive": true,
        "scrollX": true, // تمكين التمرير الأفقي
        "autoWidth": false,
        "columnDefs": [
            { "width": "15%", "targets": [0, 1] }, // الاسم الخطأ والصحيح
            { "width": "15%", "targets": 2 }, // نوع التصحيح
            { "width": "10%", "targets": 3 }, // الحالة
            { "width": "12%", "targets": 4 }, // عدد الاستخدامات
            { "width": "15%", "targets": 5 }, // تاريخ الإضافة
            { "width": "8%", "targets": 6 }, // أضافه
            { "width": "15%", "targets": 7 }, // الإجراءات
            { "orderable": false, "targets": 7 } // منع ترتيب عمود الإجراءات
        ],
        "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
               '<"row"<"col-sm-12"tr>>' +
               '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        "pagingType": "full_numbers"
    });
});

// دوال التصدير والطباعة
function exportToExcel() {
    // الحصول على بيانات الجدول
    var table = $('#correctionsTable').DataTable();
    var data = table.rows().data();

    // إنشاء محتوى CSV
    var csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "الاسم الخطأ,الاسم الصحيح,نوع التصحيح,الحالة,عدد الاستخدامات,تاريخ الإضافة,أضافه\n";

    data.each(function(row, index) {
        // استخراج النص من HTML
        var wrongName = $(row[0]).text();
        var correctName = $(row[1]).text();
        var correctionType = $(row[2]).text();
        var status = $(row[3]).text();
        var usageCount = $(row[4]).text().split('\n')[0];
        var dateAdded = $(row[5]).text().replace('\n', ' ');
        var addedBy = $(row[6]).text();

        csvContent += wrongName + "," + correctName + "," + correctionType + "," +
                     status + "," + usageCount + "," + dateAdded + "," + addedBy + "\n";
    });

    // تحميل الملف
    var encodedUri = encodeURI(csvContent);
    var link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "تصحيحات_الأسماء_" + new Date().toISOString().slice(0,10) + ".csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // إظهار رسالة نجاح
    showAlert('تم تصدير البيانات بنجاح!', 'success');
}

function printTable() {
    // إنشاء نافذة طباعة
    var printWindow = window.open('', '_blank');
    var tableHtml = document.getElementById('correctionsTable').outerHTML;

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تصحيحات الأسماء المخصصة</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
                .badge-danger { background-color: #dc3545; color: white; }
                .badge-success { background-color: #28a745; color: white; }
                .badge-primary { background-color: #007bff; color: white; }
                .badge-info { background-color: #17a2b8; color: white; }
                .badge-warning { background-color: #ffc107; color: black; }
                .badge-dark { background-color: #343a40; color: white; }
                .badge-light { background-color: #f8f9fa; color: black; }
                .badge-secondary { background-color: #6c757d; color: white; }
                .btn { display: none; }
                h1 { text-align: center; color: #333; }
                .print-date { text-align: center; color: #666; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <h1>تصحيحات الأسماء المخصصة</h1>
            <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</div>
            ${tableHtml}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

function refreshTable() {
    // إعادة تحميل الصفحة
    location.reload();
}

function showAlert(message, type) {
    // إنشاء تنبيه مؤقت
    var alertDiv = $(`
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `);

    $('body').append(alertDiv);

    // إزالة التنبيه بعد 3 ثوان
    setTimeout(function() {
        alertDiv.fadeOut(function() {
            $(this).remove();
        });
    }, 3000);
}
</script>
{% endblock %}
