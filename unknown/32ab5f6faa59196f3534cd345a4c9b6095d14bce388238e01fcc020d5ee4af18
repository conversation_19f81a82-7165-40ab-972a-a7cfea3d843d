"""
🚀 محرك البحث السريع والذكي للأسماء
يدعم البحث المرن والسريع في قاعدة بيانات تحتوي على 200,000+ اسم
"""

try:
    from rapidfuzz import fuzz, process
    RAPIDFUZZ_AVAILABLE = True
except ImportError:
    print("⚠️ RapidFuzz غير متاح، سيتم استخدام البحث التقليدي")
    RAPIDFUZZ_AVAILABLE = False

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    print("⚠️ Redis غير متاح، سيتم استخدام التخزين في الذاكرة")
    REDIS_AVAILABLE = False

import json
import time
from typing import List, Dict, Optional, Tuple
import logging

# سيتم تمرير PersonData و db من app.py
PersonData = None
db = None

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FastPersonSearchEngine:
    """محرك البحث السريع للأشخاص"""

    def __init__(self, redis_host='localhost', redis_port=6379, redis_db=0):
        """تهيئة محرك البحث"""
        self.use_redis = False

        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.Redis(
                    host=redis_host,
                    port=redis_port,
                    db=redis_db,
                    decode_responses=True,
                    socket_timeout=5,
                    socket_connect_timeout=5
                )
                # اختبار الاتصال
                self.redis_client.ping()
                self.use_redis = True
                logger.info("✅ تم الاتصال بـ Redis بنجاح")
            except Exception as e:
                logger.warning(f"⚠️ لا يمكن الاتصال بـ Redis: {e}")
                self.use_redis = False
        else:
            logger.info("⚠️ Redis غير متاح، سيتم استخدام التخزين في الذاكرة")

        self.cache_key = "person_search_index"
        self.stats_key = "search_stats"
        self.search_index = None

    def build_search_index(self, force_rebuild=False) -> Dict:
        """بناء فهرس البحث في الذاكرة"""
        start_time = time.time()

        # محاولة جلب الفهرس من Redis أولاً
        if self.use_redis and not force_rebuild:
            try:
                cached_index = self.redis_client.get(self.cache_key)
                if cached_index:
                    self.search_index = json.loads(cached_index)
                    logger.info(f"✅ تم جلب فهرس البحث من Redis ({len(self.search_index)} عنصر)")
                    return self.search_index
            except Exception as e:
                logger.warning(f"⚠️ خطأ في جلب الفهرس من Redis: {e}")

        # بناء الفهرس من قاعدة البيانات
        logger.info("🔄 بناء فهرس البحث من قاعدة البيانات...")

        try:
            # جلب البيانات بكفاءة
            persons = db.session.query(
                PersonData.id,
                PersonData.full_name,
                PersonData.nickname,
                PersonData.national_number,
                PersonData.military_number,
                PersonData.governorate,
                PersonData.agency,
                PersonData.job
            ).all()

            search_index = {}

            for person in persons:
                # إنشاء نص البحث المركب
                search_components = [
                    person.full_name or "",
                    person.nickname or "",
                    person.national_number or "",
                    person.military_number or ""
                ]

                # إضافة معلومات إضافية للبحث
                additional_info = [
                    person.governorate or "",
                    person.agency or "",
                    person.job or ""
                ]

                # النص الأساسي للبحث
                primary_search_text = " ".join(filter(None, search_components)).strip()

                # النص الثانوي للبحث
                secondary_search_text = " ".join(filter(None, additional_info)).strip()

                if primary_search_text:
                    person_data = {
                        'id': person.id,
                        'full_name': person.full_name,
                        'nickname': person.nickname,
                        'national_number': person.national_number,
                        'military_number': person.military_number,
                        'governorate': person.governorate,
                        'agency': person.agency,
                        'job': person.job,
                        'search_text': primary_search_text,
                        'additional_info': secondary_search_text
                    }

                    # استخدام الاسم الكامل كمفتاح أساسي
                    search_index[primary_search_text] = person_data

                    # إضافة مفاتيح إضافية للبحث السريع
                    if person.national_number:
                        search_index[f"رقم_وطني_{person.national_number}"] = person_data

                    if person.military_number:
                        search_index[f"رقم_عسكري_{person.military_number}"] = person_data

            self.search_index = search_index

            # حفظ في Redis للمرات القادمة
            if self.use_redis:
                try:
                    self.redis_client.setex(
                        self.cache_key,
                        3600,  # انتهاء الصلاحية بعد ساعة
                        json.dumps(search_index)
                    )
                    logger.info("✅ تم حفظ الفهرس في Redis")
                except Exception as e:
                    logger.warning(f"⚠️ خطأ في حفظ الفهرس في Redis: {e}")

            build_time = time.time() - start_time
            logger.info(f"✅ تم بناء فهرس البحث بنجاح: {len(search_index)} عنصر في {build_time:.2f} ثانية")

            return search_index

        except Exception as e:
            logger.error(f"❌ خطأ في بناء فهرس البحث: {e}")
            return {}

    def search_persons(self, query: str, limit: int = 10, threshold: int = 60) -> List[Dict]:
        """البحث السريع والذكي عن الأشخاص"""
        if not query or len(query.strip()) < 2:
            return []

        query = query.strip()
        start_time = time.time()

        # التأكد من وجود الفهرس
        if not self.search_index:
            self.build_search_index()

        if not self.search_index:
            logger.error("❌ فهرس البحث غير متاح")
            return []

        try:
            # البحث المباشر أولاً (للأرقام الوطنية والعسكرية)
            direct_matches = []

            # البحث في الأرقام الوطنية والعسكرية
            for key, person_data in self.search_index.items():
                if (key.startswith("رقم_وطني_") and query in key) or \
                   (key.startswith("رقم_عسكري_") and query in key):
                    direct_matches.append((person_data, 100))  # درجة تطابق عالية

            # البحث المرن
            search_keys = [k for k in self.search_index.keys()
                          if not k.startswith(("رقم_وطني_", "رقم_عسكري_"))]

            if RAPIDFUZZ_AVAILABLE:
                # البحث المرن باستخدام RapidFuzz
                fuzzy_matches = process.extract(
                    query,
                    search_keys,
                    scorer=fuzz.WRatio,
                    limit=limit * 2,  # جلب عدد أكبر للفلترة
                    score_cutoff=threshold
                )
            else:
                # البحث التقليدي (fallback)
                fuzzy_matches = []
                query_lower = query.lower()
                for key in search_keys:
                    if query_lower in key.lower():
                        # حساب درجة تطابق بسيطة
                        score = 100 if key.lower() == query_lower else 80
                        fuzzy_matches.append((key, score))
                        if len(fuzzy_matches) >= limit * 2:
                            break

            # دمج النتائج
            all_matches = direct_matches + [(self.search_index[match[0]], match[1]) for match in fuzzy_matches]

            # إزالة المكررات وترتيب النتائج
            seen_ids = set()
            unique_results = []

            for person_data, score in sorted(all_matches, key=lambda x: x[1], reverse=True):
                if person_data['id'] not in seen_ids:
                    seen_ids.add(person_data['id'])
                    person_data['match_score'] = score
                    unique_results.append(person_data)

                    if len(unique_results) >= limit:
                        break

            search_time = time.time() - start_time

            # تسجيل إحصائيات البحث
            self._log_search_stats(query, len(unique_results), search_time)

            logger.info(f"🔍 البحث عن '{query}': {len(unique_results)} نتيجة في {search_time:.3f} ثانية")

            return unique_results

        except Exception as e:
            logger.error(f"❌ خطأ في البحث: {e}")
            return []

    def search_by_partial_name(self, partial_name: str, limit: int = 10) -> List[Dict]:
        """البحث بالاسم الجزئي (للـ autocomplete)"""
        if not partial_name or len(partial_name.strip()) < 2:
            return []

        partial_name = partial_name.strip().lower()

        if not self.search_index:
            self.build_search_index()

        matches = []
        for search_text, person_data in self.search_index.items():
            if not search_text.startswith(("رقم_وطني_", "رقم_عسكري_")):
                if partial_name in search_text.lower():
                    matches.append(person_data)
                    if len(matches) >= limit:
                        break

        return matches

    def _log_search_stats(self, query: str, results_count: int, search_time: float):
        """تسجيل إحصائيات البحث"""
        if not self.use_redis:
            return

        try:
            stats = {
                'query': query,
                'results_count': results_count,
                'search_time': search_time,
                'timestamp': time.time()
            }

            # حفظ آخر 100 عملية بحث
            self.redis_client.lpush(self.stats_key, json.dumps(stats))
            self.redis_client.ltrim(self.stats_key, 0, 99)

        except Exception as e:
            logger.warning(f"⚠️ خطأ في تسجيل الإحصائيات: {e}")

    def get_search_stats(self) -> Dict:
        """جلب إحصائيات البحث"""
        if not self.use_redis:
            return {}

        try:
            stats_list = self.redis_client.lrange(self.stats_key, 0, -1)
            stats = [json.loads(stat) for stat in stats_list]

            if not stats:
                return {}

            total_searches = len(stats)
            avg_time = sum(s['search_time'] for s in stats) / total_searches
            avg_results = sum(s['results_count'] for s in stats) / total_searches

            return {
                'total_searches': total_searches,
                'average_search_time': avg_time,
                'average_results_count': avg_results,
                'recent_searches': stats[:10]
            }

        except Exception as e:
            logger.warning(f"⚠️ خطأ في جلب الإحصائيات: {e}")
            return {}

    def clear_cache(self):
        """مسح التخزين المؤقت"""
        if self.use_redis:
            try:
                self.redis_client.delete(self.cache_key)
                self.redis_client.delete(self.stats_key)
                logger.info("✅ تم مسح التخزين المؤقت")
            except Exception as e:
                logger.warning(f"⚠️ خطأ في مسح التخزين المؤقت: {e}")

        self.search_index = None

# دالة لتهيئة محرك البحث مع النماذج
def initialize_search_engine(person_data_model, database):
    """تهيئة محرك البحث مع النماذج المطلوبة"""
    global PersonData, db, search_engine
    PersonData = person_data_model
    db = database
    search_engine = FastPersonSearchEngine()
    return search_engine

# متغير عام لمحرك البحث
search_engine = None
