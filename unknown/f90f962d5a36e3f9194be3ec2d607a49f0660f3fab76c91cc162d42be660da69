#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 تشغيل نظام التدريب والتأهيل مع النظام الذكي للنسخ الاحتياطي
Enhanced Start Script with Smart Backup System
"""

import os
import sys
import webbrowser
import signal
import atexit
from threading import Timer

def signal_handler(signum, frame):
    """معالج إشارات النظام للإغلاق الآمن"""
    print(f"\n🛑 تم استلام إشارة الإغلاق ({signum})")
    print("⏳ جاري إنشاء نسخة احتياطية أخيرة...")
    
    try:
        # استيراد النظام الذكي
        from backup_utils import smart_backup_manager
        
        # إنشاء نسخة احتياطية أخيرة
        if hasattr(smart_backup_manager, 'app') and smart_backup_manager.app:
            with smart_backup_manager.app.app_context():
                success, message, backup_file = smart_backup_manager.create_smart_backup(backup_type="exit")
                if success:
                    print(f"✅ تم إنشاء النسخة الاحتياطية الأخيرة: {backup_file}")
                else:
                    print(f"❌ فشل في النسخة الاحتياطية الأخيرة: {message}")
        
        # إيقاف النظام الذكي
        smart_backup_manager.stop_automatic_backup()
        
    except Exception as e:
        print(f"⚠️ خطأ في النسخة الاحتياطية الأخيرة: {str(e)}")
    
    print("🏁 تم إغلاق النظام بأمان")
    sys.exit(0)

def cleanup_on_exit():
    """تنظيف عند الخروج"""
    try:
        from backup_utils import smart_backup_manager
        smart_backup_manager.cleanup_on_exit()
    except:
        pass

def open_browser():
    """فتح المتصفح"""
    try:
        webbrowser.open('http://localhost:5000')
    except:
        pass

def main():
    print("🚀 نظام التدريب والتأهيل - النسخة الذكية")
    print("=" * 50)
    
    # تسجيل معالجات الإشارات
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # إنهاء العملية
    atexit.register(cleanup_on_exit)
    
    try:
        # استيراد التطبيق
        print("🔄 تحميل التطبيق...")
        from app import app, db
        from backup_utils import smart_backup_manager
        
        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        print("🔄 التحقق من قاعدة البيانات...")
        with app.app_context():
            db.create_all()

            # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
            from app import User
            from werkzeug.security import generate_password_hash

            if not User.query.first():
                print("🔄 إنشاء مستخدم افتراضي...")
                hashed_password = generate_password_hash('admin')
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    password=hashed_password,
                    role='admin'
                )
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء المستخدم الافتراضي: admin / admin")

            # تهيئة النظام الذكي للنسخ الاحتياطي
            print("🔄 تهيئة النظام الذكي للنسخ الاحتياطي...")
            smart_backup_manager.init_app(app)
        
        print("✅ النظام جاهز!")
        print("🔒 النظام الذكي للنسخ الاحتياطي مفعل")
        print("📊 سيتم إنشاء نسخ احتياطية تلقائية كل 30 دقيقة")
        print("🌐 http://localhost:5000")
        print("🔑 admin / admin123")
        print("=" * 50)
        print("💡 للإغلاق الآمن: اضغط Ctrl+C")
        print("=" * 50)
        
        # فتح المتصفح بعد 3 ثوان
        Timer(3.0, open_browser).start()
        
        # تشغيل الخادم
        print("🌟 الخادم يعمل الآن...")
        app.run(
            host='localhost',
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
        signal_handler(signal.SIGINT, None)
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("💡 تأكد من تثبيت جميع المتطلبات:")
        print("   pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n👋 شكراً لاستخدام نظام التدريب والتأهيل!")

if __name__ == '__main__':
    main()
