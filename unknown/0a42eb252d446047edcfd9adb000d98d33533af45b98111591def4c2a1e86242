#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف تشغيل النظام المحسن
"""

import os
import sys
import webbrowser
import time
from threading import Timer

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def open_browser():
    """فتح المتصفح تلقائياً بعد 3 ثوان"""
    urls_to_try = [
        'http://localhost:5000',
        'http://127.0.0.1:5000',
        'http://0.0.0.0:5000'
    ]
    
    for url in urls_to_try:
        try:
            print(f"🌐 محاولة فتح: {url}")
            webbrowser.open(url)
            break
        except:
            continue

def main():
    print("=" * 60)
    print("🚀 نظام التدريب والتأهيل")
    print("=" * 60)
    print("🔄 جاري تحميل التطبيق...")
    
    try:
        from app import app
        
        print("✅ تم تحميل التطبيق بنجاح")
        print("🚀 بدء تشغيل الخادم...")
        print()
        print("📍 الروابط المتاحة:")
        print("   • http://localhost:5000")
        print("   • http://127.0.0.1:5000")
        print()
        print("🔑 بيانات تسجيل الدخول:")
        print("   • اسم المستخدم: admin")
        print("   • كلمة المرور: admin")
        print()
        print("📋 الصفحات المهمة:")
        print("   • /person_data_table - جدول بيانات الأشخاص")
        print("   • /simple_person_list - قائمة الأشخاص البسيطة")
        print("   • /manage_participants/1 - إدارة المشاركين")
        print()
        print("=" * 60)
        print("⚡ الخادم جاهز! سيتم فتح المتصفح تلقائياً...")
        print("=" * 60)
        
        # فتح المتصفح تلقائياً بعد 3 ثوان
        Timer(3.0, open_browser).start()
        
        # تشغيل الخادم
        app.run(
            host='0.0.0.0',  # الاستماع على جميع العناوين
            port=5000,
            debug=False,  # إيقاف debug لتجنب المشاكل
            threaded=True,
            use_reloader=False
        )
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print("🔧 تأكد من وجود ملف app.py في نفس المجلد")
        print("🔧 تأكد من تثبيت Flask: pip install flask")
        
    except OSError as e:
        if "Address already in use" in str(e):
            print("❌ المنفذ 5000 مستخدم من برنامج آخر")
            print("🔧 أغلق البرنامج الآخر أو استخدم منفذ مختلف")
        else:
            print(f"❌ خطأ في الشبكة: {e}")
            
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        print("🔧 تفاصيل الخطأ:")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\n" + "=" * 60)
        print("🔴 تم إيقاف الخادم")
        print("=" * 60)
        input("⏸️  اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
