{% extends "layout.html" %}
{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">البحث عن الدورات</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                {{ form.hidden_tag() }}
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.title.label(class="form-label") }}
                            {{ form.title(class="form-control") }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.category.label(class="form-label") }}
                            {{ form.category(class="form-select") }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.level.label(class="form-label") }}
                            {{ form.level(class="form-select") }}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.trainer_id.label(class="form-label") }}
                            {{ form.trainer_id(class="form-select") }}
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="form-check mt-4">
                            {{ form.use_hijri_dates(class="form-check-input") }}
                            {{ form.use_hijri_dates.label(class="form-check-label") }}
                        </div>
                    </div>
                </div>
                
                <div class="card mb-3" id="gregorian-dates">
                    <div class="card-header">
                        <h5>البحث بالتاريخ الميلادي</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.start_date.label(class="form-label") }}
                                    {{ form.start_date(class="form-control") }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.end_date.label(class="form-label") }}
                                    {{ form.end_date(class="form-control") }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-3" id="hijri-dates" style="display: none;">
                    <div class="card-header">
                        <h5>البحث بالتاريخ الهجري</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">تاريخ البدء (هجري)</label>
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form.start_date_hijri_day(class="form-select") }}
                                        <small class="form-text">اليوم</small>
                                    </div>
                                    <div class="col-md-5">
                                        {{ form.start_date_hijri_month(class="form-select") }}
                                        <small class="form-text">الشهر</small>
                                    </div>
                                    <div class="col-md-4">
                                        {{ form.start_date_hijri_year(class="form-select") }}
                                        <small class="form-text">السنة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">تاريخ النهاية (هجري)</label>
                                <div class="row">
                                    <div class="col-md-3">
                                        {{ form.end_date_hijri_day(class="form-select") }}
                                        <small class="form-text">اليوم</small>
                                    </div>
                                    <div class="col-md-5">
                                        {{ form.end_date_hijri_month(class="form-select") }}
                                        <small class="form-text">الشهر</small>
                                    </div>
                                    <div class="col-md-4">
                                        {{ form.end_date_hijri_year(class="form-select") }}
                                        <small class="form-text">السنة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    {{ form.submit(class="btn btn-primary") }}
                    <a href="{{ url_for('courses') }}" class="btn btn-secondary">العودة</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const useHijriDates = document.getElementById('use_hijri_dates');
        const gregorianDates = document.getElementById('gregorian-dates');
        const hijriDates = document.getElementById('hijri-dates');
        
        // تحديث عرض حقول التاريخ عند تحميل الصفحة
        updateDateFields();
        
        // تحديث عرض حقول التاريخ عند تغيير الخيار
        useHijriDates.addEventListener('change', updateDateFields);
        
        function updateDateFields() {
            if (useHijriDates.checked) {
                gregorianDates.style.display = 'none';
                hijriDates.style.display = 'block';
            } else {
                gregorianDates.style.display = 'block';
                hijriDates.style.display = 'none';
            }
        }
    });
</script>
{% endblock %}
