#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح تفاصيل التقييم
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db

def fix_evaluation_details():
    """إصلاح تفاصيل التقييم"""
    
    with app.app_context():
        print("🔧 إصلاح تفاصيل التقييم...")
        
        # فحص التقييمات الموجودة
        evaluations = db.engine.execute("SELECT * FROM participant_evaluations").fetchall()
        print(f"📊 عدد التقييمات: {len(evaluations)}")
        
        for evaluation in evaluations:
            print(f"\n📝 التقييم رقم: {evaluation['id']}")
            print(f"   - المشارك: {evaluation['participant_id']}")
            print(f"   - الدرجة الإجمالية: {evaluation['total_score']}")
            
            # فحص تفاصيل التقييم
            details = db.engine.execute("""
                SELECT ed.*, ec.name as criteria_name 
                FROM evaluation_details ed
                JOIN evaluation_criteria ec ON ed.criteria_id = ec.id
                WHERE ed.evaluation_id = ?
            """, (evaluation['id'],)).fetchall()
            
            print(f"   - عدد التفاصيل: {len(details)}")
            
            if details:
                for detail in details:
                    print(f"     * {detail['criteria_name']}: {detail['score']} ({detail['notes']})")
            else:
                print("   - لا توجد تفاصيل، سيتم إضافتها...")
                
                # إضافة تفاصيل افتراضية
                criteria_scores = [
                    (evaluation['id'], 1, 18.0, 'حضور ممتاز'),
                    (evaluation['id'], 2, 19.0, 'مشاركة فعالة'),
                    (evaluation['id'], 3, 27.0, 'أداء جيد'),
                    (evaluation['id'], 4, 26.5, 'تطبيق ممتاز'),
                    (evaluation['id'], 6, 14.0, 'مهارات جيدة')
                ]
                
                for eval_id, criteria_id, score, notes in criteria_scores:
                    db.engine.execute("""
                        INSERT INTO evaluation_details (evaluation_id, criteria_id, score, notes)
                        VALUES (?, ?, ?, ?)
                    """, (eval_id, criteria_id, score, notes))
                
                print("   ✅ تم إضافة التفاصيل")
        
        # اختبار الاستعلام المحدث
        print("\n🔍 اختبار الاستعلام المحدث...")
        
        result = db.engine.execute("""
            SELECT pe.*, cp.*, pd.full_name, pd.national_number, pd.military_number,
                   ed1.score as attendance_score, ed1.notes as attendance_notes,
                   ed2.score as participation_score, ed2.notes as participation_notes,
                   ed3.score as theory_score, ed3.notes as theory_notes,
                   ed4.score as practical_score, ed4.notes as practical_notes,
                   ed5.score as personal_score, ed5.notes as personal_notes,
                   u.username as evaluator_name
            FROM participant_evaluations pe
            JOIN course_participant cp ON pe.participant_id = cp.id
            JOIN person_data pd ON cp.personal_data_id = pd.id
            LEFT JOIN evaluation_details ed1 ON pe.id = ed1.evaluation_id AND ed1.criteria_id = 1
            LEFT JOIN evaluation_details ed2 ON pe.id = ed2.evaluation_id AND ed2.criteria_id = 2
            LEFT JOIN evaluation_details ed3 ON pe.id = ed3.evaluation_id AND ed3.criteria_id = 3
            LEFT JOIN evaluation_details ed4 ON pe.id = ed4.evaluation_id AND ed4.criteria_id = 4
            LEFT JOIN evaluation_details ed5 ON pe.id = ed5.evaluation_id AND ed5.criteria_id = 6
            LEFT JOIN user u ON pe.evaluator_id = u.id
            WHERE pe.course_id = 1 AND pe.participant_id = 1
            ORDER BY pe.created_at DESC
            LIMIT 1
        """).fetchone()
        
        if result:
            print("✅ الاستعلام يعمل:")
            print(f"   - المشارك: {result['full_name']}")
            print(f"   - الحضور: {result['attendance_score']}")
            print(f"   - المشاركة: {result['participation_score']}")
            print(f"   - النظري: {result['theory_score']}")
            print(f"   - العملي: {result['practical_score']}")
            print(f"   - المهارات: {result['personal_score']}")
        else:
            print("❌ لا توجد نتائج")

if __name__ == "__main__":
    fix_evaluation_details()
