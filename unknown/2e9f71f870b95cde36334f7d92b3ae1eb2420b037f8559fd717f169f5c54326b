from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from sqlalchemy import or_

# سيتم استيراد النماذج من app.py
db = None
PersonalData = None
Governorate = None
Directorate = None
Village = None
QualificationType = None
Agency = None
MaritalStatus = None

def init_api(app_db, app_models):
    global db, PersonalData, Governorate, Directorate, Village, QualificationType, Agency, MaritalStatus
    db = app_db
    PersonalData = app_models['PersonalData']
    Governorate = app_models['Governorate']
    Directorate = app_models['Directorate']
    Village = app_models['Village']
    QualificationType = app_models['QualificationType']
    Agency = app_models['Agency']
    MaritalStatus = app_models['MaritalStatus']

api = Blueprint('api', __name__)

@api.route('/personal_data', methods=['GET'])
@login_required
def get_personal_data():
    """
    الحصول على البيانات الشخصية بتنسيق DataTables
    """
    # الحصول على معلمات DataTables
    draw = request.args.get('draw', type=int, default=1)
    start = request.args.get('start', type=int, default=0)
    length = request.args.get('length', type=int, default=10)
    search_value = request.args.get('search[value]', default='')

    # إنشاء استعلام أساسي
    query = PersonalData.query

    # تطبيق البحث إذا تم توفير قيمة البحث
    if search_value:
        query = query.filter(
            or_(
                PersonalData.full_name.like(f'%{search_value}%'),
                PersonalData.nickname.like(f'%{search_value}%'),
                PersonalData.national_number.like(f'%{search_value}%'),
                PersonalData.military_number.like(f'%{search_value}%'),
                PersonalData.job_title.like(f'%{search_value}%'),
                PersonalData.work_place_text.like(f'%{search_value}%'),
                PersonalData.phone_yemen_mobile.like(f'%{search_value}%')
            )
        )

    # الحصول على إجمالي عدد السجلات
    total_records = query.count()
    total_filtered = total_records

    # تطبيق الترتيب والتصفية
    order_column_idx = request.args.get('order[0][column]', type=int, default=1)
    order_dir = request.args.get('order[0][dir]', default='asc')

    # تحديد عمود الترتيب
    if order_column_idx == 1:
        order_column = PersonalData.full_name
    elif order_column_idx == 2:
        order_column = PersonalData.nickname
    elif order_column_idx == 3:
        order_column = PersonalData.age
    elif order_column_idx == 13:
        order_column = PersonalData.national_number
    elif order_column_idx == 14:
        order_column = PersonalData.military_number
    elif order_column_idx == 15:
        order_column = PersonalData.phone_yemen_mobile
    else:
        order_column = PersonalData.full_name

    # تطبيق اتجاه الترتيب
    if order_dir == 'desc':
        query = query.order_by(order_column.desc())
    else:
        query = query.order_by(order_column.asc())

    # تطبيق الصفحات
    query = query.offset(start).limit(length)

    # الحصول على البيانات
    records = query.all()

    # تحويل البيانات إلى تنسيق JSON
    data = []
    for record in records:
        data.append({
            'id': record.id,
            'full_name': record.full_name,
            'nickname': record.nickname if record.nickname else '',
            'age': record.age if record.age else '',
            'governorate_id': record.governorate_id,
            'governorate_name': record.governorate.name if record.governorate else '',
            'directorate_id': record.directorate_id,
            'directorate_name': record.directorate.name if record.directorate else '',
            # 'uzla': record.uzla if record.uzla else '',
            'village_id': record.village_id,
            'village_name': record.village.name if record.village else '',
            'qualification_type_id': record.qualification_type_id,
            'qualification_name': record.qualification_type.name if record.qualification_type else '',
            'marital_status_id': record.marital_status_id,
            'marital_status_name': record.marital_status.name if record.marital_status else '',
            'job_title': record.job_title if record.job_title else '',
            'agency_id': record.agency_id,
            'agency_name': record.agency.name if record.agency else '',
            'work_place_text': record.work_place_text if record.work_place_text else '',
            'national_number': record.national_number if record.national_number else '',
            'military_number': record.military_number if record.military_number else '',
            'phone_yemen_mobile': record.phone_yemen_mobile if record.phone_yemen_mobile else '',
            'work_number': record.work_number if record.work_number else '',
            'work_rank': record.work_rank if record.work_rank else ''
        })

    # إعداد استجابة DataTables
    response = {
        'draw': draw,
        'recordsTotal': total_records,
        'recordsFiltered': total_filtered,
        'data': data
    }

    return jsonify(response)

@api.route('/personal_data/<int:id>', methods=['GET'])
@login_required
def get_personal_data_by_id(id):
    """
    الحصول على بيانات شخصية محددة بواسطة المعرف
    """
    record = PersonalData.query.get(id)

    if not record:
        return jsonify({
            'success': False,
            'message': 'لم يتم العثور على السجل'
        }), 404

    data = {
        'id': record.id,
        'full_name': record.full_name,
        'nickname': record.nickname if record.nickname else '',
        'age': record.age if record.age else '',
        'governorate_id': record.governorate_id,
        'directorate_id': record.directorate_id,
        'uzla': record.uzla if record.uzla else '',
        'village_id': record.village_id,
        'qualification_type_id': record.qualification_type_id,
        'marital_status_id': record.marital_status_id,
        'job_title': record.job_title if record.job_title else '',
        'agency_id': record.agency_id,
        'work_place_text': record.work_place_text if record.work_place_text else '',
        'national_number': record.national_number if record.national_number else '',
        'military_number': record.military_number if record.military_number else '',
        'phone_yemen_mobile': record.phone_yemen_mobile if record.phone_yemen_mobile else '',
        'work_number': record.work_number if record.work_number else '',
        'work_rank': record.work_rank if record.work_rank else ''
    }

    return jsonify({
        'success': True,
        'data': data
    })

@api.route('/personal_data', methods=['POST'])
@login_required
def create_personal_data():
    """
    إنشاء بيانات شخصية جديدة
    """
    if current_user.role != 'admin':
        return jsonify({
            'success': False,
            'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'
        }), 403

    data = request.json

    # التحقق من وجود البيانات المطلوبة
    if not data.get('full_name'):
        return jsonify({
            'success': False,
            'message': 'الاسم الشخصي مطلوب'
        }), 400

    try:
        # إنشاء سجل جديد - معالجة جميع الحقول كنصوص بسيطة
        record = PersonalData(
            user_id=current_user.id,
            full_name=data.get('full_name'),
            nickname=data.get('nickname') if data.get('nickname') else None,
            age=data.get('age') if data.get('age') else None,
            # تخزين أسماء الحقول الترميزية كنصوص
            governorate_id=None,
            directorate_id=None,
            uzla=data.get('uzla') if data.get('uzla') else None,
            village_id=None,
            qualification_type_id=None,
            marital_status_id=None,
            job_title=data.get('job_title') if data.get('job_title') else None,
            agency_id=None,
            # تخزين النصوص في الحقول النصية
            work_place_text=data.get('work_place_text') if data.get('work_place_text') else None,
            national_number=data.get('national_number') if data.get('national_number') else None,
            military_number=data.get('military_number') if data.get('military_number') else None,
            phone_yemen_mobile=data.get('phone_yemen_mobile') if data.get('phone_yemen_mobile') else None,
            work_number=data.get('work_number') if data.get('work_number') else None,
            work_rank=data.get('work_rank') if data.get('work_rank') else None
        )

        db.session.add(record)
        db.session.commit()

        return jsonify({
            'success': True,
            'id': record.id,
            'message': 'تم إنشاء السجل بنجاح'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@api.route('/personal_data/<int:id>', methods=['PUT'])
@login_required
def update_personal_data(id):
    """
    تحديث بيانات شخصية موجودة
    """
    if current_user.role != 'admin':
        return jsonify({
            'success': False,
            'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'
        }), 403

    data = request.json

    # التحقق من وجود البيانات المطلوبة
    if not data.get('full_name'):
        return jsonify({
            'success': False,
            'message': 'الاسم الشخصي مطلوب'
        }), 400

    try:
        # البحث عن السجل
        record = PersonalData.query.get(id)

        if not record:
            return jsonify({
                'success': False,
                'message': 'لم يتم العثور على السجل'
            }), 404

        # تحديث البيانات - معالجة جميع الحقول كنصوص بسيطة
        record.full_name = data.get('full_name')
        record.nickname = data.get('nickname') if data.get('nickname') else None
        record.age = data.get('age') if data.get('age') else None
        # لا نقوم بتحديث الحقول الترميزية
        # record.governorate_id = data.get('governorate_id') if data.get('governorate_id') else None
        # record.directorate_id = data.get('directorate_id') if data.get('directorate_id') else None
        record.uzla = data.get('uzla') if data.get('uzla') else None
        # record.village_id = data.get('village_id') if data.get('village_id') else None
        # record.qualification_type_id = data.get('qualification_type_id') if data.get('qualification_type_id') else None
        # record.marital_status_id = data.get('marital_status_id') if data.get('marital_status_id') else None
        record.job_title = data.get('job_title') if data.get('job_title') else None
        # record.agency_id = data.get('agency_id') if data.get('agency_id') else None
        record.work_place_text = data.get('work_place_text') if data.get('work_place_text') else None
        record.national_number = data.get('national_number') if data.get('national_number') else None
        record.military_number = data.get('military_number') if data.get('military_number') else None
        record.phone_yemen_mobile = data.get('phone_yemen_mobile') if data.get('phone_yemen_mobile') else None
        record.work_number = data.get('work_number') if data.get('work_number') else None
        record.work_rank = data.get('work_rank') if data.get('work_rank') else None

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم تحديث السجل بنجاح'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@api.route('/personal_data/<int:id>', methods=['DELETE'])
@login_required
def delete_personal_data(id):
    """
    حذف بيانات شخصية
    """
    if current_user.role != 'admin':
        return jsonify({
            'success': False,
            'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'
        }), 403

    try:
        # البحث عن السجل
        record = PersonalData.query.get(id)

        if not record:
            return jsonify({
                'success': False,
                'message': 'لم يتم العثور على السجل'
            }), 404

        # حذف السجل
        db.session.delete(record)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حذف السجل بنجاح'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500
