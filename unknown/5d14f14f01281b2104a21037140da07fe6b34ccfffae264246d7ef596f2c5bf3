#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# إنشاء ملف اختبار سريع
data = {
    'الاسم الشخصي': [
        'مرتضي علي',
        'عيسي محمد', 
        'ابو_الدين صالح',
        'ام_الخير فاطمة',
        'محمد123 أحمد',
        'علي@صالح حسن',
        'الحوثى المرتضي',
        'ايمان اسراء',
        'عبداللة احمد'
    ]
}

df = pd.DataFrame(data)

# حفظ الملف
with pd.ExcelWriter('اختبار_سريع.xlsx', engine='openpyxl') as writer:
    df.to_excel(writer, sheet_name='البيانات', index=False)
    worksheet = writer.sheets['البيانات']
    worksheet.sheet_view.rightToLeft = True

print("✅ تم إنشاء ملف اختبار_سريع.xlsx")
print("📊 يحتوي على 9 أسماء تحتاج تصحيح")
print("🎯 استخدم هذا الملف لاختبار التصحيحات المخصصة")
