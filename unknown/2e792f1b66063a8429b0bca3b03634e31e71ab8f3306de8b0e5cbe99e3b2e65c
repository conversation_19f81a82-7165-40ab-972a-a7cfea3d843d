#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار صفحة إدارة البيانات بالإكسل
"""

import requests

def test_excel_management_page():
    """اختبار صفحة إدارة البيانات بالإكسل"""
    try:
        print("🔗 اختبار صفحة إدارة البيانات بالإكسل")
        print("=" * 50)
        
        # اختبار الصفحة الرئيسية
        print("📋 اختبار: /person_data/person_data_table")
        response = requests.get('http://127.0.0.1:5000/person_data/person_data_table', allow_redirects=False)
        print(f"   الحالة: {response.status_code}")
        
        # اختبار صفحة إدارة Excel
        print("\n📊 اختبار: /person_data/person_data_excel")
        response = requests.get('http://127.0.0.1:5000/person_data/person_data_excel', allow_redirects=False)
        print(f"   الحالة: {response.status_code}")
        
        # اختبار مسارات التصدير
        export_routes = [
            '/person_data/person_data_export',
            '/person_data/export_duplicates',
            '/person_data/person_data_template',
            '/person_data/create_sample_excel'
        ]
        
        print("\n📤 اختبار مسارات التصدير:")
        for route in export_routes:
            response = requests.get(f'http://127.0.0.1:5000{route}', allow_redirects=False)
            status = "✅" if response.status_code in [200, 302] else "❌"
            print(f"   {status} {route}: {response.status_code}")
        
        # اختبار مسارات التحليل
        analysis_routes = [
            '/person_data/name_analysis',
            '/person_data/duplicate_names'
        ]
        
        print("\n🔍 اختبار مسارات التحليل:")
        for route in analysis_routes:
            response = requests.get(f'http://127.0.0.1:5000{route}', allow_redirects=False)
            status = "✅" if response.status_code in [200, 302] else "❌"
            print(f"   {status} {route}: {response.status_code}")
        
        print("\n🎉 تم الانتهاء من الاختبار!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")

if __name__ == "__main__":
    test_excel_management_page()
