<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المكتبات المحلية</title>
    
    <!-- Bootstrap RTL CSS - محلي -->
    <link rel="stylesheet" href="static/libs/bootstrap/bootstrap.rtl.min.css">
    
    <!-- Font Awesome - محلي -->
    <link rel="stylesheet" href="static/libs/fontawesome/all.min.css">
    
    <!-- DataTables CSS - محلي -->
    <link rel="stylesheet" href="static/libs/datatables/dataTables.bootstrap5.min.css">
    
    <!-- Select2 CSS - محلي -->
    <link rel="stylesheet" href="static/libs/select2/select2.min.css">
    
    <!-- DevExtreme CSS - محلي -->
    <link rel="stylesheet" href="static/libs/devextreme/dx.light.css">
    
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-check-circle"></i>
                    اختبار المكتبات المحلية
                </h1>
                
                <!-- اختبار Bootstrap -->
                <div class="test-section">
                    <h3><i class="fas fa-palette"></i> اختبار Bootstrap</h3>
                    <div class="alert alert-info">
                        <strong>Bootstrap يعمل!</strong> هذا تنبيه من Bootstrap.
                    </div>
                    <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample">
                        اختبار Collapse
                    </button>
                    <div class="collapse mt-2" id="collapseExample">
                        <div class="card card-body">
                            Bootstrap Collapse يعمل بشكل صحيح!
                        </div>
                    </div>
                </div>
                
                <!-- اختبار Font Awesome -->
                <div class="test-section">
                    <h3><i class="fas fa-icons"></i> اختبار Font Awesome</h3>
                    <p>الأيقونات التالية يجب أن تظهر بشكل صحيح:</p>
                    <div class="row">
                        <div class="col-md-3"><i class="fas fa-home fa-2x"></i> Home</div>
                        <div class="col-md-3"><i class="fas fa-user fa-2x"></i> User</div>
                        <div class="col-md-3"><i class="fas fa-cog fa-2x"></i> Settings</div>
                        <div class="col-md-3"><i class="fas fa-chart-bar fa-2x"></i> Chart</div>
                    </div>
                </div>
                
                <!-- اختبار DataTables -->
                <div class="test-section">
                    <h3><i class="fas fa-table"></i> اختبار DataTables</h3>
                    <table id="testTable" class="table table-striped">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>العمر</th>
                                <th>المدينة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr><td>أحمد محمد</td><td>25</td><td>الرياض</td></tr>
                            <tr><td>فاطمة علي</td><td>30</td><td>جدة</td></tr>
                            <tr><td>محمد أحمد</td><td>28</td><td>الدمام</td></tr>
                            <tr><td>نورا سعد</td><td>22</td><td>مكة</td></tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- اختبار Select2 -->
                <div class="test-section">
                    <h3><i class="fas fa-list"></i> اختبار Select2</h3>
                    <select id="testSelect" class="form-select" style="width: 100%">
                        <option value="">اختر مدينة</option>
                        <option value="riyadh">الرياض</option>
                        <option value="jeddah">جدة</option>
                        <option value="dammam">الدمام</option>
                        <option value="mecca">مكة</option>
                    </select>
                </div>
                
                <!-- اختبار Chart.js -->
                <div class="test-section">
                    <h3><i class="fas fa-chart-pie"></i> اختبار Chart.js</h3>
                    <canvas id="testChart" width="400" height="200"></canvas>
                </div>
                
                <!-- نتائج الاختبار -->
                <div class="test-section">
                    <h3><i class="fas fa-clipboard-check"></i> نتائج الاختبار</h3>
                    <div id="testResults">
                        <p>جاري تشغيل الاختبارات...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery - محلي -->
    <script src="static/libs/jquery/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS - محلي -->
    <script src="static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS - محلي -->
    <script src="static/libs/datatables/jquery.dataTables.min.js"></script>
    <script src="static/libs/datatables/dataTables.bootstrap5.min.js"></script>
    
    <!-- Select2 JS - محلي -->
    <script src="static/libs/select2/select2.min.js"></script>
    
    <!-- Chart.js - محلي -->
    <script src="static/libs/chartjs/chart.min.js"></script>
    
    <script>
        $(document).ready(function() {
            let results = [];
            
            // اختبار jQuery
            try {
                if (typeof $ !== 'undefined') {
                    results.push('<p class="success"><i class="fas fa-check"></i> jQuery: يعمل بشكل صحيح</p>');
                } else {
                    results.push('<p class="error"><i class="fas fa-times"></i> jQuery: لا يعمل</p>');
                }
            } catch(e) {
                results.push('<p class="error"><i class="fas fa-times"></i> jQuery: خطأ - ' + e.message + '</p>');
            }
            
            // اختبار Bootstrap
            try {
                if (typeof bootstrap !== 'undefined') {
                    results.push('<p class="success"><i class="fas fa-check"></i> Bootstrap: يعمل بشكل صحيح</p>');
                } else {
                    results.push('<p class="error"><i class="fas fa-times"></i> Bootstrap: لا يعمل</p>');
                }
            } catch(e) {
                results.push('<p class="error"><i class="fas fa-times"></i> Bootstrap: خطأ - ' + e.message + '</p>');
            }
            
            // اختبار DataTables
            try {
                $('#testTable').DataTable({
                    "language": {
                        "search": "البحث:",
                        "lengthMenu": "عرض _MENU_ عنصر",
                        "info": "عرض _START_ إلى _END_ من _TOTAL_ عنصر",
                        "paginate": {
                            "first": "الأول",
                            "last": "الأخير",
                            "next": "التالي",
                            "previous": "السابق"
                        }
                    }
                });
                results.push('<p class="success"><i class="fas fa-check"></i> DataTables: يعمل بشكل صحيح</p>');
            } catch(e) {
                results.push('<p class="error"><i class="fas fa-times"></i> DataTables: خطأ - ' + e.message + '</p>');
            }
            
            // اختبار Select2
            try {
                $('#testSelect').select2({
                    placeholder: "اختر مدينة",
                    allowClear: true
                });
                results.push('<p class="success"><i class="fas fa-check"></i> Select2: يعمل بشكل صحيح</p>');
            } catch(e) {
                results.push('<p class="error"><i class="fas fa-times"></i> Select2: خطأ - ' + e.message + '</p>');
            }
            
            // اختبار Chart.js
            try {
                const ctx = document.getElementById('testChart').getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل'],
                        datasets: [{
                            label: 'المبيعات',
                            data: [12, 19, 3, 5],
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                results.push('<p class="success"><i class="fas fa-check"></i> Chart.js: يعمل بشكل صحيح</p>');
            } catch(e) {
                results.push('<p class="error"><i class="fas fa-times"></i> Chart.js: خطأ - ' + e.message + '</p>');
            }
            
            // عرض النتائج
            $('#testResults').html(results.join(''));
            
            // إضافة ملخص
            const successCount = results.filter(r => r.includes('success')).length;
            const totalCount = results.length;
            
            if (successCount === totalCount) {
                $('#testResults').append('<div class="alert alert-success mt-3"><strong>ممتاز!</strong> جميع المكتبات تعمل بشكل صحيح (' + successCount + '/' + totalCount + ')</div>');
            } else {
                $('#testResults').append('<div class="alert alert-warning mt-3"><strong>تحذير!</strong> بعض المكتبات لا تعمل (' + successCount + '/' + totalCount + ')</div>');
            }
        });
    </script>
</body>
</html>
