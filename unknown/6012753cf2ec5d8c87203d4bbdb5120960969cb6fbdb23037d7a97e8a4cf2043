#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف لإنشاء ملف إكسل اختبار لاستيراد البيانات
"""

import pandas as pd

def create_test_excel():
    """إنشاء ملف إكسل اختبار"""
    
    # البيانات الاختبارية
    data = [
        {
            'الاسم الشخصي': 'أحمد محمد علي',
            'الاسم المستعار': 'أبو محمد',
            'العمر': 35,
            'المحافظة': 'صنعاء',
            'المديرية': 'السبعين',
            'العزلة': 'بني الحارث',
            'الحي/القرية': 'حي الجامعة',
            'المؤهل العلمي': 'بكالوريوس',
            'الحالة الاجتماعية': 'متزوج',
            'العمل': 'مهندس برمجيات',
            'الإدارة': 'وزارة الدفاع',
            'مكان العمل': 'مركز التدريب',
            'الرقم الوطني': '1234567890',
            'الرقم العسكري': '987654321',
            'رقم التلفون': '777123456'
        },
        {
            'الاسم الشخصي': 'فاطمة أحمد محمد',
            'الاسم المستعار': 'أم أحمد',
            'العمر': 28,
            'المحافظة': 'عدن',
            'المديرية': 'المنصورة',
            'العزلة': 'الشيخ عثمان',
            'الحي/القرية': 'المنصورة الشمالية',
            'المؤهل العلمي': 'ماجستير',
            'الحالة الاجتماعية': 'متزوجة',
            'العمل': 'مدرسة',
            'الإدارة': 'وزارة التربية',
            'مكان العمل': 'مدرسة الثورة',
            'الرقم الوطني': '0987654321',
            'الرقم العسكري': '123456789',
            'رقم التلفون': '733987654'
        },
        {
            'الاسم الشخصي': 'محمد علي حسن',
            'الاسم المستعار': 'أبو علي',
            'العمر': 42,
            'المحافظة': 'تعز',
            'المديرية': 'صالة',
            'العزلة': 'الأشراف',
            'الحي/القرية': 'حي الوحدة',
            'المؤهل العلمي': 'دبلوم',
            'الحالة الاجتماعية': 'متزوج',
            'العمل': 'فني',
            'الإدارة': 'وزارة الصحة',
            'مكان العمل': 'مستشفى الثورة',
            'الرقم الوطني': '1122334455',
            'الرقم العسكري': '556677889',
            'رقم التلفون': '771234567'
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(data)
    
    # حفظ الملف
    filename = 'test_import_data.xlsx'
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"تم إنشاء ملف الاختبار: {filename}")
    print(f"عدد السجلات: {len(data)}")
    print("الأعمدة المتضمنة:")
    for col in df.columns:
        print(f"  - {col}")

if __name__ == "__main__":
    create_test_excel()
