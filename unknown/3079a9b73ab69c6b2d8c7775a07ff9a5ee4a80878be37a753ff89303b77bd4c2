#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح إضافة المشاركين
"""

import sqlite3
from datetime import datetime

def check_table_structure():
    """فحص هيكل جدول course_participant"""
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        print("🔍 فحص هيكل جدول course_participant:")
        cursor.execute("PRAGMA table_info(course_participant)")
        columns = cursor.fetchall()
        
        print("📋 الأعمدة الموجودة:")
        for column in columns:
            print(f"   - {column[1]} ({column[2]})")
        
        conn.close()
        return [col[1] for col in columns]
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجدول: {str(e)}")
        return []

def add_participants_fixed():
    """إضافة المشاركين بالأعمدة الصحيحة"""
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        print("\n🎯 إضافة المشاركين إلى الدورة الأولى...")
        
        # جلب الدورة الأولى
        cursor.execute("SELECT id FROM course WHERE course_number = '3455667'")
        course_result = cursor.fetchone()
        
        if not course_result:
            print("❌ لم يتم العثور على الدورة الأولى")
            return False
        
        course_id = course_result[0]
        print(f"✅ العثور على الدورة ID: {course_id}")
        
        # جلب جميع الأشخاص
        cursor.execute("SELECT id, full_name FROM person_data")
        persons = cursor.fetchall()
        
        if not persons:
            print("❌ لا يوجد أشخاص في قاعدة البيانات")
            return False
        
        print(f"👥 العثور على {len(persons)} شخص")
        
        # حذف المشاركين الموجودين
        cursor.execute("DELETE FROM course_participant WHERE course_id = ?", (course_id,))
        
        # إضافة المشاركين بالأعمدة الأساسية فقط
        for person_id, person_name in persons:
            try:
                cursor.execute("""
                    INSERT INTO course_participant (
                        course_id, personal_data_id, status
                    ) VALUES (?, ?, ?)
                """, (course_id, person_id, 'active'))
                
                print(f"✅ تم إضافة: {person_name}")
            except Exception as e:
                print(f"❌ خطأ في إضافة {person_name}: {str(e)}")
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من النتيجة
        cursor.execute("SELECT COUNT(*) FROM course_participant WHERE course_id = ?", (course_id,))
        participants_count = cursor.fetchone()[0]
        
        print(f"\n✅ تم إضافة {participants_count} مشارك إلى الدورة الأولى")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المشاركين: {str(e)}")
        return False

def verify_final_results():
    """التحقق من النتائج النهائية"""
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        print("\n📊 النتائج النهائية:")
        print("=" * 50)
        
        # فحص الدورات
        cursor.execute("SELECT COUNT(*) FROM course")
        courses_count = cursor.fetchone()[0]
        print(f"📚 الدورات: {courses_count}")
        
        # فحص الأشخاص
        cursor.execute("SELECT COUNT(*) FROM person_data")
        persons_count = cursor.fetchone()[0]
        print(f"👤 الأشخاص: {persons_count}")
        
        # فحص المشاركين
        cursor.execute("SELECT COUNT(*) FROM course_participant")
        participants_count = cursor.fetchone()[0]
        print(f"🎯 المشاركين: {participants_count}")
        
        # عرض تفاصيل الدورات مع المشاركين
        cursor.execute("""
            SELECT c.course_number, c.title, COUNT(cp.id) as participants
            FROM course c
            LEFT JOIN course_participant cp ON c.id = cp.course_id
            GROUP BY c.id, c.course_number, c.title
            ORDER BY c.course_number
        """)
        
        courses_details = cursor.fetchall()
        print(f"\n📋 تفاصيل الدورات:")
        for course in courses_details:
            print(f"   - {course[0]}: {course[1]} ({course[2]} مشارك)")
        
        conn.close()
        
        return courses_count, persons_count, participants_count
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")
        return 0, 0, 0

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح إضافة المشاركين")
    print("=" * 50)
    
    # فحص هيكل الجدول
    columns = check_table_structure()
    
    # إضافة المشاركين
    if add_participants_fixed():
        print("✅ تم إضافة المشاركين بنجاح")
    else:
        print("❌ فشل في إضافة المشاركين")
        return
    
    # التحقق من النتائج
    courses_count, persons_count, participants_count = verify_final_results()
    
    print("\n" + "=" * 50)
    if courses_count == 3 and participants_count == persons_count:
        print("🎉 تم إصلاح النظام بنجاح!")
        print("✅ جميع البيانات موجودة ومترابطة")
        print("\n🔗 الروابط المهمة:")
        print("   📚 الدورات: http://127.0.0.1:5000/courses")
        print("   👥 بيانات الأشخاص: http://127.0.0.1:5000/person_data/person_data_table")
        print("   📊 إدارة Excel: http://127.0.0.1:5000/person_data/person_data_excel")
    else:
        print("⚠️ هناك مشكلة في البيانات")
        print(f"   📚 الدورات: {courses_count} (المطلوب: 3)")
        print(f"   👤 الأشخاص: {persons_count}")
        print(f"   🎯 المشاركين: {participants_count} (المطلوب: {persons_count})")

if __name__ == "__main__":
    main()
