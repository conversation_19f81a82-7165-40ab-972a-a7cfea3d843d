كمل تأكد من كل زر حفظ وتعديل وازارار الروابط هناك ازار تضغط عليها وتفتح شاشات غير امطلوبة الامور مخلوطة وازارار تظهر اخطاء وبالاخص ازار التهيئة تاكد من المشروع الموجود كامل لكي ننتقا الخطوه التالية
E:\app\TRINING> python app.py



python -c "from app import app; app.run(debug=True, host='127.0.0.1', port=5000)"

python run.py

python run_server.py

venv\Scripts\activate
pip install -r requirements.txt  
python init_db.py

python create_admin.py

python create_admin_direct.py

from app import app, db, User

with app.app_context():
    user = User.query.filter_by(username='admin').first()
    if user:
        db.session.delete(user)
        db.session.commit()
        print("تم حذف المستخدم بنجاح")
    else:
        print("المستخدم غير موجود")

python delete_user.py


اجعل 
المديرية تظهر على حسب المحافظة المختارة والحارة/القرية حسب ماتختار من مديرية   وتاكد من 
نوع الإصابة
سبب الإصابة و
نوع الإصابة
سبب الإصابة  فارغات اعمل لها جداول ترميزية ايد اكمال ادخال بيانات الشخصية اجرب الحفظ واضيف اسماء لدورة 

-------------

اضف الخارجين من الدورة الى الرئبسية  الى مربع المواد 
--------------------------

نوع الشاركين عند الاضافة   
sqlalchemy.exc.IntegrityError

sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) UNIQUE constraint failed: participant_type.name
[SQL: INSERT INTO participant_type (name) VALUES (?)]
[parameters: ('نوع1',)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
-----------------------
نوع التكليف فارغ انواع التكاليف  


#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تحديث قاعدة البيانات الشامل
يقوم بإنشاء الجداول المفقودة وتحديث البيانات الموجودة
"""

import os
import sqlite3
import shutil
from datetime import datetime
from app import app, db
from app import (
    User, PersonalData, PersonData, Course, CourseParticipant,
    Agency, Department, Governorate, Directorate, Village,
    CoursePath, CoursePathLevel, ForceClassification,
    MaritalStatus, BloodType, IssuingAuthority, QualificationType,
    Specialization, AssignmentType, MilitaryRank, InjuryType,
    InjuryCause, CourseType, TrainingCenterType, Location,
    TrainingCenter, ParticipantType, CardType, CourseLevel,
    CourseCategory
)

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        db_path = 'training_system.db'
        if os.path.exists(db_path):
            backup_path = f'training_system_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'

            # نسخ قاعدة البيانات
            shutil.copy2(db_path, backup_path)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
            return backup_path
        else:
            print("⚠️ لم يتم العثور على قاعدة البيانات الحالية")
            return None
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return None

def update_database_schema(db_path):  


جهة إصدار القرار أو التكليف  فارغ 
الرتبة فارغ 
اضافة عزلة  jinja2.exceptions.TemplateNotFound

jinja2.exceptions.TemplateNotFound: reference_tables/add_village.html

عند الضغط على الدفعات يظهر  التقارير الاحصائية

عند الضغط على المدربين يظهر   المستخدمين   


عرض   قائمة أنواع المشاركين
وشاشة وجداول التراميز ل
نوع المؤهل و   
التخصص
عند الضغط على    


اجعل الاسم يتكون من خمسة حقول واللقب اربعة اجباري واللقب اجباري فعل نظام المباينة العربي الذي يعمل تصفية قبل الادخال في كل مربع مثلا يكتب عبداللة يقوم بتبديلها الى عبدالله لكي يتم مقارنتها مع المخفوظ في القاعدة لكي لايحفظ اسم مكررر 
اي تصفية وعدم تكرار   

اريد ان تضيف تاريخ بداية الدورة وتاريخ نهاية الدورة هجري الى جانب الميبلادي ويكون اجباري  في شاشة اضافة دورة 
   وتضيف حقل المسار وجدول ترميزي له وحقل رقم  الدورة في الجهة غير الرقم العام يعني اجعل الرقم العام غير الي واجعل المستخدم يدخله  
 واضف اسم المكان واسم  ورقم الدورة في المكان   واضف نوع المستوى واضف جدول لانواع المستويات واجعلة في نفي شاشة المسارات 
 لكل مسار عدة مستويات  واضف اسم المكان  واضف تصنيف القوه   واضف الجهة التابعة للمستهدفين  وعدد المستهدفين وتاكد منجداول التراميز للاضافة امضي قدما 


في  إضافة بيانات شخصية جديدة في  إدارة البيانات الشخصية  اجعل الخقول تتناسق مع الصورة المرفقة للاكسل وزر الاستيراد لكي اعمل البيانات في ملف اكسل واستوردها وبدون اي حقول ترميزية  


ادخال واستيراد البيانات الشخصية  


 
  
