# تعليمات استخدام المكتبات المحلية

## نظرة عامة

تم تحويل جميع المكتبات الخارجية (Bootstrap, Font Awesome, jQuery, إلخ) إلى ملفات محلية في مجلد `static/libs/`.

## بنية المجلدات

```
static/libs/
├── bootstrap/          # Bootstrap CSS & JS
├── fontawesome/        # Font Awesome CSS & Fonts
├── jquery/            # jQuery JavaScript
├── chartjs/           # Chart.js للرسوم البيانية
├── datatables/        # DataTables للجداول التفاعلية
├── select2/           # Select2 للقوائم المنسدلة
├── devextreme/        # DevExtreme للمكونات المتقدمة
└── other/             # مكتبات أخرى (jsPDF, XLSX, JSZip)
```

## الاستخدام في القوالب

### القالب الأساسي
استخدم `templates/layout.html` الذي تم تحديثه ليستخدم المكتبات المحلية:

```html
<!-- Bootstrap RTL CSS - محلي -->
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">

<!-- Font Awesome - محلي -->
<link rel="stylesheet" href="{{ url_for('static', filename='libs/fontawesome/all.min.css') }}">

<!-- jQuery - محلي -->
<script src="{{ url_for('static', filename='libs/jquery/jquery-3.6.0.min.js') }}"></script>

<!-- Bootstrap JS - محلي -->
<script src="{{ url_for('static', filename='libs/bootstrap/bootstrap.bundle.min.js') }}"></script>
```

### للصفحات التي تحتاج DataTables

```html
{% block styles %}
<!-- DataTables CSS - محلي -->
<link rel="stylesheet" href="{{ url_for('static', filename='libs/datatables/dataTables.bootstrap5.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='libs/datatables/responsive.bootstrap5.min.css') }}">
{% endblock %}

{% block scripts %}
<!-- DataTables JS - محلي -->
<script src="{{ url_for('static', filename='libs/datatables/jquery.dataTables.min.js') }}"></script>
<script src="{{ url_for('static', filename='libs/datatables/dataTables.bootstrap5.min.js') }}"></script>
{% endblock %}
```

### للصفحات التي تحتاج Chart.js

```html
{% block scripts %}
<!-- Chart.js - محلي -->
<script src="{{ url_for('static', filename='libs/chartjs/chart.min.js') }}"></script>
{% endblock %}
```

### للصفحات التي تحتاج DevExtreme

```html
{% block styles %}
<!-- DevExtreme CSS - محلي -->
<link rel="stylesheet" href="{{ url_for('static', filename='libs/devextreme/dx.light.css') }}">
{% endblock %}

{% block scripts %}
<!-- DevExtreme JS - محلي -->
<script src="{{ url_for('static', filename='libs/devextreme/dx.all.js') }}"></script>
{% endblock %}
```

## إضافة مكتبات جديدة

### 1. تحديث سكريبت التنزيل
أضف المكتبة الجديدة إلى `download_libraries.py`:

```python
# مكتبة جديدة
'static/libs/newlib/newlib.min.css': 'https://cdn.example.com/newlib.min.css',
'static/libs/newlib/newlib.min.js': 'https://cdn.example.com/newlib.min.js',
```

### 2. تشغيل التنزيل
```bash
python download_libraries.py
```

### 3. تحديث القوالب
أضف المكتبة إلى القوالب المطلوبة:

```html
<link rel="stylesheet" href="{{ url_for('static', filename='libs/newlib/newlib.min.css') }}">
<script src="{{ url_for('static', filename='libs/newlib/newlib.min.js') }}"></script>
```

## التحقق من المكتبات

### فحص الملفات المحملة
```bash
# عرض جميع المكتبات المحلية
dir static\libs /s
```

### اختبار التحميل
1. افتح المشروع في المتصفح
2. افتح أدوات المطور (F12)
3. تحقق من تبويب Network
4. تأكد من تحميل جميع الملفات من `/static/libs/`

## استكشاف الأخطاء

### مشكلة: ملف مكتبة غير موجود
**الحل**: 
1. تحقق من وجود الملف في `static/libs/`
2. إذا لم يكن موجوداً، شغل `python download_libraries.py`

### مشكلة: الخطوط لا تظهر بشكل صحيح
**الحل**:
1. تأكد من وجود مجلد `static/libs/fontawesome/webfonts/`
2. تحقق من ملفات الخطوط (.woff2)

### مشكلة: JavaScript لا يعمل
**الحل**:
1. تحقق من ترتيب تحميل المكتبات (jQuery أولاً)
2. تأكد من تحميل جميع ملفات JS المطلوبة

## نصائح مهمة

### ✅ الممارسات الجيدة
- استخدم دائماً `url_for('static', filename='...')` في القوالب
- حافظ على ترتيب تحميل المكتبات (jQuery قبل Bootstrap)
- اختبر المشروع بدون اتصال إنترنت للتأكد

### ⚠️ تجنب
- لا تستخدم روابط مطلقة مثل `/static/libs/...`
- لا تحذف مجلد `static/libs/` عند النسخ
- لا تخلط بين المكتبات المحلية والخارجية

## النسخ الاحتياطي

### إنشاء نسخة احتياطية من المكتبات
```bash
# ضغط مجلد المكتبات
tar -czf static_libs_backup.tar.gz static/libs/
```

### استعادة المكتبات
```bash
# استخراج النسخة الاحتياطية
tar -xzf static_libs_backup.tar.gz
```

## الخلاصة

المشروع الآن مستقل تماماً ولا يحتاج اتصال بالإنترنت للعمل. جميع المكتبات محفوظة محلياً ويمكن نسخ المشروع إلى أي جهاز وسيعمل بنفس التنسيق والوظائف.
