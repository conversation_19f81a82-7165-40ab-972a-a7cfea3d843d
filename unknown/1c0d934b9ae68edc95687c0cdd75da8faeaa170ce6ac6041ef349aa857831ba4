#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
استعادة البيانات الأصلية من النسخة الاحتياطية
"""

import sqlite3
import shutil
import os
from datetime import datetime

def check_backup_data(backup_file):
    """فحص محتويات النسخة الاحتياطية"""
    try:
        conn = sqlite3.connect(backup_file)
        cursor = conn.cursor()
        
        print(f"🔍 فحص النسخة الاحتياطية: {backup_file}")
        print("-" * 50)
        
        # فحص الجداول الرئيسية
        tables_to_check = ['course', 'person_data', 'personal_data', 'course_participant']
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"📋 {table}: {count} سجل")
                
                # عرض عينة من البيانات
                if table == 'course' and count > 0:
                    cursor.execute("SELECT id, course_number, title FROM course LIMIT 3")
                    courses = cursor.fetchall()
                    for course in courses:
                        print(f"   - دورة {course[0]}: {course[1]} - {course[2]}")
                
                elif table == 'person_data' and count > 0:
                    cursor.execute("SELECT id, full_name FROM person_data LIMIT 5")
                    persons = cursor.fetchall()
                    for person in persons:
                        print(f"   - شخص {person[0]}: {person[1]}")
                        
            except Exception as e:
                print(f"❌ خطأ في جدول {table}: {str(e)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص النسخة الاحتياطية: {str(e)}")
        return False

def restore_from_backup(backup_file):
    """استعادة البيانات من النسخة الاحتياطية"""
    try:
        # إنشاء نسخة احتياطية من الحالة الحالية
        current_backup = f"training_system_before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        shutil.copy2('training_system.db', current_backup)
        print(f"✅ تم حفظ النسخة الحالية في: {current_backup}")
        
        # استعادة النسخة الاحتياطية
        shutil.copy2(backup_file, 'training_system.db')
        print(f"✅ تم استعادة البيانات من: {backup_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستعادة: {str(e)}")
        return False

def copy_person_data_only(source_backup):
    """نسخ بيانات person_data فقط من النسخة الاحتياطية"""
    try:
        print("🔄 نسخ بيانات person_data من النسخة الاحتياطية...")
        
        # الاتصال بالنسخة الاحتياطية
        source_conn = sqlite3.connect(source_backup)
        source_cursor = source_conn.cursor()
        
        # الاتصال بقاعدة البيانات الحالية
        target_conn = sqlite3.connect('training_system.db')
        target_cursor = target_conn.cursor()
        
        # فحص وجود بيانات في النسخة الاحتياطية
        source_cursor.execute("SELECT COUNT(*) FROM person_data")
        source_count = source_cursor.fetchone()[0]
        print(f"📊 البيانات في النسخة الاحتياطية: {source_count} شخص")
        
        if source_count > 0:
            # حذف البيانات الحالية
            target_cursor.execute("DELETE FROM person_data")
            
            # نسخ البيانات
            source_cursor.execute("SELECT * FROM person_data")
            rows = source_cursor.fetchall()
            
            # الحصول على أسماء الأعمدة
            source_cursor.execute("PRAGMA table_info(person_data)")
            columns = [row[1] for row in source_cursor.fetchall()]
            
            # إدراج البيانات
            placeholders = ','.join(['?' for _ in columns])
            target_cursor.executemany(f"INSERT INTO person_data VALUES ({placeholders})", rows)
            
            target_conn.commit()
            print(f"✅ تم نسخ {len(rows)} شخص بنجاح")
        
        source_conn.close()
        target_conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نسخ البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔄 استعادة البيانات الأصلية")
    print("=" * 60)
    
    # قائمة النسخ الاحتياطية المتوفرة
    backup_files = [
        'training_system_backup_20250529_034725.db',
        'training_system.db.bak_20250503_044906',
        'training_system_backup_columns_20250529_035306.db'
    ]
    
    print("📋 النسخ الاحتياطية المتوفرة:")
    for i, backup in enumerate(backup_files, 1):
        if os.path.exists(backup):
            print(f"   {i}. {backup}")
            check_backup_data(backup)
            print()
    
    # اختيار النسخة المناسبة
    print("🎯 سأستخدم النسخة الأولى التي تحتوي على البيانات الأصلية...")
    
    # استخدام النسخة الاحتياطية الأولى
    best_backup = 'training_system_backup_20250529_034725.db'
    
    if os.path.exists(best_backup):
        print(f"📦 استعادة البيانات من: {best_backup}")
        
        # خيار 1: نسخ بيانات person_data فقط
        if copy_person_data_only(best_backup):
            print("✅ تم استعادة بيانات الأشخاص بنجاح!")
        else:
            print("❌ فشل في استعادة البيانات")
    else:
        print(f"❌ النسخة الاحتياطية غير موجودة: {best_backup}")
    
    # التحقق من النتيجة
    print("\n🔍 التحقق من النتيجة:")
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM course")
        courses = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM person_data")
        persons = cursor.fetchone()[0]
        
        print(f"📚 الدورات: {courses}")
        print(f"👤 الأشخاص: {persons}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")

if __name__ == "__main__":
    main()
