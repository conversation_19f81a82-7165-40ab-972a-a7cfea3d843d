#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بيانات التصحيحات
"""

import sqlite3

def test_corrections_data():
    """
    اختبار بيانات التصحيحات
    """
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # جلب البيانات بنفس طريقة الكود
        cursor.execute("""
            SELECT nc.id, nc.wrong_name, nc.correct_name, nc.correction_type,
                   nc.created_at, nc.usage_count, nc.is_active, nc.created_by
            FROM name_correction nc
            ORDER BY nc.created_at DESC
        """)
        
        rows = cursor.fetchall()
        print(f"📊 عدد التصحيحات المجلبة: {len(rows)}")
        
        if len(rows) > 0:
            print("\n📋 البيانات:")
            print("ID | الاسم الخطأ | الاسم الصحيح | النوع | نشط")
            print("-" * 60)
            
            for row in rows:
                print(f"{row[0]:2d} | {row[1]:12s} | {row[2]:12s} | {row[3]:15s} | {row[6]:4d}")
            
            # تحويل البيانات لنفس تنسيق الكود
            corrections = []
            for row in rows:
                corrections.append({
                    'id': row[0],
                    'wrong_name': row[1],
                    'correct_name': row[2],
                    'correction_type': row[3],
                    'created_at': row[4],
                    'usage_count': row[5] or 0,
                    'is_active': bool(row[6]),
                    'creator': {'username': 'admin'}
                })
            
            print(f"\n✅ تم تحويل {len(corrections)} تصحيح بنجاح")
            
            # عرض أول تصحيح كمثال
            if corrections:
                first = corrections[0]
                print(f"\n🔍 مثال على التصحيح الأول:")
                print(f"   • ID: {first['id']}")
                print(f"   • الاسم الخطأ: {first['wrong_name']}")
                print(f"   • الاسم الصحيح: {first['correct_name']}")
                print(f"   • النوع: {first['correction_type']}")
                print(f"   • نشط: {first['is_active']}")
                print(f"   • الاستخدام: {first['usage_count']}")
        else:
            print("⚠️ لا توجد بيانات في الجدول")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    test_corrections_data()
