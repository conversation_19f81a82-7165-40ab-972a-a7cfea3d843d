@echo off
chcp 65001 >nul
echo ========================================
echo    إعداد النظام للنشر
echo    Preparing System for Deployment
echo ========================================
echo.

cd /d "E:\app\TRINING"

echo 🔧 تفعيل البيئة الافتراضية...
call .venv\Scripts\activate.bat

echo.
echo 📋 إنشاء قائمة المتطلبات المحدثة...
pip freeze > requirements_frozen.txt

echo.
echo 📁 إنشاء مجلدات النشر...
if not exist "deployment" mkdir deployment
if not exist "deployment\exe" mkdir deployment\exe
if not exist "deployment\docker" mkdir deployment\docker
if not exist "deployment\source" mkdir deployment\source

echo.
echo 📦 نسخ الملفات الأساسية للنشر...

echo نسخ ملفات Python...
copy "*.py" "deployment\source\" >nul
copy "requirements*.txt" "deployment\source\" >nul
copy ".env.example" "deployment\source\" >nul

echo نسخ ملفات Docker...
copy "Dockerfile" "deployment\docker\" >nul
copy "docker-compose.yml" "deployment\docker\" >nul
copy "docker_build.bat" "deployment\docker\" >nul

echo نسخ ملفات EXE...
copy "build_exe.spec" "deployment\exe\" >nul
copy "build_exe.bat" "deployment\exe\" >nul

echo نسخ المجلدات...
xcopy "templates" "deployment\source\templates\" /E /I /Q >nul
xcopy "static" "deployment\source\static\" /E /I /Q >nul

echo نسخ قاعدة البيانات...
if exist "training_system.db" (
    copy "training_system.db" "deployment\source\" >nul
    echo ✅ تم نسخ قاعدة البيانات
) else (
    echo ⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها عند التشغيل
)

echo.
echo 📄 إنشاء ملف README للنشر...
echo # نظام تحليل الأسماء - ملفات النشر > deployment\README.md
echo # Training System - Deployment Files >> deployment\README.md
echo. >> deployment\README.md
echo ## المجلدات: >> deployment\README.md
echo - source: الكود المصدري >> deployment\README.md
echo - exe: ملفات بناء EXE >> deployment\README.md
echo - docker: ملفات Docker >> deployment\README.md
echo. >> deployment\README.md
echo ## للنشر: >> deployment\README.md
echo 1. EXE: استخدم ملفات مجلد exe >> deployment\README.md
echo 2. Docker: استخدم ملفات مجلد docker >> deployment\README.md
echo 3. Source: استخدم ملفات مجلد source >> deployment\README.md

echo.
echo 🔍 فحص الملفات المطلوبة...
set MISSING_FILES=0

if not exist "deployment\source\app.py" (
    echo ❌ app.py مفقود
    set /a MISSING_FILES+=1
)

if not exist "deployment\source\templates" (
    echo ❌ مجلد templates مفقود
    set /a MISSING_FILES+=1
)

if not exist "deployment\source\static" (
    echo ❌ مجلد static مفقود
    set /a MISSING_FILES+=1
)

if %MISSING_FILES% EQU 0 (
    echo ✅ جميع الملفات المطلوبة متوفرة
) else (
    echo ⚠️ %MISSING_FILES% ملف مفقود
)

echo.
echo 📊 إحصائيات النشر:
echo ==================
for /f %%i in ('dir "deployment\source\*.py" /b 2^>nul ^| find /c /v ""') do echo ملفات Python: %%i
for /f %%i in ('dir "deployment\source\templates\*.*" /s /b 2^>nul ^| find /c /v ""') do echo ملفات Templates: %%i
for /f %%i in ('dir "deployment\source\static\*.*" /s /b 2^>nul ^| find /c /v ""') do echo ملفات Static: %%i

echo.
echo ========================================
echo    ✅ تم إعداد النظام للنشر!
echo    ✅ System prepared for deployment!
echo ========================================
echo.
echo 📁 الملفات متوفرة في مجلد: deployment\
echo 📁 Files available in folder: deployment\
echo.
echo 🚀 الخطوات التالية:
echo 🚀 Next steps:
echo.
echo 1. للنشر كـ EXE:
echo    cd deployment\exe
echo    build_exe.bat
echo.
echo 2. للنشر كـ Docker:
echo    cd deployment\docker
echo    docker_build.bat
echo.
echo 3. للنشر كـ Source:
echo    نسخ مجلد source إلى الخادم المستهدف
echo    Copy source folder to target server
echo.
pause
