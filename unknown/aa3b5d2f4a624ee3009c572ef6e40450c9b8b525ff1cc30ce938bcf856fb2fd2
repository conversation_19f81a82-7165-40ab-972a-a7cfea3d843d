#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث جميع ملفات HTML لاستخدام المكتبات المحلية
Update all HTML files to use local libraries
"""

import os
import re
import glob
from pathlib import Path

def update_file_links(file_path):
    """تحديث الروابط في ملف واحد"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # قائمة الاستبدالات
        replacements = [
            # Bootstrap
            (r'https://cdn\.jsdelivr\.net/npm/bootstrap@5\.0\.2/dist/css/bootstrap\.rtl\.min\.css', 
             "{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}"),
            (r'https://cdn\.jsdelivr\.net/npm/bootstrap@5\.0\.2/dist/js/bootstrap\.bundle\.min\.js', 
             "{{ url_for('static', filename='libs/bootstrap/bootstrap.bundle.min.js') }}"),
            (r'https://cdn\.jsdelivr\.net/npm/bootstrap@5\.3\.0/dist/css/bootstrap\.rtl\.min\.css', 
             "{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}"),
            (r'https://cdn\.jsdelivr\.net/npm/bootstrap@5\.3\.0/dist/js/bootstrap\.bundle\.min\.js', 
             "{{ url_for('static', filename='libs/bootstrap/bootstrap.bundle.min.js') }}"),
            (r'https://cdn\.jsdelivr\.net/npm/bootstrap@5\.1\.3/dist/css/bootstrap\.min\.css', 
             "{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}"),
            (r'https://cdn\.jsdelivr\.net/npm/bootstrap@4\.6\.0/dist/js/bootstrap\.bundle\.min\.js', 
             "{{ url_for('static', filename='libs/bootstrap/bootstrap.bundle.min.js') }}"),
            
            # Font Awesome
            (r'https://cdnjs\.cloudflare\.com/ajax/libs/font-awesome/6\.0\.0-beta3/css/all\.min\.css', 
             "{{ url_for('static', filename='libs/fontawesome/all.min.css') }}"),
            (r'https://cdnjs\.cloudflare\.com/ajax/libs/font-awesome/6\.0\.0/css/all\.min\.css', 
             "{{ url_for('static', filename='libs/fontawesome/all.min.css') }}"),
            
            # jQuery
            (r'https://code\.jquery\.com/jquery-3\.6\.0\.min\.js', 
             "{{ url_for('static', filename='libs/jquery/jquery-3.6.0.min.js') }}"),
            
            # Chart.js
            (r'https://cdn\.jsdelivr\.net/npm/chart\.js@3\.9\.1/dist/chart\.min\.css', 
             "{{ url_for('static', filename='libs/chartjs/chart.min.css') }}"),
            (r'https://cdn\.jsdelivr\.net/npm/chart\.js', 
             "{{ url_for('static', filename='libs/chartjs/chart.min.js') }}"),
            
            # DataTables
            (r'https://cdn\.datatables\.net/1\.11\.5/css/dataTables\.bootstrap5\.min\.css', 
             "{{ url_for('static', filename='libs/datatables/dataTables.bootstrap5.min.css') }}"),
            (r'https://cdn\.datatables\.net/1\.11\.5/js/jquery\.dataTables\.min\.js', 
             "{{ url_for('static', filename='libs/datatables/jquery.dataTables.min.js') }}"),
            (r'https://cdn\.datatables\.net/1\.11\.5/js/dataTables\.bootstrap5\.min\.js', 
             "{{ url_for('static', filename='libs/datatables/dataTables.bootstrap5.min.js') }}"),
            (r'https://cdn\.datatables\.net/1\.11\.5/js/dataTables\.bootstrap4\.min\.js', 
             "{{ url_for('static', filename='libs/datatables/dataTables.bootstrap5.min.js') }}"),
            
            # Select2
            (r'https://cdn\.jsdelivr\.net/npm/select2@4\.1\.0-rc\.0/dist/css/select2\.min\.css', 
             "{{ url_for('static', filename='libs/select2/select2.min.css') }}"),
            (r'https://cdn\.jsdelivr\.net/npm/select2@4\.1\.0-rc\.0/dist/js/select2\.min\.js', 
             "{{ url_for('static', filename='libs/select2/select2.min.js') }}"),
            
            # DevExtreme
            (r'https://cdn\.jsdelivr\.net/npm/devextreme@23\.1\.3/dist/css/dx\.light\.css', 
             "{{ url_for('static', filename='libs/devextreme/dx.light.css') }}"),
            (r'https://cdn\.jsdelivr\.net/npm/devextreme@23\.1\.3/dist/js/dx\.all\.js', 
             "{{ url_for('static', filename='libs/devextreme/dx.all.js') }}"),
            
            # Other libraries
            (r'https://cdnjs\.cloudflare\.com/ajax/libs/jspdf/2\.5\.1/jspdf\.umd\.min\.js', 
             "{{ url_for('static', filename='libs/other/jspdf.umd.min.js') }}"),
            (r'https://cdnjs\.cloudflare\.com/ajax/libs/xlsx/0\.18\.5/xlsx\.full\.min\.js', 
             "{{ url_for('static', filename='libs/other/xlsx.full.min.js') }}"),
            (r'https://cdnjs\.cloudflare\.com/ajax/libs/jszip/3\.1\.3/jszip\.min\.js', 
             "{{ url_for('static', filename='libs/other/jszip.min.js') }}"),
        ]
        
        # تطبيق الاستبدالات
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        # حفظ الملف إذا تم تغييره
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ في معالجة {file_path}: {e}")
        return False

def main():
    """تحديث جميع ملفات HTML"""
    print("🚀 بدء تحديث ملفات HTML...")
    print("=" * 60)
    
    # البحث عن جميع ملفات HTML
    html_files = []
    for pattern in ['templates/**/*.html', '*.html']:
        html_files.extend(glob.glob(pattern, recursive=True))
    
    updated_count = 0
    total_count = len(html_files)
    
    for file_path in html_files:
        print(f"📄 معالجة: {file_path}")
        if update_file_links(file_path):
            print(f"✅ تم تحديث: {file_path}")
            updated_count += 1
        else:
            print(f"⏭️ لا يحتاج تحديث: {file_path}")
        print("-" * 40)
    
    print("=" * 60)
    print(f"✅ تم تحديث {updated_count} من {total_count} ملف")
    
    if updated_count > 0:
        print("🎉 تم تحديث الملفات بنجاح!")
    else:
        print("ℹ️ لا توجد ملفات تحتاج تحديث")

if __name__ == "__main__":
    main()
