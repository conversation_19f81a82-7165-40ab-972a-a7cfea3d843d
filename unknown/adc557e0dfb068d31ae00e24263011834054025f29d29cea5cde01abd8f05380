#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف لإنشاء جداول قاعدة البيانات
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# استيراد التطبيق والنماذج
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask بسيط
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///training_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

from datetime import datetime, timezone

# تعريف نموذج User (مبسط)
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    role = db.Column(db.String(20), nullable=False, default='user')

# تعريف نموذج PersonData
class PersonData(db.Model):
    __tablename__ = 'person_data'

    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(200), nullable=False)
    nickname = db.Column(db.String(100))
    age = db.Column(db.Integer)
    governorate = db.Column(db.String(100))
    directorate = db.Column(db.String(100))
    uzla = db.Column(db.String(100))
    village = db.Column(db.String(100))
    qualification = db.Column(db.String(100))
    marital_status = db.Column(db.String(50))
    job = db.Column(db.String(100))
    agency = db.Column(db.String(100))
    work_place = db.Column(db.String(100))
    national_number = db.Column(db.String(50))
    military_number = db.Column(db.String(50))
    phone = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

# تعريف نموذج التصحيحات المخصصة
class NameCorrection(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    wrong_name = db.Column(db.String(200), nullable=False)     # الاسم الخطأ
    correct_name = db.Column(db.String(200), nullable=False)   # الاسم الصحيح
    correction_type = db.Column(db.String(50), nullable=False) # نوع التصحيح
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False) # المستخدم الذي أضاف التصحيح
    created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    is_active = db.Column(db.Boolean, nullable=False, default=True) # هل التصحيح نشط
    usage_count = db.Column(db.Integer, nullable=False, default=0)  # عدد مرات الاستخدام

    # العلاقات
    creator = db.relationship('User', backref='name_corrections', lazy=True)

def create_tables():
    """إنشاء جميع الجداول في قاعدة البيانات"""
    with app.app_context():
        try:
            # إنشاء جميع الجداول
            db.create_all()
            print("✅ تم إنشاء جميع الجداول بنجاح!")

            # طباعة أسماء الجداول المنشأة
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"📊 الجداول الموجودة في قاعدة البيانات: {tables}")

            # التحقق من جدول التصحيحات المخصصة
            if 'name_correction' in tables:
                print("✅ تم إنشاء جدول التصحيحات المخصصة (name_correction) بنجاح!")
                print("📝 يمكنك الآن إضافة تصحيحات مخصصة من خلال واجهة الإدارة")
            else:
                print("⚠️ لم يتم إنشاء جدول التصحيحات المخصصة")

        except Exception as e:
            print(f"❌ حدث خطأ أثناء إنشاء الجداول: {str(e)}")

if __name__ == "__main__":
    create_tables()
