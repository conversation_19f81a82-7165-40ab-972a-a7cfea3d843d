#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار القالب مع البيانات
"""

import sqlite3

def create_debug_template():
    """
    إنشاء قالب تصحيح للاختبار
    """
    
    # جلب البيانات من قاعدة البيانات
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT nc.id, nc.wrong_name, nc.correct_name, nc.correction_type,
               nc.created_at, nc.usage_count, nc.is_active, nc.created_by
        FROM name_correction nc
        ORDER BY nc.created_at DESC
    """)
    
    rows = cursor.fetchall()
    
    # تحويل البيانات
    corrections = []
    for row in rows:
        corrections.append({
            'id': row[0],
            'wrong_name': row[1],
            'correct_name': row[2],
            'correction_type': row[3],
            'created_at': row[4],
            'usage_count': row[5] or 0,
            'is_active': bool(row[6]),
            'creator': {'username': 'admin'}
        })
    
    conn.close()
    
    # إنشاء HTML للاختبار
    html_content = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار عرض التصحيحات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>اختبار عرض التصحيحات</h2>
        
        <div class="alert alert-info">
            <strong>عدد التصحيحات:</strong> """ + str(len(corrections)) + """
        </div>
        
        <table class="table table-striped">
            <thead class="thead-dark">
                <tr>
                    <th>الاسم الخطأ</th>
                    <th>الاسم الصحيح</th>
                    <th>نوع التصحيح</th>
                    <th>الحالة</th>
                    <th>عدد الاستخدامات</th>
                </tr>
            </thead>
            <tbody>
    """
    
    # إضافة البيانات
    for correction in corrections:
        # تحديد نوع التصحيح
        type_name = "غير معروف"
        if correction['correction_type'] == 'hamza':
            type_name = "تصحيح الهمزات"
        elif correction['correction_type'] == 'alif_maqsura':
            type_name = "الألف المقصورة"
        elif correction['correction_type'] == 'compound_names':
            type_name = "أسماء مركبة"
        elif correction['correction_type'] == 'symbols':
            type_name = "إزالة رموز"
        elif correction['correction_type'] == 'other':
            type_name = "أخرى"
        
        # تحديد الحالة
        status = "نشط" if correction['is_active'] else "معطل"
        status_class = "success" if correction['is_active'] else "warning"
        
        html_content += f"""
                <tr>
                    <td><span class="badge badge-danger">{correction['wrong_name']}</span></td>
                    <td><span class="badge badge-success">{correction['correct_name']}</span></td>
                    <td><span class="badge badge-info">{type_name}</span></td>
                    <td><span class="badge badge-{status_class}">{status}</span></td>
                    <td><span class="badge badge-secondary">{correction['usage_count']}</span></td>
                </tr>
        """
    
    html_content += """
            </tbody>
        </table>
    </div>
</body>
</html>
    """
    
    # حفظ الملف
    with open('debug_corrections.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ تم إنشاء ملف debug_corrections.html")
    print(f"📊 يحتوي على {len(corrections)} تصحيح")
    print("🌐 افتح الملف في المتصفح لرؤية البيانات")
    
    # طباعة البيانات للتأكد
    print("\n📋 البيانات المُصدرة:")
    for i, correction in enumerate(corrections[:3], 1):  # أول 3 فقط
        print(f"{i}. {correction['wrong_name']} → {correction['correct_name']} ({correction['correction_type']})")

if __name__ == "__main__":
    create_debug_template()
