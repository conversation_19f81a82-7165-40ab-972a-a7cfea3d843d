#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, CourseParticipant, Course, PersonData

def check_participants():
    with app.app_context():
        # جلب الدورة رقم 1
        course = Course.query.get(1)
        if course:
            print(f'الدورة: {course.title}')
            print(f'رقم الدورة: {course.course_number}')
            print('=' * 50)
            
            # عدد المشاركين في قاعدة البيانات
            participants_count = CourseParticipant.query.filter_by(course_id=1).count()
            print(f'العدد الفعلي في قاعدة البيانات: {participants_count}')
            
            # جلب جميع المشاركين
            participants = CourseParticipant.query.filter_by(course_id=1).all()
            print(f'عدد المشاركين المجلوبين: {len(participants)}')
            
            # عرض أسماء المشاركين
            print('\nقائمة المشاركين:')
            for i, p in enumerate(participants, 1):
                person_name = p.personal_data.full_name if p.personal_data else 'غير محدد'
                print(f'{i}. {person_name} (ID: {p.personal_data_id}, Status: {p.status})')
            
            # التحقق من إجمالي المشاركين في جدول الدورة
            print(f'\nإجمالي المشاركين في جدول الدورة: {course.total_participants}')
            
            # تحديث العدد الصحيح
            course.total_participants = participants_count
            db.session.commit()
            print(f'تم تحديث العدد إلى: {participants_count}')
        else:
            print('لم يتم العثور على الدورة رقم 1')

if __name__ == '__main__':
    check_participants()
