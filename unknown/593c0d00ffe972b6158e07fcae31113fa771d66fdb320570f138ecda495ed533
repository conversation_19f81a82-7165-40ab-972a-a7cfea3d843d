#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, CourseParticipant, Course, PersonData

def find_all_courses():
    with app.app_context():
        print("🔍 البحث عن جميع الدورات...")
        print("=" * 60)
        
        # جلب جميع الدورات
        courses = Course.query.all()
        print(f"إجمالي عدد الدورات: {len(courses)}")
        
        for course in courses:
            # حساب عدد المشاركين الفعلي
            participants_count = CourseParticipant.query.filter_by(course_id=course.id).count()
            
            print(f"\n🎓 الدورة ID: {course.id}")
            print(f"   رقم الدورة: {course.course_number}")
            print(f"   اسم الدورة: {course.title}")
            print(f"   المشاركين المسجل: {course.total_participants}")
            print(f"   المشاركين الفعلي: {participants_count}")
            
            if course.course_number == '3455667':
                print("   🎯 هذه هي الدورة المطلوبة!")
                
                # عرض المشاركين
                participants = CourseParticipant.query.filter_by(course_id=course.id).all()
                print(f"   المشاركين:")
                for i, p in enumerate(participants, 1):
                    name = p.personal_data.full_name if p.personal_data else f"ID: {p.personal_data_id}"
                    print(f"     {i}. {name}")
            
            print("-" * 40)

if __name__ == '__main__':
    find_all_courses()
