#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة المزيد من البيانات التجريبية للتقارير
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Course, PersonData, CourseParticipant
from datetime import datetime, timedelta
import random

def add_more_courses():
    """إضافة المزيد من الدورات"""
    
    with app.app_context():
        print("🚀 إضافة المزيد من الدورات...")
        
        # دورات إضافية متنوعة
        additional_courses = [
            # دورات متقدمة
            {
                'title': 'دورة الذكاء الاصطناعي في الأمن',
                'course_number': 'AI-SEC-001',
                'description': 'دورة متقدمة في تطبيقات الذكاء الاصطناعي في الأمن',
                'category': 'تقنية متقدمة',
                'level': 'متقدم',
                'agency_id': 2,  # وزارة الداخلية
                'center_id': 2,
                'participants': 15
            },
            {
                'title': 'دورة إدارة الموارد البشرية',
                'course_number': 'HR-MGT-001',
                'description': 'دورة شاملة في إدارة الموارد البشرية',
                'category': 'إدارية',
                'level': 'متوسط',
                'agency_id': 5,  # وزارة المالية
                'center_id': 1,
                'participants': 28
            },
            {
                'title': 'دورة الطب الطارئ المتقدم',
                'course_number': 'ADV-MED-001',
                'description': 'دورة متقدمة في الطب الطارئ والإنعاش',
                'category': 'طبية متقدمة',
                'level': 'متقدم',
                'agency_id': 4,  # وزارة الصحة
                'center_id': 4,
                'participants': 12
            },
            {
                'title': 'دورة التعليم الرقمي والتكنولوجيا',
                'course_number': 'DIGI-EDU-001',
                'description': 'دورة في التعليم الرقمي والتكنولوجيا التعليمية',
                'category': 'تعليمية تقنية',
                'level': 'متوسط',
                'agency_id': 3,  # وزارة التعليم
                'center_id': 3,
                'participants': 35
            },
            {
                'title': 'دورة القيادة الاستراتيجية',
                'course_number': 'STRAT-LEAD-001',
                'description': 'دورة في القيادة الاستراتيجية وإدارة التغيير',
                'category': 'قيادية',
                'level': 'متقدم',
                'agency_id': 1,  # وزارة الدفاع
                'center_id': 1,
                'participants': 20
            },
            {
                'title': 'دورة الأمن السيبراني للمؤسسات',
                'course_number': 'CYBER-ORG-001',
                'description': 'دورة متخصصة في أمن المؤسسات السيبراني',
                'category': 'أمنية تقنية',
                'level': 'متقدم',
                'agency_id': 2,  # وزارة الداخلية
                'center_id': 2,
                'participants': 18
            },
            {
                'title': 'دورة المحاسبة الرقمية',
                'course_number': 'DIGI-ACC-001',
                'description': 'دورة في المحاسبة الرقمية والأنظمة المالية',
                'category': 'مالية تقنية',
                'level': 'متوسط',
                'agency_id': 5,  # وزارة المالية
                'center_id': 1,
                'participants': 25
            },
            {
                'title': 'دورة إدارة الأزمات الصحية',
                'course_number': 'HEALTH-CRISIS-001',
                'description': 'دورة في إدارة الأزمات والطوارئ الصحية',
                'category': 'طبية إدارية',
                'level': 'متقدم',
                'agency_id': 4,  # وزارة الصحة
                'center_id': 4,
                'participants': 22
            },
            {
                'title': 'دورة تطوير المناهج التعليمية',
                'course_number': 'CURR-DEV-001',
                'description': 'دورة في تطوير وتحديث المناهج التعليمية',
                'category': 'تعليمية تطويرية',
                'level': 'متوسط',
                'agency_id': 3,  # وزارة التعليم
                'center_id': 3,
                'participants': 30
            },
            {
                'title': 'دورة الأمن الوطني المتقدم',
                'course_number': 'NAT-SEC-ADV-001',
                'description': 'دورة متقدمة في الأمن الوطني والاستراتيجي',
                'category': 'أمنية استراتيجية',
                'level': 'متقدم',
                'agency_id': 1,  # وزارة الدفاع
                'center_id': 1,
                'participants': 16
            }
        ]
        
        # أسماء إضافية
        additional_names = [
            'يحيى عبدالله محمد الحوثي', 'عبدالملك أحمد علي الزنداني', 'صالح محمد عبدالله الأحمر',
            'حمود سعد علي العولقي', 'عبدالرب منصور هادي الأحمر', 'علي عبدالله صالح المخلافي',
            'عبدالعزيز عبدالغني الحوثي', 'محمد علي الحوثي الزنداني', 'أحمد عبدالله الأحمر العولقي',
            'سعد علي محمد الشهري', 'خالد عبدالعزيز الدوسري', 'فهد محمد أحمد القحطاني',
            'عبدالرحمن سعد الغامدي', 'علي خالد العتيبي', 'محمد فهد المطيري',
            'أحمد عبدالعزيز العنزي', 'سعد محمد الرشيد', 'علي أحمد السبيعي',
            'عبدالله خالد الخالدي', 'محمد علي الصالح', 'أحمد سعد البقمي',
            'خالد عبدالرحمن الفهد', 'علي محمد الناصر', 'عبدالعزيز أحمد الراشد',
            'محمد سعد العبدالله', 'أحمد علي الملك', 'سعد عبدالله الأمير',
            'علي خالد الوزير', 'عبدالرحمن محمد الشيخ', 'فهد أحمد الدكتور'
        ]
        
        created_courses = 0
        total_participants = 0
        
        for course_data in additional_courses:
            print(f"📚 إنشاء دورة: {course_data['title']}")
            
            # إنشاء الدورة
            course = Course(
                title=course_data['title'],
                course_number=course_data['course_number'],
                description=course_data['description'],
                category=course_data['category'],
                level=course_data['level'],
                start_date=datetime.now() - timedelta(days=random.randint(10, 120)),
                end_date=datetime.now() + timedelta(days=random.randint(10, 60)),
                start_date_hijri='1445/06/01',
                end_date_hijri='1445/07/01',
                duration_days=random.randint(15, 45),
                trainer_id=1,
                agency_id=course_data['agency_id'],
                center_id=course_data['center_id'],
                total_participants=course_data['participants']
            )
            
            db.session.add(course)
            db.session.flush()
            
            # إنشاء المشاركين
            for i in range(course_data['participants']):
                name = additional_names[i % len(additional_names)]
                
                # إنشاء بيانات شخصية
                person = PersonData(
                    full_name=f"{name} - {course_data['course_number']}-{i+1}",
                    national_number=f"{random.randint(200000000, 999999999)}",
                    phone=f"07{random.randint(10000000, 99999999)}",
                    governorate=random.choice(['صنعاء', 'عدن', 'تعز', 'الحديدة', 'حضرموت', 'إب', 'ذمار', 'البيضاء']),
                    directorate=random.choice(['المديرية المركزية', 'مديرية الشمال', 'مديرية الجنوب', 'مديرية الشرق', 'مديرية الغرب']),
                    village=random.choice(['الحي السكني', 'الحي التجاري', 'الحي الحكومي', 'الحي الشعبي']),
                    age=random.randint(25, 55),
                    qualification=random.choice(['بكالوريوس', 'ماجستير', 'دكتوراه', 'دبلوم', 'ثانوية عامة']),
                    marital_status=random.choice(['متزوج', 'أعزب', 'مطلق', 'أرمل']),
                    job=random.choice(['موظف حكومي', 'ضابط', 'مهندس', 'طبيب', 'معلم', 'محاسب', 'إداري']),
                    agency=random.choice(['وزارة الدفاع', 'وزارة الداخلية', 'وزارة التعليم', 'وزارة الصحة', 'وزارة المالية'])
                )
                
                db.session.add(person)
                db.session.flush()
                
                # إنشاء مشاركة
                participation = CourseParticipant(
                    course_id=course.id,
                    personal_data_id=person.id,
                    status=random.choice(['مسجل', 'مكتمل', 'منسحب', 'نشط']),
                    entry_date=course.start_date + timedelta(days=random.randint(0, 3))
                )
                
                db.session.add(participation)
            
            created_courses += 1
            total_participants += course_data['participants']
            print(f"   ✅ تم إنشاء {course_data['participants']} مشارك")
        
        # حفظ التغييرات
        db.session.commit()
        
        print(f"\n🎉 تم إنشاء {created_courses} دورة إضافية بنجاح!")
        print(f"👥 إجمالي المشاركين الجدد: {total_participants}")
        
        # إحصائيات إجمالية
        total_courses = Course.query.count()
        total_all_participants = CourseParticipant.query.count()
        
        print(f"\n📊 الإحصائيات الإجمالية:")
        print(f"   - إجمالي الدورات: {total_courses}")
        print(f"   - إجمالي المشاركين: {total_all_participants}")

if __name__ == "__main__":
    add_more_courses()
