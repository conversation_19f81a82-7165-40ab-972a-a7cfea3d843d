{% extends 'layout.html' %}

{% block title %}إدارة البيانات الشخصية - DevExtreme{% endblock %}

{% block styles %}
<!-- DevExtreme CSS - محلي -->
<link rel="stylesheet" href="{{ url_for('static', filename='libs/devextreme/dx.light.css') }}">
<style>
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-size: 24px;
    }

    .dx-datagrid-headers {
        background-color: #f5f5f5;
    }

    .dx-datagrid-headers .dx-datagrid-text-content {
        font-weight: bold;
    }

    .dx-datagrid-rowsview .dx-row-alt {
        background-color: #f9f9f9;
    }

    .dx-datagrid-rowsview .dx-row:hover {
        background-color: #e6f7ff;
    }

    .dx-datagrid-headers .dx-header-row {
        background-color: #e6e6e6;
    }

    .dx-datagrid-headers .dx-header-row > td {
        border-bottom: 1px solid #ccc;
    }

    .dx-datagrid-borders .dx-datagrid-rowsview,
    .dx-datagrid-headers + .dx-datagrid-rowsview {
        border-top: 1px solid #ddd;
    }

    .dx-datagrid-borders .dx-datagrid-rowsview,
    .dx-datagrid-headers + .dx-datagrid-rowsview,
    .dx-datagrid-rowsview {
        border: 1px solid #ddd;
    }

    .dx-toolbar-items-container {
        margin-bottom: 10px;
    }

    .dx-button {
        margin-right: 5px;
    }

    .dx-popup-title {
        font-weight: bold;
        text-align: center;
    }

    .dx-field {
        margin-bottom: 10px;
    }

    .dx-field-label {
        font-weight: bold;
    }

    .dx-field-value {
        width: 100%;
    }

    .dx-texteditor-input {
        padding: 8px;
    }

    .dx-button-text {
        font-weight: bold;
    }

    .dx-button-mode-contained.dx-button-default {
        background-color: #337ab7;
        color: white;
    }

    .dx-button-mode-contained.dx-button-danger {
        background-color: #d9534f;
        color: white;
    }

    .dx-button-mode-contained.dx-button-success {
        background-color: #5cb85c;
        color: white;
    }

    .dx-button-mode-contained.dx-button-default:hover {
        background-color: #286090;
    }

    .dx-button-mode-contained.dx-button-danger:hover {
        background-color: #c9302c;
    }

    .dx-button-mode-contained.dx-button-success:hover {
        background-color: #449d44;
    }

    .dx-popup-content {
        padding: 20px;
    }

    .dx-popup-bottom {
        padding: 10px;
    }

    .dx-popup-bottom .dx-button {
        margin-left: 10px;
    }

    .dx-popup-bottom .dx-button:first-child {
        margin-left: 0;
    }

    .dx-popup-bottom .dx-button-text {
        font-weight: bold;
    }

    .dx-popup-bottom .dx-button-mode-contained.dx-button-default {
        background-color: #337ab7;
        color: white;
    }

    .dx-popup-bottom .dx-button-mode-contained.dx-button-danger {
        background-color: #d9534f;
        color: white;
    }

    .dx-popup-bottom .dx-button-mode-contained.dx-button-success {
        background-color: #5cb85c;
        color: white;
    }

    .dx-popup-bottom .dx-button-mode-contained.dx-button-default:hover {
        background-color: #286090;
    }

    .dx-popup-bottom .dx-button-mode-contained.dx-button-danger:hover {
        background-color: #c9302c;
    }

    .dx-popup-bottom .dx-button-mode-contained.dx-button-success:hover {
        background-color: #449d44;
    }

    .dx-popup-bottom .dx-toolbar-after {
        display: flex;
        justify-content: flex-end;
    }

    .dx-popup-bottom .dx-toolbar-before {
        display: flex;
        justify-content: flex-start;
    }

    .dx-popup-bottom .dx-toolbar-center {
        display: flex;
        justify-content: center;
    }

    .import-form {
        display: none;
        margin-top: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background-color: #f9f9f9;
    }

    .import-form h4 {
        margin-top: 0;
        margin-bottom: 15px;
    }

    .import-form .form-group {
        margin-bottom: 15px;
    }

    .import-form label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }

    .import-form input[type="file"] {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .import-form button {
        margin-top: 10px;
    }

    .btn-toolbar {
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1 class="text-center mb-4">إدارة البيانات الشخصية</h1>

            <div class="btn-toolbar mb-3">
                <div class="btn-group mr-2">
                    <button id="addButton" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة جديد
                    </button>
                    <button id="exportButton" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> تصدير إلى إكسل
                    </button>
                    <button id="templateButton" class="btn btn-info">
                        <i class="fas fa-file-download"></i> تنزيل قالب
                    </button>
                    <button id="toggleImportFormButton" class="btn btn-warning">
                        <i class="fas fa-file-import"></i> استيراد من إكسل
                    </button>
                </div>
            </div>

            <div id="importForm" class="import-form">
                <h4>استيراد بيانات من ملف إكسل</h4>
                <form action="/personal_data/excel/import" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="form-group">
                        <label for="excelFile">اختر ملف إكسل</label>
                        <input type="file" id="excelFile" name="excel_file" class="form-control" accept=".xlsx,.xls" required>
                    </div>
                    <div class="form-group">
                        <label for="sheetName">اسم الورقة (اختياري)</label>
                        <input type="text" id="sheetName" name="sheet_name" class="form-control" placeholder="اترك فارغاً لاستخدام الورقة الأولى">
                    </div>
                    <button type="submit" class="btn btn-primary">استيراد</button>
                    <button type="button" id="cancelImportButton" class="btn btn-secondary">إلغاء</button>
                </form>
            </div>

            <div id="gridContainer"></div>
        </div>
    </div>
</div>

<div class="loading-overlay" style="display: none;">
    <div>جاري التحميل...</div>
</div>
{% endblock %}

{% block scripts %}
<!-- DevExtreme JS - محلي -->
<script src="{{ url_for('static', filename='libs/devextreme/dx.all.js') }}"></script>
<script>
    $(document).ready(function() {
        // إظهار/إخفاء نموذج الاستيراد
        $("#toggleImportFormButton").on("click", function() {
            $("#importForm").slideToggle();
        });

        $("#cancelImportButton").on("click", function() {
            $("#importForm").slideUp();
        });

        // إنشاء مصدر البيانات
        var dataSource = new DevExpress.data.CustomStore({
            key: "id",
            load: function(loadOptions) {
                $('.loading-overlay').show();

                var d = $.Deferred();

                $.ajax({
                    url: "/personal_data/excel/data",
                    type: "GET",
                    dataType: "json",
                    cache: false
                }).done(function(response) {
                    $('.loading-overlay').hide();
                    console.log("البيانات المستلمة من الخادم:", response);

                    if (response && response.length > 0) {
                        console.log("أول عنصر:", response[0]);
                        if (!response[0].hasOwnProperty('id')) {
                            console.error("خطأ: حقل 'id' غير موجود في البيانات!");
                            response.forEach(function(item, index) {
                                if (!item.hasOwnProperty('id')) {
                                    item.id = index + 1;
                                }
                            });
                        }
                    }

                    d.resolve(response);
                }).fail(function(xhr, status, error) {
                    $('.loading-overlay').hide();
                    console.error("خطأ في الاتصال بالخادم:", error);
                    DevExpress.ui.notify("حدث خطأ أثناء تحميل البيانات: " + error, "error", 3000);
                    d.reject([]);
                });

                return d.promise();
            },
            insert: function(values) {
                $('.loading-overlay').show();

                return $.ajax({
                    url: "/personal_data/excel/add",
                    type: "POST",
                    data: JSON.stringify(values),
                    contentType: "application/json",
                    dataType: "json"
                }).done(function(response) {
                    $('.loading-overlay').hide();
                    if (response.success) {
                        DevExpress.ui.notify("تمت إضافة البيانات بنجاح", "success", 3000);
                    } else {
                        DevExpress.ui.notify("حدث خطأ أثناء إضافة البيانات: " + response.message, "error", 3000);
                    }
                }).fail(function(xhr, status, error) {
                    $('.loading-overlay').hide();
                    DevExpress.ui.notify("حدث خطأ أثناء إضافة البيانات: " + error, "error", 3000);
                });
            },
            update: function(key, values) {
                $('.loading-overlay').show();

                return $.ajax({
                    url: "/personal_data/excel/update/" + key,
                    type: "POST",
                    data: JSON.stringify(values),
                    contentType: "application/json",
                    dataType: "json"
                }).done(function(response) {
                    $('.loading-overlay').hide();
                    if (response.success) {
                        DevExpress.ui.notify("تم تحديث البيانات بنجاح", "success", 3000);
                    } else {
                        DevExpress.ui.notify("حدث خطأ أثناء تحديث البيانات: " + response.message, "error", 3000);
                    }
                }).fail(function(xhr, status, error) {
                    $('.loading-overlay').hide();
                    DevExpress.ui.notify("حدث خطأ أثناء تحديث البيانات: " + error, "error", 3000);
                });
            },
            remove: function(key) {
                $('.loading-overlay').show();

                return $.ajax({
                    url: "/personal_data/excel/delete/" + key,
                    type: "POST",
                    dataType: "json"
                }).done(function(response) {
                    $('.loading-overlay').hide();
                    if (response.success) {
                        DevExpress.ui.notify("تم حذف البيانات بنجاح", "success", 3000);
                    } else {
                        DevExpress.ui.notify("حدث خطأ أثناء حذف البيانات: " + response.message, "error", 3000);
                    }
                }).fail(function(xhr, status, error) {
                    $('.loading-overlay').hide();
                    DevExpress.ui.notify("حدث خطأ أثناء حذف البيانات: " + error, "error", 3000);
                });
            }
        });

        // إنشاء جدول البيانات
        $("#gridContainer").dxDataGrid({
            dataSource: dataSource,
            showBorders: true,
            rtlEnabled: true,
            columnAutoWidth: true,
            wordWrapEnabled: true,
            showColumnLines: true,
            showRowLines: true,
            rowAlternationEnabled: true,
            hoverStateEnabled: true,
            paging: {
                pageSize: 10
            },
            pager: {
                showPageSizeSelector: true,
                allowedPageSizes: [10, 25, 50, 100],
                showInfo: true
            },
            searchPanel: {
                visible: true,
                width: 240,
                placeholder: "بحث..."
            },
            headerFilter: {
                visible: true
            },
            filterRow: {
                visible: true,
                applyFilter: "auto"
            },
            editing: {
                mode: "popup",
                allowAdding: true,
                allowUpdating: true,
                allowDeleting: true,
                popup: {
                    title: "بيانات الشخص",
                    showTitle: true,
                    width: 700,
                    height: 525,
                    position: {
                        my: "center",
                        at: "center",
                        of: window
                    }
                },
                form: {
                    items: [
                        {
                            itemType: "group",
                            colCount: 2,
                            items: [
                                {
                                    dataField: "full_name",
                                    caption: "الاسم الشخصي",
                                    validationRules: [{ type: "required" }]
                                },
                                {
                                    dataField: "nickname",
                                    caption: "الاسم المستعار"
                                },
                                {
                                    dataField: "age",
                                    caption: "العمر",
                                    dataType: "number"
                                },
                                {
                                    dataField: "governorate",
                                    caption: "المحافظة"
                                },
                                {
                                    dataField: "directorate",
                                    caption: "المديرية"
                                },
                                {
                                    dataField: "uzla",
                                    caption: "العزلة"
                                },
                                {
                                    dataField: "village",
                                    caption: "الحي/القرية"
                                },
                                {
                                    dataField: "qualification",
                                    caption: "المؤهل العلمي"
                                },
                                {
                                    dataField: "marital_status",
                                    caption: "الحالة الاجتماعية"
                                },
                                {
                                    dataField: "job",
                                    caption: "العمل"
                                },
                                {
                                    dataField: "agency",
                                    caption: "الإدارة"
                                },
                                {
                                    dataField: "work_place",
                                    caption: "مكان العمل"
                                },
                                {
                                    dataField: "national_number",
                                    caption: "الرقم الوطني"
                                },
                                {
                                    dataField: "military_number",
                                    caption: "الرقم العسكري"
                                },
                                {
                                    dataField: "phone",
                                    caption: "رقم التلفون"
                                }
                            ]
                        }
                    ]
                }
            },
            columns: [
                {
                    dataField: "full_name",
                    caption: "الاسم الشخصي",
                    validationRules: [{ type: "required" }]
                },
                {
                    dataField: "nickname",
                    caption: "الاسم المستعار"
                },
                {
                    dataField: "age",
                    caption: "العمر",
                    dataType: "number"
                },
                {
                    dataField: "governorate",
                    caption: "المحافظة"
                },
                {
                    dataField: "directorate",
                    caption: "المديرية"
                },
                {
                    dataField: "uzla",
                    caption: "العزلة"
                },
                {
                    dataField: "village",
                    caption: "الحي/القرية"
                },
                {
                    dataField: "qualification",
                    caption: "المؤهل العلمي"
                },
                {
                    dataField: "marital_status",
                    caption: "الحالة الاجتماعية"
                },
                {
                    dataField: "job",
                    caption: "العمل"
                },
                {
                    dataField: "agency",
                    caption: "الإدارة"
                },
                {
                    dataField: "work_place",
                    caption: "مكان العمل"
                },
                {
                    dataField: "national_number",
                    caption: "الرقم الوطني"
                },
                {
                    dataField: "military_number",
                    caption: "الرقم العسكري"
                },
                {
                    dataField: "phone",
                    caption: "رقم التلفون"
                }
            ],
            onRowInserting: function(e) {
                console.log("بيانات الإضافة:", e.data);
            },
            onRowUpdating: function(e) {
                console.log("بيانات التحديث:", e.newData);
            },
            onRowRemoving: function(e) {
                console.log("بيانات الحذف:", e.data);
            }
        });

        // زر الإضافة
        $("#addButton").on("click", function() {
            $("#gridContainer").dxDataGrid("instance").addRow();
        });

        // زر التصدير
        $("#exportButton").on("click", function() {
            window.location.href = "/personal_data/excel/export";
        });

        // زر تنزيل القالب
        $("#templateButton").on("click", function() {
            window.location.href = "/personal_data/excel/template";
        });
    });
</script>
{% endblock %}
