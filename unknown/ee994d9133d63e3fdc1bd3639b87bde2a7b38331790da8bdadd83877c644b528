#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء جدول التصحيحات المخصصة
"""

import sqlite3
from datetime import datetime, timezone

def create_corrections_table():
    """
    إنشاء جدول name_correction في قاعدة البيانات
    """
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    
    # إنشاء الجدول
    create_table_query = """
        CREATE TABLE IF NOT EXISTS name_correction (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            wrong_name VARCHAR(200) NOT NULL,
            correct_name VARCHAR(200) NOT NULL,
            correction_type VARCHAR(50) NOT NULL,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            usage_count INTEGER DEFAULT 0,
            FOREIGN KEY (created_by) REFERENCES user (id)
        )
    """
    
    try:
        cursor.execute(create_table_query)
        conn.commit()
        print("✅ تم إنشاء جدول name_correction بنجاح!")
        
        # التحقق من وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='name_correction'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ تم التحقق من وجود الجدول")
            
            # عرض هيكل الجدول
            cursor.execute("PRAGMA table_info(name_correction)")
            columns = cursor.fetchall()
            print("\n📊 هيكل الجدول:")
            for col in columns:
                print(f"   • {col[1]} ({col[2]})")
        else:
            print("❌ فشل في إنشاء الجدول")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجدول: {e}")
    
    finally:
        conn.close()

def add_sample_corrections():
    """
    إضافة تصحيحات تجريبية لقاعدة البيانات
    """
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    
    # التصحيحات التجريبية
    sample_corrections = [
        {
            'wrong_name': 'مرتضي',
            'correct_name': 'مرتضى',
            'correction_type': 'alif_maqsura',
            'created_by': 1,  # افتراض أن المستخدم الأول هو admin
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'عيسي',
            'correct_name': 'عيسى',
            'correction_type': 'alif_maqsura',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'ابو_الدين',
            'correct_name': 'أبو الدين',
            'correction_type': 'compound_names',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'ام_الخير',
            'correct_name': 'أم الخير',
            'correction_type': 'compound_names',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'محمد123',
            'correct_name': 'محمد',
            'correction_type': 'symbols',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'علي@صالح',
            'correct_name': 'علي صالح',
            'correction_type': 'symbols',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'الحوثى',
            'correct_name': 'الحوثي',
            'correction_type': 'alif_maqsura',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'ايمان',
            'correct_name': 'إيمان',
            'correction_type': 'hamza',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'اسراء',
            'correct_name': 'إسراء',
            'correction_type': 'hamza',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'عبداللة',
            'correct_name': 'عبدالله',
            'correction_type': 'other',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        }
    ]
    
    # إدراج التصحيحات
    insert_query = """
        INSERT INTO name_correction 
        (wrong_name, correct_name, correction_type, created_by, created_at, is_active, usage_count)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """
    
    added_count = 0
    for correction in sample_corrections:
        try:
            # التحقق من عدم وجود التصحيح مسبقاً
            check_query = "SELECT id FROM name_correction WHERE wrong_name = ? AND correct_name = ?"
            existing = cursor.execute(check_query, (correction['wrong_name'], correction['correct_name'])).fetchone()
            
            if not existing:
                cursor.execute(insert_query, (
                    correction['wrong_name'],
                    correction['correct_name'],
                    correction['correction_type'],
                    correction['created_by'],
                    correction['created_at'],
                    correction['is_active'],
                    correction['usage_count']
                ))
                added_count += 1
                print(f"✅ تم إضافة: {correction['wrong_name']} → {correction['correct_name']}")
            else:
                print(f"⚠️  موجود مسبقاً: {correction['wrong_name']} → {correction['correct_name']}")
                
        except Exception as e:
            print(f"❌ خطأ في إضافة {correction['wrong_name']}: {e}")
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print(f"\n🎉 تم إضافة {added_count} تصحيح جديد بنجاح!")
    print("🌐 يمكنك الآن مراجعة التصحيحات في: http://localhost:5000/person_data/manage_corrections")
    
    return added_count

if __name__ == "__main__":
    print("🔧 إنشاء جدول التصحيحات المخصصة...")
    create_corrections_table()
    
    print("\n📝 إضافة تصحيحات تجريبية...")
    add_sample_corrections()
