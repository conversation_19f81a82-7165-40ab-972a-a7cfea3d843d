#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 تشخيص مشكلة ظهور المشاركين الحاليين في البحث
"""

import sqlite3
import requests

def check_database_participants():
    """فحص المشاركين في قاعدة البيانات مباشرة"""
    print("🔍 فحص المشاركين في قاعدة البيانات مباشرة...")
    
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        # فحص المشاركين في الدورة رقم 1
        cursor.execute("""
            SELECT cp.id, cp.course_id, cp.personal_data_id, pd.full_name, cp.status
            FROM course_participant cp 
            JOIN person_data pd ON cp.personal_data_id = pd.id 
            WHERE cp.course_id = 1
            ORDER BY cp.id
        """)
        participants = cursor.fetchall()
        
        print(f"📊 عدد المشاركين في الدورة 1: {len(participants)}")
        print("📋 قائمة المشاركين:")
        
        participant_ids = []
        for p in participants:
            print(f"   ID: {p[0]} | PersonID: {p[2]} | الاسم: {p[3]} | الحالة: {p[4]}")
            participant_ids.append(p[2])
        
        # فحص الشخص المحدد
        cursor.execute("SELECT id, full_name FROM person_data WHERE full_name LIKE '%علي صالح محمد الحميري1%'")
        ali_records = cursor.fetchall()
        
        print(f"\n🎯 البحث عن 'علي صالح محمد الحميري1':")
        for record in ali_records:
            person_id = record[0]
            name = record[1]
            is_participant = person_id in participant_ids
            print(f"   ID: {person_id} | الاسم: {name} | مشارك: {'نعم' if is_participant else 'لا'}")
        
        conn.close()
        return participant_ids, ali_records
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return [], []

def test_search_api():
    """اختبار API البحث"""
    print("\n🔍 اختبار API البحث...")
    
    try:
        # اختبار البحث عن "علي"
        response = requests.get("http://localhost:5002/course/1/search_people?q=علي", timeout=10)
        
        if response.status_code == 200:
            results = response.json()
            print(f"📊 نتائج البحث: {len(results)} شخص")
            
            # البحث عن "علي صالح محمد الحميري1"
            found_ali = None
            for person in results:
                if "علي صالح محمد الحميري1" in person['name']:
                    found_ali = person
                    break
            
            if found_ali:
                print(f"❌ المشكلة: تم العثور على {found_ali['name']} في نتائج البحث!")
                print(f"   ID: {found_ali['id']}")
                return found_ali
            else:
                print("✅ لم يتم العثور على 'علي صالح محمد الحميري1' في نتائج البحث")
                return None
        else:
            print(f"❌ خطأ في API: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        return None

def test_specific_person_check():
    """اختبار فحص شخص محدد"""
    print("\n🎯 اختبار فحص شخص محدد...")
    
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        # البحث عن الشخص
        cursor.execute("SELECT id, full_name FROM person_data WHERE full_name = 'علي صالح محمد الحميري1'")
        person = cursor.fetchone()
        
        if person:
            person_id = person[0]
            person_name = person[1]
            print(f"✅ تم العثور على الشخص: ID {person_id} - {person_name}")
            
            # فحص وجوده في الدورة
            cursor.execute("""
                SELECT id, course_id, status 
                FROM course_participant 
                WHERE course_id = 1 AND personal_data_id = ?
            """, (person_id,))
            participation = cursor.fetchone()
            
            if participation:
                print(f"✅ الشخص مشارك في الدورة: ID {participation[0]} | الحالة: {participation[2]}")
                return True
            else:
                print("❌ الشخص غير مشارك في الدورة!")
                return False
        else:
            print("❌ لم يتم العثور على الشخص في قاعدة البيانات")
            return False
            
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الفحص: {e}")
        return False

def fix_search_logic():
    """إصلاح منطق البحث"""
    print("\n🔧 تشخيص منطق البحث...")
    
    # محاكاة منطق البحث
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        # البحث عن الأشخاص الذين يحتوون على "علي"
        cursor.execute("SELECT id, full_name FROM person_data WHERE full_name LIKE '%علي%' LIMIT 10")
        people = cursor.fetchall()
        
        print(f"📊 الأشخاص الذين يحتوون على 'علي': {len(people)}")
        
        for person in people:
            person_id = person[0]
            person_name = person[1]
            
            # فحص المشاركة
            cursor.execute("""
                SELECT id FROM course_participant 
                WHERE course_id = 1 AND personal_data_id = ?
            """, (person_id,))
            participation = cursor.fetchone()
            
            is_participant = participation is not None
            status = "مشارك" if is_participant else "متاح"
            
            print(f"   ID: {person_id} | {person_name} | {status}")
            
            # إذا كان "علي صالح محمد الحميري1" ومشارك، فهذه هي المشكلة
            if "علي صالح محمد الحميري1" in person_name and is_participant:
                print(f"🎯 وجدت المشكلة: {person_name} مشارك لكن يظهر في البحث!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص شامل لمشكلة ظهور المشاركين في البحث")
    print("=" * 60)
    
    # 1. فحص قاعدة البيانات مباشرة
    participant_ids, ali_records = check_database_participants()
    
    # 2. اختبار API البحث
    found_in_search = test_search_api()
    
    # 3. فحص شخص محدد
    is_participant = test_specific_person_check()
    
    # 4. تشخيص منطق البحث
    fix_search_logic()
    
    print("\n" + "=" * 60)
    print("📊 ملخص التشخيص:")
    
    if ali_records:
        for record in ali_records:
            person_id = record[0]
            name = record[1]
            is_in_course = person_id in participant_ids
            appears_in_search = found_in_search is not None and found_in_search['id'] == person_id
            
            print(f"🎯 {name}:")
            print(f"   ID: {person_id}")
            print(f"   في الدورة: {'نعم' if is_in_course else 'لا'}")
            print(f"   يظهر في البحث: {'نعم' if appears_in_search else 'لا'}")
            
            if is_in_course and appears_in_search:
                print("   ❌ مشكلة: مشارك لكن يظهر في البحث!")
            elif is_in_course and not appears_in_search:
                print("   ✅ صحيح: مشارك ولا يظهر في البحث")
            elif not is_in_course and appears_in_search:
                print("   ✅ صحيح: غير مشارك ويظهر في البحث")

if __name__ == "__main__":
    main()
