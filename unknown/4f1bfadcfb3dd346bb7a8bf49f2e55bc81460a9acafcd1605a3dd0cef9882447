{% extends "layout.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-file-excel me-2"></i>إدارة البيانات بالإكسل
                </h1>
                <p class="page-subtitle">استيراد وتصدير وإدارة بيانات الأشخاص باستخدام ملفات Excel</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- قسم التصدير -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-download me-2"></i>تصدير البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">تصدير بيانات الأشخاص الموجودة في النظام إلى ملف Excel</p>

                    <div class="d-grid gap-2">
                        <a href="{{ url_for('person_data.person_data_export') }}" class="btn btn-success">
                            <i class="fas fa-file-excel me-2"></i>تصدير جميع البيانات
                        </a>

                        <a href="{{ url_for('person_data.export_duplicates') }}" class="btn btn-warning">
                            <i class="fas fa-copy me-2"></i>تصدير الأسماء المكررة
                        </a>
                    </div>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            سيتم تنزيل ملف Excel يحتوي على جميع البيانات المطلوبة
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الاستيراد -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-upload me-2"></i>استيراد البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">استيراد بيانات جديدة من ملف Excel إلى النظام</p>

                    <form action="{{ url_for('person_data.person_data_import') }}" method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="excel_file" class="form-label">اختر ملف Excel</label>
                            <input type="file" class="form-control" id="excel_file" name="excel_file"
                                   accept=".xlsx,.xls" required>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i>استيراد البيانات
                            </button>
                        </div>
                    </form>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            يجب أن يكون الملف بتنسيق Excel (.xlsx أو .xls)
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- قسم القوالب -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-download me-2"></i>تنزيل القوالب
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">تنزيل قوالب Excel جاهزة للاستخدام</p>

                    <div class="d-grid gap-2">
                        <a href="{{ url_for('person_data.person_data_template') }}" class="btn btn-info">
                            <i class="fas fa-file-alt me-2"></i>قالب بيانات الأشخاص
                        </a>

                        <a href="{{ url_for('person_data.create_sample_excel') }}" class="btn btn-outline-info">
                            <i class="fas fa-file-excel me-2"></i>ملف نموذجي بالبيانات
                        </a>
                    </div>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            استخدم هذه القوالب لضمان التنسيق الصحيح
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم تحليل الأسماء -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>تحليل الأسماء
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">تحليل ومقارنة الأسماء من ملف Excel مع قاعدة البيانات</p>

                    <div class="d-grid gap-2">
                        <a href="{{ url_for('person_data.name_analysis') }}" class="btn btn-warning">
                            <i class="fas fa-analytics me-2"></i>تحليل الأسماء
                        </a>

                        <a href="{{ url_for('person_data.duplicate_names') }}" class="btn btn-outline-warning">
                            <i class="fas fa-copy me-2"></i>الأسماء المكررة
                        </a>
                    </div>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            اكتشف الأسماء المكررة والجديدة قبل الاستيراد
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم الإحصائيات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="stat-number text-primary">{{ total_persons }}</h3>
                                <p class="stat-label">إجمالي الأشخاص</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="stat-number text-success">{{ total_courses }}</h3>
                                <p class="stat-label">إجمالي الدورات</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="stat-number text-warning">{{ total_participants }}</h3>
                                <p class="stat-label">إجمالي المشاركين</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 class="stat-number text-info">{{ duplicate_names_count }}</h3>
                                <p class="stat-label">الأسماء المكررة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار التنقل -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('person_data.person_data_table') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة إلى جدول البيانات
                </a>

                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-home me-2"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.stat-card {
    padding: 20px;
    border-radius: 8px;
    background: #f8f9fa;
    margin: 10px 0;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    margin-bottom: 0;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.card-header {
    border-bottom: none;
}

.page-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.page-title {
    color: #2c3e50;
    margin-bottom: 5px;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}
</style>
{% endblock %}
