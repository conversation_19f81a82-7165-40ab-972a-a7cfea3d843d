#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص بيانات قاعدة البيانات
"""

import sqlite3

def check_corrections_data():
    """
    فحص بيانات التصحيحات في قاعدة البيانات
    """
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('database.db')
        cursor = conn.cursor()
        
        # فحص وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='name_correction'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ جدول name_correction غير موجود!")
            return
        
        print("✅ جدول name_correction موجود")
        
        # فحص هيكل الجدول
        cursor.execute("PRAGMA table_info(name_correction)")
        columns = cursor.fetchall()
        print("\n📊 هيكل الجدول:")
        for col in columns:
            print(f"   • {col[1]} ({col[2]})")
        
        # فحص البيانات
        cursor.execute("SELECT COUNT(*) FROM name_correction")
        count = cursor.fetchone()[0]
        print(f"\n📈 عدد التصحيحات: {count}")
        
        if count > 0:
            # عرض جميع البيانات
            cursor.execute("""
                SELECT nc.id, nc.wrong_name, nc.correct_name, nc.correction_type, 
                       nc.created_at, nc.usage_count, nc.is_active, u.username
                FROM name_correction nc 
                LEFT JOIN user u ON nc.created_by = u.id 
                ORDER BY nc.created_at DESC
            """)
            
            rows = cursor.fetchall()
            print("\n📋 البيانات الموجودة:")
            print("ID | الاسم الخطأ | الاسم الصحيح | النوع | التاريخ | الاستخدام | نشط | المستخدم")
            print("-" * 100)
            
            for row in rows:
                print(f"{row[0]:2d} | {row[1]:12s} | {row[2]:12s} | {row[3]:15s} | {str(row[4])[:10]:10s} | {row[5]:8d} | {row[6]:4d} | {row[7] or 'غير معروف'}")
        
        # فحص جدول المستخدمين
        cursor.execute("SELECT COUNT(*) FROM user")
        user_count = cursor.fetchone()[0]
        print(f"\n👥 عدد المستخدمين: {user_count}")
        
        if user_count > 0:
            cursor.execute("SELECT id, username, role FROM user LIMIT 5")
            users = cursor.fetchall()
            print("\n👤 المستخدمون:")
            for user in users:
                print(f"   • ID: {user[0]}, اسم المستخدم: {user[1]}, الدور: {user[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

if __name__ == "__main__":
    check_corrections_data()
