from app import app, db

with app.app_context():
    print("🔄 تحديث قاعدة البيانات...")
    
    # إنشاء جميع الجداول
    db.create_all()
    
    print("✅ تم تحديث قاعدة البيانات بنجاح!")
    
    # فحص الجداول
    from app import Course, CourseParticipant
    
    courses = Course.query.all()
    print(f"عدد الدورات: {len(courses)}")
    
    for course in courses:
        participants = CourseParticipant.query.filter_by(course_id=course.id).count()
        print(f"دورة {course.course_number}: {participants} مشارك")
