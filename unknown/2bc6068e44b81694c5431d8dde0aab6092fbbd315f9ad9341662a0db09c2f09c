#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تشغيل تحديث قاعدة البيانات الشامل
"""

import os
import sys
import subprocess
from datetime import datetime

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 80)
    print("🚀 نظام تحديث قاعدة البيانات الشامل")
    print("=" * 80)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 المجلد الحالي: {os.getcwd()}")
    print("=" * 80)

def check_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        'app.py',
        'update_database.py',
        'seed_basic_data.py'
    ]
    
    print("🔍 التحقق من الملفات المطلوبة...")
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - مفقود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def check_database():
    """التحقق من وجود قاعدة البيانات"""
    db_files = [
        'training_system.db',
        'instance/site.db',
        'site.db'
    ]
    
    print("\n🗄️ البحث عن قاعدة البيانات...")
    found_db = None
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"   ✅ العثور على: {db_file}")
            found_db = db_file
            break
        else:
            print(f"   ❌ غير موجود: {db_file}")
    
    if found_db:
        size = os.path.getsize(found_db)
        print(f"   📊 حجم قاعدة البيانات: {size:,} بايت")
        return found_db
    else:
        print("   ⚠️ لم يتم العثور على قاعدة بيانات موجودة")
        print("   🔨 سيتم إنشاء قاعدة بيانات جديدة")
        return None

def run_script(script_name, description):
    """تشغيل سكريبت Python"""
    print(f"\n🔄 {description}...")
    print(f"   📜 تشغيل: {script_name}")
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, 
                              text=True, 
                              encoding='utf-8')
        
        if result.returncode == 0:
            print(f"   ✅ تم تشغيل {script_name} بنجاح")
            if result.stdout:
                print("   📤 المخرجات:")
                for line in result.stdout.split('\n'):
                    if line.strip():
                        print(f"      {line}")
            return True
        else:
            print(f"   ❌ فشل تشغيل {script_name}")
            if result.stderr:
                print("   📤 الأخطاء:")
                for line in result.stderr.split('\n'):
                    if line.strip():
                        print(f"      {line}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في تشغيل {script_name}: {str(e)}")
        return False

def show_menu():
    """عرض قائمة الخيارات"""
    print("\n📋 خيارات التحديث:")
    print("   1️⃣  تحديث شامل (موصى به)")
    print("   2️⃣  تحديث قاعدة البيانات فقط")
    print("   3️⃣  إضافة البيانات الأساسية فقط")
    print("   4️⃣  فحص حالة قاعدة البيانات")
    print("   0️⃣  خروج")
    
    while True:
        try:
            choice = input("\n🔢 اختر رقم الخيار: ").strip()
            if choice in ['0', '1', '2', '3', '4']:
                return choice
            else:
                print("❌ خيار غير صحيح، يرجى اختيار رقم من 0 إلى 4")
        except KeyboardInterrupt:
            print("\n\n👋 تم إلغاء العملية")
            return '0'

def full_update():
    """تحديث شامل"""
    print("\n🚀 بدء التحديث الشامل...")
    
    # 1. تحديث قاعدة البيانات
    if not run_script('update_database.py', 'تحديث هيكل قاعدة البيانات'):
        print("❌ فشل تحديث قاعدة البيانات")
        return False
    
    # 2. إضافة البيانات الأساسية
    if not run_script('seed_basic_data.py', 'إضافة البيانات الأساسية'):
        print("❌ فشل إضافة البيانات الأساسية")
        return False
    
    print("\n🎉 تم التحديث الشامل بنجاح!")
    return True

def database_only_update():
    """تحديث قاعدة البيانات فقط"""
    print("\n🔧 تحديث قاعدة البيانات فقط...")
    return run_script('update_database.py', 'تحديث هيكل قاعدة البيانات')

def seed_data_only():
    """إضافة البيانات الأساسية فقط"""
    print("\n🌱 إضافة البيانات الأساسية فقط...")
    return run_script('seed_basic_data.py', 'إضافة البيانات الأساسية')

def check_status():
    """فحص حالة قاعدة البيانات"""
    print("\n🔍 فحص حالة قاعدة البيانات...")
    
    try:
        import sqlite3
        
        db_path = check_database()
        if not db_path:
            print("❌ لا توجد قاعدة بيانات للفحص")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"\n📊 إحصائيات قاعدة البيانات ({db_path}):")
        print(f"   📋 عدد الجداول: {len(tables)}")
        
        for table in sorted(tables):
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   📄 {table}: {count:,} سجل")
            except:
                print(f"   ❌ {table}: خطأ في القراءة")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # التحقق من الملفات
    if not check_files():
        print("\n❌ لا يمكن المتابعة بدون الملفات المطلوبة")
        return
    
    # التحقق من قاعدة البيانات
    check_database()
    
    # عرض القائمة والتنفيذ
    while True:
        choice = show_menu()
        
        if choice == '0':
            print("\n👋 شكراً لاستخدام نظام تحديث قاعدة البيانات")
            break
        elif choice == '1':
            full_update()
        elif choice == '2':
            database_only_update()
        elif choice == '3':
            seed_data_only()
        elif choice == '4':
            check_status()
        
        input("\n⏸️  اضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
