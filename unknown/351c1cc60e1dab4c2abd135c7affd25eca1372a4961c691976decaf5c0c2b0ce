#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from werkzeug.security import generate_password_hash

print("🔧 إصلاح نهائي لمشكلة تسجيل الدخول...")

# استخدام training_system.db
db_file = 'training_system.db'

try:
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    print(f"🔗 متصل بـ {db_file}")
    
    # حذف جميع المستخدمين الحاليين
    cursor.execute("DELETE FROM user")
    deleted_count = cursor.rowcount
    print(f"🗑️ تم حذف {deleted_count} مستخدم")
    
    # إنشاء مدير جديد بكلمة مرور بسيطة
    password_hash = generate_password_hash('admin123')
    print(f"🔐 تم تشفير كلمة المرور")
    
    cursor.execute("""
        INSERT INTO user (username, email, password, role, created_at)
        VALUES (?, ?, ?, ?, datetime('now'))
    """, ('admin', '<EMAIL>', password_hash, 'admin'))
    
    conn.commit()
    print("✅ تم إنشاء المدير الجديد!")
    
    # التحقق من إنشاء المدير
    cursor.execute("SELECT id, username, email, role, password FROM user WHERE email = ?", ('<EMAIL>',))
    admin = cursor.fetchone()
    
    if admin:
        print("✅ تم التحقق من وجود المدير:")
        print(f"  ID: {admin[0]}")
        print(f"  اسم المستخدم: {admin[1]}")
        print(f"  البريد: {admin[2]}")
        print(f"  الدور: {admin[3]}")
        print(f"  كلمة المرور المشفرة: {admin[4][:50]}...")
        
        # اختبار كلمة المرور
        from werkzeug.security import check_password_hash
        if check_password_hash(admin[4], 'admin123'):
            print("✅ كلمة المرور صحيحة")
        else:
            print("❌ كلمة المرور غير صحيحة")
    else:
        print("❌ فشل في إنشاء المدير")
    
    # إنشاء مستخدم اختبار إضافي
    test_password = generate_password_hash('test123')
    cursor.execute("""
        INSERT INTO user (username, email, password, role, created_at)
        VALUES (?, ?, ?, ?, datetime('now'))
    """, ('test', '<EMAIL>', test_password, 'trainee'))
    
    conn.commit()
    print("✅ تم إنشاء مستخدم اختبار إضافي")
    
    # عرض جميع المستخدمين
    cursor.execute("SELECT id, username, email, role FROM user")
    users = cursor.fetchall()
    print(f"\n👥 إجمالي المستخدمين: {len(users)}")
    for user in users:
        print(f"  - ID: {user[0]}, اسم المستخدم: {user[1]}, البريد: {user[2]}, الدور: {user[3]}")
    
    conn.close()
    
    # حذف ملف database.db الصغير
    if os.path.exists('database.db'):
        try:
            os.remove('database.db')
            print("🗑️ تم حذف ملف database.db الصغير")
        except:
            print("⚠️ لم يتم حذف database.db")
    
except Exception as e:
    print(f"❌ خطأ: {str(e)}")

print("\n🌐 يمكنك الآن تسجيل الدخول على:")
print("http://127.0.0.1:5000/login")
print("📧 البريد: <EMAIL>")
print("🔑 كلمة المرور: admin123")
print("\nأو:")
print("📧 البريد: <EMAIL>")
print("🔑 كلمة المرور: test123")
