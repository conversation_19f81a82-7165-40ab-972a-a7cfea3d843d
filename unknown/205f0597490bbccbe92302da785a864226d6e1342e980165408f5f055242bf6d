#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_current_db():
    conn = sqlite3.connect('training_system.db')
    cursor = conn.cursor()
    
    print("🔍 فحص قاعدة البيانات الحالية:")
    print("=" * 50)
    
    # عرض جميع الجداول
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [t[0] for t in cursor.fetchall()]
    
    print("📋 الجداول المتوفرة:")
    for table in tables:
        print(f"  - {table}")
    
    print("\n📊 إحصائيات البيانات:")
    
    # فحص الجداول المهمة
    important_tables = ['user', 'course', 'person_data', 'personal_data', 'course_participant']
    
    for table in important_tables:
        if table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  ✅ {table}: {count:,} سجل")
                
                # عرض عينة من البيانات
                if table == 'person_data' and count > 0:
                    cursor.execute("SELECT full_name FROM person_data LIMIT 5")
                    persons = cursor.fetchall()
                    print("    عينة من الأشخاص:")
                    for person in persons:
                        print(f"      - {person[0]}")
                        
                elif table == 'course' and count > 0:
                    cursor.execute("SELECT course_number, title FROM course LIMIT 5")
                    courses = cursor.fetchall()
                    print("    عينة من الدورات:")
                    for course in courses:
                        print(f"      - {course[0]}: {course[1]}")
                        
            except Exception as e:
                print(f"  ❌ خطأ في {table}: {e}")
        else:
            print(f"  ⚪ {table}: غير موجود")
    
    conn.close()

if __name__ == '__main__':
    check_current_db()
