#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة بيانات تجريبية لقاعدة البيانات لاختبار فحص التطابق
"""

import sqlite3
import os

def add_test_data():
    """إضافة بيانات تجريبية لاختبار فحص التطابق"""
    
    # الاتصال بقاعدة البيانات
    db_path = 'instance/training_system.db'
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # بيانات تجريبية للاختبار
    test_persons = [
        # هؤلاء الأشخاص موجودون في قاعدة البيانات وسيتم اختبار التطابق معهم
        ("محمد أحمد علي", "12345678901", "0501234567", "M001"),
        ("عبدالله محمد", "11111111111", "0501111111", "M003"),
        ("أحمد عبدالرحمن", "33333333333", "0503333333", "M005"),
        
        # أشخاص إضافيون لاختبار التشابه
        ("محمد عبدالله أحمد سالم", "20202020202", "0502020202", "M020"),
        ("عبدالله محمد أحمد", "21212121212", "0502121212", "M021"),
        ("أحمد محمد عبدالله", "22222222223", "0502222223", "M022"),
    ]
    
    print("🔄 إضافة بيانات تجريبية...")
    
    added_count = 0
    for name, national_id, phone, military_id in test_persons:
        try:
            # التحقق من عدم وجود الشخص مسبقاً
            cursor.execute("SELECT id FROM person_data WHERE full_name = ?", (name,))
            if cursor.fetchone():
                print(f"⚪ موجود مسبقاً: {name}")
                continue
            
            # إضافة الشخص
            cursor.execute("""
                INSERT INTO person_data (full_name, national_number, phone, military_number)
                VALUES (?, ?, ?, ?)
            """, (name, national_id, phone, military_id))
            
            added_count += 1
            print(f"✅ تم إضافة: {name}")
            
        except Exception as e:
            print(f"❌ خطأ في إضافة {name}: {e}")
    
    # حفظ التغييرات
    conn.commit()
    
    # عرض إحصائيات
    cursor.execute("SELECT COUNT(*) FROM person_data")
    total_count = cursor.fetchone()[0]
    
    print(f"\n📊 الإحصائيات:")
    print(f"   ✅ تم إضافة: {added_count} شخص جديد")
    print(f"   📋 إجمالي الأشخاص في قاعدة البيانات: {total_count}")
    
    # عرض عينة من البيانات المضافة
    print(f"\n👥 عينة من البيانات المضافة:")
    cursor.execute("""
        SELECT full_name, national_number, phone, military_number 
        FROM person_data 
        WHERE full_name IN (?, ?, ?)
    """, (test_persons[0][0], test_persons[1][0], test_persons[2][0]))
    
    for row in cursor.fetchall():
        name, national, phone, military = row
        print(f"   • {name}")
        print(f"     - رقم وطني: {national}")
        print(f"     - هاتف: {phone}")
        print(f"     - رقم عسكري: {military}")
    
    conn.close()
    
    print(f"\n🎯 الآن يمكنك اختبار ملف Excel التجريبي:")
    print(f"   📁 اختبار_التطابق_المتقدم_*.xlsx")
    print(f"   🔍 سيتم اكتشاف التطابقات في:")
    print(f"      - الاسم والرقم الوطني")
    print(f"      - الاسم ورقم الهاتف") 
    print(f"      - الاسم والرقم العسكري")
    print(f"      - الأرقام فقط (بأسماء مختلفة)")

if __name__ == '__main__':
    add_test_data()
