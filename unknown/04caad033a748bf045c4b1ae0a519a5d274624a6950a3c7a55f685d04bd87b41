#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار للتصحيحات المخصصة
"""

import pandas as pd
from datetime import datetime

def create_test_names_file():
    """
    إنشاء ملف Excel للاختبار يحتوي على أسماء تحتاج تصحيح
    """
    
    # أسماء تحتاج تصحيحات مخصصة
    test_names = [
        "مرتضي علي محمد",
        "عيسي عبداللة احمد", 
        "قاسم موسي على",
        "ابراهيم اسماعيل يحيي",
        "فاطمة هدي سلمي",
        "ايمان اسراء امل",
        "عبد الله محمد علي",
        "ابو_الدين صالح",
        "ام_الخير فاطمة",
        "محمد_علي حسن",
        "احمد123 محمد",
        "علي@صالح حسن",
        "يوسف#456 قاسم",
        "عبداللة احمد",
        "عبدالة محمد",
        "على محمد علي",
        "موسي يحيي مصطفي",
        "ليلي نجوي سهي",
        "بشري ذكري منى",
        "الحوثى المرتضي"
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame({
        'الاسم الشخصي': test_names,
        'الاسم المستعار': [''] * len(test_names),
        'العمر': [25, 30, 35, 28, 22, 27, 33, 40, 38, 29, 31, 26, 24, 32, 36, 34, 23, 21, 37, 39],
        'المحافظة': ['صنعاء'] * len(test_names),
        'المديرية': ['الثورة'] * len(test_names)
    })
    
    # حفظ الملف
    filename = f"اختبار_التصحيحات_المخصصة_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='البيانات', index=False)
        
        # تعيين اتجاه الورقة من اليمين إلى اليسار
        worksheet = writer.sheets['البيانات']
        worksheet.sheet_view.rightToLeft = True
    
    print(f"✅ تم إنشاء ملف الاختبار: {filename}")
    print(f"📊 عدد الأسماء: {len(test_names)}")
    print("\n📝 الأسماء التي تحتاج تصحيح:")
    for i, name in enumerate(test_names, 1):
        print(f"{i:2d}. {name}")
    
    print("\n🔧 التصحيحات المتوقعة:")
    expected_corrections = [
        "مرتضي → مرتضى",
        "عيسي → عيسى", 
        "عبداللة → عبدالله",
        "احمد → أحمد",
        "موسي → موسى",
        "على → علي",
        "ابراهيم → إبراهيم",
        "اسماعيل → إسماعيل",
        "يحيي → يحيى",
        "هدي → هدى",
        "سلمي → سلمى",
        "ايمان → إيمان",
        "اسراء → إسراء",
        "امل → أمل",
        "ابو_الدين → أبو الدين",
        "ام_الخير → أم الخير",
        "محمد_علي → محمد علي",
        "إزالة الأرقام والرموز",
        "ليلي → ليلى",
        "نجوي → نجوى",
        "سهي → سهى",
        "بشري → بشرى",
        "ذكري → ذكرى",
        "الحوثى → الحوثي",
        "المرتضي → المرتضى"
    ]
    
    for correction in expected_corrections:
        print(f"   • {correction}")
    
    print(f"\n🎯 استخدم هذا الملف لاختبار:")
    print("   1. التصحيحات الثابتة الموجودة")
    print("   2. إضافة تصحيحات مخصصة جديدة")
    print("   3. مراقبة عداد الاستخدام")
    print("   4. تصدير الدليل الشامل")
    
    return filename

if __name__ == "__main__":
    create_test_names_file()
