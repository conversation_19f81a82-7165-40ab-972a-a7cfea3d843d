#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from werkzeug.security import generate_password_hash

print("🔧 إصلاح قاعدة البيانات...")

# التحقق من ملفات قاعدة البيانات الموجودة
db_files = []
for file in os.listdir('.'):
    if file.endswith('.db'):
        size = os.path.getsize(file)
        db_files.append((file, size))
        print(f"📁 {file} - حجم: {size:,} بايت")

# استخدام training_system.db (الملف الأكبر)
db_file = 'training_system.db'

try:
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    print(f"🔗 متصل بـ {db_file}")
    
    # التحقق من الجداول الموجودة
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    print(f"📊 الجداول الموجودة: {len(tables)}")
    for table in tables:
        print(f"  - {table[0]}")
    
    # التحقق من جدول المستخدمين
    cursor.execute("SELECT COUNT(*) FROM user")
    user_count = cursor.fetchone()[0]
    print(f"👥 عدد المستخدمين: {user_count}")
    
    # عرض المستخدمين الموجودين
    cursor.execute("SELECT id, username, email, role FROM user")
    users = cursor.fetchall()
    print("👤 المستخدمين الموجودين:")
    for user in users:
        print(f"  - ID: {user[0]}, اسم المستخدم: {user[1]}, البريد: {user[2]}, الدور: {user[3]}")
    
    # حذف المديرين الحاليين
    cursor.execute("DELETE FROM user WHERE role = 'admin'")
    deleted_count = cursor.rowcount
    print(f"🗑️ تم حذف {deleted_count} مدير")
    
    # إنشاء مدير جديد
    password_hash = generate_password_hash('admin123')
    cursor.execute("""
        INSERT INTO user (username, email, password, role, created_at)
        VALUES (?, ?, ?, ?, datetime('now'))
    """, ('admin', '<EMAIL>', password_hash, 'admin'))
    
    conn.commit()
    print("✅ تم إنشاء المدير الجديد بنجاح!")
    
    # التحقق من إنشاء المدير
    cursor.execute("SELECT * FROM user WHERE email = ?", ('<EMAIL>',))
    admin = cursor.fetchone()
    
    if admin:
        print("✅ تم التحقق من وجود المدير:")
        print(f"  ID: {admin[0]}")
        print(f"  اسم المستخدم: {admin[1]}")
        print(f"  البريد: {admin[2]}")
        print(f"  الدور: {admin[4]}")
        
        # اختبار كلمة المرور
        from werkzeug.security import check_password_hash
        if check_password_hash(admin[3], 'admin123'):
            print("✅ كلمة المرور صحيحة")
        else:
            print("❌ كلمة المرور غير صحيحة")
    else:
        print("❌ فشل في إنشاء المدير")
    
    # التحقق من جدول الدورات
    cursor.execute("SELECT COUNT(*) FROM course")
    course_count = cursor.fetchone()[0]
    print(f"📚 عدد الدورات: {course_count}")
    
    # عرض الدورات
    cursor.execute("SELECT id, course_number, title FROM course LIMIT 5")
    courses = cursor.fetchall()
    print("📖 الدورات الموجودة:")
    for course in courses:
        print(f"  - ID: {course[0]}, رقم: {course[1]}, العنوان: {course[2]}")
    
    # التحقق من جدول البيانات الشخصية
    try:
        cursor.execute("SELECT COUNT(*) FROM person_data")
        person_count = cursor.fetchone()[0]
        print(f"👤 عدد الأشخاص في person_data: {person_count}")
    except sqlite3.OperationalError as e:
        print(f"⚠️ مشكلة في جدول person_data: {e}")
    
    try:
        cursor.execute("SELECT COUNT(*) FROM personal_data")
        personal_count = cursor.fetchone()[0]
        print(f"👤 عدد الأشخاص في personal_data: {personal_count}")
    except sqlite3.OperationalError as e:
        print(f"⚠️ مشكلة في جدول personal_data: {e}")
    
    conn.close()
    
except Exception as e:
    print(f"❌ خطأ: {str(e)}")

print("\n🌐 يمكنك الآن تسجيل الدخول على:")
print("http://127.0.0.1:5000/login")
print("📧 البريد: <EMAIL>")
print("🔑 كلمة المرور: admin123")

# حذف ملف database.db الصغير إذا كان موجوداً
if os.path.exists('database.db'):
    try:
        os.remove('database.db')
        print("🗑️ تم حذف ملف database.db الصغير")
    except:
        print("⚠️ لم يتم حذف database.db")
