# نظام إدارة التدريب - تشغيل سريع
# Training System Quick Start

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    نظام إدارة التدريب - تشغيل سريع" -ForegroundColor Yellow
Write-Host "    Training System Quick Start" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# فحص البيئة الافتراضية
if (!(Test-Path ".venv")) {
    Write-Host "❌ البيئة الافتراضية غير موجودة!" -ForegroundColor Red
    Write-Host "يرجى تشغيل setup_system.ps1 أولاً" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Please run setup_system.ps1 first" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# تفعيل البيئة الافتراضية
Write-Host "🔧 تفعيل البيئة الافتراضية..." -ForegroundColor Blue
& ".venv\Scripts\Activate.ps1"

# فحص app.py
if (!(Test-Path "app.py")) {
    Write-Host "❌ ملف app.py غير موجود!" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

Write-Host "✅ البيئة جاهزة" -ForegroundColor Green
Write-Host ""

# تشغيل التطبيق
Write-Host "🚀 تشغيل نظام إدارة التدريب..." -ForegroundColor Green
Write-Host "🌐 سيتم فتح النظام على: http://localhost:5000" -ForegroundColor Cyan
Write-Host ""
Write-Host "للإيقاف: اضغط Ctrl+C" -ForegroundColor Yellow
Write-Host "To stop: Press Ctrl+C" -ForegroundColor Yellow
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan

# تشغيل التطبيق
python app.py
