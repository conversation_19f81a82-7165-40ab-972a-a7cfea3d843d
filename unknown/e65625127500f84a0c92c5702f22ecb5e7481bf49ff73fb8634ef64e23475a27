#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع للتقارير
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Course, CourseParticipant

def quick_test():
    """اختبار سريع"""
    
    with app.app_context():
        print("🔍 اختبار سريع للبيانات...")
        
        # عدد الدورات
        total_courses = Course.query.count()
        print(f"📚 إجمالي الدورات: {total_courses}")
        
        # عدد المشاركين
        total_participants = CourseParticipant.query.count()
        print(f"👥 إجمالي المشاركين: {total_participants}")
        
        # حساب المستويات
        level1 = level2 = level3 = 0
        
        print("\n📋 تفاصيل الدورات:")
        for course in Course.query.all():
            participants_count = course.total_participants or 0
            level_name = course.level or ''
            
            print(f"   - {course.title}")
            print(f"     المستوى: '{level_name}'")
            print(f"     المشاركين: {participants_count}")
            
            if level_name in ['مبتدئ', 'أساسي', 'beginner', '1']:
                level1 += participants_count
                print(f"     ✅ تم إضافة {participants_count} للمستوى الأول")
            elif level_name in ['متوسط', 'intermediate', '2']:
                level2 += participants_count
                print(f"     ✅ تم إضافة {participants_count} للمستوى الثاني")
            elif level_name in ['متقدم', 'advanced', '3']:
                level3 += participants_count
                print(f"     ✅ تم إضافة {participants_count} للمستوى الثالث")
            else:
                print(f"     ❌ مستوى غير معروف: '{level_name}'")
            print()
        
        print(f"📊 النتائج النهائية:")
        print(f"   - المستوى الأول: {level1}")
        print(f"   - المستوى الثاني: {level2}")
        print(f"   - المستوى الثالث: {level3}")
        print(f"   - الإجمالي: {level1 + level2 + level3}")

if __name__ == "__main__":
    quick_test()
