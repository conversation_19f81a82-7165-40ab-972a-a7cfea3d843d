#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تنظيف قواعد البيانات المكررة والنسخ الاحتياطية القديمة
"""

import os
import shutil
from datetime import datetime

def backup_main_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات الرئيسية"""
    main_db = 'training_system.db'
    if os.path.exists(main_db):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'training_system_backup_before_cleanup_{timestamp}.db'
        shutil.copy2(main_db, backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    return None

def main():
    print("=" * 80)
    print("🧹 تنظيف قواعد البيانات المكررة والقديمة")
    print("=" * 80)
    
    # التحقق من وجود قاعدة البيانات الرئيسية
    main_db = 'training_system.db'
    if not os.path.exists(main_db):
        print("❌ قاعدة البيانات الرئيسية غير موجودة!")
        input("اضغط Enter للخروج...")
        return
    
    print("🔍 قاعدة البيانات المعتمدة:")
    print(f"   📁 {main_db}")
    print(f"   📏 الحجم: {os.path.getsize(main_db):,} بايت")
    print(f"   📅 آخر تعديل: {datetime.fromtimestamp(os.path.getmtime(main_db)).strftime('%Y-%m-%d %H:%M:%S')}")
    
    # قائمة الملفات المراد حذفها
    files_to_delete = [
        'instance/training_system.db',  # مكررة
        'database.db',  # فارغة
        'training_system.db.bak_20250503_044906',  # نسخة احتياطية قديمة
        'training_system_backup_20250529_034725.db',  # نسخة احتياطية
        'training_system_backup_columns_20250529_035306.db',  # نسخة احتياطية
        'training_system_before_copy_20250529_040141.db'  # نسخة احتياطية
    ]
    
    # عرض الملفات المراد حذفها
    print("\n🗑️ الملفات المراد حذفها:")
    existing_files = []
    total_size = 0
    
    for file_path in files_to_delete:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            total_size += size
            existing_files.append(file_path)
            print(f"   📄 {file_path} ({size:,} بايت)")
        else:
            print(f"   ❌ {file_path} (غير موجود)")
    
    if not existing_files:
        print("\n✅ لا توجد ملفات للحذف!")
        input("اضغط Enter للخروج...")
        return
    
    print(f"\n📊 إجمالي المساحة المراد توفيرها: {total_size:,} بايت ({total_size/1024/1024:.1f} MB)")
    
    # طلب التأكيد
    print("\n⚠️ تحذير: هذه العملية لا يمكن التراجع عنها!")
    print("💡 سيتم إنشاء نسخة احتياطية من قاعدة البيانات الرئيسية أولاً")
    
    while True:
        choice = input("\nهل تريد المتابعة؟ (y/n): ").lower().strip()
        if choice in ['y', 'yes', 'نعم', 'ن']:
            break
        elif choice in ['n', 'no', 'لا', 'ل']:
            print("تم إلغاء العملية.")
            input("اضغط Enter للخروج...")
            return
        else:
            print("يرجى الإجابة بـ y أو n")
    
    # إنشاء نسخة احتياطية
    print("\n🔄 إنشاء نسخة احتياطية...")
    backup_file = backup_main_database()
    
    if not backup_file:
        print("❌ فشل في إنشاء النسخة الاحتياطية!")
        input("اضغط Enter للخروج...")
        return
    
    # حذف الملفات
    print("\n🗑️ بدء عملية الحذف...")
    deleted_count = 0
    deleted_size = 0
    
    for file_path in existing_files:
        try:
            size = os.path.getsize(file_path)
            
            # حذف المجلد instance إذا كان فارغاً بعد حذف قاعدة البيانات
            if file_path == 'instance/training_system.db':
                os.remove(file_path)
                print(f"   ✅ تم حذف: {file_path}")
                
                # التحقق من مجلد instance
                instance_dir = 'instance'
                if os.path.exists(instance_dir) and not os.listdir(instance_dir):
                    os.rmdir(instance_dir)
                    print(f"   ✅ تم حذف المجلد الفارغ: {instance_dir}")
            else:
                os.remove(file_path)
                print(f"   ✅ تم حذف: {file_path}")
            
            deleted_count += 1
            deleted_size += size
            
        except Exception as e:
            print(f"   ❌ فشل حذف {file_path}: {e}")
    
    # النتائج
    print("\n" + "=" * 80)
    print("📊 نتائج عملية التنظيف:")
    print("=" * 80)
    print(f"✅ تم حذف {deleted_count} ملف")
    print(f"💾 تم توفير {deleted_size:,} بايت ({deleted_size/1024/1024:.1f} MB)")
    print(f"🔒 النسخة الاحتياطية: {backup_file}")
    
    print("\n🎯 قاعدة البيانات المعتمدة الآن:")
    print(f"   📁 {main_db}")
    print("   🔧 هذا ما يستخدمه النظام حسب إعدادات app.py")
    
    print("\n✅ تم تنظيف النظام بنجاح!")
    print("💡 يمكنك الآن تشغيل النظام بثقة")
    
    input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
