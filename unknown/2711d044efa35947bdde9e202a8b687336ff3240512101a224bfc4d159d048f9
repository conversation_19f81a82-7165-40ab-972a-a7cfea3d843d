from app import app, db, Course, User
from datetime import date, datetime

with app.app_context():
    # التحقق من وجود الدورة
    course = Course.query.filter_by(course_number='3455667').first()
    if course:
        print(f"الدورة موجودة: {course.id}")
    else:
        # إنشاء دورة جديدة
        trainer = User.query.first()
        course = Course()
        course.course_number = '3455667'
        course.title = 'المبيعات'
        course.description = 'دورة تدريبية في المبيعات'
        course.category = 'فنية'
        course.level = 'متوسط'
        course.start_date = datetime.now()
        course.end_date = datetime.now()
        course.start_date_hijri = '1446/05/25'
        course.end_date_hijri = '1446/05/25'
        course.duration_days = 1
        course.trainer_id = trainer.id if trainer else 1
        course.total_participants = 0
        course.total_graduates = 0
        course.total_dropouts = 0
        db.session.add(course)
        db.session.commit()
        print(f"تم إنشاء الدورة: {course.id}")
