#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص المستخدمين في النظام
"""

from app import app, db, User
from werkzeug.security import check_password_hash

def check_users():
    with app.app_context():
        users = User.query.all()
        print(f"📊 عدد المستخدمين: {len(users)}")
        
        for user in users:
            print(f"👤 المستخدم: {user.username}")
            print(f"📧 البريد: {user.email}")
            print(f"🔑 الدور: {user.role}")
            
            # اختبار كلمات المرور المختلفة
            passwords_to_test = ['admin', 'admin123', 'password']
            for pwd in passwords_to_test:
                if check_password_hash(user.password, pwd):
                    print(f"✅ كلمة المرور الصحيحة: {pwd}")
                    break
            else:
                print("❌ لم يتم العثور على كلمة المرور")
            
            print("-" * 30)

if __name__ == '__main__':
    check_users()
