#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بيانات التقارير مباشرة من قاعدة البيانات
"""

import os
import sys
from datetime import datetime, timedelta

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from app import (
    PersonData, Course, CourseParticipant, CoursePath, Agency, 
    TrainingCenter, ParticipantType, ForceClassification
)

def test_reports_data():
    """اختبار بيانات التقارير"""
    print("🔍 اختبار بيانات التقارير...")
    
    with app.app_context():
        try:
            # 1. توزيع الدورات حسب مسارات التدريب
            course_path_stats = db.session.query(
                CoursePath.name,
                db.func.count(Course.id).label('count')
            ).join(Course, Course.path_id == CoursePath.id)\
             .filter(CoursePath.name.isnot(None))\
             .filter(CoursePath.name != '')\
             .group_by(CoursePath.name).all()
            
            print(f"📊 مسارات التدريب ({len(course_path_stats)}):")
            for stat in course_path_stats:
                print(f"   - {stat.name}: {stat.count} دورة")
            
            # 2. توزيع المراكز حسب الجهات
            center_agency_stats = db.session.query(
                Agency.name,
                db.func.count(TrainingCenter.id).label('count')
            ).join(TrainingCenter, TrainingCenter.agency_id == Agency.id)\
             .filter(Agency.name.isnot(None))\
             .filter(Agency.name != '')\
             .group_by(Agency.name).all()
            
            print(f"\n🏢 الجهات ({len(center_agency_stats)}):")
            for stat in center_agency_stats:
                print(f"   - {stat.name}: {stat.count} مركز")
            
            # 3. توزيع الدورات حسب المواقع
            course_location_stats = db.session.query(
                TrainingCenter.name,
                db.func.count(Course.id).label('count')
            ).join(Course, Course.center_id == TrainingCenter.id)\
             .filter(TrainingCenter.name.isnot(None))\
             .filter(TrainingCenter.name != '')\
             .group_by(TrainingCenter.name).all()
            
            print(f"\n📍 المواقع ({len(course_location_stats)}):")
            for stat in course_location_stats[:10]:  # أول 10
                print(f"   - {stat.name}: {stat.count} دورة")
            
            # 4. توزيع المشاركين حسب أنواع المشاركين
            participant_type_stats = db.session.query(
                PersonData.job,
                db.func.count(CourseParticipant.id).label('count')
            ).join(CourseParticipant, CourseParticipant.personal_data_id == PersonData.id)\
             .filter(PersonData.job.isnot(None))\
             .filter(PersonData.job != '')\
             .group_by(PersonData.job).all()
            
            print(f"\n👥 أنواع المشاركين ({len(participant_type_stats)}):")
            for stat in participant_type_stats:
                print(f"   - {stat.job}: {stat.count} مشارك")
            
            # 5. توزيع المشاركين حسب تصنيفات القوة
            force_classification_stats = db.session.query(
                PersonData.agency,
                db.func.count(CourseParticipant.id).label('count')
            ).join(CourseParticipant, CourseParticipant.personal_data_id == PersonData.id)\
             .filter(PersonData.agency.isnot(None))\
             .filter(PersonData.agency != '')\
             .group_by(PersonData.agency).all()
            
            print(f"\n🛡️ تصنيفات القوة ({len(force_classification_stats)}):")
            for stat in force_classification_stats:
                print(f"   - {stat.agency}: {stat.count} مشارك")
            
            print("\n✅ جميع البيانات متوفرة ونظيفة (بدون قيم فارغة)!")
            
            # تحقق من عدم وجود قيم فارغة
            empty_checks = [
                ("مسارات التدريب", any(not stat.name or stat.name.strip() == '' for stat in course_path_stats)),
                ("الجهات", any(not stat.name or stat.name.strip() == '' for stat in center_agency_stats)),
                ("المواقع", any(not stat.name or stat.name.strip() == '' for stat in course_location_stats)),
                ("أنواع المشاركين", any(not stat.job or stat.job.strip() == '' for stat in participant_type_stats)),
                ("تصنيفات القوة", any(not stat.agency or stat.agency.strip() == '' for stat in force_classification_stats))
            ]
            
            print("\n🔍 فحص القيم الفارغة:")
            for check_name, has_empty in empty_checks:
                status = "❌ يحتوي على قيم فارغة" if has_empty else "✅ نظيف"
                print(f"   - {check_name}: {status}")
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_reports_data()
