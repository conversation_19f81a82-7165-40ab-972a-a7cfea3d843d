# دليل النشر - نظام تحليل الأسماء
# Deployment Guide - Training System

## خيارات النشر المتاحة
## Available Deployment Options

### 1. 🖥️ تحويل إلى EXE (للأجهزة المحلية)
### Convert to EXE (For Local Machines)

#### المتطلبات:
- Windows 10/11
- Python 3.8+
- PyInstaller

#### الخطوات:
```bash
# 1. تشغيل سكريبت البناء
.\build_exe.bat

# 2. الملفات ستكون في مجلد dist\TrainingSystem\
# 3. نسخ المجلد كاملاً لأي جهاز آخر
# 4. تشغيل TrainingSystem.exe
```

#### المميزات:
✅ لا يحتاج Python مثبت على الجهاز المستهدف
✅ ملف واحد قابل للتشغيل
✅ سهولة التوزيع
✅ يعمل على أي جهاز Windows

---

### 2. 🐳 Docker (للخوادم والسحابة)
### Docker (For Servers and Cloud)

#### المتطلبات:
- Docker Desktop
- Docker Compose (اختياري)

#### الخطوات:
```bash
# 1. بناء Docker Image
.\docker_build.bat

# 2. تشغيل النظام
docker run -p 5000:5000 training-system:latest

# أو استخدام Docker Compose
docker-compose up -d
```

#### المميزات:
✅ يعمل على أي نظام تشغيل
✅ عزل كامل للبيئة
✅ سهولة النشر على السحابة
✅ قابلية التوسع

---

### 3. ☁️ النشر السحابي
### Cloud Deployment

#### خيارات السحابة:

##### أ) Heroku
```bash
# إنشاء تطبيق Heroku
heroku create training-system-app

# رفع الكود
git push heroku main
```

##### ب) AWS EC2
```bash
# رفع Docker Image
docker tag training-system:latest your-registry/training-system
docker push your-registry/training-system

# نشر على EC2
docker run -d -p 80:5000 your-registry/training-system
```

##### ج) Google Cloud Run
```bash
# بناء ورفع
gcloud builds submit --tag gcr.io/PROJECT-ID/training-system
gcloud run deploy --image gcr.io/PROJECT-ID/training-system
```

---

### 4. 🖧 الشبكة المحلية
### Local Network

#### للوصول من أجهزة أخرى في نفس الشبكة:

```python
# في app.py غير:
app.run(host='0.0.0.0', port=5000)
```

#### ثم:
- تشغيل النظام
- معرفة IP الجهاز: `ipconfig`
- الوصول من أجهزة أخرى: `http://192.168.1.X:5000`

---

## 📋 قائمة التحقق قبل النشر
## Pre-Deployment Checklist

### الأمان:
- [ ] تغيير SECRET_KEY في الإنتاج
- [ ] تعطيل DEBUG mode
- [ ] إعداد HTTPS
- [ ] تحديث كلمات المرور

### قاعدة البيانات:
- [ ] نسخ احتياطي من قاعدة البيانات
- [ ] اختبار الاتصال
- [ ] فحص البيانات

### الملفات:
- [ ] نسخ جميع الملفات المطلوبة
- [ ] فحص الصلاحيات
- [ ] اختبار رفع الملفات

### الاختبار:
- [ ] اختبار جميع الوظائف
- [ ] اختبار الأداء
- [ ] اختبار الأمان

---

## 🔧 إعدادات الإنتاج
## Production Settings

### متغيرات البيئة:
```bash
FLASK_ENV=production
DEBUG=False
SECRET_KEY=your-very-secure-secret-key
DATABASE_URL=your-production-database-url
```

### إعدادات الأمان:
- استخدام HTTPS
- تشفير قاعدة البيانات
- تحديد صلاحيات الملفات
- مراقبة النظام

---

## 📞 الدعم الفني
## Technical Support

في حالة مواجهة مشاكل:
1. تحقق من ملفات السجل (logs)
2. راجع دليل استكشاف الأخطاء
3. تواصل مع فريق التطوير

---

## 📈 التحديثات المستقبلية
## Future Updates

النظام مُعد للتحديثات التلقائية:
- تحديث المكتبات
- إضافة مميزات جديدة
- تحسينات الأمان
- تحسينات الأداء
