#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار صفحة البيانات الشخصية للتأكد من التغييرات
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from app import (
    PersonData, Course, CourseParticipant, CoursePath, Agency, 
    TrainingCenter, ParticipantType, ForceClassification
)

def test_personal_data_stats():
    """اختبار الإحصائيات الجديدة"""
    print("🔍 اختبار الإحصائيات الجديدة...")
    
    with app.app_context():
        try:
            # توزيع الدورات حسب مسارات التدريب
            course_path_stats = db.session.query(
                CoursePath.name,
                db.func.count(Course.id).label('count')
            ).outerjoin(Course, Course.path_id == CoursePath.id).group_by(CoursePath.name).all()
            
            print(f"📊 توزيع الدورات حسب مسارات التدريب:")
            for stat in course_path_stats:
                print(f"   - {stat.name}: {stat.count}")
            
            # توزيع المراكز حسب الجهات
            center_agency_stats = db.session.query(
                Agency.name,
                db.func.count(TrainingCenter.id).label('count')
            ).outerjoin(TrainingCenter, TrainingCenter.agency_id == Agency.id).group_by(Agency.name).all()
            
            print(f"\n🏢 توزيع المراكز حسب الجهات:")
            for stat in center_agency_stats:
                print(f"   - {stat.name}: {stat.count}")
            
            # توزيع الدورات حسب المواقع
            course_location_stats = db.session.query(
                TrainingCenter.name,
                db.func.count(Course.id).label('count')
            ).outerjoin(Course, Course.center_id == TrainingCenter.id).group_by(TrainingCenter.name).all()
            
            print(f"\n📍 توزيع الدورات حسب المواقع:")
            for stat in course_location_stats[:10]:  # أول 10 فقط
                print(f"   - {stat.name}: {stat.count}")
            
            # توزيع المشاركين حسب أنواع المشاركين
            participant_type_stats = db.session.query(
                PersonData.job,
                db.func.count(CourseParticipant.id).label('count')
            ).join(CourseParticipant, CourseParticipant.personal_data_id == PersonData.id)\
             .filter(PersonData.job.isnot(None))\
             .group_by(PersonData.job).all()
            
            print(f"\n👥 توزيع المشاركين حسب أنواع المشاركين:")
            for stat in participant_type_stats:
                print(f"   - {stat.job}: {stat.count}")
            
            # توزيع المشاركين حسب تصنيفات القوة
            force_classification_stats = db.session.query(
                PersonData.agency,
                db.func.count(CourseParticipant.id).label('count')
            ).join(CourseParticipant, CourseParticipant.personal_data_id == PersonData.id)\
             .filter(PersonData.agency.isnot(None))\
             .group_by(PersonData.agency).all()
            
            print(f"\n🛡️ توزيع المشاركين حسب تصنيفات القوة:")
            for stat in force_classification_stats:
                print(f"   - {stat.agency}: {stat.count}")
            
            print("\n✅ جميع الإحصائيات تعمل بشكل صحيح!")
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_personal_data_stats()
