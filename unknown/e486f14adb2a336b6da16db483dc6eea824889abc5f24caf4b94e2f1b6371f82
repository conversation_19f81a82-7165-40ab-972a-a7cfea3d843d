#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف اختبار بسيط للتأكد من عمل Flask
"""

from flask import Flask

# إنشاء تطبيق Flask بسيط
app = Flask(__name__)

@app.route('/')
def home():
    return """
    <html>
    <head>
        <title>اختبار الخادم</title>
        <meta charset="utf-8">
    </head>
    <body style="font-family: Arial; text-align: center; padding: 50px;">
        <h1>🎉 الخادم يعمل بشكل صحيح!</h1>
        <p>هذا اختبار بسيط للتأكد من عمل Flask</p>
        <hr>
        <p><a href="/test">صفحة اختبار</a></p>
    </body>
    </html>
    """

@app.route('/test')
def test():
    return """
    <html>
    <head>
        <title>صفحة الاختبار</title>
        <meta charset="utf-8">
    </head>
    <body style="font-family: Arial; text-align: center; padding: 50px;">
        <h1>✅ صفحة الاختبار</h1>
        <p>إذا كنت ترى هذه الصفحة، فإن Flask يعمل بشكل صحيح</p>
        <hr>
        <p><a href="/">العودة للصفحة الرئيسية</a></p>
    </body>
    </html>
    """

if __name__ == '__main__':
    print("🚀 بدء تشغيل خادم الاختبار...")
    print("📍 الرابط: http://127.0.0.1:5000")
    print("📍 الرابط البديل: http://localhost:5000")
    print("=" * 40)

    try:
        app.run(
            host='0.0.0.0',  # الاستماع على جميع العناوين
            port=5000,
            debug=True,
            threaded=True,
            use_reloader=False
        )
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
