#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إضافة مشارك في الدورة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, CourseParticipant, PersonData, Course
from datetime import datetime

def test_add_participant():
    """
    اختبار إضافة مشارك في الدورة
    """
    with app.app_context():
        try:
            # البحث عن أول شخص في جدول person_data
            person = PersonData.query.first()
            if not person:
                print("❌ لا يوجد أشخاص في جدول person_data")
                return False

            print(f"✅ تم العثور على شخص: {person.full_name}")

            # البحث عن أول دورة
            course = Course.query.first()
            if not course:
                print("❌ لا يوجد دورات في النظام")
                return False

            print(f"✅ تم العثور على دورة: {course.title}")

            # التحقق من عدم وجود المشارك بالفعل
            existing = CourseParticipant.query.filter_by(
                course_id=course.id,
                personal_data_id=person.id
            ).first()

            if existing:
                print(f"⚠️ المشارك موجود بالفعل في الدورة")
                return True

            # إنشاء مشارك جديد
            participant = CourseParticipant(
                course_id=course.id,
                personal_data_id=person.id,
                status='active',
                daily_allowance=50.0,
                transportation_allowance=20.0,
                accommodation_allowance=30.0,
                total_allowance=100.0,
                payment_status='pending'
            )

            db.session.add(participant)
            db.session.commit()

            print(f"✅ تم إضافة المشارك بنجاح!")
            print(f"   - الاسم: {person.full_name}")
            print(f"   - الدورة: {course.title}")
            print(f"   - الحالة: {participant.status}")
            print(f"   - إجمالي المبلغ: {participant.total_allowance}")

            return True

        except Exception as e:
            print(f"❌ خطأ في إضافة المشارك: {e}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    print("🧪 اختبار إضافة مشارك في الدورة...")
    success = test_add_participant()
    if success:
        print("✅ تم الاختبار بنجاح!")
    else:
        print("❌ فشل الاختبار!")
