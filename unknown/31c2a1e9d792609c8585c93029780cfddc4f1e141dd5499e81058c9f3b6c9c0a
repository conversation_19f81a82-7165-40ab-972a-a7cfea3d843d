#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, CourseParticipant, Course, PersonData, User
from datetime import datetime, date

def create_course_3455667():
    with app.app_context():
        print("🆕 إنشاء الدورة رقم 3455667...")
        print("=" * 60)
        
        # التحقق من وجود الدورة
        existing_course = Course.query.filter_by(course_number='3455667').first()
        if existing_course:
            print(f"✅ الدورة موجودة بالفعل - ID: {existing_course.id}")
            course = existing_course
        else:
            # إنشاء الدورة الجديدة
            # جلب أول مستخدم كمدرب افتراضي
            trainer = User.query.first()
            if not trainer:
                print("❌ لا يوجد مستخدمين في النظام")
                return
            
            course = Course(
                course_number='3455667',
                title='المبيعات',
                agency='وزارة التجارة',
                center_name='مركز التدريب الرئيسي',
                participant_type='موظفين',
                start_date=date.today(),
                end_date=date.today(),
                trainer_id=trainer.id,
                total_participants=0,
                total_graduates=0,
                total_dropouts=0
            )
            
            db.session.add(course)
            db.session.flush()  # للحصول على ID
            print(f"✅ تم إنشاء الدورة - ID: {course.id}")
        
        # إنشاء أسماء المشاركين كما تظهر في الصورة
        participants_names = [
            "فاطمة صالح محمد الحميري",
            "علي أحمد محمد قاسم",
            "علي أحمد علي قاسم",
            "فاطمة صالح محمد عبدالله",
            "علي عبدالله علي عبدالله",
            "عبدالله قاسم محمد صالح"
        ]
        
        print(f"\n👥 إضافة المشاركين...")
        added_count = 0
        
        for name in participants_names:
            # التحقق من وجود الشخص في قاعدة البيانات
            person = PersonData.query.filter_by(full_name=name).first()
            
            if not person:
                # إنشاء شخص جديد
                person = PersonData(full_name=name)
                db.session.add(person)
                db.session.flush()
                print(f"   ✅ تم إنشاء شخص جديد: {name}")
            else:
                print(f"   📋 الشخص موجود: {name}")
            
            # التحقق من عدم وجود المشارك في الدورة مسبقاً
            existing_participant = CourseParticipant.query.filter_by(
                course_id=course.id,
                personal_data_id=person.id
            ).first()
            
            if not existing_participant:
                # إضافة المشارك للدورة
                participant = CourseParticipant(
                    course_id=course.id,
                    personal_data_id=person.id,
                    status='active',
                    entry_date=datetime.now()
                )
                db.session.add(participant)
                added_count += 1
                print(f"   ➕ تم إضافة {name} للدورة")
            else:
                print(f"   ⚠️ {name} مضاف مسبقاً للدورة")
        
        # تحديث عدد المشاركين في الدورة
        total_participants = CourseParticipant.query.filter_by(course_id=course.id).count()
        course.total_participants = total_participants
        
        # حفظ جميع التغييرات
        db.session.commit()
        
        print(f"\n✅ تم الانتهاء!")
        print(f"   إجمالي المشاركين في الدورة: {course.total_participants}")
        print(f"   المشاركين المضافين الجدد: {added_count}")
        print(f"   رابط الدورة: http://127.0.0.1:5000/course/{course.id}/participants")

if __name__ == '__main__':
    create_course_3455667()
