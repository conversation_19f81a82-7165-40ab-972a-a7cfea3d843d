#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نسخ قاعدة البيانات الصحيحة من instance إلى المجلد الرئيسي
"""

import shutil
import sqlite3
from datetime import datetime

def backup_current_db():
    """إنشاء نسخة احتياطية من قاعدة البيانات الحالية"""
    try:
        backup_name = f"training_system_before_copy_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        shutil.copy2('training_system.db', backup_name)
        print(f"✅ تم حفظ النسخة الحالية في: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return None

def check_database_content(db_path, db_name):
    """فحص محتوى قاعدة البيانات"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"\n🔍 فحص {db_name}:")
        print("-" * 40)
        
        # فحص الجداول الرئيسية
        tables = ['course', 'person_data', 'course_participant']
        
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"📋 {table}: {count} سجل")
                
                # عرض عينة من البيانات
                if table == 'course' and count > 0:
                    cursor.execute("SELECT course_number, title FROM course")
                    courses = cursor.fetchall()
                    for course in courses:
                        print(f"   - {course[0]}: {course[1]}")
                
                elif table == 'person_data' and count > 0:
                    cursor.execute("SELECT full_name, governorate FROM person_data LIMIT 3")
                    persons = cursor.fetchall()
                    for person in persons:
                        print(f"   - {person[0]} ({person[1]})")
                        
            except Exception as e:
                print(f"❌ خطأ في جدول {table}: {str(e)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")
        return False

def copy_database():
    """نسخ قاعدة البيانات من instance إلى المجلد الرئيسي"""
    try:
        source_db = 'instance/training_system.db'
        target_db = 'training_system.db'
        
        # التحقق من وجود قاعدة البيانات المصدر
        import os
        if not os.path.exists(source_db):
            print(f"❌ قاعدة البيانات المصدر غير موجودة: {source_db}")
            return False
        
        # نسخ قاعدة البيانات
        shutil.copy2(source_db, target_db)
        print(f"✅ تم نسخ قاعدة البيانات من {source_db} إلى {target_db}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نسخ قاعدة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔄 استعادة قاعدة البيانات الأصلية")
    print("=" * 60)
    
    # فحص قاعدة البيانات الحالية
    check_database_content('training_system.db', 'قاعدة البيانات الحالية')
    
    # فحص قاعدة البيانات في instance
    check_database_content('instance/training_system.db', 'قاعدة البيانات في instance')
    
    # إنشاء نسخة احتياطية
    backup_name = backup_current_db()
    
    # نسخ قاعدة البيانات الصحيحة
    if copy_database():
        print("\n🎉 تم استعادة قاعدة البيانات الأصلية بنجاح!")
        
        # التحقق من النتيجة
        check_database_content('training_system.db', 'قاعدة البيانات بعد النسخ')
        
    else:
        print("\n❌ فشل في استعادة قاعدة البيانات")

if __name__ == "__main__":
    main()
