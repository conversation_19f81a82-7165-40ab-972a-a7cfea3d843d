<!-- نموذج التقييم الديناميكي المطابق للنموذج الأصلي -->
<form id="dynamicEvaluationForm" onsubmit="submitDynamicEvaluation(event)">
    <div class="row">
        <div class="col-md-12">
            <h6 class="mb-3">
                <i class="fas fa-star text-warning"></i>
                تقييم المشاركين - الدورة: {{ course.title }}
            </h6>
            
            <!-- معلومات المشاركين -->
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h6 class="mb-0">المشاركين المحددين ({{ participants|length }})</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for participant in participants %}
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <input type="hidden" name="participant_ids" value="{{ participant.id }}">
                                <i class="fas fa-user text-primary me-2"></i>
                                <span><strong>{{ participant.personal_data.full_name if participant.personal_data else 'غير محدد' }}</strong></span>
                                <small class="text-muted ms-2">({{ participant.status }})</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- معايير التقييم الديناميكية -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">معايير التقييم</h6>
                </div>
                <div class="card-body">
                    {% if criteria_data %}
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th width="20%">المعيار</th>
                                    <th width="15%">البند</th>
                                    <th width="10%">الدرجة العظمى</th>
                                    <th width="15%">الدرجة المحققة</th>
                                    <th width="25%">ملاحظات</th>
                                    <th width="15%">النسبة المئوية</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for criteria in criteria_data %}
                                    {% for item in criteria.items %}
                                    <tr>
                                        {% if loop.first %}
                                        <td rowspan="{{ criteria.items|length }}" class="align-middle">
                                            <strong>{{ criteria.name }}</strong>
                                            <br><small class="text-muted">{{ criteria.description or '' }}</small>
                                        </td>
                                        {% endif %}
                                        <td>{{ item.name }}</td>
                                        <td class="text-center">{{ item.max_score }}</td>
                                        <td>
                                            <input type="number" 
                                                   class="form-control score-input" 
                                                   name="score_item_{{ item.id }}" 
                                                   min="0" 
                                                   max="{{ item.max_score }}" 
                                                   step="0.5" 
                                                   data-max="{{ item.max_score }}"
                                                   data-criteria="{{ criteria.name }}"
                                                   data-item="{{ item.name }}"
                                                   onchange="calculateDynamicTotal()">
                                        </td>
                                        <td>
                                            <textarea class="form-control" 
                                                      name="notes_item_{{ item.id }}" 
                                                      rows="2" 
                                                      placeholder="ملاحظات اختيارية"></textarea>
                                        </td>
                                        <td class="text-center">
                                            <span class="percentage-display" id="percentage_{{ item.id }}">0%</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-dark">
                                <tr>
                                    <th colspan="2">الإجمالي</th>
                                    <th class="text-center" id="totalMaxScore">{{ total_max_score }}</th>
                                    <th class="text-center">
                                        <span id="dynamicTotalScore" class="fs-5 text-warning">0</span>
                                    </th>
                                    <th class="text-center">
                                        <span id="dynamicPercentage" class="fs-5 text-warning">0%</span>
                                    </th>
                                    <th class="text-center">
                                        <span id="dynamicGrade" class="fs-5 text-warning">غير محدد</span>
                                    </th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- التقدير النهائي -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">التقدير النهائي:</label>
                            <select class="form-select" name="final_grade" id="dynamicFinalGrade">
                                <option value="">اختر التقدير</option>
                                <option value="ممتاز">ممتاز (90-100%)</option>
                                <option value="جيد جداً">جيد جداً (80-89%)</option>
                                <option value="جيد">جيد (70-79%)</option>
                                <option value="مقبول">مقبول (60-69%)</option>
                                <option value="ضعيف">ضعيف (أقل من 60%)</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">حالة التقييم:</label>
                            <select class="form-select" name="evaluation_status">
                                <option value="draft">مسودة</option>
                                <option value="final">نهائي</option>
                            </select>
                        </div>
                    </div>

                    <!-- ملاحظات عامة -->
                    <div class="mt-3">
                        <label class="form-label">ملاحظات عامة:</label>
                        <textarea class="form-control" name="general_notes" rows="3" 
                                  placeholder="ملاحظات عامة حول أداء المشارك/المشاركين"></textarea>
                    </div>

                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> لم يتم تحديد معايير تقييم لهذه الدورة.
                        <br>
                        يرجى إضافة معايير التقييم أولاً من إعدادات الدورة.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الحفظ -->
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        {% if criteria_data %}
        <button type="submit" class="btn btn-success">
            <i class="fas fa-save"></i> حفظ التقييم
        </button>
        <button type="button" class="btn btn-primary" onclick="saveDynamicAndPrint()">
            <i class="fas fa-print"></i> حفظ وطباعة
        </button>
        {% endif %}
    </div>
</form>

<script>
function calculateDynamicTotal() {
    const scoreInputs = document.querySelectorAll('.score-input');
    let totalScore = 0;
    let totalMaxScore = {{ total_max_score }};
    
    scoreInputs.forEach(input => {
        const score = parseFloat(input.value) || 0;
        const maxScore = parseFloat(input.dataset.max) || 0;
        const itemId = input.name.replace('score_item_', '');
        
        totalScore += score;
        
        // حساب النسبة المئوية للبند
        const percentage = maxScore > 0 ? ((score / maxScore) * 100).toFixed(1) : 0;
        const percentageElement = document.getElementById(`percentage_${itemId}`);
        if (percentageElement) {
            percentageElement.textContent = percentage + '%';
        }
    });
    
    const overallPercentage = totalMaxScore > 0 ? ((totalScore / totalMaxScore) * 100).toFixed(1) : 0;
    
    document.getElementById('dynamicTotalScore').textContent = totalScore.toFixed(1);
    document.getElementById('dynamicPercentage').textContent = overallPercentage + '%';
    
    // تحديد التقدير تلقائياً
    const gradeSelect = document.getElementById('dynamicFinalGrade');
    const gradeDisplay = document.getElementById('dynamicGrade');
    
    let grade = 'غير محدد';
    if (overallPercentage >= 90) {
        grade = 'ممتاز';
        gradeSelect.value = 'ممتاز';
    } else if (overallPercentage >= 80) {
        grade = 'جيد جداً';
        gradeSelect.value = 'جيد جداً';
    } else if (overallPercentage >= 70) {
        grade = 'جيد';
        gradeSelect.value = 'جيد';
    } else if (overallPercentage >= 60) {
        grade = 'مقبول';
        gradeSelect.value = 'مقبول';
    } else if (overallPercentage > 0) {
        grade = 'ضعيف';
        gradeSelect.value = 'ضعيف';
    }
    
    gradeDisplay.textContent = grade;
}

function submitDynamicEvaluation(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());
    
    // إضافة الدرجة الإجمالية والنسبة المئوية
    data.total_score = document.getElementById('dynamicTotalScore').textContent;
    data.percentage = parseFloat(document.getElementById('dynamicPercentage').textContent);
    
    // الحصول على CSRF token
    const csrfTokenElement = document.querySelector('meta[name=csrf-token]');
    const csrfToken = csrfTokenElement ? csrfTokenElement.getAttribute('content') : '';
    
    fetch(`/course/{{ course.id }}/evaluation/save-dynamic`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('تم حفظ التقييم بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('evaluationModal')).hide();
            location.reload();
        } else {
            alert('حدث خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في حفظ التقييم');
    });
}

function saveDynamicAndPrint() {
    const form = document.getElementById('dynamicEvaluationForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    data.total_score = document.getElementById('dynamicTotalScore').textContent;
    data.percentage = parseFloat(document.getElementById('dynamicPercentage').textContent);
    data.print_after_save = true;
    
    const csrfTokenElement = document.querySelector('meta[name=csrf-token]');
    const csrfToken = csrfTokenElement ? csrfTokenElement.getAttribute('content') : '';
    
    fetch(`/course/{{ course.id }}/evaluation/save-dynamic`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            window.open(`/course/{{ course.id }}/evaluation/print-dynamic/${result.evaluation_id}`);
            bootstrap.Modal.getInstance(document.getElementById('evaluationModal')).hide();
            location.reload();
        } else {
            alert('حدث خطأ: ' + result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في حفظ التقييم');
    });
}

// تشغيل الحساب عند تحميل النموذج
document.addEventListener('DOMContentLoaded', function() {
    calculateDynamicTotal();
});
</script>
