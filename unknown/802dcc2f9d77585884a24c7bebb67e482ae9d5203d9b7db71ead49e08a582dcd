{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<style>
    .course-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-left: 5px solid #667eea;
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .upload-zone {
        border: 3px dashed #667eea;
        border-radius: 20px;
        padding: 50px;
        text-align: center;
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        transition: all 0.3s ease;
        margin: 30px 0;
    }

    .upload-zone:hover {
        border-color: #5a67d8;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        transform: scale(1.02);
    }

    .upload-icon {
        font-size: 4rem;
        color: #667eea;
        margin-bottom: 20px;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }

    .btn-analyze {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        padding: 15px 40px;
        font-size: 1.2rem;
        font-weight: bold;
        border-radius: 30px;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-analyze:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
    }

    .current-participants {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        margin-top: 20px;
    }

    .participant-item {
        background: white;
        border-radius: 10px;
        padding: 10px 15px;
        margin: 5px 0;
        border-left: 3px solid #28a745;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .progress-container {
        margin-top: 20px;
        display: none;
    }

    .progress {
        height: 30px;
        border-radius: 15px;
        overflow: hidden;
    }

    .progress-bar {
        background: linear-gradient(45deg, #667eea, #764ba2);
        transition: width 0.3s ease;
    }

    .feature-highlight {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        border-radius: 15px;
        padding: 25px;
        margin: 20px 0;
        border-left: 5px solid #ff6b6b;
    }

    .info-badge {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.9rem;
        margin: 2px;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header الدورة -->
    <div class="course-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-users-cog"></i> استيراد مشاركين للدورة
                </h1>
                <h3 class="mb-3 text-light">📚 {{ course.title }}</h3>
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-1"><i class="fas fa-calendar"></i> <strong>تاريخ البداية:</strong> {{ course.start_date.strftime('%Y-%m-%d') if course.start_date else 'غير محدد' }}</p>
                        <p class="mb-1"><i class="fas fa-clock"></i> <strong>المدة:</strong> {{ course.duration }} أيام</p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><i class="fas fa-map-marker-alt"></i> <strong>المكان:</strong> {{ course.location or 'غير محدد' }}</p>
                        <p class="mb-1"><i class="fas fa-user-tie"></i> <strong>المدرب:</strong> {{ course.trainer or 'غير محدد' }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="stats-card bg-white text-dark">
                    <h2 class="text-primary mb-2">{{ current_participants_count }}</h2>
                    <p class="mb-0"><i class="fas fa-users text-primary"></i> مشارك حالي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- منطقة الرفع الذكية -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-primary text-white text-center">
                    <h3 class="mb-0">
                        <i class="fas fa-cloud-upload-alt"></i> مركز التحليل الذكي للمشاركين
                    </h3>
                </div>
                <div class="card-body">
                    <form id="importForm" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <input type="hidden" id="course_id_hidden" value="{{ course.id }}">

                        <div class="upload-zone" id="uploadZone">
                            <div class="upload-icon">
                                <i class="fas fa-file-excel"></i>
                            </div>
                            <h4 class="text-primary mb-3">اختر ملف Excel للتحليل الذكي</h4>
                            <input type="file" class="form-control form-control-lg" name="excel_file"
                                   id="excelFile" accept=".xlsx,.xls,.csv" required
                                   style="max-width: 400px; margin: 0 auto;">
                            <p class="text-muted mt-3">
                                <i class="fas fa-info-circle"></i>
                                يجب أن يحتوي الملف على عمود "الاسم الشخصي" أو "الاسم"
                            </p>
                            <div class="mt-3">
                                <span class="info-badge">Excel</span>
                                <span class="info-badge">CSV</span>
                                <span class="info-badge">تحليل ذكي</span>
                                <span class="info-badge">تصنيف تلقائي</span>
                            </div>
                        </div>

                        <div class="text-center">
                            <a href="/name_analysis_course" class="btn btn-analyze" id="analyzeBtn"
                               title="الانتقال إلى صفحة التحليل الذكي للدورات">
                                <i class="fas fa-brain"></i> بدء التحليل الذكي
                            </a>
                            <p class="text-muted mt-2 small">
                                <i class="fas fa-info-circle"></i> سيتم توجيهك إلى صفحة التحليل الذكي للدورات
                            </p>
                        </div>

                        <!-- مؤشر التقدم -->
                        <div class="progress-container" id="progressContainer">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 0%" id="progressBar">
                                    <span id="progressText">0%</span>
                                </div>
                            </div>
                            <p class="text-center mt-2" id="progressMessage">جاري التحليل...</p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- ميزات التحليل الذكي -->
    <div class="feature-highlight">
        <h5 class="text-danger mb-3">
            <i class="fas fa-magic"></i> ما سيحدث أثناء التحليل الذكي:
        </h5>
        <div class="row">
            <div class="col-md-4">
                <h6 class="text-primary"><i class="fas fa-user-plus"></i> تصنيف الأشخاص الجدد</h6>
                <p class="small">سيتم تحديد الأشخاص غير الموجودين في قاعدة البيانات</p>
            </div>
            <div class="col-md-4">
                <h6 class="text-success"><i class="fas fa-user-check"></i> تحديد المتاحين للإضافة</h6>
                <p class="small">الأشخاص الموجودين في قاعدة البيانات وغير مشاركين في الدورة</p>
            </div>
            <div class="col-md-4">
                <h6 class="text-warning"><i class="fas fa-user-times"></i> كشف المكررين</h6>
                <p class="small">الأشخاص المشاركين بالفعل في الدورة</p>
            </div>
        </div>
    </div>

    <!-- المشاركين الحاليين -->
    {% if current_participants %}
    <div class="current-participants">
        <h5 class="text-primary mb-3">
            <i class="fas fa-users"></i> المشاركين الحاليين (أول 5):
        </h5>
        <div class="row">
            {% for participant, person in current_participants %}
            <div class="col-md-6">
                <div class="participant-item">
                    <strong>{{ person.full_name }}</strong>
                    {% if person.job %}
                    <small class="text-muted">- {{ person.job }}</small>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% if current_participants_count > 5 %}
        <p class="text-center mt-3 text-muted">
            <i class="fas fa-ellipsis-h"></i> و {{ current_participants_count - 5 }} مشارك آخر
        </p>
        {% endif %}
    </div>
    {% endif %}

    <!-- أزرار التنقل -->
    <div class="text-center mt-4">
        <a href="/manage_participants/{{ course.id }}/"
           class="btn btn-outline-secondary btn-lg">
            <i class="fas fa-arrow-right"></i> العودة لإدارة المشاركين
        </a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 🚀 الحل الإبداعي المحسن: الحصول على course_id من مصادر متعددة
let courseId = null;

// المصدر الأول: الحقل المخفي (الأكثر موثوقية)
const hiddenField = document.getElementById('course_id_hidden');
if (hiddenField && hiddenField.value) {
    courseId = hiddenField.value;
    console.log('✅ Found course_id from hidden field:', courseId);
}

// المصدر الثاني: استخراج من URL
if (!courseId) {
    const currentUrl = window.location.pathname;
    console.log('🔍 Current URL:', currentUrl);

    // Pattern 1: /course/1/import_participants
    const courseMatch = currentUrl.match(/\/course\/(\d+)\//);
    if (courseMatch) {
        courseId = courseMatch[1];
        console.log('✅ Found course_id from /course/ pattern:', courseId);
    }

    // Pattern 2: /manage_participants/1/
    if (!courseId) {
        const manageMatch = currentUrl.match(/\/manage_participants\/(\d+)\//);
        if (manageMatch) {
            courseId = manageMatch[1];
            console.log('✅ Found course_id from /manage_participants/ pattern:', courseId);
        }
    }

    // Pattern 3: استخراج من أي رقم في URL
    if (!courseId) {
        const numberMatch = currentUrl.match(/\/(\d+)\//);
        if (numberMatch) {
            courseId = numberMatch[1];
            console.log('✅ Found course_id from number pattern:', courseId);
        }
    }
}

// Fallback
if (!courseId) {
    courseId = '1';
    console.log('⚠️ Using fallback course_id:', courseId);
}

console.log('🎯 Final Course ID:', courseId);

$(document).ready(function() {
    console.log('✅ صفحة استيراد المشاركين جاهزة');
    console.log('🎯 زر التحليل الذكي سيذهب إلى: /name_analysis_course');

    // تأثير السحب والإفلات
    $('#uploadZone').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('border-success');
    });

    $('#uploadZone').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('border-success');
    });

    $('#uploadZone').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('border-success');

        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            $('#excelFile')[0].files = files;
        }
    });
});
</script>
{% endblock %}
