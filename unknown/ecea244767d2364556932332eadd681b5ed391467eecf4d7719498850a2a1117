#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة المزيد من التصحيحات للاختبار
"""

import sqlite3
from datetime import datetime, timezone

def add_more_corrections():
    """
    إضافة تصحيحات إضافية للاختبار
    """
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    
    # تصحيحات إضافية للاختبار
    additional_corrections = [
        {
            'wrong_name': 'موسي',
            'correct_name': 'موسى',
            'correction_type': 'alif_maqsura',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'يحيي',
            'correct_name': 'يحيى',
            'correction_type': 'alif_maqsura',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'ابراهيم',
            'correct_name': 'إبراهيم',
            'correction_type': 'hamza',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'اسماعيل',
            'correct_name': 'إسماعيل',
            'correction_type': 'hamza',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        },
        {
            'wrong_name': 'محمد_علي',
            'correct_name': 'محمد علي',
            'correction_type': 'compound_names',
            'created_by': 1,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': 1,
            'usage_count': 0
        }
    ]
    
    # إدراج التصحيحات
    insert_query = """
        INSERT INTO name_correction 
        (wrong_name, correct_name, correction_type, created_by, created_at, is_active, usage_count)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """
    
    added_count = 0
    for correction in additional_corrections:
        try:
            # التحقق من عدم وجود التصحيح مسبقاً
            check_query = "SELECT id FROM name_correction WHERE wrong_name = ? AND correct_name = ?"
            existing = cursor.execute(check_query, (correction['wrong_name'], correction['correct_name'])).fetchone()
            
            if not existing:
                cursor.execute(insert_query, (
                    correction['wrong_name'],
                    correction['correct_name'],
                    correction['correction_type'],
                    correction['created_by'],
                    correction['created_at'],
                    correction['is_active'],
                    correction['usage_count']
                ))
                added_count += 1
                print(f"✅ تم إضافة: {correction['wrong_name']} → {correction['correct_name']}")
            else:
                print(f"⚠️  موجود مسبقاً: {correction['wrong_name']} → {correction['correct_name']}")
                
        except Exception as e:
            print(f"❌ خطأ في إضافة {correction['wrong_name']}: {e}")
    
    # حفظ التغييرات
    conn.commit()
    
    # عرض العدد الكلي
    cursor.execute("SELECT COUNT(*) FROM name_correction")
    total_count = cursor.fetchone()[0]
    
    conn.close()
    
    print(f"\n🎉 تم إضافة {added_count} تصحيح جديد!")
    print(f"📊 إجمالي التصحيحات الآن: {total_count}")
    print("🌐 يمكنك الآن مراجعة التصحيحات في: http://localhost:5000/person_data/manage_corrections")
    
    return added_count

if __name__ == "__main__":
    add_more_corrections()
