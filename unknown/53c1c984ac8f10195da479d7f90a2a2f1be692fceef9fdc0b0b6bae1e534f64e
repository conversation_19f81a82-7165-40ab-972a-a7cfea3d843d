#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قائمة المشاركين المتاحين للإضافة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, CourseParticipant, PersonData, Course

def test_available_participants():
    """
    اختبار قائمة المشاركين المتاحين للإضافة
    """
    with app.app_context():
        try:
            course_id = 1
            
            # جلب إجمالي عدد الأشخاص في جدول person_data
            total_people = PersonData.query.count()
            print(f"📊 إجمالي عدد الأشخاص في person_data: {total_people}")
            
            # جلب المشاركين المضافين مسبقاً في الدورة
            existing_participants = CourseParticipant.query.filter_by(course_id=course_id).all()
            existing_participant_ids = [p.personal_data_id for p in existing_participants]
            print(f"👥 عدد المشاركين المضافين مسبقاً في الدورة {course_id}: {len(existing_participants)}")
            print(f"🔢 معرفات المضافين مسبقاً: {existing_participant_ids}")
            
            # جلب الأشخاص المتاحين للإضافة
            if existing_participant_ids:
                available_people = PersonData.query.filter(~PersonData.id.in_(existing_participant_ids)).all()
            else:
                available_people = PersonData.query.all()
            
            print(f"✅ عدد الأشخاص المتاحين للإضافة: {len(available_people)}")
            
            # عرض أول 5 أشخاص متاحين
            print("\n📋 أول 5 أشخاص متاحين للإضافة:")
            for i, person in enumerate(available_people[:5], 1):
                print(f"   {i}. {person.full_name} (ID: {person.id})")
            
            # عرض المضافين مسبقاً
            if existing_participants:
                print("\n👤 المشاركين المضافين مسبقاً:")
                for i, participant in enumerate(existing_participants, 1):
                    person = PersonData.query.get(participant.personal_data_id)
                    person_name = person.full_name if person else "غير موجود"
                    print(f"   {i}. {person_name} (ID: {participant.personal_data_id})")
            
            # التحقق من صحة المنطق
            expected_available = total_people - len(existing_participants)
            actual_available = len(available_people)
            
            if expected_available == actual_available:
                print(f"\n✅ المنطق صحيح: {expected_available} = {actual_available}")
                return True
            else:
                print(f"\n❌ خطأ في المنطق: متوقع {expected_available} لكن الفعلي {actual_available}")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في اختبار المشاركين المتاحين: {e}")
            return False

if __name__ == "__main__":
    print("🧪 اختبار قائمة المشاركين المتاحين للإضافة...")
    success = test_available_participants()
    if success:
        print("✅ تم الاختبار بنجاح!")
    else:
        print("❌ فشل الاختبار!")
