#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف تشخيص مشاكل الخادم
"""

import sys
import traceback
import os

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    print("🔄 اختبار الاستيرادات الأساسية...")
    
    try:
        import flask
        print("  ✅ Flask")
        
        import sqlalchemy
        print("  ✅ SQLAlchemy")
        
        import flask_login
        print("  ✅ Flask-Login")
        
        return True
    except Exception as e:
        print(f"  ❌ خطأ: {e}")
        return False

def test_app_import():
    """اختبار استيراد التطبيق"""
    print("\n🔄 اختبار استيراد التطبيق...")
    
    try:
        print("  - استيراد app...")
        from app import app
        print("  ✅ تم استيراد app بنجاح")
        
        print("  - استيراد db...")
        from app import db
        print("  ✅ تم استيراد db بنجاح")
        
        print("  - فحص التطبيق...")
        print(f"    📍 اسم التطبيق: {app.name}")
        print(f"    📍 وضع التصحيح: {app.debug}")
        
        return app
    except Exception as e:
        print(f"  ❌ خطأ في استيراد التطبيق: {e}")
        traceback.print_exc()
        return None

def test_simple_run(app):
    """اختبار تشغيل بسيط"""
    print("\n🔄 اختبار تشغيل بسيط...")
    
    try:
        print("  - إعداد الخادم...")
        print("  - المنفذ: 5000")
        print("  - العنوان: 127.0.0.1")
        print("  - وضع التصحيح: مُعطل")
        print("\n🚀 بدء تشغيل الخادم...")
        print("📝 للوصول إلى التقارير: http://127.0.0.1:5000/reports/dashboard")
        print("🛑 اضغط Ctrl+C للإيقاف")
        print("-" * 60)
        
        # تشغيل بسيط بدون debug
        app.run(
            debug=False,
            host='127.0.0.1',
            port=5000,
            threaded=True,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        traceback.print_exc()

def main():
    print("🧪 تشخيص مشاكل الخادم")
    print("=" * 60)
    
    # اختبار الاستيرادات الأساسية
    if not test_basic_imports():
        print("\n❌ فشل في الاستيرادات الأساسية")
        return
    
    # اختبار استيراد التطبيق
    app = test_app_import()
    if not app:
        print("\n❌ فشل في استيراد التطبيق")
        return
    
    print("\n✅ جميع الاختبارات نجحت!")
    print("🚀 محاولة تشغيل الخادم...")
    print("=" * 60)
    
    # تشغيل الخادم
    test_simple_run(app)

if __name__ == '__main__':
    main()
