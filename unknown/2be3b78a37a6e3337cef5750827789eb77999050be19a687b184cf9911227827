#!/usr/bin/env python3
"""
اختبار نظام البحث السريع
"""

import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🔄 بدء اختبار نظام البحث...")
    
    # اختبار استيراد المكتبات
    print("📦 اختبار استيراد المكتبات...")
    from rapidfuzz import fuzz, process
    print("✅ rapidfuzz imported successfully")
    
    try:
        import redis
        print("✅ redis imported successfully")
    except ImportError:
        print("⚠️ redis not available, will use fallback")
    
    # اختبار استيراد محرك البحث
    print("🔍 اختبار استيراد محرك البحث...")
    from fast_search_engine import initialize_search_engine
    print("✅ fast_search_engine imported successfully")
    
    print("🎉 جميع الاختبارات نجحت!")
    
except Exception as e:
    print(f"❌ خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()
