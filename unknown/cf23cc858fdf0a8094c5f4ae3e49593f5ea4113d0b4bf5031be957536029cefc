#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لصفحة name_analysis_course
"""

import urllib.request
import urllib.parse
import json
import time

def test_url(url, description, expected_content=None):
    """اختبار URL مع التحقق من المحتوى"""
    try:
        print(f"🔍 اختبار {description}...")
        print(f"   URL: {url}")
        
        response = urllib.request.urlopen(url, timeout=10)
        status_code = response.getcode()
        content = response.read().decode('utf-8')
        
        print(f"   Status: {status_code}")
        print(f"   Content Length: {len(content)} characters")
        
        if status_code == 200:
            if expected_content:
                found_count = 0
                for item in expected_content:
                    if item in content:
                        print(f"   ✅ {item}: موجود")
                        found_count += 1
                    else:
                        print(f"   ❌ {item}: غير موجود")
                
                success_rate = found_count / len(expected_content)
                print(f"   📊 معدل النجاح: {found_count}/{len(expected_content)} ({success_rate*100:.1f}%)")
                
                if success_rate >= 0.8:  # 80% نجاح
                    print(f"   ✅ {description}: نجح")
                    return True, content
                else:
                    print(f"   ⚠️ {description}: نجح جزئياً")
                    return False, content
            else:
                print(f"   ✅ {description}: نجح")
                return True, content
        else:
            print(f"   ❌ {description}: فشل - Status {status_code}")
            return False, content
            
    except Exception as e:
        print(f"   ❌ {description}: خطأ - {str(e)}")
        return False, ""

def test_api_courses():
    """اختبار API الدورات المحسن"""
    print("\n📚 اختبار API الدورات المحسن:")
    success, content = test_url("http://localhost:5000/api/courses", "API الدورات")
    
    if success:
        try:
            courses = json.loads(content)
            print(f"   📋 عدد الدورات: {len(courses)}")
            
            if courses:
                course = courses[0]
                print(f"   📚 أول دورة:")
                print(f"      العنوان: {course.get('title', 'غير محدد')}")
                print(f"      الرقم: {course.get('course_number', 'غير محدد')}")
                print(f"      التصنيف: {course.get('category', 'غير محدد')}")
                print(f"      المستوى: {course.get('level', 'غير محدد')}")
                print(f"      المشاركين: {course.get('participants_count', 0)}")
                print(f"      الحالة: {course.get('status', 'غير محدد')}")
                print(f"      المدرب: {course.get('trainer', 'غير محدد')}")
                
                # التحقق من الحقول المطلوبة
                required_fields = ['id', 'title', 'course_number', 'category', 'participants_count']
                missing_fields = [field for field in required_fields if field not in course]
                
                if missing_fields:
                    print(f"   ⚠️ حقول مفقودة: {missing_fields}")
                    return False
                else:
                    print(f"   ✅ جميع الحقول المطلوبة موجودة")
                    return True
            else:
                print(f"   ⚠️ لا توجد دورات في النظام")
                return True  # ليس خطأ، فقط لا توجد بيانات
            
        except json.JSONDecodeError as e:
            print(f"   ❌ خطأ في تحليل JSON: {e}")
            return False
    
    return False

def test_name_analysis_course_page():
    """اختبار صفحة name_analysis_course"""
    print("\n🎯 اختبار صفحة name_analysis_course:")
    
    expected_elements = [
        'التحليل الذكي للدورات',
        'رفع ملف Excel للتحليل الذكي',
        'اختر ملف Excel أو اسحبه هنا',
        'بدء التحليل الذكي',
        'العودة للتحليل العادي',
        'loadCourses',
        'setupFileUpload',
        'selectCourse',
        'addSelectedToCourse',
        'courses-list',
        'uploadForm',
        'progressContainer'
    ]
    
    success, content = test_url(
        "http://localhost:5000/name_analysis_course", 
        "صفحة name_analysis_course",
        expected_elements
    )
    
    return success

def test_course_participants_api():
    """اختبار API مشاركي الدورة"""
    print("\n👥 اختبار API مشاركي الدورة:")
    
    # أولاً نحصل على قائمة الدورات
    try:
        response = urllib.request.urlopen("http://localhost:5000/api/courses", timeout=5)
        courses = json.loads(response.read().decode('utf-8'))
        
        if courses:
            course_id = courses[0]['id']
            print(f"   🎯 اختبار مشاركي الدورة رقم: {course_id}")
            
            success, content = test_url(
                f"http://localhost:5000/api/course/{course_id}/participants",
                f"API مشاركي الدورة {course_id}"
            )
            
            if success:
                try:
                    participants = json.loads(content)
                    print(f"   👥 عدد المشاركين: {len(participants)}")
                    
                    if participants:
                        participant = participants[0]
                        print(f"   👤 أول مشارك:")
                        print(f"      الاسم: {participant.get('name', 'غير محدد')}")
                        print(f"      العمر: {participant.get('age', 'غير محدد')}")
                        print(f"      المحافظة: {participant.get('governorate', 'غير محدد')}")
                        print(f"      الحالة: {participant.get('status', 'غير محدد')}")
                    
                    return True
                except json.JSONDecodeError:
                    print(f"   ❌ خطأ في تحليل JSON للمشاركين")
                    return False
            else:
                return False
        else:
            print(f"   ⚠️ لا توجد دورات لاختبار مشاركيها")
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار API المشاركين: {e}")
        return False

def test_file_upload_interface():
    """اختبار واجهة رفع الملفات"""
    print("\n📤 اختبار واجهة رفع الملفات:")
    
    upload_elements = [
        'uploadForm',
        'excelFile',
        'uploadBtn',
        'progressContainer',
        'progressBar',
        'uploadZone',
        'setupFileUpload',
        'resetUploadForm'
    ]
    
    success, content = test_url(
        "http://localhost:5000/name_analysis_course",
        "واجهة رفع الملفات",
        upload_elements
    )
    
    return success

def test_javascript_functions():
    """اختبار وجود دوال JavaScript المطلوبة"""
    print("\n⚙️ اختبار دوال JavaScript:")
    
    js_functions = [
        'loadCourses()',
        'selectCourse(',
        'updateStatsForCourse(',
        'displayResults()',
        'addSelectedToCourse()',
        'setupFileUpload()',
        'resetUploadForm()',
        'updateSelectionCount()'
    ]
    
    success, content = test_url(
        "http://localhost:5000/name_analysis_course",
        "دوال JavaScript",
        js_functions
    )
    
    return success

def main():
    """الدالة الرئيسية للاختبار الشامل"""
    print("🚀 بدء الاختبار الشامل لصفحة name_analysis_course")
    print("=" * 70)
    
    tests = [
        ("API الدورات", test_api_courses),
        ("صفحة name_analysis_course", test_name_analysis_course_page),
        ("API مشاركي الدورة", test_course_participants_api),
        ("واجهة رفع الملفات", test_file_upload_interface),
        ("دوال JavaScript", test_javascript_functions)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            time.sleep(1)  # توقف قصير بين الاختبارات
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # ملخص النتائج
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار الشامل:")
    print("=" * 70)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name:<30}: {status}")
        if result:
            success_count += 1
    
    print(f"\n🎯 النتيجة النهائية: {success_count}/{len(results)} اختبارات نجحت")
    success_rate = success_count / len(results) * 100
    
    if success_count == len(results):
        print("🎉 جميع الاختبارات نجحت! النظام يعمل بشكل مثالي")
    elif success_rate >= 80:
        print("⚠️ معظم الاختبارات نجحت، النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة")
    elif success_rate >= 60:
        print("🔧 النظام يعمل بشكل أساسي لكن يحتاج تحسينات")
    else:
        print("❌ النظام يحتاج إصلاحات جوهرية")
    
    print(f"📈 معدل النجاح: {success_rate:.1f}%")

if __name__ == "__main__":
    main()
