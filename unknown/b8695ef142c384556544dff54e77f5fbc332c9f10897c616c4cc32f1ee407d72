#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت إصلاح بيانات الدورات
"""

from app import app, db, Course, User
from datetime import datetime

def fix_courses_data():
    """إصلاح بيانات الدورات"""
    with app.app_context():
        print("🔧 إصلاح بيانات الدورات...")
        
        # التحقق من وجود مستخدم admin
        admin_user = User.query.filter_by(role='admin').first()
        if not admin_user:
            print("❌ لا يوجد مستخدم admin")
            return
        
        # جلب جميع الدورات
        courses = Course.query.all()
        print(f"📋 العثور على {len(courses)} دورة")
        
        fixed_count = 0
        for course in courses:
            print(f"\n🔍 فحص الدورة: {course.title}")
            print(f"   ID: {course.id}")
            print(f"   رقم الدورة: {course.course_number}")
            print(f"   trainer_id: {course.trainer_id}")
            
            # إصلاح المدرب إذا كان مفقود
            if not course.trainer_id:
                course.trainer_id = admin_user.id
                print(f"   ✅ تم تعيين المدرب: {admin_user.username}")
                fixed_count += 1
            else:
                # التحقق من وجود المدرب
                try:
                    trainer = course.trainer
                    if trainer:
                        print(f"   ✅ المدرب موجود: {trainer.username}")
                    else:
                        course.trainer_id = admin_user.id
                        print(f"   ✅ تم إصلاح المدرب: {admin_user.username}")
                        fixed_count += 1
                except Exception as e:
                    print(f"   ❌ خطأ في المدرب: {e}")
                    course.trainer_id = admin_user.id
                    print(f"   ✅ تم إصلاح المدرب: {admin_user.username}")
                    fixed_count += 1
            
            # التحقق من التواريخ
            if not course.start_date:
                course.start_date = datetime.now()
                print("   ✅ تم إصلاح تاريخ البدء")
                fixed_count += 1
            
            if not course.end_date:
                course.end_date = datetime.now()
                print("   ✅ تم إصلاح تاريخ الانتهاء")
                fixed_count += 1
        
        # حفظ التغييرات
        try:
            db.session.commit()
            print(f"\n🎉 تم إصلاح {fixed_count} مشكلة في الدورات")
            
            # عرض الدورات بعد الإصلاح
            print("\n📋 الدورات بعد الإصلاح:")
            courses = Course.query.all()
            for course in courses:
                print(f"   - {course.title} (المدرب: {course.trainer.username if course.trainer else 'غير محدد'})")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ التغييرات: {e}")
            db.session.rollback()

def main():
    print("🚀 بدء إصلاح بيانات الدورات")
    print("=" * 50)
    fix_courses_data()
    print("=" * 50)
    print("✅ تم الانتهاء من إصلاح البيانات")

if __name__ == "__main__":
    main()
