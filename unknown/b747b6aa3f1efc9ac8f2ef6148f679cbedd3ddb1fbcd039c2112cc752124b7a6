{% extends "layout.html" %}

{% block styles %}
<style>
    .backup-container {
        margin-top: 20px;
    }

    .backup-card {
        margin-bottom: 20px;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .backup-card .card-header {
        border-radius: 10px 10px 0 0;
        font-weight: bold;
    }

    .backup-form {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .backup-list {
        margin-top: 20px;
    }

    .backup-item {
        padding: 10px;
        border-bottom: 1px solid #eee;
        transition: all 0.3s;
    }

    .backup-item:hover {
        background-color: #f8f9fa;
    }

    .backup-item .backup-date {
        font-weight: bold;
        color: #007bff;
    }

    .backup-item .backup-size {
        color: #6c757d;
    }

    .backup-item .backup-actions {
        text-align: right;
    }

    .backup-item .backup-actions .btn {
        margin-left: 5px;
    }

    .backup-empty {
        text-align: center;
        padding: 20px;
        color: #6c757d;
    }

    .backup-info {
        margin-top: 10px;
        font-size: 0.9rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 text-gray-800 mb-0">
            <i class="fas fa-database"></i>
            النسخ الاحتياطي
        </h1>
        <div class="text-end">
            <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                {% if backups %}
                    {{ backups|length }} نسخة احتياطية متوفرة
                {% else %}
                    لا توجد نسخ احتياطية
                {% endif %}
            </small>
        </div>
    </div>

    {% if backups %}
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center bg-primary text-white">
                <div class="card-body">
                    <i class="fas fa-archive fa-2x mb-2"></i>
                    <h4>{{ backups|length }}</h4>
                    <small>إجمالي النسخ</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center bg-success text-white">
                <div class="card-body">
                    <i class="fas fa-calendar-check fa-2x mb-2"></i>
                    <h4>{{ backups[0].formatted_date_ar if backups else 'لا يوجد' }}</h4>
                    <small>آخر نسخة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center bg-info text-white">
                <div class="card-body">
                    <i class="fas fa-hdd fa-2x mb-2"></i>
                    <h4>{{ ((backups|sum(attribute='size')) / 1024 / 1024) | round(1) }} MB</h4>
                    <small>إجمالي الحجم</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center bg-warning text-white">
                <div class="card-body">
                    <i class="fas fa-shield-alt fa-2x mb-2"></i>
                    <h4>محمي</h4>
                    <small>حالة البيانات</small>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <div class="col-md-4">
            <div class="card backup-card">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-plus-circle"></i>
                    إنشاء نسخة احتياطية جديدة
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('backup') }}">
                        {{ form.hidden_tag() }}

                        <div class="mb-3">
                            <label for="backup_name" class="form-label">اسم النسخة الاحتياطية</label>
                            {{ form.backup_name(class="form-control", placeholder="اترك فارغاً لاستخدام اسم تلقائي") }}
                            <div class="form-text">
                                إذا تركت هذا الحقل فارغاً، سيتم إنشاء اسم تلقائي يتضمن التاريخ والوقت الحاليين.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="backup_path" class="form-label">مسار النسخة الاحتياطية</label>
                            {{ form.backup_path(class="form-control", placeholder="اترك فارغاً لاستخدام المسار الافتراضي") }}
                            <div class="form-text">
                                إذا تركت هذا الحقل فارغاً، سيتم استخدام المسار الافتراضي للنسخ الاحتياطية.
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.include_personal_data(class="form-check-input") }}
                                <label class="form-check-label" for="include_personal_data">
                                    تضمين البيانات الشخصية
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.include_courses(class="form-check-input") }}
                                <label class="form-check-label" for="include_courses">
                                    تضمين بيانات الدورات
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.include_uploads(class="form-check-input") }}
                                <label class="form-check-label" for="include_uploads">
                                    تضمين الملفات المرفقة
                                </label>
                            </div>
                        </div>

                        <div class="d-grid">
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>

            <div class="card backup-card">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-info-circle"></i>
                    معلومات النسخ الاحتياطي
                </div>
                <div class="card-body">
                    <p>
                        <i class="fas fa-check-circle text-success"></i>
                        يتم حفظ النسخ الاحتياطية في مجلد <code>backups</code> بشكل افتراضي.
                    </p>
                    <p>
                        <i class="fas fa-check-circle text-success"></i>
                        تتضمن النسخة الاحتياطية قاعدة البيانات والملفات المرفقة.
                    </p>
                    <p>
                        <i class="fas fa-check-circle text-success"></i>
                        يمكنك استعادة النسخة الاحتياطية في أي وقت.
                    </p>
                    <p>
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        سيتم استبدال البيانات الحالية عند استعادة نسخة احتياطية.
                    </p>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card backup-card">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-list"></i>
                    النسخ الاحتياطية المتوفرة
                </div>
                <div class="card-body">
                    {% if backups %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><i class="fas fa-file-archive me-2"></i>اسم الملف</th>
                                    <th><i class="fas fa-calendar-alt me-2"></i>تاريخ الإنشاء</th>
                                    <th><i class="fas fa-hdd me-2"></i>الحجم</th>
                                    <th><i class="fas fa-cogs me-2"></i>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for backup in backups %}
                                <tr>
                                    <td>
                                        <i class="fas fa-file-archive text-primary me-2"></i>
                                        {{ backup.name }}
                                    </td>
                                    <td>
                                        <i class="fas fa-calendar-alt text-info me-2"></i>
                                        <strong>{{ backup.formatted_date_ar }}</strong>
                                        <br>
                                        <small class="text-muted">
                                            {{ backup.formatted_date }}
                                        </small>
                                        {% if backup.info %}
                                        <br>
                                        <small class="text-success">
                                            <i class="fas fa-info-circle"></i>
                                            يحتوي على معلومات إضافية
                                        </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <i class="fas fa-hdd text-success me-2"></i>
                                        {{ (backup.size / 1024 / 1024) | round(2) }} ميجابايت
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('download_backup', filename=backup.name) }}" class="btn btn-sm btn-primary" title="تنزيل النسخة الاحتياطية">
                                                <i class="fas fa-download"></i>
                                                تنزيل
                                            </a>
                                            <a href="{{ url_for('restore_backup', filename=backup.name) }}" class="btn btn-sm btn-warning" title="استعادة النسخة الاحتياطية" onclick="return confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')">
                                                <i class="fas fa-undo"></i>
                                                استعادة
                                            </a>
                                            <a href="{{ url_for('delete_backup', filename=backup.name) }}" class="btn btn-sm btn-danger" title="حذف النسخة الاحتياطية" onclick="return confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="backup-empty">
                        <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                        <h5>لا توجد نسخ احتياطية متوفرة</h5>
                        <p>قم بإنشاء نسخة احتياطية جديدة باستخدام النموذج على اليمين.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
