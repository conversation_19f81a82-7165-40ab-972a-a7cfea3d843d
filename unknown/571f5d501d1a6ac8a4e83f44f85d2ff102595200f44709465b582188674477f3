# دليل تشغيل نظام التدريب والتأهيل

## 🚀 طريقة التشغيل السريع

### الطريقة الأولى: استخدام الملف المحدث (الأسهل)
```bash
python تشغيل_النظام_المحدث.py
```

### الطريقة الثانية: استخدام الملف الأصلي
```bash
python تشغيل_النظام.py
```

### الطريقة الثالثة: التشغيل المباشر
```bash
python run_server.py
```

## 📋 متطلبات النظام

### Python
- Python 3.8 أو أحدث
- pip (مدير الحزم)

### الحزم المطلوبة
سيتم تثبيتها تلقائياً عند التشغيل:
- Flask
- Flask-SQLAlchemy
- Flask-Login
- Flask-WTF
- pandas
- numpy
- arabic-reshaper
- python-bidi
- wtforms
- werkzeug
- tqdm

## 🔑 بيانات تسجيل الدخول

- **اسم المستخدم:** admin
- **كلمة المرور:** admin

## 🌐 الروابط المهمة

بعد تشغيل النظام، يمكنك الوصول إلى:

### الصفحات الرئيسية
- **الصفحة الرئيسية:** http://localhost:5000
- **تسجيل الدخول:** http://localhost:5000/login

### إدارة البيانات
- **لوحة البيانات:** http://localhost:5000/person_data
- **جدول البيانات:** http://localhost:5000/person_data_table
- **قائمة الأشخاص:** http://localhost:5000/simple_person_list

### التقارير
- **لوحة التقارير:** http://localhost:5000/reports/dashboard
- **تقارير الدورات:** http://localhost:5000/course_reports

### الإدارة
- **إدارة المستخدمين:** http://localhost:5000/users
- **الجداول المرجعية:** http://localhost:5000/reference_tables

## 🔧 حل المشاكل الشائعة

### المشكلة: خطأ في استيراد الوحدات
**الحل:**
```bash
pip install flask flask-sqlalchemy flask-login flask-wtf pandas numpy arabic-reshaper python-bidi
```

### المشكلة: المنفذ 5000 مستخدم
**الحل:**
1. أغلق أي برنامج يستخدم المنفذ 5000
2. أو أعد تشغيل الحاسوب
3. أو استخدم منفذ مختلف في الكود

### المشكلة: لا يمكن الوصول للنظام
**الحل:**
1. تأكد من تشغيل النظام بنجاح
2. جرب الروابط التالية:
   - http://localhost:5000
   - http://127.0.0.1:5000

### المشكلة: قاعدة البيانات فارغة
**الحل:**
- النظام سينشئ قاعدة البيانات تلقائياً عند التشغيل الأول
- إذا كانت فارغة، يمكنك إضافة بيانات تجريبية من لوحة الإدارة

## 📁 هيكل الملفات المهمة

```
نظام التدريب/
├── app.py                      # الملف الرئيسي للتطبيق
├── تشغيل_النظام_المحدث.py      # ملف التشغيل المحسن
├── تشغيل_النظام.py             # ملف التشغيل الأصلي
├── run_server.py               # ملف تشغيل بديل
├── training_system.db          # قاعدة البيانات
├── templates/                  # قوالب HTML
├── static/                     # الملفات الثابتة (CSS, JS, صور)
└── requirements.txt            # قائمة المتطلبات
```

## 🎯 الميزات الرئيسية

### إدارة البيانات الشخصية
- إضافة وتعديل بيانات الأشخاص
- استيراد من ملفات Excel
- تصحيح الأسماء والبيانات
- البحث والتصفية المتقدمة

### إدارة الدورات التدريبية
- إنشاء وإدارة الدورات
- إضافة المشاركين
- تتبع الحضور والغياب
- إدارة الشهادات

### التقارير والإحصائيات
- تقارير شاملة عن الدورات
- إحصائيات المشاركين
- تقارير قابلة للتصدير
- رسوم بيانية تفاعلية

### الأمان والصلاحيات
- نظام تسجيل دخول آمن
- صلاحيات متعددة المستويات
- حماية CSRF
- تشفير كلمات المرور

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من تثبيت Python 3.8 أو أحدث
2. تأكد من تثبيت جميع المتطلبات
3. تحقق من رسائل الخطأ في وحدة التحكم
4. تأكد من عدم استخدام المنفذ 5000 من برنامج آخر

## 🔄 التحديثات

للحصول على أحدث إصدار:
1. احتفظ بنسخة احتياطية من قاعدة البيانات
2. استبدل الملفات القديمة بالجديدة
3. شغل النظام مرة أخرى

---

**ملاحظة:** هذا النظام مصمم للاستخدام المحلي. لا تستخدمه على الإنترنت بدون إعدادات أمان إضافية.
