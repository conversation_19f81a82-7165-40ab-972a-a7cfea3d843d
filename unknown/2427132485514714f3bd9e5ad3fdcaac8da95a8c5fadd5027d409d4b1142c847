from app import app, db, User
from werkzeug.security import generate_password_hash
from datetime import datetime

# إنشاء سياق التطبيق
with app.app_context():
    # إنشاء جميع الجداول
    db.create_all()
    
    # التحقق من وجود مستخدم مسؤول
    admin = User.query.filter_by(email='<EMAIL>').first()
    if not admin:
        # إنشاء مستخدم مسؤول جديد
        admin = User(
            username='admin',
            email='<EMAIL>',
            password=generate_password_hash('admin123'),
            role='admin',
            created_at=datetime.utcnow()
        )
        db.session.add(admin)
        db.session.commit()
        print("تم إنشاء المستخدم المسؤول بنجاح!")
    else:
        print("المستخدم المسؤول موجود بالفعل.")
    
    print("تم تهيئة قاعدة البيانات بنجاح!")
