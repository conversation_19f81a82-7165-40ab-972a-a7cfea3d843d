#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة الدورات الثلاث المفقودة
"""

from app import app, db, Course, User
from datetime import datetime

def add_courses():
    """إضافة الدورات الثلاث"""
    with app.app_context():
        print("📚 إضافة الدورات الثلاث المفقودة...")
        
        # التأكد من وجود مستخدم admin
        admin_user = User.query.filter_by(role='admin').first()
        if not admin_user:
            print("❌ لا يوجد مستخدم admin")
            return False
        
        # بيانات الدورات الثلاث
        courses_data = [
            {
                'course_number': '3455667',
                'title': 'المبيعات2',
                'description': 'دورة تدريبية في المبيعات والتسويق',
                'category': 'management',
                'level': 'intermediate',
                'start_date': datetime(2025, 5, 22),
                'end_date': datetime(2025, 5, 31),
                'start_date_hijri': '1446/11/22',
                'end_date_hijri': '1446/12/01',
                'duration_days': 10,
                'trainer_id': admin_user.id
            },
            {
                'course_number': '10054',
                'title': 'تدريب الذكاء الاصطناعي',
                'description': 'دورة تدريبية في الذكاء الاصطناعي والتعلم الآلي',
                'category': 'ai',
                'level': 'advanced',
                'start_date': datetime(2025, 5, 22),
                'end_date': datetime(2025, 6, 20),
                'start_date_hijri': '1446/11/22',
                'end_date_hijri': '1446/12/20',
                'duration_days': 30,
                'trainer_id': admin_user.id
            },
            {
                'course_number': '1100255',
                'title': 'تدريب الذكاء الاصطناعي 2',
                'description': 'دورة متقدمة في الذكاء الاصطناعي',
                'category': 'ai',
                'level': 'advanced',
                'start_date': datetime(2025, 5, 22),
                'end_date': datetime(2025, 5, 31),
                'start_date_hijri': '1446/11/22',
                'end_date_hijri': '1446/12/01',
                'duration_days': 10,
                'trainer_id': admin_user.id
            }
        ]
        
        # حذف الدورات الموجودة أولاً
        Course.query.delete()
        db.session.commit()
        
        # إضافة الدورات الجديدة
        created_courses = []
        for course_data in courses_data:
            course = Course(**course_data)
            db.session.add(course)
            created_courses.append(course)
        
        try:
            db.session.commit()
            
            print(f"✅ تم إنشاء {len(created_courses)} دورة:")
            for course in created_courses:
                print(f"   - {course.course_number}: {course.title}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حفظ الدورات: {str(e)}")
            db.session.rollback()
            return False

def verify_data():
    """التحقق من البيانات النهائية"""
    with app.app_context():
        from app import PersonData, CourseParticipant
        
        courses_count = Course.query.count()
        persons_count = PersonData.query.count()
        participants_count = CourseParticipant.query.count()
        
        print(f"\n📊 البيانات النهائية:")
        print(f"📚 الدورات: {courses_count}")
        print(f"👥 الأشخاص: {persons_count}")
        print(f"🎯 المشاركين: {participants_count}")
        
        if courses_count == 3 and persons_count == 10:
            print("🎉 تم استعادة البيانات الأصلية بنجاح!")
            
            # عرض تفاصيل الدورات
            print("\n📚 تفاصيل الدورات:")
            courses = Course.query.all()
            for course in courses:
                print(f"   - {course.course_number}: {course.title}")
            
            # عرض عينة من الأشخاص
            print("\n👥 عينة من الأشخاص:")
            persons = PersonData.query.limit(5).all()
            for person in persons:
                print(f"   - {person.full_name}")
            
            return True
        else:
            print("⚠️ هناك مشكلة في البيانات")
            return False

def main():
    """الدالة الرئيسية"""
    print("🔄 إضافة الدورات المفقودة")
    print("=" * 50)
    
    # إضافة الدورات
    if add_courses():
        print("✅ تم إضافة الدورات بنجاح")
    else:
        print("❌ فشل في إضافة الدورات")
        return
    
    # التحقق من البيانات
    verify_data()

if __name__ == "__main__":
    main()
