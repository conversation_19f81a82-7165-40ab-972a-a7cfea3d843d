#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعادة إنشاء البيانات الأصلية (3 دورات و 10 أشخاص)
"""

from app import app, db, Course, PersonData, User
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_original_courses():
    """إنشاء الدورات الثلاث الأصلية"""
    with app.app_context():
        print("📚 إنشاء الدورات الثلاث الأصلية...")
        
        # التأكد من وجود مستخدم admin
        admin_user = User.query.filter_by(role='admin').first()
        if not admin_user:
            print("❌ لا يوجد مستخدم admin")
            return False
        
        # حذف الدورات الموجودة
        Course.query.delete()
        db.session.commit()
        
        # إنشاء الدورات الثلاث
        courses_data = [
            {
                'course_number': '3455667',
                'title': 'المبيعات2',
                'description': 'دورة تدريبية في المبيعات والتسويق',
                'category': 'management',
                'level': 'intermediate',
                'start_date': datetime(2025, 5, 22),
                'end_date': datetime(2025, 5, 31),
                'duration_days': 10,
                'trainer_id': admin_user.id
            },
            {
                'course_number': '10054',
                'title': 'تدريب الذكاء الاصطناعي',
                'description': 'دورة تدريبية في الذكاء الاصطناعي والتعلم الآلي',
                'category': 'ai',
                'level': 'advanced',
                'start_date': datetime(2025, 5, 22),
                'end_date': datetime(2025, 6, 20),
                'duration_days': 30,
                'trainer_id': admin_user.id
            },
            {
                'course_number': '1100255',
                'title': 'تدريب الذكاء الاصطناعي 2',
                'description': 'دورة متقدمة في الذكاء الاصطناعي',
                'category': 'ai',
                'level': 'advanced',
                'start_date': datetime(2025, 5, 22),
                'end_date': datetime(2025, 5, 31),
                'duration_days': 10,
                'trainer_id': admin_user.id
            }
        ]
        
        created_courses = []
        for course_data in courses_data:
            course = Course(**course_data)
            db.session.add(course)
            created_courses.append(course)
        
        db.session.commit()
        
        print(f"✅ تم إنشاء {len(created_courses)} دورة:")
        for course in created_courses:
            print(f"   - {course.course_number}: {course.title}")
        
        return True

def create_original_persons():
    """إنشاء الأشخاص العشرة الأصليين"""
    with app.app_context():
        print("👥 إنشاء الأشخاص العشرة الأصليين...")
        
        # حذف البيانات الموجودة
        PersonData.query.delete()
        db.session.commit()
        
        # بيانات الأشخاص العشرة
        persons_data = [
            {
                'full_name': 'أحمد محمد علي',
                'age': 28,
                'governorate': 'صنعاء',
                'directorate': 'شعوب',
                'qualification': 'بكالوريوس',
                'job': 'موظف',
                'agency': 'وزارة الداخلية',
                'phone': '777123456',
                'national_number': '01234567890'
            },
            {
                'full_name': 'فاطمة أحمد محمد',
                'age': 25,
                'governorate': 'عدن',
                'directorate': 'المعلا',
                'qualification': 'دبلوم',
                'job': 'معلمة',
                'agency': 'وزارة التربية',
                'phone': '777234567',
                'national_number': '01234567891'
            },
            {
                'full_name': 'محمد علي أحمد',
                'age': 32,
                'governorate': 'تعز',
                'directorate': 'صالة',
                'qualification': 'ماجستير',
                'job': 'مهندس',
                'agency': 'وزارة الأشغال',
                'phone': '777345678',
                'national_number': '01234567892'
            },
            {
                'full_name': 'عائشة محمد علي',
                'age': 30,
                'governorate': 'الحديدة',
                'directorate': 'الحديدة',
                'qualification': 'بكالوريوس',
                'job': 'طبيبة',
                'agency': 'وزارة الصحة',
                'phone': '777456789',
                'national_number': '01234567893'
            },
            {
                'full_name': 'علي أحمد محمد',
                'age': 27,
                'governorate': 'إب',
                'directorate': 'إب',
                'qualification': 'دبلوم',
                'job': 'فني',
                'agency': 'وزارة الكهرباء',
                'phone': '777567890',
                'national_number': '01234567894'
            },
            {
                'full_name': 'زينب علي أحمد',
                'age': 26,
                'governorate': 'ذمار',
                'directorate': 'ذمار',
                'qualification': 'بكالوريوس',
                'job': 'محاسبة',
                'agency': 'وزارة المالية',
                'phone': '777678901',
                'national_number': '01234567895'
            },
            {
                'full_name': 'حسن محمد علي',
                'age': 35,
                'governorate': 'حضرموت',
                'directorate': 'المكلا',
                'qualification': 'ماجستير',
                'job': 'مدير',
                'agency': 'وزارة التخطيط',
                'phone': '777789012',
                'national_number': '01234567896'
            },
            {
                'full_name': 'مريم أحمد علي',
                'age': 24,
                'governorate': 'المحويت',
                'directorate': 'المحويت',
                'qualification': 'دبلوم',
                'job': 'سكرتيرة',
                'agency': 'وزارة الإعلام',
                'phone': '777890123',
                'national_number': '01234567897'
            },
            {
                'full_name': 'يوسف علي محمد',
                'age': 29,
                'governorate': 'حجة',
                'directorate': 'حجة',
                'qualification': 'بكالوريوس',
                'job': 'مدرس',
                'agency': 'وزارة التربية',
                'phone': '777901234',
                'national_number': '01234567898'
            },
            {
                'full_name': 'خديجة محمد أحمد',
                'age': 31,
                'governorate': 'صعدة',
                'directorate': 'صعدة',
                'qualification': 'بكالوريوس',
                'job': 'ممرضة',
                'agency': 'وزارة الصحة',
                'phone': '777012345',
                'national_number': '01234567899'
            }
        ]
        
        created_persons = []
        for person_data in persons_data:
            person = PersonData(**person_data)
            db.session.add(person)
            created_persons.append(person)
        
        db.session.commit()
        
        print(f"✅ تم إنشاء {len(created_persons)} شخص:")
        for person in created_persons:
            print(f"   - {person.full_name} ({person.governorate})")
        
        return True

def main():
    """الدالة الرئيسية"""
    print("🔄 إعادة إنشاء البيانات الأصلية")
    print("=" * 60)
    
    # إنشاء الدورات
    if create_original_courses():
        print("✅ تم إنشاء الدورات بنجاح")
    else:
        print("❌ فشل في إنشاء الدورات")
        return
    
    # إنشاء الأشخاص
    if create_original_persons():
        print("✅ تم إنشاء الأشخاص بنجاح")
    else:
        print("❌ فشل في إنشاء الأشخاص")
        return
    
    # التحقق من النتيجة
    with app.app_context():
        courses_count = Course.query.count()
        persons_count = PersonData.query.count()
        
        print("\n📊 النتيجة النهائية:")
        print(f"📚 الدورات: {courses_count}")
        print(f"👥 الأشخاص: {persons_count}")
        
        if courses_count == 3 and persons_count == 10:
            print("🎉 تم إنشاء البيانات الأصلية بنجاح!")
        else:
            print("⚠️ هناك مشكلة في البيانات")

if __name__ == "__main__":
    main()
