#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح بنود التقييم
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db

def fix_evaluation_items():
    """إصلاح بنود التقييم"""
    
    with app.app_context():
        print("🔧 إصلاح بنود التقييم...")
        
        # حذف البنود الموجودة
        db.engine.execute("DELETE FROM evaluation_items")
        print("✅ تم حذف البنود القديمة")
        
        # جلب المعايير
        criteria = db.engine.execute("SELECT * FROM evaluation_criteria ORDER BY order_index").fetchall()
        
        # إضافة البنود لكل معيار حسب النموذج الأصلي
        criteria_items_data = [
            # معيار 1 - 7 بنود
            (1, [
                ('بند 1', 10.0), ('بند 2', 10.0), ('بند 3', 10.0), 
                ('بند 4', 10.0), ('بند 5', 10.0), ('بند 6', 10.0), ('بند 7', 10.0)
            ]),
            # معيار 2 - 3 بنود  
            (2, [
                ('بند 1', 10.0), ('بند 2', 10.0), ('بند 3', 10.0)
            ]),
            # معيار 3 - 4 بنود
            (3, [
                ('بند 1', 10.0), ('بند 2', 10.0), ('بند 3', 10.0), ('بند 4', 10.0)
            ]),
            # معيار 4 - 2 بنود
            (4, [
                ('بند 1', 10.0), ('بند 2', 10.0)
            ]),
            # معيار 5 - 3 بنود
            (5, [
                ('بند 1', 10.0), ('بند 2', 10.0), ('بند 3', 10.0)
            ]),
            # معيار 6 - 3 بنود
            (6, [
                ('بند 1', 10.0), ('بند 2', 10.0), ('بند 3', 10.0)
            ])
        ]
        
        total_items = 0
        total_score = 0
        
        for criteria_id, items in criteria_items_data:
            print(f"📝 إضافة بنود للمعيار {criteria_id}:")
            
            for order, (item_name, max_score) in enumerate(items, 1):
                db.engine.execute("""
                    INSERT INTO evaluation_items (criteria_id, name, max_score, order_index)
                    VALUES (?, ?, ?, ?)
                """, (criteria_id, item_name, max_score, order))
                
                print(f"   - {item_name}: {max_score} درجة")
                total_items += 1
                total_score += max_score
        
        print(f"\n✅ تم إضافة {total_items} بند بإجمالي {total_score} درجة")
        
        # اختبار الاستعلام
        print(f"\n🔍 اختبار الاستعلام:")
        
        criteria_results = db.engine.execute("""
            SELECT DISTINCT ec.id, ec.name, ec.description, ec.order_index
            FROM evaluation_criteria ec
            JOIN course_evaluation_criteria cec ON ec.id = cec.criteria_id
            WHERE cec.course_id = 1 AND cec.is_active = 1 AND ec.is_active = 1
            ORDER BY ec.order_index
        """).fetchall()
        
        test_total_items = 0
        test_total_score = 0
        
        for criteria in criteria_results:
            print(f"   📝 {criteria['name']}")
            
            items_results = db.engine.execute("""
                SELECT id, name, max_score, order_index
                FROM evaluation_items
                WHERE criteria_id = ? AND is_active = 1
                ORDER BY order_index
            """, (criteria['id'],)).fetchall()
            
            for item in items_results:
                print(f"      - {item['name']}: {item['max_score']} درجة")
                test_total_items += 1
                test_total_score += item['max_score']
        
        print(f"\n📊 النتيجة النهائية:")
        print(f"   - عدد المعايير: {len(criteria_results)}")
        print(f"   - عدد البنود: {test_total_items}")
        print(f"   - الدرجة العظمى: {test_total_score}")

if __name__ == "__main__":
    fix_evaluation_items()
