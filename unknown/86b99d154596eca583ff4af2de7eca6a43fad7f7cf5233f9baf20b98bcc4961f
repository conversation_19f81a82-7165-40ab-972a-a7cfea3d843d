#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص جميع النسخ الاحتياطية للعثور على البيانات الأصلية
"""

import sqlite3
import os
import zipfile
import tempfile
import shutil

def check_zip_backup(zip_file):
    """فحص النسخة الاحتياطية المضغوطة"""
    try:
        print(f"🔍 فحص النسخة المضغوطة: {zip_file}")
        
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            # البحث عن ملف قاعدة البيانات
            db_files = [f for f in zip_ref.namelist() if f.endswith('.db')]
            
            if db_files:
                # استخراج مؤقت
                with tempfile.TemporaryDirectory() as temp_dir:
                    for db_file in db_files:
                        zip_ref.extract(db_file, temp_dir)
                        db_path = os.path.join(temp_dir, db_file)
                        
                        print(f"   📁 ملف قاعدة البيانات: {db_file}")
                        check_db_content(db_path)
            else:
                print("   ❌ لا يحتوي على ملفات قاعدة بيانات")
                
    except Exception as e:
        print(f"   ❌ خطأ في فحص الملف المضغوط: {str(e)}")

def check_db_content(db_path):
    """فحص محتوى قاعدة البيانات"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الجداول الرئيسية
        tables_to_check = ['course', 'person_data', 'personal_data', 'course_participant']
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"      📋 {table}: {count} سجل")
                
                # إذا وجدنا بيانات، نعرض عينة
                if table == 'course' and count > 0:
                    cursor.execute("SELECT id, course_number, title FROM course")
                    courses = cursor.fetchall()
                    for course in courses:
                        print(f"         - دورة {course[0]}: {course[1]} - {course[2]}")
                
                elif table == 'person_data' and count > 0:
                    cursor.execute("SELECT id, full_name FROM person_data LIMIT 5")
                    persons = cursor.fetchall()
                    for person in persons:
                        print(f"         - شخص {person[0]}: {person[1]}")
                        
            except Exception as e:
                print(f"      ❌ خطأ في جدول {table}: {str(e)}")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص قاعدة البيانات: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص جميع النسخ الاحتياطية")
    print("=" * 60)
    
    # فحص النسخ الاحتياطية في المجلد الرئيسي
    print("📁 النسخ الاحتياطية في المجلد الرئيسي:")
    backup_files = [
        'training_system_backup_20250529_034725.db',
        'training_system.db.bak_20250503_044906',
        'training_system_backup_columns_20250529_035306.db'
    ]
    
    for backup in backup_files:
        if os.path.exists(backup):
            print(f"\n🔍 فحص: {backup}")
            check_db_content(backup)
    
    # فحص النسخ الاحتياطية في مجلد backups
    print("\n📁 النسخ الاحتياطية في مجلد backups:")
    backups_dir = 'backups'
    
    if os.path.exists(backups_dir):
        backup_files = os.listdir(backups_dir)
        backup_files.sort(reverse=True)  # الأحدث أولاً
        
        for backup_file in backup_files:
            if backup_file.endswith('.zip'):
                backup_path = os.path.join(backups_dir, backup_file)
                print(f"\n📦 {backup_file}")
                check_zip_backup(backup_path)
    
    # فحص قاعدة البيانات الحالية
    print("\n📊 قاعدة البيانات الحالية:")
    if os.path.exists('training_system.db'):
        check_db_content('training_system.db')
    
    # فحص قاعدة البيانات في instance
    print("\n📊 قاعدة البيانات في instance:")
    instance_db = 'instance/training_system.db'
    if os.path.exists(instance_db):
        check_db_content(instance_db)

if __name__ == "__main__":
    main()
