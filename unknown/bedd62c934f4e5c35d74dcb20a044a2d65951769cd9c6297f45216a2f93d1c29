import os
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///training_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# تعريف نموذج البيانات الشخصية
class PersonalData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    national_number = db.Column(db.String(20), nullable=True)
    nickname = db.Column(db.String(50), nullable=True)
    birth_date = db.Column(db.Date, nullable=True)
    age = db.Column(db.Integer, nullable=True)
    phone_yemen_mobile = db.Column(db.String(20), nullable=True)
    job_title = db.Column(db.String(100), nullable=True)
    work_place = db.Column(db.String(100), nullable=True)

    def __repr__(self):
        return f"PersonalData('{self.full_name}')"

def add_custom_person(full_name, national_number, phone=None, job_title=None, work_place=None):
    """
    إضافة شخص جديد إلى قاعدة البيانات بناءً على البيانات المدخلة
    
    Args:
        full_name (str): الاسم الكامل
        national_number (str): الرقم الوطني
        phone (str, optional): رقم الهاتف
        job_title (str, optional): المسمى الوظيفي
        work_place (str, optional): مكان العمل
    
    Returns:
        bool: نجاح أو فشل العملية
    """
    try:
        # إنشاء كائن PersonalData جديد
        new_person = PersonalData(
            user_id=1,  # افتراض أن معرف المستخدم هو 1
            full_name=full_name,
            national_number=national_number,
            phone_yemen_mobile=phone,
            job_title=job_title,
            work_place=work_place
        )
        
        # التحقق من أن الكائن هو من نوع PersonalData
        print(f"نوع الكائن: {type(new_person)}")
        
        # إضافة الكائن إلى قاعدة البيانات
        db.session.add(new_person)
        db.session.commit()
        
        print(f"تم إضافة الشخص بنجاح: {new_person.full_name}, الرقم الوطني: {new_person.national_number}")
        return True
    except Exception as e:
        db.session.rollback()
        print(f"حدث خطأ أثناء إضافة الشخص: {str(e)}")
        return False

if __name__ == "__main__":
    # الحصول على البيانات من المستخدم
    print("إضافة شخص جديد إلى قاعدة البيانات")
    print("----------------------------------")
    
    full_name = input("الاسم الكامل: ")
    national_number = input("الرقم الوطني: ")
    phone = input("رقم الهاتف (اختياري): ")
    job_title = input("المسمى الوظيفي (اختياري): ")
    work_place = input("مكان العمل (اختياري): ")
    
    # تحويل القيم الفارغة إلى None
    phone = phone if phone.strip() else None
    job_title = job_title if job_title.strip() else None
    work_place = work_place if work_place.strip() else None
    
    # إضافة الشخص إلى قاعدة البيانات
    with app.app_context():
        add_custom_person(full_name, national_number, phone, job_title, work_place)
