#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار نهائي لـ API إضافة المشاركين
"""

import requests
import json
import time

BASE_URL = "http://localhost:5001"
COURSE_ID = 1

def test_add_participant_api():
    """اختبار API إضافة المشاركين"""
    print("🎯 اختبار API إضافة المشاركين...")
    
    # قائمة الأشخاص للاختبار (من قاعدة البيانات)
    test_people = [2, 3, 4, 5]  # IDs من قاعدة البيانات
    
    for person_id in test_people:
        print(f"\n🔄 اختبار إضافة الشخص ID: {person_id}")
        
        try:
            # إرسال طلب إضافة
            response = requests.post(
                f"{BASE_URL}/course/{COURSE_ID}/add_participant_api",
                json={"person_id": person_id},
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            print(f"📡 رمز الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ نجح: {result.get('message')}")
                    person_info = result.get('person', {})
                    print(f"   الاسم: {person_info.get('full_name')}")
                else:
                    print(f"⚠️ فشل: {result.get('message')}")
            else:
                print(f"❌ خطأ HTTP: {response.status_code}")
                print(f"📄 الاستجابة: {response.text[:200]}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ في الطلب: {e}")
        except Exception as e:
            print(f"❌ خطأ عام: {e}")
        
        # انتظار قصير بين الطلبات
        time.sleep(0.5)

def test_create_new_person_api():
    """اختبار API إنشاء شخص جديد"""
    print("\n👤 اختبار API إنشاء شخص جديد...")
    
    new_person = {
        "full_name": "اختبار API النهائي",
        "national_number": "8888888888",
        "military_number": "API001",
        "age": 28,
        "governorate": "صنعاء",
        "directorate": "شعوب",
        "job": "مختبر API",
        "qualification": "بكالوريوس حاسوب",
        "phone": "777888999"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/course/{COURSE_ID}/add_new_person_api",
            json=new_person,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📡 رمز الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ تم إنشاء الشخص: {result.get('message')}")
                person_info = result.get('person', {})
                print(f"   ID: {person_info.get('id')}")
                print(f"   الاسم: {person_info.get('full_name')}")
                print(f"   الرقم الوطني: {person_info.get('national_number')}")
            else:
                print(f"⚠️ فشل في الإنشاء: {result.get('message')}")
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            print(f"📄 الاستجابة: {response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ خطأ في الطلب: {e}")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

def check_server_status():
    """فحص حالة الخادم"""
    print("🔍 فحص حالة الخادم...")
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ الخادم يعمل بشكل صحيح")
            return True
        else:
            print(f"⚠️ الخادم يستجيب برمز: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ لا يمكن الوصول للخادم: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نهائي شامل لـ APIs")
    print("=" * 50)
    
    # 1. فحص حالة الخادم
    if not check_server_status():
        print("❌ الخادم لا يعمل! تأكد من تشغيل python app.py")
        return
    
    # 2. اختبار إضافة المشاركين
    test_add_participant_api()
    
    # 3. اختبار إنشاء شخص جديد
    test_create_new_person_api()
    
    print("\n" + "=" * 50)
    print("🎉 انتهى الاختبار النهائي!")
    print("🌐 يمكنك الآن اختبار الواجهة من المتصفح:")
    print(f"   {BASE_URL}/manage_participants/{COURSE_ID}/")

if __name__ == "__main__":
    main()
