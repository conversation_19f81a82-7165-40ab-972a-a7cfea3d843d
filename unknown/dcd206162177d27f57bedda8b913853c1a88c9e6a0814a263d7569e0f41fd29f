#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص جميع النسخ الاحتياطية للعثور على البيانات المفقودة
"""

import sqlite3
import os
import zipfile
import tempfile

def check_db_file(db_path):
    """فحص ملف قاعدة بيانات واحد"""
    if not os.path.exists(db_path):
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الجداول المهمة
        tables_to_check = ['user', 'course', 'personal_data', 'course_participant']
        result = {'path': db_path, 'size': os.path.getsize(db_path), 'data': {}}
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                result['data'][table] = count
                
                # عرض عينة من البيانات
                if table == 'course' and count > 0:
                    cursor.execute("SELECT course_number, title FROM course LIMIT 3")
                    courses = cursor.fetchall()
                    result['course_sample'] = courses
                    
                elif table == 'personal_data' and count > 0:
                    cursor.execute("SELECT full_name FROM personal_data LIMIT 3")
                    persons = cursor.fetchall()
                    result['person_sample'] = persons
                    
            except Exception as e:
                result['data'][table] = f'خطأ: {str(e)}'
        
        conn.close()
        return result
        
    except Exception as e:
        return {'path': db_path, 'error': str(e)}

def check_zip_backup(zip_path):
    """فحص النسخة الاحتياطية المضغوطة"""
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            db_files = [f for f in zip_ref.namelist() if f.endswith('.db')]
            
            if db_files:
                with tempfile.TemporaryDirectory() as temp_dir:
                    for db_file in db_files:
                        zip_ref.extract(db_file, temp_dir)
                        extracted_path = os.path.join(temp_dir, db_file)
                        result = check_db_file(extracted_path)
                        if result:
                            result['path'] = f"{zip_path} -> {db_file}"
                            return result
            return None
    except Exception as e:
        return {'path': zip_path, 'error': str(e)}

def main():
    print("🔍 فحص جميع النسخ الاحتياطية للعثور على البيانات المفقودة")
    print("=" * 70)
    
    # قائمة الملفات للفحص
    files_to_check = [
        'training_system.db',
        'training_system_backup_before_cleanup_20250531_000426.db',
        'instance/training_system.db'
    ]
    
    # فحص النسخ المضغوطة
    zip_files = [
        'backups/نسخة مع البيانات.zip',
        'backups/backup_20250529_032723.zip',
        'backups/backup_20250529_032736.zip',
        'backups/backup_20250529_033144.zip',
        'backups/smart_backup_startup_20250531_174916.zip'
    ]
    
    best_backup = None
    max_records = 0
    
    print("\n📁 فحص ملفات قواعد البيانات المباشرة:")
    for db_file in files_to_check:
        print(f"\n🔍 فحص: {db_file}")
        result = check_db_file(db_file)
        
        if result is None:
            print("   ❌ الملف غير موجود")
            continue
            
        if 'error' in result:
            print(f"   ❌ خطأ: {result['error']}")
            continue
            
        print(f"   📊 حجم الملف: {result['size']:,} بايت")
        
        total_records = 0
        for table, count in result['data'].items():
            if isinstance(count, int):
                total_records += count
                if count > 0:
                    print(f"   ✅ {table}: {count:,} سجل")
                else:
                    print(f"   ⚪ {table}: فارغ")
            else:
                print(f"   ❌ {table}: {count}")
        
        # عرض عينات البيانات
        if 'course_sample' in result:
            print("   📋 عينة من الدورات:")
            for course in result['course_sample']:
                print(f"      - {course[0]}: {course[1]}")
                
        if 'person_sample' in result:
            print("   👥 عينة من الأشخاص:")
            for person in result['person_sample']:
                print(f"      - {person[0]}")
        
        if total_records > max_records:
            max_records = total_records
            best_backup = result
    
    print("\n📦 فحص النسخ الاحتياطية المضغوطة:")
    for zip_file in zip_files:
        print(f"\n🔍 فحص: {zip_file}")
        result = check_zip_backup(zip_file)
        
        if result is None:
            print("   ❌ الملف غير موجود أو لا يحتوي على قواعد بيانات")
            continue
            
        if 'error' in result:
            print(f"   ❌ خطأ: {result['error']}")
            continue
            
        print(f"   📊 حجم الملف: {result['size']:,} بايت")
        
        total_records = 0
        for table, count in result['data'].items():
            if isinstance(count, int):
                total_records += count
                if count > 0:
                    print(f"   ✅ {table}: {count:,} سجل")
                else:
                    print(f"   ⚪ {table}: فارغ")
            else:
                print(f"   ❌ {table}: {count}")
        
        # عرض عينات البيانات
        if 'course_sample' in result:
            print("   📋 عينة من الدورات:")
            for course in result['course_sample']:
                print(f"      - {course[0]}: {course[1]}")
                
        if 'person_sample' in result:
            print("   👥 عينة من الأشخاص:")
            for person in result['person_sample']:
                print(f"      - {person[0]}")
        
        if total_records > max_records:
            max_records = total_records
            best_backup = result
    
    # النتائج النهائية
    print("\n" + "=" * 70)
    print("🎯 النتائج النهائية:")
    
    if best_backup and max_records > 0:
        print(f"\n🏆 أفضل نسخة احتياطية: {best_backup['path']}")
        print(f"📊 إجمالي السجلات: {max_records:,}")
        
        print("\n💡 لاستعادة البيانات:")
        if '->' in best_backup['path']:  # ملف مضغوط
            print("1. استخرج الملف المضغوط يدوياً")
            print("2. انسخ ملف قاعدة البيانات إلى training_system.db")
        else:
            print(f"copy \"{best_backup['path']}\" training_system.db")
            
        print("\n⚠️ تأكد من إيقاف الخادم قبل استعادة البيانات!")
        
    else:
        print("\n❌ لم يتم العثور على نسخة احتياطية تحتوي على بيانات")

if __name__ == '__main__':
    main()
