#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, CourseParticipant, Course, PersonData

def update_participants_count():
    with app.app_context():
        print("🔄 تحديث عدد المشاركين في جميع الدورات...")
        print("=" * 60)
        
        # جلب جميع الدورات
        courses = Course.query.all()
        
        for course in courses:
            # حساب العدد الفعلي للمشاركين
            actual_count = CourseParticipant.query.filter_by(course_id=course.id).count()
            
            # حساب عدد الخريجين
            graduates_count = CourseParticipant.query.filter_by(course_id=course.id, status='completed').count()
            
            # حساب عدد المنسحبين
            dropouts_count = CourseParticipant.query.filter_by(course_id=course.id, status='dropped').count()
            
            print(f"الدورة {course.course_number}: {course.title}")
            print(f"  - العدد المسجل: {course.total_participants}")
            print(f"  - العدد الفعلي: {actual_count}")
            print(f"  - الخريجين: {graduates_count}")
            print(f"  - المنسحبين: {dropouts_count}")
            
            # تحديث الأرقام
            course.total_participants = actual_count
            course.total_graduates = graduates_count
            course.total_dropouts = dropouts_count
            
            print(f"  ✅ تم التحديث")
            print("-" * 40)
        
        # حفظ التغييرات
        db.session.commit()
        print("✅ تم حفظ جميع التحديثات بنجاح!")
        
        # عرض تفاصيل الدورة رقم 1
        print("\n" + "=" * 60)
        print("تفاصيل الدورة رقم 1:")
        course1 = Course.query.get(1)
        if course1:
            participants = CourseParticipant.query.filter_by(course_id=1).all()
            print(f"اسم الدورة: {course1.title}")
            print(f"العدد المحدث: {course1.total_participants}")
            print(f"عدد المشاركين الفعلي: {len(participants)}")
            
            print("\nقائمة المشاركين:")
            for i, p in enumerate(participants, 1):
                name = p.personal_data.full_name if p.personal_data else f"ID: {p.personal_data_id}"
                print(f"  {i}. {name} ({p.status})")

if __name__ == '__main__':
    update_participants_count()
