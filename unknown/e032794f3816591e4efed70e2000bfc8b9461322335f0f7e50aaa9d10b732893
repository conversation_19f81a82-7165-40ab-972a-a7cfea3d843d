#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بيانات المراكز والجهات
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, TrainingCenter, Agency, Course

def test_centers_data():
    """اختبار بيانات المراكز والجهات"""
    
    with app.app_context():
        print("🔍 اختبار بيانات المراكز والجهات...")
        
        # إحصائيات عامة
        total_agencies = Agency.query.count()
        total_centers = TrainingCenter.query.count()
        total_courses = Course.query.count()
        
        print(f"📊 الإحصائيات العامة:")
        print(f"   - إجمالي الجهات: {total_agencies}")
        print(f"   - إجمالي المراكز: {total_centers}")
        print(f"   - إجمالي الدورات: {total_courses}")
        
        # عرض الجهات
        print(f"\n🏛️ الجهات:")
        for agency in Agency.query.all():
            centers_count = TrainingCenter.query.filter_by(agency_id=agency.id).count()
            courses_count = Course.query.filter_by(agency_id=agency.id).count()
            print(f"   - {agency.name} ({agency.code})")
            print(f"     المراكز: {centers_count}, الدورات: {courses_count}")
        
        # عرض المراكز
        print(f"\n🏢 المراكز:")
        for center in TrainingCenter.query.all():
            courses_count = Course.query.filter_by(center_id=center.id).count()
            agency_name = center.agency.name if center.agency else 'غير محدد'
            print(f"   - {center.name}")
            print(f"     الجهة: {agency_name}")
            print(f"     الدورات: {courses_count}")
        
        # توزيع الدورات حسب المراكز
        print(f"\n📚 توزيع الدورات حسب المراكز:")
        center_data = {}
        for course in Course.query.all():
            center_name = (course.center.name if course.center else None) or 'غير محدد'
            if center_name not in center_data:
                center_data[center_name] = 0
            center_data[center_name] += course.total_participants or 0
        
        for center, participants in center_data.items():
            print(f"   - {center}: {participants} مشارك")
        
        # توزيع المراكز حسب الجهات
        print(f"\n🏛️ توزيع المراكز حسب الجهات:")
        agency_center_data = {}
        for center in TrainingCenter.query.all():
            agency_name = center.agency.name if center.agency else 'غير محدد'
            if agency_name not in agency_center_data:
                agency_center_data[agency_name] = 0
            agency_center_data[agency_name] += 1
        
        for agency, centers_count in agency_center_data.items():
            print(f"   - {agency}: {centers_count} مركز")

if __name__ == "__main__":
    test_centers_data()
