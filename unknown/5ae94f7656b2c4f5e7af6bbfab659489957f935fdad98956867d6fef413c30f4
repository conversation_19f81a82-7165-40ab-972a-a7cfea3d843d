#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
محاكاة ما يحدث في صفحة إضافة المشارك
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, CourseParticipant, PersonData, CourseParticipantForm

def test_add_participant_form():
    """
    محاكاة ما يحدث في صفحة إضافة المشارك
    """
    with app.app_context():
        try:
            course_id = 1
            print(f"🎯 محاكاة صفحة إضافة المشارك للدورة رقم: {course_id}")
            print("=" * 70)

            # تطبيق نفس الكود الموجود في app.py (بدون النموذج)
            print("📋 تطبيق الكود من app.py:")
            print("   existing_participant_ids = [p.personal_data_id for p in CourseParticipant.query.filter_by(course_id=course_id).all()]")
            existing_participant_ids = [p.personal_data_id for p in CourseParticipant.query.filter_by(course_id=course_id).all()]
            print(f"   النتيجة: {existing_participant_ids}")

            print("\n   available_people = PersonData.query.filter(~PersonData.id.in_(existing_participant_ids)).all()")
            available_people = PersonData.query.filter(~PersonData.id.in_(existing_participant_ids)).all()
            print(f"   النتيجة: {len(available_people)} شخص متاح")

            print("\n   choices = [(0, 'اختر مشارك')] + [(p.id, p.full_name) for p in available_people]")
            choices = [(0, 'اختر مشارك')] + [(p.id, p.full_name) for p in available_people]
            print(f"   النتيجة: {len(choices)} خيار في القائمة")

            print("\n" + "=" * 70)

            # عرض النتائج التفصيلية
            print("📊 النتائج التفصيلية:")
            print(f"   - المشاركين المضافين مسبقاً: {len(existing_participant_ids)}")
            for i, pid in enumerate(existing_participant_ids, 1):
                person = PersonData.query.get(pid)
                name = person.full_name if person else "غير موجود"
                print(f"     {i}. ID: {pid} - {name}")

            print(f"\n   - الأشخاص المتاحين للإضافة: {len(available_people)}")
            print("     أول 5 أشخاص:")
            for i, person in enumerate(available_people[:5], 1):
                print(f"     {i}. ID: {person.id} - {person.full_name}")

            print(f"\n   - خيارات القائمة المنسدلة: {len(choices)}")
            print("     أول 5 خيارات:")
            for i, choice in enumerate(choices[:5], 1):
                print(f"     {i}. ({choice[0]}, '{choice[1]}')")

            print("\n" + "=" * 70)

            # اختبار إضافة مشارك جديد
            print("🧪 اختبار إضافة مشارك جديد:")
            if available_people:
                selected_person = available_people[0]
                print(f"   - اختيار الشخص: {selected_person.full_name} (ID: {selected_person.id})")

                # محاكاة إضافة المشارك
                print("   - إنشاء مشارك جديد...")
                new_participant = CourseParticipant(
                    course_id=course_id,
                    personal_data_id=selected_person.id,
                    status='active',
                    notes='مشارك تجريبي من الاختبار'
                )

                # لا نحفظ في قاعدة البيانات، فقط نختبر
                print(f"   - ✅ تم إنشاء المشارك بنجاح (بدون حفظ)")
                print(f"     - course_id: {new_participant.course_id}")
                print(f"     - personal_data_id: {new_participant.personal_data_id}")
                print(f"     - status: {new_participant.status}")

                # اختبار العلاقة
                try:
                    person_name = PersonData.query.get(new_participant.personal_data_id).full_name
                    print(f"     - اسم المشارك: {person_name}")
                    print("   - ✅ العلاقة تعمل بشكل صحيح")
                except Exception as e:
                    print(f"   - ❌ خطأ في العلاقة: {e}")
            else:
                print("   - ⚠️ لا يوجد أشخاص متاحين للإضافة")

            return True

        except Exception as e:
            print(f"❌ خطأ في محاكاة صفحة إضافة المشارك: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("🧪 محاكاة صفحة إضافة المشارك...")
    success = test_add_participant_form()
    if success:
        print("\n✅ تمت المحاكاة بنجاح!")
    else:
        print("\n❌ فشلت المحاكاة!")
