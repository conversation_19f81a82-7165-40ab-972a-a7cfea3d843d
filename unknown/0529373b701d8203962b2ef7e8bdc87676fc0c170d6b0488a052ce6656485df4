from app import app, db
from app import (
    Agency, Department, Governorate, Directorate, Village,
    CoursePath, CoursePathLevel, ForceClassification,
    MaritalStatus, BloodType, IssuingAuthority, QualificationType,
    Specialization, AssignmentType, MilitaryRank, InjuryType,
    InjuryCause, CourseType, TrainingCenterType, Location,
    TrainingCenter, ParticipantType, CardType, CourseLevel,
    CourseCategory
)

def seed_agencies():
    """إضافة بيانات تجريبية للجهات"""
    # حذف الجهات الموجودة
    Agency.query.delete()
    db.session.commit()

    agencies = [
        # الجهات الرئيسية
        Agency(name='وزارة الدفاع', code='MOD'),
        Agency(name='وزارة الداخلية', code='MOI'),
        Agency(name='وزارة التعليم', code='MOE'),
        Agency(name='وزارة الصحة', code='MOH'),
        Agency(name='وزارة المالية', code='MOF'),

        # سيتم تحديث الجهات الفرعية لاحقًا بعد إضافة الجهات الرئيسية
    ]

    db.session.add_all(agencies)
    db.session.commit()

    # إضافة الجهات الفرعية
    mod = Agency.query.filter_by(code='MOD').first()
    moi = Agency.query.filter_by(code='MOI').first()
    moe = Agency.query.filter_by(code='MOE').first()

    sub_agencies = [
        # وزارة الدفاع
        Agency(name='القوات البرية', code='MOD-LF', parent_id=mod.id),
        Agency(name='القوات الجوية', code='MOD-AF', parent_id=mod.id),
        Agency(name='القوات البحرية', code='MOD-NF', parent_id=mod.id),
        Agency(name='الدفاع الجوي', code='MOD-AD', parent_id=mod.id),

        # وزارة الداخلية
        Agency(name='الأمن العام', code='MOI-PS', parent_id=moi.id),
        Agency(name='الأمن المركزي', code='MOI-CS', parent_id=moi.id),
        Agency(name='الدفاع المدني', code='MOI-CD', parent_id=moi.id),

        # وزارة التعليم
        Agency(name='التعليم العام', code='MOE-GE', parent_id=moe.id),
        Agency(name='التعليم العالي', code='MOE-HE', parent_id=moe.id),
        Agency(name='التعليم الفني', code='MOE-TE', parent_id=moe.id),
    ]

    db.session.add_all(sub_agencies)
    db.session.commit()

    print("تم إضافة بيانات الجهات بنجاح")

def seed_departments():
    """إضافة بيانات تجريبية للأقسام"""
    # الحصول على الجهات
    mod = Agency.query.filter_by(code='MOD').first()
    moi = Agency.query.filter_by(code='MOI').first()
    moe = Agency.query.filter_by(code='MOE').first()

    # حذف الأقسام الموجودة
    Department.query.delete()
    db.session.commit()

    departments = [
        # أقسام وزارة الدفاع
        Department(name='قسم التدريب العسكري', code='MOD-TR', agency_id=mod.id),
        Department(name='قسم الإمداد والتموين', code='MOD-LO', agency_id=mod.id),
        Department(name='قسم الشؤون الإدارية', code='MOD-AD', agency_id=mod.id),
        Department(name='قسم الشؤون المالية', code='MOD-FI', agency_id=mod.id),

        # أقسام وزارة الداخلية
        Department(name='قسم التدريب الأمني', code='MOI-TR', agency_id=moi.id),
        Department(name='قسم المرور', code='MOI-TF', agency_id=moi.id),
        Department(name='قسم الجوازات', code='MOI-PA', agency_id=moi.id),
        Department(name='قسم الأحوال المدنية', code='MOI-CI', agency_id=moi.id),

        # أقسام وزارة التعليم
        Department(name='قسم المناهج', code='MOE-CU', agency_id=moe.id),
        Department(name='قسم التدريب التربوي', code='MOE-TR', agency_id=moe.id),
        Department(name='قسم الإشراف التربوي', code='MOE-SU', agency_id=moe.id),
    ]

    db.session.add_all(departments)
    db.session.commit()

    print("تم إضافة بيانات الأقسام بنجاح")

def seed_governorates():
    """إضافة بيانات تجريبية للمحافظات والمديريات والقرى"""
    # حذف البيانات الموجودة
    Village.query.delete()
    Directorate.query.delete()
    Governorate.query.delete()
    db.session.commit()

    # إضافة المحافظات
    governorates = [
        Governorate(name='صنعاء', code='SAN'),
        Governorate(name='عدن', code='ADN'),
        Governorate(name='تعز', code='TAZ'),
        Governorate(name='الحديدة', code='HOD'),
        Governorate(name='حضرموت', code='HAD'),
    ]

    db.session.add_all(governorates)
    db.session.commit()

    # إضافة المديريات
    sanaa = Governorate.query.filter_by(code='SAN').first()
    aden = Governorate.query.filter_by(code='ADN').first()
    taiz = Governorate.query.filter_by(code='TAZ').first()

    directorates = [
        # مديريات صنعاء
        Directorate(name='السبعين', code='SAN-01', governorate_id=sanaa.id),
        Directorate(name='معين', code='SAN-02', governorate_id=sanaa.id),
        Directorate(name='الصافية', code='SAN-03', governorate_id=sanaa.id),
        Directorate(name='شعوب', code='SAN-04', governorate_id=sanaa.id),

        # مديريات عدن
        Directorate(name='المنصورة', code='ADN-01', governorate_id=aden.id),
        Directorate(name='الشيخ عثمان', code='ADN-02', governorate_id=aden.id),
        Directorate(name='خور مكسر', code='ADN-03', governorate_id=aden.id),

        # مديريات تعز
        Directorate(name='المظفر', code='TAZ-01', governorate_id=taiz.id),
        Directorate(name='القاهرة', code='TAZ-02', governorate_id=taiz.id),
        Directorate(name='صالة', code='TAZ-03', governorate_id=taiz.id),
    ]

    db.session.add_all(directorates)
    db.session.commit()

    # إضافة القرى/الأحياء
    sabeen = Directorate.query.filter_by(code='SAN-01').first()
    maeen = Directorate.query.filter_by(code='SAN-02').first()
    mansoora = Directorate.query.filter_by(code='ADN-01').first()

    villages = [
        # أحياء السبعين
        Village(name='حي الجامعة', code='SAN-01-01', directorate_id=sabeen.id),
        Village(name='حي الزراعة', code='SAN-01-02', directorate_id=sabeen.id),
        Village(name='حي السنينة', code='SAN-01-03', directorate_id=sabeen.id),

        # أحياء معين
        Village(name='حي الدائري', code='SAN-02-01', directorate_id=maeen.id),
        Village(name='حي هائل', code='SAN-02-02', directorate_id=maeen.id),

        # أحياء المنصورة
        Village(name='حي عبد العزيز', code='ADN-01-01', directorate_id=mansoora.id),
        Village(name='حي الشهيد', code='ADN-01-02', directorate_id=mansoora.id),
    ]

    db.session.add_all(villages)
    db.session.commit()

    print("تم إضافة بيانات المحافظات والمديريات والقرى بنجاح")

def seed_course_paths():
    """إضافة بيانات تجريبية لمسارات الدورات ومستوياتها"""
    # حذف البيانات الموجودة
    CoursePathLevel.query.delete()
    CoursePath.query.delete()
    db.session.commit()

    # إضافة مسارات الدورات
    paths = [
        CoursePath(name='تطوير البرمجيات', description='مسار متكامل لتطوير البرمجيات', code='SD'),
        CoursePath(name='إدارة المشاريع', description='مسار متكامل لإدارة المشاريع', code='PM'),
        CoursePath(name='الأمن السيبراني', description='مسار متكامل للأمن السيبراني', code='CS'),
        CoursePath(name='الذكاء الاصطناعي', description='مسار متكامل للذكاء الاصطناعي', code='AI'),
    ]

    db.session.add_all(paths)
    db.session.commit()

    # إضافة مستويات المسارات
    sd_path = CoursePath.query.filter_by(code='SD').first()
    pm_path = CoursePath.query.filter_by(code='PM').first()
    cs_path = CoursePath.query.filter_by(code='CS').first()
    ai_path = CoursePath.query.filter_by(code='AI').first()

    levels = [
        # مستويات مسار تطوير البرمجيات
        CoursePathLevel(name='أساسيات البرمجة', description='تعلم أساسيات البرمجة', code='SD-01', order=1, path_id=sd_path.id),
        CoursePathLevel(name='تطوير الواجهات', description='تعلم تطوير واجهات المستخدم', code='SD-02', order=2, path_id=sd_path.id),
        CoursePathLevel(name='تطوير الخلفيات', description='تعلم تطوير خلفيات التطبيقات', code='SD-03', order=3, path_id=sd_path.id),
        CoursePathLevel(name='تطوير التطبيقات المتكاملة', description='تعلم تطوير التطبيقات المتكاملة', code='SD-04', order=4, path_id=sd_path.id),

        # مستويات مسار إدارة المشاريع
        CoursePathLevel(name='أساسيات إدارة المشاريع', description='تعلم أساسيات إدارة المشاريع', code='PM-01', order=1, path_id=pm_path.id),
        CoursePathLevel(name='إدارة الموارد', description='تعلم إدارة موارد المشاريع', code='PM-02', order=2, path_id=pm_path.id),
        CoursePathLevel(name='إدارة المخاطر', description='تعلم إدارة مخاطر المشاريع', code='PM-03', order=3, path_id=pm_path.id),

        # مستويات مسار الأمن السيبراني
        CoursePathLevel(name='أساسيات الأمن السيبراني', description='تعلم أساسيات الأمن السيبراني', code='CS-01', order=1, path_id=cs_path.id),
        CoursePathLevel(name='اختبار الاختراق', description='تعلم اختبار الاختراق', code='CS-02', order=2, path_id=cs_path.id),
        CoursePathLevel(name='تحليل البرمجيات الخبيثة', description='تعلم تحليل البرمجيات الخبيثة', code='CS-03', order=3, path_id=cs_path.id),

        # مستويات مسار الذكاء الاصطناعي
        CoursePathLevel(name='أساسيات الذكاء الاصطناعي', description='تعلم أساسيات الذكاء الاصطناعي', code='AI-01', order=1, path_id=ai_path.id),
        CoursePathLevel(name='تعلم الآلة', description='تعلم أساسيات تعلم الآلة', code='AI-02', order=2, path_id=ai_path.id),
        CoursePathLevel(name='التعلم العميق', description='تعلم أساسيات التعلم العميق', code='AI-03', order=3, path_id=ai_path.id),
    ]

    db.session.add_all(levels)
    db.session.commit()

    print("تم إضافة بيانات مسارات الدورات ومستوياتها بنجاح")

def seed_reference_tables():
    """إضافة بيانات تجريبية للجداول الترميزية الأخرى"""
    # حذف البيانات الموجودة
    CardType.query.delete()
    ParticipantType.query.delete()
    TrainingCenter.query.delete()
    Location.query.delete()
    TrainingCenterType.query.delete()
    CourseType.query.delete()
    InjuryCause.query.delete()
    InjuryType.query.delete()
    MilitaryRank.query.delete()
    AssignmentType.query.delete()
    Specialization.query.delete()
    QualificationType.query.delete()
    IssuingAuthority.query.delete()
    BloodType.query.delete()
    MaritalStatus.query.delete()
    ForceClassification.query.delete()
    CourseLevel.query.delete()
    CourseCategory.query.delete()
    db.session.commit()

    # تصنيفات القوة
    force_classifications = [
        ForceClassification(name='قوات برية', description='القوات البرية'),
        ForceClassification(name='قوات جوية', description='القوات الجوية'),
        ForceClassification(name='قوات بحرية', description='القوات البحرية'),
        ForceClassification(name='دفاع جوي', description='قوات الدفاع الجوي'),
    ]
    db.session.add_all(force_classifications)

    # الحالة الاجتماعية
    marital_statuses = [
        MaritalStatus(name='أعزب'),
        MaritalStatus(name='متزوج'),
        MaritalStatus(name='مطلق'),
        MaritalStatus(name='أرمل'),
    ]
    db.session.add_all(marital_statuses)

    # فصائل الدم
    blood_types = [
        BloodType(name='A+'),
        BloodType(name='A-'),
        BloodType(name='B+'),
        BloodType(name='B-'),
        BloodType(name='AB+'),
        BloodType(name='AB-'),
        BloodType(name='O+'),
        BloodType(name='O-'),
    ]
    db.session.add_all(blood_types)

    # جهات الإصدار
    issuing_authorities = [
        IssuingAuthority(name='الأحوال المدنية'),
        IssuingAuthority(name='وزارة الدفاع'),
        IssuingAuthority(name='وزارة الداخلية'),
        IssuingAuthority(name='وزارة الخارجية'),
    ]
    db.session.add_all(issuing_authorities)

    # المؤهلات العلمية
    qualification_types = [
        QualificationType(name='ابتدائي', level='primary', code='PRI'),
        QualificationType(name='إعدادي', level='middle', code='MID'),
        QualificationType(name='ثانوي', level='secondary', code='SEC'),
        QualificationType(name='دبلوم', level='diploma', code='DIP'),
        QualificationType(name='بكالوريوس', level='bachelor', code='BAC'),
        QualificationType(name='ماجستير', level='master', code='MAS'),
        QualificationType(name='دكتوراه', level='phd', code='PHD'),
    ]
    db.session.add_all(qualification_types)

    # التخصصات
    specializations = [
        Specialization(name='هندسة برمجيات', field='engineering', code='SE'),
        Specialization(name='طب عام', field='medical', code='GM'),
        Specialization(name='إدارة أعمال', field='administrative', code='BA'),
        Specialization(name='علوم حاسوب', field='technical', code='CS'),
        Specialization(name='هندسة كهربائية', field='engineering', code='EE'),
        Specialization(name='هندسة ميكانيكية', field='engineering', code='ME'),
    ]
    db.session.add_all(specializations)

    # أنواع التكليف
    assignment_types = [
        AssignmentType(name='تكليف دائم'),
        AssignmentType(name='تكليف مؤقت'),
        AssignmentType(name='انتداب'),
        AssignmentType(name='إعارة'),
    ]
    db.session.add_all(assignment_types)

    # الرتب العسكرية
    military_ranks = [
        MilitaryRank(name='ملازم', code='LT'),
        MilitaryRank(name='ملازم أول', code='1LT'),
        MilitaryRank(name='نقيب', code='CPT'),
        MilitaryRank(name='رائد', code='MAJ'),
        MilitaryRank(name='مقدم', code='LTC'),
        MilitaryRank(name='عقيد', code='COL'),
        MilitaryRank(name='عميد', code='BG'),
        MilitaryRank(name='لواء', code='MG'),
        MilitaryRank(name='فريق', code='LG'),
        MilitaryRank(name='فريق أول', code='GEN'),
    ]
    db.session.add_all(military_ranks)

    # أنواع الإصابات
    injury_types = [
        InjuryType(name='كسر'),
        InjuryType(name='جرح'),
        InjuryType(name='حرق'),
        InjuryType(name='إصابة داخلية'),
    ]
    db.session.add_all(injury_types)

    # أسباب الإصابات
    injury_causes = [
        InjuryCause(name='حادث مروري'),
        InjuryCause(name='حادث عمل'),
        InjuryCause(name='عملية عسكرية'),
        InjuryCause(name='كارثة طبيعية'),
    ]
    db.session.add_all(injury_causes)

    # أنواع الدورات
    course_types = [
        CourseType(name='دورة فنية', category='فنية'),
        CourseType(name='دورة قيادية', category='قيادية'),
        CourseType(name='دورة إدارية', category='إدارية'),
        CourseType(name='دورة أمنية', category='أمنية'),
        CourseType(name='دورة عسكرية', category='عسكرية'),
    ]
    db.session.add_all(course_types)

    # أنواع المراكز التدريبية
    center_types = [
        TrainingCenterType(name='مركز تدريب عسكري', description='مركز مخصص للتدريب العسكري'),
        TrainingCenterType(name='مركز تدريب فني', description='مركز مخصص للتدريب الفني'),
        TrainingCenterType(name='مركز تدريب إداري', description='مركز مخصص للتدريب الإداري'),
        TrainingCenterType(name='مركز تدريب أمني', description='مركز مخصص للتدريب الأمني'),
    ]
    db.session.add_all(center_types)

    # المواقع
    locations = [
        Location(name='مجمع التدريب المركزي', address='صنعاء - شارع الستين'),
        Location(name='مركز التدريب الفني', address='صنعاء - شارع تعز'),
        Location(name='مركز التدريب الإداري', address='عدن - المنصورة'),
    ]
    db.session.add_all(locations)

    # أنواع المشاركين
    participant_types = [
        ParticipantType(name='ضباط'),
        ParticipantType(name='صف ضباط'),
        ParticipantType(name='جنود'),
        ParticipantType(name='مدنيين'),
        ParticipantType(name='مختلط'),
    ]
    db.session.add_all(participant_types)

    # أنواع البطاقات
    card_types = [
        CardType(name='بطاقة شخصية'),
        CardType(name='بطاقة عسكرية'),
        CardType(name='جواز سفر'),
        CardType(name='رخصة قيادة'),
    ]
    db.session.add_all(card_types)

    # مستويات الدورات
    course_levels = [
        CourseLevel(name='مبتدئ'),
        CourseLevel(name='متوسط'),
        CourseLevel(name='متقدم'),
        CourseLevel(name='خبير'),
    ]
    db.session.add_all(course_levels)

    # تصنيفات الدورات
    course_categories = [
        CourseCategory(name='دورات فنية', code='TC'),
        CourseCategory(name='دورات إدارية', code='AC'),
        CourseCategory(name='دورات قيادية', code='LC'),
        CourseCategory(name='دورات أمنية', code='SC'),
        CourseCategory(name='دورات عسكرية', code='MC'),
    ]
    db.session.add_all(course_categories)

    db.session.commit()
    print("تم إضافة بيانات الجداول الترميزية الأخرى بنجاح")

def seed_training_centers():
    """إضافة بيانات تجريبية لمراكز التدريب"""
    # حذف البيانات الموجودة
    TrainingCenter.query.delete()
    db.session.commit()

    # الحصول على البيانات المرجعية
    military_type = TrainingCenterType.query.filter_by(name='مركز تدريب عسكري').first()
    technical_type = TrainingCenterType.query.filter_by(name='مركز تدريب فني').first()
    admin_type = TrainingCenterType.query.filter_by(name='مركز تدريب إداري').first()

    central_location = Location.query.filter_by(name='مجمع التدريب المركزي').first()
    technical_location = Location.query.filter_by(name='مركز التدريب الفني').first()
    admin_location = Location.query.filter_by(name='مركز التدريب الإداري').first()

    sanaa = Governorate.query.filter_by(code='SAN').first()
    aden = Governorate.query.filter_by(code='ADN').first()

    sabeen = Directorate.query.filter_by(code='SAN-01').first()
    mansoora = Directorate.query.filter_by(code='ADN-01').first()

    mod = Agency.query.filter_by(code='MOD').first()
    moi = Agency.query.filter_by(code='MOI').first()

    # إضافة مراكز التدريب
    centers = [
        TrainingCenter(
            name='مركز التدريب العسكري المركزي',
            center_type_id=military_type.id,
            location_id=central_location.id,
            governorate_id=sanaa.id,
            directorate_id=sabeen.id,
            agency_id=mod.id,
            capacity=500,
            is_ready=True
        ),
        TrainingCenter(
            name='مركز التدريب الفني',
            center_type_id=technical_type.id,
            location_id=technical_location.id,
            governorate_id=sanaa.id,
            directorate_id=sabeen.id,
            agency_id=mod.id,
            capacity=300,
            is_ready=True
        ),
        TrainingCenter(
            name='مركز التدريب الإداري',
            center_type_id=admin_type.id,
            location_id=admin_location.id,
            governorate_id=aden.id,
            directorate_id=mansoora.id,
            agency_id=moi.id,
            capacity=200,
            is_ready=True
        ),
        TrainingCenter(
            name='مركز التدريب الأمني',
            center_type_id=admin_type.id,
            governorate_id=aden.id,
            directorate_id=mansoora.id,
            agency_id=moi.id,
            capacity=150,
            is_ready=False,
            not_ready_reason='قيد الإنشاء'
        ),
    ]

    db.session.add_all(centers)
    db.session.commit()

    print("تم إضافة بيانات مراكز التدريب بنجاح")

def seed_all(force=True):
    """إضافة جميع البيانات التجريبية"""
    with app.app_context():
        # التحقق من وجود بيانات
        if Agency.query.count() > 0 and not force:
            print("يبدو أن البيانات موجودة بالفعل. هل تريد إعادة إدخال البيانات؟ (نعم/لا)")
            response = input().strip().lower()
            if response != 'نعم' and response != 'yes':
                print("تم إلغاء إدخال البيانات")
                return

        # إضافة البيانات
        seed_agencies()
        seed_departments()
        seed_governorates()
        seed_course_paths()
        seed_reference_tables()
        seed_training_centers()

        print("تم إدخال جميع البيانات التجريبية بنجاح!")

if __name__ == "__main__":
    with app.app_context():
        # حذف جميع البيانات الموجودة
        TrainingCenter.query.delete()
        CardType.query.delete()
        ParticipantType.query.delete()
        Location.query.delete()
        TrainingCenterType.query.delete()
        CourseType.query.delete()
        InjuryCause.query.delete()
        InjuryType.query.delete()
        MilitaryRank.query.delete()
        AssignmentType.query.delete()
        Specialization.query.delete()
        QualificationType.query.delete()
        IssuingAuthority.query.delete()
        BloodType.query.delete()
        MaritalStatus.query.delete()
        ForceClassification.query.delete()
        CourseLevel.query.delete()
        CourseCategory.query.delete()
        CoursePathLevel.query.delete()
        CoursePath.query.delete()
        Village.query.delete()
        Directorate.query.delete()
        Governorate.query.delete()
        Department.query.delete()
        Agency.query.delete()
        db.session.commit()

        # إضافة البيانات
        seed_agencies()
        seed_departments()
        seed_governorates()
        seed_course_paths()
        seed_reference_tables()
        seed_training_centers()

        print("تم إدخال جميع البيانات التجريبية بنجاح!")
