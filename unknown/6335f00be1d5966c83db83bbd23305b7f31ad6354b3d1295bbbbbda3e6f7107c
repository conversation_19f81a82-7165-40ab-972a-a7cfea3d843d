import pandas as pd

# إنشاء بيانات نموذجية للاختبار
data = {
    'name': [
        'علي أحمد محمد',
        'سارة محمد علي', 
        'خالد سعد أحمد',
        'نور فاطمة حسن',
        'محمد عبدالله حسن'  # هذا موجود بالفعل
    ],
    'national_id': [
        '9876543210',
        '9876543211', 
        '9876543212',
        '9876543213',
        '1234567892'  # هذا موجود بالفعل
    ],
    'military_number': [
        'M101',
        'M102',
        'M103', 
        'M104',
        'M003'  # هذا موجود بالفعل
    ]
}

# إنشاء DataFrame
df = pd.DataFrame(data)

# حفظ الملف
try:
    df.to_excel('participants_sample.xlsx', index=False)
    print("تم إنشاء ملف participants_sample.xlsx بنجاح!")
    print("الملف يحتوي على:")
    print("- 3 أشخاص جدد")
    print("- 1 شخص موجود بالفعل (للاختبار)")
    print("- أعمدة: name, national_id, military_number")
except Exception as e:
    print(f"خطأ في إنشاء الملف: {e}")
