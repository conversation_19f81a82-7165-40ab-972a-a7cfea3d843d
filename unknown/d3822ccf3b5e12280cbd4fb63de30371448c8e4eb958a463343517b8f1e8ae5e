#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار إصلاح مشكلة csrf_token
"""

import requests
import time

def test_person_data_table():
    """اختبار صفحة person_data_table"""
    print("🔍 اختبار صفحة person_data_table...")
    
    try:
        response = requests.get("http://localhost:5001/person_data_table", timeout=10)
        
        print(f"📡 رمز الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ الصفحة تعمل بنجاح!")
            
            # فحص محتوى الصفحة
            content = response.text
            
            if 'csrf_token' in content:
                print("✅ csrf_token موجود في الصفحة")
            else:
                print("⚠️ csrf_token غير موجود في الصفحة")
            
            if 'jinja2.exceptions.UndefinedError' in content:
                print("❌ لا يزال هناك خطأ Jinja2!")
            else:
                print("✅ لا توجد أخطاء Jinja2")
            
            if 'بيانات الأشخاص' in content:
                print("✅ محتوى الصفحة يظهر بشكل صحيح")
            else:
                print("⚠️ محتوى الصفحة قد لا يظهر بشكل صحيح")
                
            return True
            
        elif response.status_code == 302:
            print("🔄 تم إعادة التوجيه (ربما لصفحة تسجيل الدخول)")
            print(f"📍 الموقع الجديد: {response.headers.get('Location', 'غير محدد')}")
            return False
            
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            print(f"📄 محتوى الاستجابة: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الطلب: {e}")
        return False

def test_other_routes():
    """اختبار routes أخرى"""
    print("\n🔍 اختبار routes أخرى...")
    
    routes_to_test = [
        ("الصفحة الرئيسية", "http://localhost:5001/"),
        ("إدارة المشاركين", "http://localhost:5001/manage_participants/1/"),
        ("البحث (API)", "http://localhost:5001/course/1/search_people?q=علي"),
        ("اختبار route", "http://localhost:5001/test_search_route")
    ]
    
    for name, url in routes_to_test:
        try:
            print(f"\n📋 اختبار {name}:")
            response = requests.get(url, timeout=5)
            print(f"   📡 رمز الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ {name} يعمل")
                
                # فحص خاص للبحث API
                if 'search_people' in url:
                    try:
                        data = response.json()
                        if isinstance(data, list):
                            print(f"   📊 نتائج البحث: {len(data)} شخص")
                        else:
                            print(f"   📄 استجابة: {type(data)}")
                    except:
                        print(f"   📄 استجابة نصية: {response.text[:100]}...")
                        
            elif response.status_code == 302:
                print(f"   🔄 {name} يعيد التوجيه")
            else:
                print(f"   ❌ {name} خطأ {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ خطأ في {name}: {e}")
        
        time.sleep(0.5)

def test_login_required():
    """اختبار إذا كانت المشكلة في تسجيل الدخول"""
    print("\n🔐 اختبار تسجيل الدخول...")
    
    try:
        # محاولة الوصول لصفحة تتطلب تسجيل دخول
        response = requests.get("http://localhost:5001/person_data_table", timeout=10, allow_redirects=False)
        
        if response.status_code == 302:
            location = response.headers.get('Location', '')
            if 'login' in location.lower():
                print("🔐 الصفحة تتطلب تسجيل دخول")
                print("💡 يجب تسجيل الدخول أولاً لاختبار الصفحة")
                return False
            else:
                print(f"🔄 إعادة توجيه إلى: {location}")
                return False
        else:
            print("✅ لا تتطلب تسجيل دخول أو تم تسجيل الدخول مسبقاً")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار إصلاح مشكلة csrf_token")
    print("=" * 50)
    
    # 1. اختبار تسجيل الدخول
    login_ok = test_login_required()
    
    # 2. اختبار صفحة person_data_table
    table_ok = test_person_data_table()
    
    # 3. اختبار routes أخرى
    test_other_routes()
    
    # الخلاصة
    print("\n" + "=" * 50)
    print("📊 خلاصة الاختبار:")
    
    if not login_ok:
        print("🔐 يجب تسجيل الدخول أولاً:")
        print("   1. افتح http://localhost:5001/")
        print("   2. سجل الدخول بـ admin/admin")
        print("   3. ثم اختبر http://localhost:5001/person_data_table")
    elif table_ok:
        print("✅ تم إصلاح مشكلة csrf_token بنجاح!")
        print("✅ صفحة person_data_table تعمل بشكل صحيح")
    else:
        print("❌ لا تزال هناك مشكلة في الصفحة")
    
    print("\n🌐 للاختبار المباشر:")
    print("   http://localhost:5001/person_data_table")

if __name__ == "__main__":
    main()
