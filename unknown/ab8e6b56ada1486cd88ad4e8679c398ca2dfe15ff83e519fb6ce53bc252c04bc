#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار القالب مباشرة
"""

import sqlite3
from datetime import datetime

def test_template_direct():
    """
    اختبار القالب مباشرة مع البيانات
    """
    
    # جلب البيانات
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT nc.id, nc.wrong_name, nc.correct_name, nc.correction_type,
               nc.created_at, nc.usage_count, nc.is_active, nc.created_by
        FROM name_correction nc
        ORDER BY nc.created_at DESC
    """)
    
    rows = cursor.fetchall()
    
    # تحويل البيانات
    corrections = []
    for row in rows:
        corrections.append({
            'id': row[0],
            'wrong_name': row[1],
            'correct_name': row[2],
            'correction_type': row[3],
            'created_at': row[4],
            'usage_count': row[5] or 0,
            'is_active': bool(row[6]),
            'creator': {'username': 'admin'}
        })
    
    conn.close()
    
    # إنشاء HTML مطابق للقالب
    html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار التصحيحات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="alert alert-info">
            <strong>عدد التصحيحات:</strong> {len(corrections)}
        </div>
        
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-table"></i> قائمة التصحيحات المخصصة
                </h6>
            </div>
            <div class="card-body">
    """
    
    if corrections:
        html += """
                <div class="table-responsive">
                    <table class="table table-bordered" id="correctionsTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>الاسم الخطأ</th>
                                <th>الاسم الصحيح</th>
                                <th>نوع التصحيح</th>
                                <th>الحالة</th>
                                <th>عدد الاستخدامات</th>
                                <th>تاريخ الإضافة</th>
                                <th>أضافه</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
        """
        
        for correction in corrections:
            # تحديد نوع التصحيح
            type_badge = ""
            if correction['correction_type'] == 'hamza':
                type_badge = '<span class="badge badge-primary"><i class="fas fa-font"></i> تصحيح الهمزات</span>'
            elif correction['correction_type'] == 'alif_maqsura':
                type_badge = '<span class="badge badge-info"><i class="fas fa-italic"></i> الألف المقصورة</span>'
            elif correction['correction_type'] == 'compound_names':
                type_badge = '<span class="badge badge-warning"><i class="fas fa-link"></i> أسماء مركبة</span>'
            elif correction['correction_type'] == 'symbols':
                type_badge = '<span class="badge badge-dark"><i class="fas fa-eraser"></i> إزالة رموز</span>'
            else:
                type_badge = '<span class="badge badge-light text-dark"><i class="fas fa-question-circle"></i> أخرى</span>'
            
            # تحديد الحالة
            status_badge = ""
            if correction['is_active']:
                status_badge = '<span class="badge badge-success"><i class="fas fa-check"></i> نشط</span>'
            else:
                status_badge = '<span class="badge badge-warning"><i class="fas fa-pause"></i> معطل</span>'
            
            # تحديد الاستخدام
            usage_text = "لم يُستخدم بعد" if correction['usage_count'] == 0 else "مرة"
            
            html += f"""
                            <tr>
                                <td>
                                    <span class="badge badge-danger" style="font-size: 14px; padding: 8px 12px;">
                                        <i class="fas fa-times-circle"></i> {correction['wrong_name']}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-success" style="font-size: 14px; padding: 8px 12px;">
                                        <i class="fas fa-check-circle"></i> {correction['correct_name']}
                                    </span>
                                </td>
                                <td>{type_badge}</td>
                                <td>{status_badge}</td>
                                <td class="text-center">
                                    <span class="badge badge-info" style="font-size: 14px; padding: 8px 12px;">
                                        <i class="fas fa-chart-line"></i> {correction['usage_count']}
                                    </span>
                                    <br><small class="text-muted">{usage_text}</small>
                                </td>
                                <td class="text-center">
                                    <strong>{str(correction['created_at'])[:10]}</strong><br>
                                    <small class="text-muted">{str(correction['created_at'])[11:16]}</small>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-secondary">
                                        <i class="fas fa-user"></i> {correction['creator']['username']}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <button class="btn btn-sm btn-warning" style="margin: 2px;">
                                        <i class="fas fa-pause"></i> إيقاف
                                    </button>
                                    <button class="btn btn-sm btn-danger" style="margin: 2px;">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </td>
                            </tr>
            """
        
        html += """
                        </tbody>
                    </table>
                </div>
        """
    else:
        html += """
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-500">لا توجد تصحيحات مخصصة</h5>
                    <p class="text-gray-400">ابدأ بإضافة تصحيحات جديدة لتحسين دقة النظام</p>
                </div>
        """
    
    html += """
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
    
    <script>
    $(document).ready(function() {
        $('#correctionsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
            },
            "order": [[ 5, "desc" ]],
            "pageLength": 25,
            "responsive": true
        });
    });
    </script>
</body>
</html>
    """
    
    # حفظ الملف
    with open('test_corrections_full.html', 'w', encoding='utf-8') as f:
        f.write(html)
    
    print(f"✅ تم إنشاء ملف test_corrections_full.html")
    print(f"📊 يحتوي على {len(corrections)} تصحيح")
    print("🌐 افتح الملف في المتصفح لرؤية النتيجة")

if __name__ == "__main__":
    test_template_direct()
