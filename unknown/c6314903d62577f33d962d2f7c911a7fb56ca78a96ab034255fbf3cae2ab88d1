#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة بيانات تجريبية للمراكز والجهات
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, TrainingCenter, Agency, Course
import random

def add_centers_and_agencies():
    """إضافة مراكز وجهات تجريبية"""
    
    with app.app_context():
        print("🏢 إضافة مراكز وجهات تجريبية...")
        
        # التحقق من الجهات الموجودة
        existing_agencies = Agency.query.all()
        print(f"📊 الجهات الموجودة: {len(existing_agencies)}")
        for agency in existing_agencies[:5]:
            print(f"   - {agency.name}")
        
        # التحقق من المراكز الموجودة
        existing_centers = TrainingCenter.query.all()
        print(f"🏢 المراكز الموجودة: {len(existing_centers)}")
        for center in existing_centers:
            print(f"   - {center.name} (الجهة: {center.agency_id})")
        
        # إضافة جهات جديدة إذا لم تكن موجودة
        new_agencies = [
            {'name': 'وزارة الدفاع الوطني', 'code': 'MOD'},
            {'name': 'وزارة الداخلية والأمن', 'code': 'MOI'},
            {'name': 'وزارة التربية والتعليم', 'code': 'MOE'},
            {'name': 'وزارة الصحة العامة', 'code': 'MOH'},
            {'name': 'وزارة المالية والاقتصاد', 'code': 'MOF'},
            {'name': 'وزارة العدل', 'code': 'MOJ'},
            {'name': 'وزارة النقل والمواصلات', 'code': 'MOT'},
            {'name': 'وزارة الاتصالات وتقنية المعلومات', 'code': 'MCIT'},
            {'name': 'وزارة الطاقة والمعادن', 'code': 'MEM'},
            {'name': 'وزارة الزراعة والري', 'code': 'MAI'}
        ]
        
        agencies_created = 0
        for agency_data in new_agencies:
            existing = Agency.query.filter_by(name=agency_data['name']).first()
            if not existing:
                agency = Agency(
                    name=agency_data['name'],
                    code=agency_data['code'],
                    description=f"وصف {agency_data['name']}"
                )
                db.session.add(agency)
                agencies_created += 1
                print(f"   ✅ تم إنشاء جهة: {agency_data['name']}")
        
        db.session.commit()
        print(f"🎉 تم إنشاء {agencies_created} جهة جديدة")
        
        # الحصول على جميع الجهات
        all_agencies = Agency.query.all()
        
        # إضافة مراكز تدريبية جديدة
        new_centers = [
            {
                'name': 'مركز التدريب العسكري المتقدم',
                'code': 'AMTC',
                'location': 'صنعاء',
                'agency_name': 'وزارة الدفاع الوطني'
            },
            {
                'name': 'مركز التدريب الأمني المركزي',
                'code': 'CSTC',
                'location': 'عدن',
                'agency_name': 'وزارة الداخلية والأمن'
            },
            {
                'name': 'مركز تدريب المعلمين',
                'code': 'TTC',
                'location': 'تعز',
                'agency_name': 'وزارة التربية والتعليم'
            },
            {
                'name': 'مركز التدريب الطبي المتخصص',
                'code': 'SMTC',
                'location': 'الحديدة',
                'agency_name': 'وزارة الصحة العامة'
            },
            {
                'name': 'مركز التدريب المالي والمحاسبي',
                'code': 'FATC',
                'location': 'صنعاء',
                'agency_name': 'وزارة المالية والاقتصاد'
            },
            {
                'name': 'مركز التدريب القضائي',
                'code': 'JTC',
                'location': 'صنعاء',
                'agency_name': 'وزارة العدل'
            },
            {
                'name': 'مركز تدريب النقل والمواصلات',
                'code': 'TTC2',
                'location': 'عدن',
                'agency_name': 'وزارة النقل والمواصلات'
            },
            {
                'name': 'مركز التدريب التقني والمعلوماتي',
                'code': 'ITTC',
                'location': 'صنعاء',
                'agency_name': 'وزارة الاتصالات وتقنية المعلومات'
            },
            {
                'name': 'مركز تدريب الطاقة والمعادن',
                'code': 'EMTC',
                'location': 'مأرب',
                'agency_name': 'وزارة الطاقة والمعادن'
            },
            {
                'name': 'مركز التدريب الزراعي',
                'code': 'ATC',
                'location': 'إب',
                'agency_name': 'وزارة الزراعة والري'
            }
        ]
        
        centers_created = 0
        for center_data in new_centers:
            existing = TrainingCenter.query.filter_by(name=center_data['name']).first()
            if not existing:
                # البحث عن الجهة
                agency = Agency.query.filter_by(name=center_data['agency_name']).first()
                if agency:
                    center = TrainingCenter(
                        name=center_data['name'],
                        code=center_data['code'],
                        location=center_data['location'],
                        agency_id=agency.id,
                        description=f"مركز تدريبي متخصص تابع لـ {agency.name}"
                    )
                    db.session.add(center)
                    centers_created += 1
                    print(f"   ✅ تم إنشاء مركز: {center_data['name']} (تابع لـ {agency.name})")
                else:
                    print(f"   ❌ لم يتم العثور على الجهة: {center_data['agency_name']}")
        
        db.session.commit()
        print(f"🎉 تم إنشاء {centers_created} مركز جديد")
        
        # تحديث ربط الدورات بالمراكز والجهات الجديدة
        print("\n🔄 تحديث ربط الدورات...")
        
        all_centers = TrainingCenter.query.all()
        all_courses = Course.query.all()
        
        updated_courses = 0
        for course in all_courses:
            # اختيار مركز عشوائي
            center = random.choice(all_centers)
            
            # تحديث الدورة
            course.center_id = center.id
            course.agency_id = center.agency_id
            
            updated_courses += 1
            print(f"   ✅ تم ربط دورة '{course.title}' بمركز '{center.name}'")
        
        db.session.commit()
        print(f"🎉 تم تحديث {updated_courses} دورة")
        
        # طباعة إحصائيات نهائية
        print(f"\n📊 الإحصائيات النهائية:")
        
        total_agencies = Agency.query.count()
        total_centers = TrainingCenter.query.count()
        total_courses = Course.query.count()
        
        print(f"   - إجمالي الجهات: {total_agencies}")
        print(f"   - إجمالي المراكز: {total_centers}")
        print(f"   - إجمالي الدورات: {total_courses}")
        
        # توزيع المراكز حسب الجهات
        print(f"\n🏢 توزيع المراكز حسب الجهات:")
        for agency in Agency.query.all():
            centers_count = TrainingCenter.query.filter_by(agency_id=agency.id).count()
            if centers_count > 0:
                print(f"   - {agency.name}: {centers_count} مركز")
        
        # توزيع الدورات حسب المراكز
        print(f"\n📚 توزيع الدورات حسب المراكز:")
        for center in TrainingCenter.query.all():
            courses_count = Course.query.filter_by(center_id=center.id).count()
            if courses_count > 0:
                print(f"   - {center.name}: {courses_count} دورة")

if __name__ == "__main__":
    add_centers_and_agencies()
