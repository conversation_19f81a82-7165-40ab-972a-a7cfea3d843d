#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار رابط إدارة البيانات بالإكسل
"""

import requests

def test_excel_link():
    """اختبار الرابط"""
    try:
        # اختبار الرابط القديم
        print("🔗 اختبار الرابط القديم: /personal_data/excel")
        response = requests.get('http://127.0.0.1:5000/personal_data/excel', allow_redirects=False)
        print(f"   الحالة: {response.status_code}")
        if response.status_code == 302:
            print(f"   إعادة توجيه إلى: {response.headers.get('Location', 'غير محدد')}")
        
        # اختبار الرابط الجديد
        print("\n🔗 اختبار الرابط الجديد: /person_data/person_data_excel")
        response = requests.get('http://127.0.0.1:5000/person_data/person_data_excel', allow_redirects=False)
        print(f"   الحالة: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ الرابط يعمل بنجاح!")
        else:
            print(f"   ❌ مشكلة في الرابط: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")

if __name__ == "__main__":
    test_excel_link()
