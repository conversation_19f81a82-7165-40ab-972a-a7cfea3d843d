from flask import Flask, render_template, send_from_directory
import os

app = Flask(__name__, template_folder=os.path.abspath('templates'))

@app.route('/')
def home():
    return "مرحباً بك في تطبيق الترحيب النهائي 6!"

@app.route('/welcome-final')
def welcome_final():
    # طباعة مسار القوالب
    print("مسار القوالب:", app.template_folder)
    print("هل الملف موجود؟", os.path.exists(os.path.join(app.template_folder, 'welcome_final.html')))
    
    return render_template('welcome_final.html')

@app.route('/welcome-file')
def welcome_file():
    return send_from_directory('templates', 'welcome_final.html')

@app.route('/welcome-direct')
def welcome_direct():
    with open(os.path.join(app.template_folder, 'welcome_final.html'), 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/welcome-text')
def welcome_text():
    return "أهلاً ومرحباً صديقي المبدع!"

if __name__ == '__main__':
    app.run(debug=True, port=5010)
