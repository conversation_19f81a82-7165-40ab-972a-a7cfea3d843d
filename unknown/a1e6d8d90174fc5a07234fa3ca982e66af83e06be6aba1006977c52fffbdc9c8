#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل سريع لنظام التدريب والتأهيل
للمستخدمين المتقدمين الذين لديهم جميع المتطلبات مثبتة مسبقاً
"""

import os
import sys
import webbrowser
from threading import Timer

def open_browser():
    """فتح المتصفح تلقائياً"""
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 تم فتح المتصفح")
    except:
        print("⚠️ تعذر فتح المتصفح تلقائياً")
        print("افتح المتصفح يدوياً واذهب إلى: http://localhost:5000")

def main():
    print("🚀 تشغيل سريع - نظام التدريب والتأهيل")
    print("=" * 50)
    
    try:
        # استيراد التطبيق مباشرة
        from app import app, db
        
        # إنشاء قاعدة البيانات إذا لزم الأمر
        with app.app_context():
            db.create_all()
        
        print("✅ النظام جاهز!")
        print("📍 الرابط: http://localhost:5000")
        print("🔑 admin / admin")
        print("🛑 Ctrl+C للإيقاف")
        print("=" * 50)
        
        # فتح المتصفح بعد ثانيتين
        Timer(2.0, open_browser).start()
        
        # تشغيل الخادم
        app.run(
            host='localhost',
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام")
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("استخدم 'تشغيل_النظام_المحدث.py' لتثبيت المتطلبات")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == '__main__':
    main()
