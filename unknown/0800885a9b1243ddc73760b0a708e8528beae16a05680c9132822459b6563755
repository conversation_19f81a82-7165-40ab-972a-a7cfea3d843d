#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعدادات النظام الذكي للنسخ الاحتياطي
Smart Backup System Configuration
"""

import os
from datetime import timedelta

class SmartBackupConfig:
    """إعدادات النظام الذكي للنسخ الاحتياطي"""
    
    # الإعدادات الأساسية
    BACKUP_INTERVAL_MINUTES = 30  # فترة النسخ الاحتياطي بالدقائق
    MAX_BACKUPS = 10  # الحد الأقصى للنسخ الاحتياطية
    
    # مجلد النسخ الاحتياطية
    BACKUP_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')
    
    # أنواع النسخ الاحتياطية
    BACKUP_TYPES = {
        'startup': 'نسخة احتياطية عند البدء',
        'auto': 'نسخة احتياطية تلقائية',
        'manual': 'نسخة احتياطية يدوية',
        'exit': 'نسخة احتياطية عند الخروج',
        'scheduled': 'نسخة احتياطية مجدولة'
    }
    
    # إعدادات التسجيل
    LOG_FILE = 'backup_log.txt'
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
    
    # إعدادات قاعدة البيانات
    DB_CHECK_INTERVAL = 60  # فحص تغييرات قاعدة البيانات كل دقيقة
    
    # إعدادات التنظيف
    CLEANUP_OLD_BACKUPS = True
    KEEP_DAILY_BACKUPS_FOR_DAYS = 7  # الاحتفاظ بنسخة يومية لمدة أسبوع
    KEEP_WEEKLY_BACKUPS_FOR_WEEKS = 4  # الاحتفاظ بنسخة أسبوعية لمدة شهر
    KEEP_MONTHLY_BACKUPS_FOR_MONTHS = 12  # الاحتفاظ بنسخة شهرية لمدة سنة
    
    # إعدادات الأمان
    ENCRYPT_BACKUPS = False  # تشفير النسخ الاحتياطية (مستقبلاً)
    BACKUP_PASSWORD = None  # كلمة مرور النسخ الاحتياطية (مستقبلاً)
    
    # إعدادات الضغط
    COMPRESSION_LEVEL = 6  # مستوى الضغط (0-9)
    
    # إعدادات التنبيهات
    ENABLE_NOTIFICATIONS = True
    NOTIFICATION_EMAIL = None  # البريد الإلكتروني للتنبيهات (مستقبلاً)
    
    # إعدادات النسخ الاحتياطي السحابي (مستقبلاً)
    CLOUD_BACKUP_ENABLED = False
    CLOUD_PROVIDER = None  # 'google_drive', 'dropbox', 'aws_s3'
    CLOUD_CREDENTIALS = None
    
    @classmethod
    def get_backup_interval_seconds(cls):
        """الحصول على فترة النسخ الاحتياطي بالثواني"""
        return cls.BACKUP_INTERVAL_MINUTES * 60
    
    @classmethod
    def get_backup_folder(cls):
        """الحصول على مجلد النسخ الاحتياطية وإنشاؤه إذا لم يكن موجوداً"""
        os.makedirs(cls.BACKUP_FOLDER, exist_ok=True)
        return cls.BACKUP_FOLDER
    
    @classmethod
    def get_log_file_path(cls):
        """الحصول على مسار ملف السجل"""
        return os.path.join(os.path.dirname(os.path.abspath(__file__)), cls.LOG_FILE)
    
    @classmethod
    def load_from_file(cls, config_file='backup_config.ini'):
        """تحميل الإعدادات من ملف (مستقبلاً)"""
        # يمكن تطوير هذه الوظيفة لاحقاً لقراءة الإعدادات من ملف INI
        pass
    
    @classmethod
    def save_to_file(cls, config_file='backup_config.ini'):
        """حفظ الإعدادات في ملف (مستقبلاً)"""
        # يمكن تطوير هذه الوظيفة لاحقاً لحفظ الإعدادات في ملف INI
        pass

# إعدادات افتراضية للبيئات المختلفة
class DevelopmentConfig(SmartBackupConfig):
    """إعدادات بيئة التطوير"""
    BACKUP_INTERVAL_MINUTES = 15  # نسخ احتياطية أكثر تكراراً في التطوير
    MAX_BACKUPS = 5

class ProductionConfig(SmartBackupConfig):
    """إعدادات بيئة الإنتاج"""
    BACKUP_INTERVAL_MINUTES = 60  # نسخ احتياطية أقل تكراراً في الإنتاج
    MAX_BACKUPS = 20
    ENCRYPT_BACKUPS = True

class TestingConfig(SmartBackupConfig):
    """إعدادات بيئة الاختبار"""
    BACKUP_INTERVAL_MINUTES = 5  # نسخ احتياطية سريعة للاختبار
    MAX_BACKUPS = 3

# تحديد الإعدادات المستخدمة حسب البيئة
def get_config():
    """الحصول على الإعدادات المناسبة حسب البيئة"""
    env = os.environ.get('FLASK_ENV', 'development')
    
    if env == 'production':
        return ProductionConfig
    elif env == 'testing':
        return TestingConfig
    else:
        return DevelopmentConfig

# الإعدادات الحالية
current_config = get_config()
