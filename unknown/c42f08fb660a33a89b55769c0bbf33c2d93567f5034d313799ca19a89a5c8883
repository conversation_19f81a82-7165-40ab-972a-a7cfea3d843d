#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 سكريبت اختبار شامل لنظام إدارة المشاركين في الدورات
يقوم بـ:
1. فحص هيكل قاعدة البيانات
2. إضافة بيانات تجريبية
3. اختبار إضافة المشاركين للدورة
4. التحقق من العلاقات والجداول
"""

import os
import sys
from datetime import datetime, timezone

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# استيراد التطبيق والنماذج
from app import app, db, PersonData, Course, CourseParticipant, User

def print_header(title):
    """طباعة عنوان مميز"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)

def print_step(step):
    """طباعة خطوة"""
    print(f"\n📋 {step}")
    print("-" * 40)

def check_database_structure():
    """فحص هيكل قاعدة البيانات"""
    print_header("فحص هيكل قاعدة البيانات")
    
    try:
        # فحص الجداول الأساسية
        print_step("فحص الجداول الأساسية")
        
        # عدد الأشخاص
        person_count = PersonData.query.count()
        print(f"✅ جدول PersonData: {person_count} شخص")
        
        # عدد الدورات
        course_count = Course.query.count()
        print(f"✅ جدول Course: {course_count} دورة")
        
        # عدد المشاركين
        participant_count = CourseParticipant.query.count()
        print(f"✅ جدول CourseParticipant: {participant_count} مشارك")
        
        # عدد المستخدمين
        user_count = User.query.count()
        print(f"✅ جدول User: {user_count} مستخدم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def create_test_data():
    """إنشاء بيانات تجريبية"""
    print_header("إنشاء بيانات تجريبية")
    
    try:
        # إنشاء أشخاص تجريبيين
        print_step("إنشاء أشخاص تجريبيين")
        
        test_people = [
            {
                'full_name': 'أحمد محمد علي الزبيدي',
                'national_number': '01234567890',
                'military_number': 'M12345',
                'phone': '777123456',
                'age': 30,
                'governorate': 'صنعاء',
                'directorate': 'شعوب',
                'job': 'مهندس برمجيات',
                'qualification': 'بكالوريوس هندسة'
            },
            {
                'full_name': 'فاطمة أحمد سالم المقطري',
                'national_number': '01234567891',
                'military_number': 'M12346',
                'phone': '777123457',
                'age': 28,
                'governorate': 'تعز',
                'directorate': 'صالة',
                'job': 'محاسبة',
                'qualification': 'بكالوريوس محاسبة'
            },
            {
                'full_name': 'محمد عبدالله حسن الحوثي',
                'national_number': '01234567892',
                'military_number': 'M12347',
                'phone': '777123458',
                'age': 35,
                'governorate': 'الحديدة',
                'directorate': 'الحوك',
                'job': 'طبيب',
                'qualification': 'دكتوراه طب'
            },
            {
                'full_name': 'عائشة علي محمد الشامي',
                'national_number': '01234567893',
                'military_number': 'M12348',
                'phone': '777123459',
                'age': 26,
                'governorate': 'إب',
                'directorate': 'يريم',
                'job': 'معلمة',
                'qualification': 'بكالوريوس تربية'
            },
            {
                'full_name': 'خالد سعد أحمد الأهدل',
                'national_number': '01234567894',
                'military_number': 'M12349',
                'phone': '777123460',
                'age': 32,
                'governorate': 'عدن',
                'directorate': 'كريتر',
                'job': 'مدير مشاريع',
                'qualification': 'ماجستير إدارة أعمال'
            }
        ]
        
        created_people = []
        for person_data in test_people:
            # التحقق من عدم وجود الشخص مسبقاً
            existing = PersonData.query.filter_by(national_number=person_data['national_number']).first()
            if not existing:
                person = PersonData(**person_data)
                db.session.add(person)
                created_people.append(person)
                print(f"✅ تم إنشاء: {person_data['full_name']}")
            else:
                created_people.append(existing)
                print(f"🔄 موجود مسبقاً: {person_data['full_name']}")
        
        db.session.commit()
        print(f"✅ تم إنشاء/التحقق من {len(created_people)} شخص")
        
        return created_people
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return []

def test_course_participant_operations(people):
    """اختبار عمليات المشاركين في الدورات"""
    print_header("اختبار عمليات المشاركين في الدورات")
    
    try:
        # البحث عن الدورة رقم 1
        print_step("البحث عن الدورة رقم 1")
        course = Course.query.get(1)
        if not course:
            print("❌ الدورة رقم 1 غير موجودة!")
            return False
        
        print(f"✅ تم العثور على الدورة: {course.title}")
        print(f"📋 تفاصيل الدورة:")
        print(f"   - العنوان: {course.title}")
        print(f"   - الفئة: {course.category}")
        print(f"   - المستوى: {course.level}")
        print(f"   - تاريخ البدء: {course.start_date}")
        print(f"   - المدة: {course.duration_days} يوم")
        
        # عرض المشاركين الحاليين
        print_step("المشاركين الحاليين في الدورة")
        current_participants = CourseParticipant.query.filter_by(course_id=1).all()
        print(f"📊 عدد المشاركين الحاليين: {len(current_participants)}")
        
        for i, participant in enumerate(current_participants, 1):
            person = participant.personal_data
            print(f"   {i}. {person.full_name} (ID: {person.id})")
        
        # اختبار إضافة مشاركين جدد
        print_step("اختبار إضافة مشاركين جدد")
        
        added_count = 0
        for person in people[:3]:  # إضافة أول 3 أشخاص فقط
            # التحقق من عدم وجوده في الدورة مسبقاً
            existing = CourseParticipant.query.filter_by(
                course_id=1,
                personal_data_id=person.id
            ).first()
            
            if not existing:
                # إضافة المشارك
                participant = CourseParticipant(
                    course_id=1,
                    personal_data_id=person.id,
                    status='active'
                )
                db.session.add(participant)
                added_count += 1
                print(f"✅ تم إضافة: {person.full_name}")
            else:
                print(f"🔄 موجود مسبقاً: {person.full_name}")
        
        db.session.commit()
        print(f"✅ تم إضافة {added_count} مشارك جديد")
        
        # عرض المشاركين بعد الإضافة
        print_step("المشاركين بعد الإضافة")
        updated_participants = CourseParticipant.query.filter_by(course_id=1).all()
        print(f"📊 العدد الجديد للمشاركين: {len(updated_participants)}")
        
        for i, participant in enumerate(updated_participants, 1):
            person = participant.personal_data
            print(f"   {i}. {person.full_name} (ID: {person.id}) - الحالة: {participant.status}")
        
        return True
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في اختبار عمليات المشاركين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_relationships():
    """اختبار العلاقات في قاعدة البيانات"""
    print_header("اختبار العلاقات في قاعدة البيانات")
    
    try:
        print_step("اختبار العلاقة بين CourseParticipant و PersonData")
        
        # جلب مشارك واختبار العلاقة
        participant = CourseParticipant.query.first()
        if participant:
            person = participant.personal_data
            print(f"✅ المشارك: {person.full_name}")
            print(f"✅ الدورة: {participant.course.title}")
            print(f"✅ العلاقة تعمل بشكل صحيح")
        else:
            print("⚠️ لا يوجد مشاركين لاختبار العلاقة")
        
        print_step("اختبار العلاقة العكسية")
        
        # اختبار العلاقة العكسية
        person = PersonData.query.first()
        if person:
            participations = person.course_participations
            print(f"✅ الشخص: {person.full_name}")
            print(f"✅ عدد الدورات المشارك فيها: {len(participations)}")
            for participation in participations:
                print(f"   - {participation.course.title}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العلاقات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print_header("🧪 اختبار شامل لنظام إدارة المشاركين في الدورات")
    
    with app.app_context():
        # 1. فحص هيكل قاعدة البيانات
        if not check_database_structure():
            print("❌ فشل في فحص قاعدة البيانات")
            return
        
        # 2. إنشاء بيانات تجريبية
        people = create_test_data()
        if not people:
            print("❌ فشل في إنشاء البيانات التجريبية")
            return
        
        # 3. اختبار عمليات المشاركين
        if not test_course_participant_operations(people):
            print("❌ فشل في اختبار عمليات المشاركين")
            return
        
        # 4. اختبار العلاقات
        if not test_database_relationships():
            print("❌ فشل في اختبار العلاقات")
            return
        
        print_header("🎉 تم الانتهاء من جميع الاختبارات بنجاح!")
        print("✅ جميع الاختبارات نجحت")
        print("✅ النظام يعمل بشكل صحيح")
        print("✅ يمكن الآن اختبار الواجهة من المتصفح")

if __name__ == "__main__":
    main()
