#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار عرض المشاركين في الدورة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, CourseParticipant, PersonData, Course

def test_participants():
    """
    اختبار عرض المشاركين في الدورة
    """
    with app.app_context():
        try:
            # جلب الدورة الأولى
            course = Course.query.get(1)
            if not course:
                print("❌ لا توجد دورة بالرقم 1")
                return False
            
            print(f"✅ تم العثور على الدورة: {course.title}")
            
            # جلب المشاركين في الدورة
            participants = CourseParticipant.query.filter_by(course_id=1).all()
            print(f"📊 عدد المشاركين في الدورة: {len(participants)}")
            
            if not participants:
                print("⚠️ لا يوجد مشاركين في هذه الدورة")
                return True
            
            # عرض تفاصيل كل مشارك
            for i, participant in enumerate(participants, 1):
                print(f"\n👤 المشارك {i}:")
                print(f"   - ID: {participant.id}")
                print(f"   - personal_data_id: {participant.personal_data_id}")
                print(f"   - الحالة: {participant.status}")
                print(f"   - تاريخ الدخول: {participant.entry_date}")
                
                # التحقق من العلاقة مع PersonData
                if participant.personal_data:
                    print(f"   - الاسم: {participant.personal_data.full_name}")
                    print(f"   - الهاتف: {participant.personal_data.phone}")
                else:
                    print(f"   - ❌ لا توجد بيانات شخصية مرتبطة")
                    
                    # البحث عن الشخص يدوياً
                    person = PersonData.query.get(participant.personal_data_id)
                    if person:
                        print(f"   - ✅ تم العثور على الشخص يدوياً: {person.full_name}")
                    else:
                        print(f"   - ❌ لا يوجد شخص بالرقم {participant.personal_data_id}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار المشاركين: {e}")
            return False

if __name__ == "__main__":
    print("🧪 اختبار عرض المشاركين في الدورة...")
    success = test_participants()
    if success:
        print("✅ تم الاختبار بنجاح!")
    else:
        print("❌ فشل الاختبار!")
