#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نسخ التصحيحات من database.db إلى training_system.db
"""

import sqlite3
from datetime import datetime, timezone

def copy_corrections():
    """
    نسخ التصحيحات من قاعدة البيانات القديمة إلى الجديدة
    """
    
    try:
        # الاتصال بقاعدة البيانات القديمة
        old_conn = sqlite3.connect('database.db')
        old_cursor = old_conn.cursor()
        
        # الاتصال بقاعدة البيانات الجديدة
        new_conn = sqlite3.connect('training_system.db')
        new_cursor = new_conn.cursor()
        
        # إنشاء جدول التصحيحات في قاعدة البيانات الجديدة
        new_cursor.execute("""
            CREATE TABLE IF NOT EXISTS name_correction (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                wrong_name VARCHAR(200) NOT NULL,
                correct_name VARCHAR(200) NOT NULL,
                correction_type VARCHAR(50) NOT NULL,
                created_by INTEGER,
                created_at DATETIME NOT NULL,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                usage_count INTEGER NOT NULL DEFAULT 0
            )
        """)
        
        print("✅ تم إنشاء جدول name_correction في training_system.db")
        
        # جلب البيانات من قاعدة البيانات القديمة
        old_cursor.execute("""
            SELECT wrong_name, correct_name, correction_type, created_by, 
                   created_at, is_active, usage_count
            FROM name_correction
        """)
        
        corrections = old_cursor.fetchall()
        print(f"📊 تم جلب {len(corrections)} تصحيح من database.db")
        
        # نسخ البيانات إلى قاعدة البيانات الجديدة
        insert_query = """
            INSERT INTO name_correction 
            (wrong_name, correct_name, correction_type, created_by, created_at, is_active, usage_count)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        
        copied_count = 0
        for correction in corrections:
            try:
                # التحقق من عدم وجود التصحيح مسبقاً
                check_query = "SELECT id FROM name_correction WHERE wrong_name = ? AND correct_name = ?"
                existing = new_cursor.execute(check_query, (correction[0], correction[1])).fetchone()
                
                if not existing:
                    new_cursor.execute(insert_query, correction)
                    copied_count += 1
                    print(f"✅ تم نسخ: {correction[0]} → {correction[1]}")
                else:
                    print(f"⚠️  موجود مسبقاً: {correction[0]} → {correction[1]}")
                    
            except Exception as e:
                print(f"❌ خطأ في نسخ {correction[0]}: {e}")
        
        # حفظ التغييرات
        new_conn.commit()
        
        # التحقق من النتائج
        new_cursor.execute("SELECT COUNT(*) FROM name_correction")
        total_count = new_cursor.fetchone()[0]
        
        print(f"\n🎉 تم نسخ {copied_count} تصحيح جديد!")
        print(f"📊 إجمالي التصحيحات في training_system.db: {total_count}")
        
        # إغلاق الاتصالات
        old_conn.close()
        new_conn.close()
        
        print("✅ تم إغلاق الاتصالات بنجاح")
        print("🌐 يمكنك الآن الوصول إلى: http://localhost:5000/person_data/manage_corrections")
        
        return copied_count
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return 0

if __name__ == "__main__":
    copy_corrections()
