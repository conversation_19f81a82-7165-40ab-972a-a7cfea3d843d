@echo off
chcp 65001 >nul
echo ========================================
echo    بناء Docker للنظام
echo    Building Docker for Training System
echo ========================================
echo.

cd /d "E:\app\TRINING"

echo 🐳 فحص Docker...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker غير مثبت! يرجى تثبيت Docker أولاً
    echo ❌ Docker not installed! Please install Docker first
    echo.
    echo تحميل Docker من: https://www.docker.com/products/docker-desktop
    echo Download Docker from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker متوفر
echo.

echo 🔨 بناء Docker Image...
docker build -t training-system:latest .

if errorlevel 0 (
    echo ✅ تم بناء Docker Image بنجاح!
    echo ✅ Docker Image built successfully!
    echo.
    
    echo 🚀 تشغيل النظام باستخدام Docker:
    echo 🚀 Run system using Docker:
    echo.
    echo    docker run -p 5000:5000 training-system:latest
    echo.
    echo أو استخدم Docker Compose:
    echo Or use Docker Compose:
    echo.
    echo    docker-compose up
    echo.
    
    echo 💾 حفظ Image كملف:
    echo 💾 Save Image as file:
    echo.
    echo    docker save training-system:latest -o training-system.tar
    echo.
) else (
    echo ❌ فشل في بناء Docker Image
    echo ❌ Failed to build Docker Image
    echo تحقق من الأخطاء أعلاه
    echo Check errors above
)

echo.
pause
