#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح التواريخ المعطوبة في جدول الدورات
"""

import sqlite3
import sys
import os
from datetime import datetime

def fix_course_dates():
    """
    إصلاح التواريخ المعطوبة في جدول الدورات
    """
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()

        print("🔍 فحص البيانات المعطوبة في جدول course...")

        # فحص جميع الدورات
        cursor.execute('SELECT id, title, start_date, end_date FROM course')
        courses = cursor.fetchall()

        print(f"📊 إجمالي الدورات: {len(courses)}")

        corrupted_courses = []
        for course in courses:
            course_id, title, start_date, end_date = course

            # فحص التواريخ المعطوبة
            if start_date == '0' or start_date == 0 or end_date == '0' or end_date == 0:
                corrupted_courses.append(course)
                print(f"❌ دورة معطوبة - ID: {course_id}, العنوان: {title}")
                print(f"   start_date: {start_date}, end_date: {end_date}")

        if not corrupted_courses:
            print("✅ لا توجد دورات معطوبة")
            return True

        print(f"\n🔧 إصلاح {len(corrupted_courses)} دورة معطوبة...")

        # إصلاح التواريخ المعطوبة
        for course in corrupted_courses:
            course_id = course[0]

            # تعيين تواريخ افتراضية صحيحة
            default_start_date = datetime(2024, 1, 1).strftime('%Y-%m-%d %H:%M:%S')
            default_end_date = datetime(2024, 1, 31).strftime('%Y-%m-%d %H:%M:%S')

            cursor.execute('''
                UPDATE course
                SET start_date = ?, end_date = ?
                WHERE id = ?
            ''', (default_start_date, default_end_date, course_id))

            print(f"✅ تم إصلاح الدورة ID: {course_id}")
            print(f"   تاريخ البدء: {default_start_date}")
            print(f"   تاريخ النهاية: {default_end_date}")

        # حفظ التغييرات
        conn.commit()
        print(f"\n✅ تم إصلاح جميع الدورات المعطوبة!")

        # التحقق من النتائج
        cursor.execute('SELECT id, title, start_date, end_date FROM course')
        fixed_courses = cursor.fetchall()

        print(f"\n📋 حالة الدورات بعد الإصلاح:")
        for course in fixed_courses:
            course_id, title, start_date, end_date = course
            print(f"  - ID: {course_id}, العنوان: {title[:30]}...")
            print(f"    start_date: {start_date}, end_date: {end_date}")

        conn.close()
        return True

    except Exception as e:
        print(f"❌ خطأ في إصلاح البيانات: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("🧪 إصلاح التواريخ المعطوبة في جدول الدورات...")
    success = fix_course_dates()
    if success:
        print("✅ تم الإصلاح بنجاح!")
    else:
        print("❌ فشل الإصلاح!")
