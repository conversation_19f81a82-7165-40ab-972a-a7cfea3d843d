from flask import Flask, render_template
import os

app = Flask(__name__)

@app.route('/')
def home():
    return "مرحباً بك في تطبيق الترحيب النهائي 2!"

@app.route('/welcome-final')
def welcome_final():
    # طباعة مسار القوالب
    print("مسار القوالب:", app.template_folder)
    print("هل الملف موجود؟", os.path.exists(os.path.join(app.template_folder, 'welcome_final.html')))
    
    return render_template('welcome_final.html')

if __name__ == '__main__':
    app.run(debug=True, port=5006)
