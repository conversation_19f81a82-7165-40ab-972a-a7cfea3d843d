{% extends "layout.html" %}
{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h3 class="mb-0">نتائج البحث</h3>
            <a href="{{ url_for('search_courses') }}" class="btn btn-light">
                <i class="fas fa-search"></i> بحث جديد
            </a>
        </div>
        <div class="card-body">
            {% if courses %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th scope="col">رقم الدورة</th>
                            <th scope="col">عنوان الدورة</th>
                            <th scope="col">التصنيف</th>
                            <th scope="col">المستوى</th>
                            <th scope="col">تاريخ البدء (ميلادي)</th>
                            <th scope="col">تاريخ البدء (هجري)</th>
                            <th scope="col">المدة (أيام)</th>
                            <th scope="col">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for course in courses %}
                        <tr>
                            <td>{{ course.course_number }}</td>
                            <td>{{ course.title }}</td>
                            <td>{{ course.category }}</td>
                            <td>{{ course.level }}</td>
                            <td>{{ course.start_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ course.start_date_hijri }}</td>
                            <td>{{ course.duration_days }}</td>
                            <td>
                                <a href="{{ url_for('course_details', course_id=course.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="mt-3">
                <p class="text-muted">تم العثور على {{ courses|length }} دورة</p>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> لم يتم العثور على دورات تطابق معايير البحث.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
