#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء ملف Excel تجريبي لاختبار ميزات فحص التطابق المتقدم
"""

import pandas as pd
from datetime import datetime

def create_test_file():
    """إنشاء ملف Excel تجريبي مع بيانات متنوعة لاختبار النظام"""
    
    # بيانات تجريبية متنوعة
    test_data = [
        # حالات التطابق في الاسم والرقم الوطني (يجب رفضها)
        {"الاسم الشخصي": "محمد أحمد علي", "الرقم الوطني": "12345678901", "رقم الهاتف": "0501234567", "الرقم العسكري": "M001"},
        
        # نفس الاسم لكن رقم وطني مختلف (يجب قبولها)
        {"الاسم الشخصي": "محمد أحمد علي", "الرقم الوطني": "98765432109", "رقم الهاتف": "0509876543", "الرقم العسكري": "M002"},
        
        # حالات التطابق في الاسم ورقم الهاتف (يجب رفضها)
        {"الاسم الشخصي": "عبدالله محمد", "الرقم الوطني": "11111111111", "رقم الهاتف": "0501111111", "الرقم العسكري": "M003"},
        
        # نفس الاسم لكن رقم هاتف مختلف (يجب قبولها)
        {"الاسم الشخصي": "عبدالله محمد", "الرقم الوطني": "22222222222", "رقم الهاتف": "0502222222", "الرقم العسكري": "M004"},
        
        # حالات التطابق في الاسم والرقم العسكري (يجب رفضها)
        {"الاسم الشخصي": "أحمد عبدالرحمن", "الرقم الوطني": "33333333333", "رقم الهاتف": "0503333333", "الرقم العسكري": "M005"},
        
        # نفس الاسم لكن رقم عسكري مختلف (يجب قبولها)
        {"الاسم الشخصي": "أحمد عبدالرحمن", "الرقم الوطني": "44444444444", "رقم الهاتف": "0504444444", "الرقم العسكري": "M006"},
        
        # أسماء تحتاج تصحيح
        {"الاسم الشخصي": "احمد ابراهيم", "الرقم الوطني": "55555555555", "رقم الهاتف": "0505555555", "الرقم العسكري": "M007"},
        {"الاسم الشخصي": "عيسي محمد", "الرقم الوطني": "66666666666", "رقم الهاتف": "0506666666", "الرقم العسكري": "M008"},
        {"الاسم الشخصي": "عبداللة احمد", "الرقم الوطني": "77777777777", "رقم الهاتف": "0507777777", "الرقم العسكري": "M009"},
        
        # أسماء جديدة تماماً
        {"الاسم الشخصي": "خالد سعد المطيري", "الرقم الوطني": "88888888888", "رقم الهاتف": "0508888888", "الرقم العسكري": "M010"},
        {"الاسم الشخصي": "فهد عبدالعزيز القحطاني", "الرقم الوطني": "99999999999", "رقم الهاتف": "0509999999", "الرقم العسكري": "M011"},
        
        # حالات تطابق الرقم الوطني فقط (أسماء مختلفة)
        {"الاسم الشخصي": "سالم أحمد", "الرقم الوطني": "12345678901", "رقم الهاتف": "0501111222", "الرقم العسكري": "M012"},
        
        # حالات تطابق رقم الهاتف فقط (أسماء مختلفة)
        {"الاسم الشخصي": "ناصر محمد", "الرقم الوطني": "12121212121", "رقم الهاتف": "0501111111", "الرقم العسكري": "M013"},
        
        # حالات تطابق الرقم العسكري فقط (أسماء مختلفة)
        {"الاسم الشخصي": "عمر عبدالله", "الرقم الوطني": "13131313131", "رقم الهاتف": "0501313131", "الرقم العسكري": "M005"},
        
        # أسماء بدون بعض البيانات (لاختبار التعامل مع البيانات الناقصة)
        {"الاسم الشخصي": "يوسف أحمد", "الرقم الوطني": "", "رقم الهاتف": "0501414141", "الرقم العسكري": ""},
        {"الاسم الشخصي": "زياد محمد", "الرقم الوطني": "14141414141", "رقم الهاتف": "", "الرقم العسكري": "M014"},
        
        # أسماء طويلة (أكثر من 6 أجزاء)
        {"الاسم الشخصي": "عبدالرحمن بن عبدالله بن محمد بن أحمد بن سعد بن علي الغامدي", "الرقم الوطني": "15151515151", "رقم الهاتف": "0501515151", "الرقم العسكري": "M015"},
        
        # أسماء للتشابه الثلاثي والرباعي والخماسي
        {"الاسم الشخصي": "محمد عبدالله أحمد", "الرقم الوطني": "16161616161", "رقم الهاتف": "0501616161", "الرقم العسكري": "M016"},
        {"الاسم الشخصي": "عبدالله محمد أحمد سالم", "الرقم الوطني": "17171717171", "رقم الهاتف": "0501717171", "الرقم العسكري": "M017"},
        {"الاسم الشخصي": "أحمد محمد عبدالله سالم ناصر", "الرقم الوطني": "18181818181", "رقم الهاتف": "0501818181", "الرقم العسكري": "M018"},
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(test_data)
    
    # إنشاء اسم الملف مع الوقت الحالي
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"اختبار_التطابق_المتقدم_{timestamp}.xlsx"
    
    # حفظ الملف
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف الاختبار: {filename}")
    print(f"📊 عدد السجلات: {len(test_data)}")
    print("\n📋 محتويات الملف:")
    print("=" * 50)
    
    # عرض ملخص المحتويات
    categories = {
        "🔴 حالات يجب رفضها (تطابق في البيانات)": [
            "محمد أحمد علي + رقم وطني مكرر",
            "عبدالله محمد + رقم هاتف مكرر", 
            "أحمد عبدالرحمن + رقم عسكري مكرر",
            "سالم أحمد + رقم وطني مكرر (اسم مختلف)",
            "ناصر محمد + رقم هاتف مكرر (اسم مختلف)",
            "عمر عبدالله + رقم عسكري مكرر (اسم مختلف)"
        ],
        "🟢 حالات يجب قبولها (أسماء مكررة ببيانات مختلفة)": [
            "محمد أحمد علي + رقم وطني مختلف",
            "عبدالله محمد + رقم هاتف مختلف",
            "أحمد عبدالرحمن + رقم عسكري مختلف"
        ],
        "🟡 أسماء تحتاج تصحيح": [
            "احمد ابراهيم → أحمد إبراهيم",
            "عيسي محمد → عيسى محمد", 
            "عبداللة احمد → عبدالله أحمد"
        ],
        "🔵 أسماء جديدة": [
            "خالد سعد المطيري",
            "فهد عبدالعزيز القحطاني"
        ],
        "🟣 حالات خاصة": [
            "أسماء بدون بعض البيانات",
            "أسماء طويلة (أكثر من 6 أجزاء)",
            "أسماء للتشابه الثلاثي/الرباعي/الخماسي"
        ]
    }
    
    for category, items in categories.items():
        print(f"\n{category}:")
        for item in items:
            print(f"  • {item}")
    
    print(f"\n💡 استخدم هذا الملف لاختبار جميع ميزات فحص التطابق المتقدم!")
    print(f"📁 مسار الملف: {filename}")
    
    return filename

if __name__ == '__main__':
    create_test_file()
