#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تحديث قاعدة البيانات الشامل
يقوم بإنشاء الجداول المفقودة وتحديث البيانات الموجودة
"""

import os
import sqlite3
import shutil
from datetime import datetime
from app import app, db
from app import (
    User, PersonalData, PersonData, Course, CourseParticipant,
    Agency, Department, Governorate, Directorate, Village,
    CoursePath, CoursePathLevel, ForceClassification,
    MaritalStatus, BloodType, IssuingAuthority, QualificationType,
    Specialization, AssignmentType, MilitaryRank, InjuryType,
    InjuryCause, CourseType, TrainingCenterType, Location,
    TrainingCenter, ParticipantType, CardType, CourseLevel,
    CourseCategory
)

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        db_path = 'training_system.db'
        if os.path.exists(db_path):
            backup_path = f'training_system_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'

            # نسخ قاعدة البيانات
            shutil.copy2(db_path, backup_path)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
            return backup_path
        else:
            print("⚠️ لم يتم العثور على قاعدة البيانات الحالية")
            return None
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return None

def check_database_structure():
    """فحص هيكل قاعدة البيانات الحالي"""
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()

        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]

        print("📋 الجداول الموجودة حالياً:")
        for table in sorted(tables):
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   - {table}: {count} سجل")

        conn.close()
        return tables
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")
        return []

def create_missing_tables():
    """إنشاء الجداول المفقودة"""
    try:
        print("🔨 إنشاء الجداول المفقودة...")

        with app.app_context():
            # إنشاء جميع الجداول
            db.create_all()
            print("✅ تم إنشاء جميع الجداول بنجاح")

    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {str(e)}")

def migrate_old_data():
    """ترحيل البيانات القديمة إلى الهيكل الجديد"""
    try:
        print("🔄 ترحيل البيانات القديمة...")

        with app.app_context():
            # فحص وجود جدول personal_data القديم
            conn = sqlite3.connect('training_system.db')
            cursor = conn.cursor()

            # التحقق من وجود جدول personal_data
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='personal_data';")
            if cursor.fetchone():
                print("📦 العثور على جدول personal_data القديم")

                # نسخ البيانات إلى جدول person_data الجديد
                cursor.execute("""
                    INSERT OR IGNORE INTO person_data
                    (full_name, nickname, age, governorate, directorate, uzla, village,
                     qualification, marital_status, job, agency, work_place,
                     national_number, military_number, phone)
                    SELECT full_name, nickname, age, governorate, directorate, uzla, village,
                           qualification, marital_status, job, agency, work_place,
                           national_number, military_number, phone
                    FROM personal_data
                    WHERE full_name IS NOT NULL
                """)

                migrated_count = cursor.rowcount
                print(f"✅ تم ترحيل {migrated_count} سجل من personal_data إلى person_data")

            conn.commit()
            conn.close()

    except Exception as e:
        print(f"❌ خطأ في ترحيل البيانات: {str(e)}")

def fix_foreign_keys():
    """إصلاح المفاتيح الخارجية والعلاقات"""
    try:
        print("🔗 إصلاح المفاتيح الخارجية...")

        with app.app_context():
            # إصلاح علاقات الدورات
            courses = Course.query.all()
            for course in courses:
                # التأكد من وجود المراكز والجهات
                if course.center_id and not course.center:
                    print(f"⚠️ مركز غير موجود للدورة {course.course_number}")

                if course.agency_id and not course.agency:
                    print(f"⚠️ جهة غير موجودة للدورة {course.course_number}")

            print("✅ تم فحص المفاتيح الخارجية")

    except Exception as e:
        print(f"❌ خطأ في إصلاح المفاتيح الخارجية: {str(e)}")

def update_course_relationships():
    """تحديث علاقات الدورات"""
    try:
        print("🎓 تحديث علاقات الدورات...")

        with app.app_context():
            # إنشاء مراكز افتراضية إذا لم تكن موجودة
            if TrainingCenter.query.count() == 0:
                default_centers = [
                    "المركز التدريبي الرئيسي",
                    "مركز التدريب المتقدم",
                    "مركز التدريب التخصصي"
                ]

                for center_name in default_centers:
                    center = TrainingCenter(name=center_name)
                    db.session.add(center)

                db.session.commit()
                print("✅ تم إنشاء المراكز التدريبية الافتراضية")

            # إنشاء جهات افتراضية إذا لم تكن موجودة
            if Agency.query.count() == 0:
                default_agencies = [
                    "وزارة الداخلية",
                    "وزارة الدفاع",
                    "الأمن المركزي",
                    "الحرس الجمهوري"
                ]

                for agency_name in default_agencies:
                    agency = Agency(name=agency_name)
                    db.session.add(agency)

                db.session.commit()
                print("✅ تم إنشاء الجهات الافتراضية")

    except Exception as e:
        print(f"❌ خطأ في تحديث علاقات الدورات: {str(e)}")

def optimize_database():
    """تحسين قاعدة البيانات"""
    try:
        print("⚡ تحسين قاعدة البيانات...")

        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()

        # تحسين قاعدة البيانات
        cursor.execute("VACUUM;")
        cursor.execute("ANALYZE;")

        # إنشاء فهارس للبحث السريع
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_person_data_name ON person_data(full_name);",
            "CREATE INDEX IF NOT EXISTS idx_person_data_phone ON person_data(phone);",
            "CREATE INDEX IF NOT EXISTS idx_person_data_national ON person_data(national_number);",
            "CREATE INDEX IF NOT EXISTS idx_course_number ON course(course_number);",
            "CREATE INDEX IF NOT EXISTS idx_course_dates ON course(start_date, end_date);",
            "CREATE INDEX IF NOT EXISTS idx_course_participant_course ON course_participant(course_id);",
            "CREATE INDEX IF NOT EXISTS idx_course_participant_person ON course_participant(personal_data_id);"
        ]

        for index_sql in indexes:
            cursor.execute(index_sql)

        conn.commit()
        conn.close()

        print("✅ تم تحسين قاعدة البيانات وإنشاء الفهارس")

    except Exception as e:
        print(f"❌ خطأ في تحسين قاعدة البيانات: {str(e)}")

def verify_data_integrity():
    """التحقق من سلامة البيانات"""
    try:
        print("🔍 التحقق من سلامة البيانات...")

        with app.app_context():
            # فحص البيانات الأساسية
            users_count = User.query.count()
            courses_count = Course.query.count()
            person_data_count = PersonData.query.count()
            participants_count = CourseParticipant.query.count()

            print(f"👥 المستخدمين: {users_count}")
            print(f"🎓 الدورات: {courses_count}")
            print(f"👤 بيانات الأشخاص: {person_data_count}")
            print(f"🎯 المشاركين في الدورات: {participants_count}")

            # فحص البيانات المرجعية
            agencies_count = Agency.query.count()
            centers_count = TrainingCenter.query.count()
            governorates_count = Governorate.query.count()

            print(f"🏢 الجهات: {agencies_count}")
            print(f"🏫 المراكز التدريبية: {centers_count}")
            print(f"🗺️ المحافظات: {governorates_count}")

            print("✅ تم التحقق من سلامة البيانات")

    except Exception as e:
        print(f"❌ خطأ في التحقق من سلامة البيانات: {str(e)}")

def main():
    """الدالة الرئيسية لتحديث قاعدة البيانات"""
    print("🚀 بدء تحديث قاعدة البيانات")
    print("=" * 60)

    # 1. إنشاء نسخة احتياطية
    backup_path = backup_database()

    # 2. فحص الهيكل الحالي
    existing_tables = check_database_structure()

    # 3. إنشاء الجداول المفقودة
    create_missing_tables()

    # 4. ترحيل البيانات القديمة
    migrate_old_data()

    # 5. إصلاح المفاتيح الخارجية
    fix_foreign_keys()

    # 6. تحديث علاقات الدورات
    update_course_relationships()

    # 7. تحسين قاعدة البيانات
    optimize_database()

    # 8. التحقق من سلامة البيانات
    verify_data_integrity()

    print("=" * 60)
    print("🎉 تم تحديث قاعدة البيانات بنجاح!")
    if backup_path:
        print(f"💾 النسخة الاحتياطية محفوظة في: {backup_path}")

if __name__ == "__main__":
    main()
