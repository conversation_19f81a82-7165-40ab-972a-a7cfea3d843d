from app import app, db
import sqlalchemy

with app.app_context():
    # تحديد جميع الجداول في قاعدة البيانات
    tables = db.metadata.tables.keys()
    
    for table_name in tables:
        # الحصول على معلومات الجدول
        table = db.metadata.tables[table_name]
        date_columns = []
        
        # البحث عن أعمدة التاريخ في الجدول
        for column in table.columns:
            if str(column.type).startswith('DATE') or str(column.type).startswith('DATETIME'):
                date_columns.append(column.name)
        
        # تحديث قيم التاريخ غير الصالحة في كل عمود
        for column_name in date_columns:
            print(f"فحص الجدول {table_name}, العمود {column_name}")
            try:
                # تحديث القيم '0' إلى NULL
                query = f"UPDATE {table_name} SET {column_name} = NULL WHERE {column_name} = '0' OR {column_name} = 0"
                db.session.execute(sqlalchemy.text(query))
                
                # تحديث القيم الفارغة إلى NULL
                query = f"UPDATE {table_name} SET {column_name} = NULL WHERE {column_name} = ''"
                db.session.execute(sqlalchemy.text(query))
            except Exception as e:
                print(f"خطأ في تحديث الجدول {table_name}, العمود {column_name}: {e}")
    
    # حفظ التغييرات
    db.session.commit()
    print("تم تحديث قاعدة البيانات بنجاح")