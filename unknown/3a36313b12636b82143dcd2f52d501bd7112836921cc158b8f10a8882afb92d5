#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, CourseParticipant, Course, PersonData

def check_participants_table():
    with app.app_context():
        print("🔍 فحص جدول المشاركين مباشرة...")
        print("=" * 60)
        
        # جلب جميع المشاركين
        all_participants = CourseParticipant.query.all()
        print(f"إجمالي المشاركين في جميع الدورات: {len(all_participants)}")
        
        # تجميع حسب الدورة
        course_participants = {}
        for participant in all_participants:
            course_id = participant.course_id
            if course_id not in course_participants:
                course_participants[course_id] = []
            course_participants[course_id].append(participant)
        
        print(f"\nالدورات التي تحتوي على مشاركين: {len(course_participants)}")
        
        for course_id, participants in course_participants.items():
            course = Course.query.get(course_id)
            course_name = course.title if course else f"دورة غير موجودة (ID: {course_id})"
            course_number = course.course_number if course else "غير محدد"
            
            print(f"\n🎓 الدورة ID: {course_id} - رقم: {course_number}")
            print(f"   اسم الدورة: {course_name}")
            print(f"   عدد المشاركين: {len(participants)}")
            
            for i, p in enumerate(participants, 1):
                # التحقق من وجود البيانات الشخصية
                person = PersonData.query.get(p.personal_data_id)
                if person:
                    name = person.full_name
                    status = "✅ موجود"
                else:
                    name = f"غير موجود (ID: {p.personal_data_id})"
                    status = "❌ مفقود"
                
                print(f"     {i}. {name} - {status} - حالة: {p.status}")
        
        # فحص خاص للدورة رقم 1
        print("\n" + "=" * 60)
        print("فحص خاص للدورة ID = 1:")
        course1_participants = CourseParticipant.query.filter_by(course_id=1).all()
        print(f"عدد المشاركين: {len(course1_participants)}")
        
        for i, p in enumerate(course1_participants, 1):
            print(f"  {i}. participant_id: {p.id}")
            print(f"     personal_data_id: {p.personal_data_id}")
            print(f"     status: {p.status}")
            print(f"     entry_date: {p.entry_date}")
            
            # التحقق من العلاقة
            if p.personal_data:
                print(f"     الاسم (من العلاقة): {p.personal_data.full_name}")
            else:
                print(f"     ❌ العلاقة مفقودة")
                
            # البحث المباشر
            person = PersonData.query.get(p.personal_data_id)
            if person:
                print(f"     الاسم (بحث مباشر): {person.full_name}")
            else:
                print(f"     ❌ الشخص غير موجود في person_data")
            print("-" * 30)

if __name__ == '__main__':
    check_participants_table()
