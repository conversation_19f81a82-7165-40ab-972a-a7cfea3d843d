#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 اختبار شامل لنظام الاستيراد الذكي للمشاركين
"""

import pandas as pd
import os
import tempfile

def create_test_excel_file():
    """إنشاء ملف Excel للاختبار"""
    print("📁 إنشاء ملف Excel للاختبار...")
    
    # بيانات اختبار متنوعة
    test_data = {
        'الاسم الشخصي': [
            'علي صالح محمد الحميري1',  # موجود ومشارك في الدورة
            'فاطمة أحمد علي المقطري2',  # موجود ومتاح للإضافة
            'محمد عبدالله أحمد الزبيري',  # جديد تماماً
            'سارة محمد علي الشامي',      # جديد تماماً
            'علي صالح محمد الحميري3',   # موجود ومتاح للإضافة
            'احمد محمد علي الحوثي',      # جديد مع خطأ إملائي (أحمد)
            'فاطمه عبدالله محمد',        # جديد مع خطأ إملائي (فاطمة)
            'علي صالح محمد الحميري2',   # موجود ومشارك في الدورة
        ]
    }
    
    df = pd.DataFrame(test_data)
    
    # حفظ في ملف مؤقت
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
    df.to_excel(temp_file.name, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف الاختبار: {temp_file.name}")
    print(f"📊 عدد الأسماء: {len(test_data['الاسم الشخصي'])}")
    
    return temp_file.name

def test_database_state():
    """اختبار حالة قاعدة البيانات"""
    print("\n🔍 فحص حالة قاعدة البيانات...")
    
    try:
        import sqlite3
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        # فحص الأشخاص في قاعدة البيانات
        cursor.execute("SELECT COUNT(*) FROM person_data")
        total_people = cursor.fetchone()[0]
        print(f"👥 إجمالي الأشخاص في قاعدة البيانات: {total_people}")
        
        # فحص المشاركين في الدورة 1
        cursor.execute("""
            SELECT COUNT(*) FROM course_participant 
            WHERE course_id = 1
        """)
        course_participants = cursor.fetchone()[0]
        print(f"🎓 المشاركين في الدورة 1: {course_participants}")
        
        # فحص الأسماء التي تحتوي على "علي"
        cursor.execute("SELECT full_name FROM person_data WHERE full_name LIKE '%علي%'")
        ali_names = cursor.fetchall()
        print(f"🔍 الأسماء التي تحتوي على 'علي': {len(ali_names)}")
        
        for name in ali_names[:5]:  # أول 5 فقط
            print(f"   - {name[0]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def test_smart_import_urls():
    """اختبار URLs النظام الجديد"""
    print("\n🌐 اختبار URLs النظام الجديد...")
    
    urls_to_test = [
        ("صفحة إدارة المشاركين", "http://localhost:5001/manage_participants/1/"),
        ("صفحة الاستيراد الذكي", "http://localhost:5001/course/1/import_participants"),
        ("API تحليل الملف", "http://localhost:5001/course/1/import_participants_api"),
        ("صفحة النتائج", "http://localhost:5001/course/1/import_results"),
        ("API معالجة النتائج", "http://localhost:5001/course/1/process_import"),
    ]
    
    try:
        import requests
        
        for name, url in urls_to_test:
            try:
                if 'api' in url or 'process' in url:
                    print(f"   ⚠️ {name}: API endpoint (يحتاج POST request)")
                else:
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        print(f"   ✅ {name}: يعمل")
                    elif response.status_code == 302:
                        print(f"   🔄 {name}: يعيد التوجيه (تسجيل دخول مطلوب)")
                    else:
                        print(f"   ❌ {name}: خطأ {response.status_code}")
            except Exception as e:
                print(f"   ❌ {name}: خطأ ({e})")
                
    except ImportError:
        print("⚠️ مكتبة requests غير متاحة - لا يمكن اختبار URLs")

def show_expected_analysis_results():
    """عرض النتائج المتوقعة للتحليل"""
    print("\n📊 النتائج المتوقعة للتحليل الذكي:")
    print("=" * 60)
    
    expected_results = {
        'جدد تماماً': [
            'محمد عبدالله أحمد الزبيري',
            'سارة محمد علي الشامي', 
            'أحمد محمد علي الحوثي',  # مصحح من "احمد"
            'فاطمة عبدالله محمد'      # مصحح من "فاطمه"
        ],
        'موجودين ومتاحين': [
            'فاطمة أحمد علي المقطري2',
            'علي صالح محمد الحميري3'
        ],
        'مشاركين بالفعل': [
            'علي صالح محمد الحميري1',
            'علي صالح محمد الحميري2'
        ]
    }
    
    for category, names in expected_results.items():
        print(f"\n🎯 {category} ({len(names)} أشخاص):")
        for name in names:
            print(f"   - {name}")
    
    print(f"\n📈 الإحصائيات المتوقعة:")
    print(f"   📊 إجمالي الأسماء: 8")
    print(f"   🆕 جدد: {len(expected_results['جدد تماماً'])}")
    print(f"   👥 متاحين: {len(expected_results['موجودين ومتاحين'])}")
    print(f"   ⚠️ مكررين: {len(expected_results['مشاركين بالفعل'])}")
    print(f"   🔧 مصححين: 2 (احمد → أحمد، فاطمه → فاطمة)")

def show_testing_instructions():
    """عرض تعليمات الاختبار"""
    print("\n🎯 تعليمات الاختبار اليدوي:")
    print("=" * 60)
    
    steps = [
        "1. افتح المتصفح على: http://localhost:5001/",
        "2. سجل الدخول بـ admin/admin",
        "3. اذهب إلى: http://localhost:5001/manage_participants/1/",
        "4. اضغط على زر '🚀 استيراد ذكي'",
        "5. ارفع ملف Excel الذي تم إنشاؤه",
        "6. اضغط 'بدء التحليل الذكي'",
        "7. راجع النتائج في صفحة مركز التحكم الذكي",
        "8. جرب الخيارات المختلفة للإضافة",
        "9. تحقق من النتائج النهائية"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print(f"\n🎨 الميزات المتوقعة:")
    features = [
        "✅ واجهة مستخدم جميلة ومتطورة",
        "✅ تحليل ذكي للأسماء مع التصنيف",
        "✅ تصحيح تلقائي للأخطاء الإملائية", 
        "✅ كروت تفاعلية للفئات المختلفة",
        "✅ خيارات مرنة للإضافة",
        "✅ مؤشرات تقدم أثناء التنفيذ",
        "✅ ملخص نجاح مفصل",
        "✅ منع إضافة المكررين"
    ]
    
    for feature in features:
        print(f"   {feature}")

def main():
    """الدالة الرئيسية"""
    print("🎯 اختبار شامل لنظام الاستيراد الذكي للمشاركين")
    print("=" * 70)
    
    # 1. فحص قاعدة البيانات
    db_ok = test_database_state()
    
    # 2. إنشاء ملف اختبار
    test_file = create_test_excel_file()
    
    # 3. اختبار URLs
    test_smart_import_urls()
    
    # 4. عرض النتائج المتوقعة
    show_expected_analysis_results()
    
    # 5. تعليمات الاختبار
    show_testing_instructions()
    
    # الخلاصة
    print("\n" + "=" * 70)
    print("📊 خلاصة الاختبار:")
    print("=" * 70)
    
    print(f"🗄️  قاعدة البيانات: {'✅ تعمل' if db_ok else '❌ مشكلة'}")
    print(f"📁 ملف الاختبار: ✅ تم إنشاؤه ({test_file})")
    print(f"🌐 الخادم: ✅ يعمل على http://localhost:5001")
    
    print(f"\n🎉 النظام جاهز للاختبار!")
    print(f"📁 ملف Excel للاختبار: {test_file}")
    print(f"🌐 ابدأ من: http://localhost:5001/manage_participants/1/")
    
    print(f"\n🎯 المتوقع من النظام الجديد:")
    print(f"   🧠 تحليل ذكي للأسماء")
    print(f"   🎨 واجهة مستخدم متطورة") 
    print(f"   🔧 تصحيح تلقائي للأخطاء")
    print(f"   🎮 خيارات مرنة للإضافة")
    print(f"   📊 إحصائيات مفصلة")
    print(f"   ⚡ أداء سريع وموثوق")

if __name__ == "__main__":
    main()
