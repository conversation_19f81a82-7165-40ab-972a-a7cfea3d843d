#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء نظام التقييم الديناميكي المطابق للنموذج الأصلي
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db

def create_dynamic_evaluation_system():
    """إنشاء نظام التقييم الديناميكي"""
    
    with app.app_context():
        print("🔧 إنشاء نظام التقييم الديناميكي...")
        
        # حذف الجداول القديمة
        try:
            db.engine.execute("DROP TABLE IF EXISTS evaluation_details")
            db.engine.execute("DROP TABLE IF EXISTS participant_evaluations") 
            db.engine.execute("DROP TABLE IF EXISTS template_criteria")
            db.engine.execute("DROP TABLE IF EXISTS evaluation_templates")
            db.engine.execute("DROP TABLE IF EXISTS evaluation_criteria")
            db.engine.execute("DROP TABLE IF EXISTS course_evaluation_criteria")
            print("✅ تم حذف الجداول القديمة")
        except:
            pass
        
        # إنشاء جدول أنواع المعايير
        db.engine.execute("""
        CREATE TABLE IF NOT EXISTS criteria_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            order_index INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # إنشاء جدول المعايير الرئيسية
        db.engine.execute("""
        CREATE TABLE IF NOT EXISTS evaluation_criteria (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            criteria_type_id INTEGER NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            order_index INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (criteria_type_id) REFERENCES criteria_types (id)
        )
        """)
        
        # إنشاء جدول البنود الفرعية
        db.engine.execute("""
        CREATE TABLE IF NOT EXISTS evaluation_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            criteria_id INTEGER NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            max_score FLOAT DEFAULT 10.0,
            order_index INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (criteria_id) REFERENCES evaluation_criteria (id)
        )
        """)
        
        # إنشاء جدول ربط الدورات بالمعايير
        db.engine.execute("""
        CREATE TABLE IF NOT EXISTS course_evaluation_criteria (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            course_id INTEGER NOT NULL,
            criteria_id INTEGER NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES course (id),
            FOREIGN KEY (criteria_id) REFERENCES evaluation_criteria (id)
        )
        """)
        
        # إنشاء جدول تقييمات المشاركين
        db.engine.execute("""
        CREATE TABLE IF NOT EXISTS participant_evaluations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            course_id INTEGER NOT NULL,
            participant_id INTEGER NOT NULL,
            evaluator_id INTEGER NOT NULL,
            evaluation_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            total_score FLOAT,
            percentage FLOAT,
            grade VARCHAR(20),
            notes TEXT,
            is_final BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES course (id),
            FOREIGN KEY (participant_id) REFERENCES course_participant (id),
            FOREIGN KEY (evaluator_id) REFERENCES user (id)
        )
        """)
        
        # إنشاء جدول تفاصيل التقييم
        db.engine.execute("""
        CREATE TABLE IF NOT EXISTS evaluation_item_scores (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            evaluation_id INTEGER NOT NULL,
            item_id INTEGER NOT NULL,
            score FLOAT NOT NULL,
            notes TEXT,
            FOREIGN KEY (evaluation_id) REFERENCES participant_evaluations (id),
            FOREIGN KEY (item_id) REFERENCES evaluation_items (id)
        )
        """)
        
        print("✅ تم إنشاء جداول النظام الديناميكي")
        
        # إضافة أنواع المعايير حسب النموذج
        criteria_types_data = [
            ('معيار 1', 'المعيار الأول', 1),
            ('معيار 2', 'المعيار الثاني', 2),
            ('معيار 3', 'المعيار الثالث', 3),
            ('معيار 4', 'المعيار الرابع', 4),
            ('معيار 5', 'المعيار الخامس', 5),
            ('معيار 6', 'المعيار السادس', 6)
        ]
        
        for name, desc, order in criteria_types_data:
            db.engine.execute("""
                INSERT INTO criteria_types (name, description, order_index)
                VALUES (?, ?, ?)
            """, (name, desc, order))
        
        print("✅ تم إضافة أنواع المعايير")
        
        # إضافة المعايير والبنود حسب النموذج
        criteria_items_data = [
            # معيار 1 - 7 بنود
            (1, 'معيار 1', [
                ('بند 1', 10.0), ('بند 2', 10.0), ('بند 3', 10.0), 
                ('بند 4', 10.0), ('بند 5', 10.0), ('بند 6', 10.0), ('بند 7', 10.0)
            ]),
            # معيار 2 - 3 بنود  
            (2, 'معيار 2', [
                ('بند 1', 10.0), ('بند 2', 10.0), ('بند 3', 10.0)
            ]),
            # معيار 3 - 4 بنود
            (3, 'معيار 3', [
                ('بند 1', 10.0), ('بند 2', 10.0), ('بند 3', 10.0), ('بند 4', 10.0)
            ]),
            # معيار 4 - 2 بنود
            (4, 'معيار 4', [
                ('بند 1', 10.0), ('بند 2', 10.0)
            ]),
            # معيار 5 - 3 بنود
            (5, 'معيار 5', [
                ('بند 1', 10.0), ('بند 2', 10.0), ('بند 3', 10.0)
            ]),
            # معيار 6 - 3 بنود
            (6, 'معيار 6', [
                ('بند 1', 10.0), ('بند 2', 10.0), ('بند 3', 10.0)
            ])
        ]
        
        for criteria_type_id, criteria_name, items in criteria_items_data:
            # إضافة المعيار
            db.engine.execute("""
                INSERT INTO evaluation_criteria (criteria_type_id, name, order_index)
                VALUES (?, ?, ?)
            """, (criteria_type_id, criteria_name, criteria_type_id))
            
            # الحصول على معرف المعيار
            criteria_result = db.engine.execute("SELECT last_insert_rowid()").fetchone()
            criteria_id = criteria_result[0]
            
            # إضافة البنود
            for order, (item_name, max_score) in enumerate(items, 1):
                db.engine.execute("""
                    INSERT INTO evaluation_items (criteria_id, name, max_score, order_index)
                    VALUES (?, ?, ?, ?)
                """, (criteria_id, item_name, max_score, order))
        
        print("✅ تم إضافة المعايير والبنود")
        
        # إضافة المعايير للدورة الأولى كمثال
        db.engine.execute("""
            INSERT INTO course_evaluation_criteria (course_id, criteria_id)
            SELECT 1, id FROM evaluation_criteria WHERE is_active = 1
        """)
        
        print("✅ تم ربط المعايير بالدورة الأولى")
        print("🎉 تم إنشاء النظام الديناميكي بنجاح!")

if __name__ == "__main__":
    create_dynamic_evaluation_system()
