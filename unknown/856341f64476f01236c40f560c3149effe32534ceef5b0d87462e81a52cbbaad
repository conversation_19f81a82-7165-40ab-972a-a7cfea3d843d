#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نظام إدارة التدريب - تشغيل فوري
Training System - Instant Start
"""

import os
import sys

def main():
    print("🚀 نظام إدارة التدريب")
    print("=" * 30)
    
    try:
        # استيراد التطبيق مباشرة
        from app import app, db
        
        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        with app.app_context():
            db.create_all()
        
        print("✅ النظام جاهز!")
        print("🌐 http://localhost:5000")
        print("🔑 admin / admin")
        print("=" * 30)
        
        # تشغيل الخادم
        app.run(host='localhost', port=5000, debug=False)
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("\nيرجى تثبيت المكتبات المطلوبة:")
        print("pip install flask flask-sqlalchemy flask-login flask-wtf pandas openpyxl")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
