#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء بيانات شاملة للنظام - إصدار آمن
- أكثر من 1000 اسم للأشخاص
- أكثر من 200 دورة متنوعة
- جميع الجداول الترميزية مع بيانات كاملة
"""

import os
import sys
import random
from datetime import datetime, timedelta
import subprocess

# تثبيت faker إذا لم يكن مثبتاً
try:
    from faker import Faker
except ImportError:
    print("🔄 تثبيت مكتبة Faker...")
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'faker'])
    from faker import Faker

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, Course, PersonData, Enrollment, CourseParticipant
from app import (
    Agency, TrainingCenter, TrainingCenterType, Governorate, Directorate,
    Village, Location, Department, MilitaryRank, Specialization,
    QualificationType, ParticipantType, CourseCategory, CourseLevel,
    CoursePath, CoursePathLevel, ForceClassification, CardType,
    BloodType, MaritalStatus, InjuryType, InjuryCause
)

# إنشاء مولد البيانات العربية
fake = Faker('ar_SA')
fake_en = Faker('en_US')

def safe_add_or_get(model_class, **kwargs):
    """إضافة آمنة للبيانات أو جلب الموجود"""
    try:
        # البحث عن السجل الموجود
        existing = model_class.query.filter_by(**kwargs).first()
        if existing:
            return existing

        # إنشاء سجل جديد
        new_record = model_class(**kwargs)
        db.session.add(new_record)
        db.session.flush()
        return new_record
    except Exception as e:
        db.session.rollback()
        # محاولة جلب السجل مرة أخرى
        return model_class.query.filter_by(**kwargs).first()

def create_reference_data():
    """إنشاء البيانات المرجعية الأساسية"""
    print("🔄 إنشاء البيانات المرجعية...")

    # الجهات
    agencies_data = [
        ('وزارة الدفاع', 'MOD'),
        ('وزارة الداخلية', 'MOI'),
        ('وزارة التعليم', 'MOE'),
        ('وزارة الصحة', 'MOH'),
        ('وزارة المالية', 'MOF'),
        ('وزارة العدل', 'MOJ'),
        ('وزارة الخارجية', 'MOFA'),
        ('وزارة النقل', 'MOT'),
        ('وزارة الاتصالات', 'MOCI'),
        ('وزارة الطاقة', 'MOEN'),
        ('الحرس الوطني', 'NG'),
        ('قوات الأمن الخاصة', 'SSF'),
        ('المخابرات العامة', 'GID'),
        ('الأمن السياسي', 'PSO'),
        ('قوات الطوارئ', 'EF'),
        ('الدفاع المدني', 'CD'),
        ('خفر السواحل', 'CG'),
        ('الشرطة العسكرية', 'MP'),
        ('القوات الجوية', 'AF'),
        ('القوات البحرية', 'NF')
    ]

    for name, code in agencies_data:
        safe_add_or_get(Agency, name=name, code=code)

    # أنواع مراكز التدريب
    center_types_data = [
        'مركز تدريب عسكري',
        'مركز تدريب مدني',
        'أكاديمية عسكرية',
        'معهد تدريب متخصص',
        'مركز تطوير القدرات',
        'مركز التدريب المهني',
        'مركز التدريب التقني',
        'مركز التدريب الإداري',
        'مركز التدريب الأمني',
        'مركز التدريب الطبي'
    ]

    for center_type in center_types_data:
        safe_add_or_get(TrainingCenterType, name=center_type)

    try:
        db.session.commit()
    except:
        db.session.rollback()

    # مراكز التدريب
    centers_data = [
        'أكاديمية الملك عبدالعزيز الحربية',
        'كلية الملك فهد الأمنية',
        'معهد الإدارة العامة',
        'أكاديمية نايف العربية للعلوم الأمنية',
        'مركز التدريب المتقدم',
        'معهد التدريب التقني',
        'مركز التطوير المهني',
        'أكاديمية القيادة والأركان',
        'مركز التدريب الطبي العسكري',
        'معهد التدريب الفني',
        'مركز التدريب الأمني المتقدم',
        'أكاديمية الطيران المدني',
        'مركز التدريب البحري',
        'معهد التدريب الإلكتروني',
        'مركز التدريب اللوجستي'
    ]

    # جلب نوع مركز التدريب الأول
    first_type = TrainingCenterType.query.first()
    type_id = first_type.id if first_type else 1

    for name in centers_data:
        safe_add_or_get(TrainingCenter, name=name, center_type_id=type_id, capacity=100, is_ready=True)

    try:
        db.session.commit()
    except:
        db.session.rollback()

    print("✅ تم إنشاء البيانات المرجعية الأساسية")

def create_geographic_data():
    """إنشاء البيانات الجغرافية"""
    print("🔄 إنشاء البيانات الجغرافية...")

    # المحافظات والمديريات
    geographic_data = {
        'صنعاء': ['مديرية الصافية', 'مديرية بني حشيش', 'مديرية همدان', 'مديرية معين', 'مديرية الطيال'],
        'عدن': ['مديرية المعلا', 'مديرية كريتر', 'مديرية الشيخ عثمان', 'مديرية دار سعد', 'مديرية البريقة'],
        'تعز': ['مديرية الوازعية', 'مديرية الشمايتين', 'مديرية صالة', 'مديرية المسراخ', 'مديرية الصلو'],
        'الحديدة': ['مديرية الحوك', 'مديرية الدريهمي', 'مديرية بيت الفقيه', 'مديرية زبيد', 'مديرية التحيتا'],
        'إب': ['مديرية يريم', 'مديرية السياني', 'مديرية المخادر', 'مديرية النادرة', 'مديرية حبيش'],
        'ذمار': ['مديرية عتمة', 'مديرية الحداء', 'مديرية وصاب السافل', 'مديرية جهران', 'مديرية مغرب عنس'],
        'حجة': ['مديرية عبس', 'مديرية حرض', 'مديرية ميدي', 'مديرية حيران', 'مديرية بكيل المير'],
        'صعدة': ['مديرية الصفراء', 'مديرية كتاف', 'مديرية رازح', 'مديرية شدا', 'مديرية منبه'],
        'عمران': ['مديرية ثلا', 'مديرية حبور ظليمة', 'مديرية السودة', 'مديرية خارف', 'مديرية ريدة'],
        'مأرب': ['مديرية صرواح', 'مديرية الجوبة', 'مديرية رحبة', 'مديرية حريب', 'مديرية ماهلية'],
        'الجوف': ['مديرية الحزم', 'مديرية خب والشعف', 'مديرية برط العنان', 'مديرية الغيل', 'مديرية المصلوب'],
        'لحج': ['مديرية الحوطة', 'مديرية تبن', 'مديرية يافع', 'مديرية الحد', 'مديرية ردفان'],
        'أبين': ['مديرية زنجبار', 'مديرية خنفر', 'مديرية لودر', 'مديرية أحور', 'مديرية مودية'],
        'شبوة': ['مديرية عتق', 'مديرية بيحان', 'مديرية مرخة السفلى', 'مديرية حبان', 'مديرية الروضة'],
        'حضرموت': ['مديرية المكلا', 'مديرية سيئون', 'مديرية تريم', 'مديرية الشحر', 'مديرية القطن'],
        'المهرة': ['مديرية الغيضة', 'مديرية قشن', 'مديرية حوف', 'مديرية المسيلة', 'مديرية شحن'],
        'سقطرى': ['مديرية حديبو', 'مديرية قلنسية', 'مديرية الرحة', 'مديرية نوجد', 'مديرية هومهيل'],
        'البيضاء': ['مديرية الزاهر', 'مديرية ولد ربيع', 'مديرية الصومعة', 'مديرية مكيراس', 'مديرية ذي ناعم'],
        'الضالع': ['مديرية الضالع', 'مديرية دمت', 'مديرية قعطبة', 'مديرية الحشاء', 'مديرية جحاف'],
        'ريمة': ['مديرية الجعفرية', 'مديرية بلاد الطعام', 'مديرية كسمة', 'مديرية مزهر', 'مديرية السلفية'],
        'المحويت': ['مديرية الحوك', 'مديرية ملحان', 'مديرية شبام كوكبان', 'مديرية بني سعد', 'مديرية الرجم']
    }

    for gov_name, directorates in geographic_data.items():
        # إنشاء المحافظة
        governorate = safe_add_or_get(Governorate, name=gov_name)

        # إنشاء المديريات
        for dir_name in directorates:
            directorate = safe_add_or_get(Directorate, name=dir_name, governorate_id=governorate.id)

            # إنشاء قرى لكل مديرية
            villages = [
                f'قرية الشهيد {fake.first_name_male()}',
                f'حي {fake.first_name_male()}',
                f'منطقة {fake.first_name_male()}',
                f'عزلة {fake.first_name_male()}',
                f'حارة {fake.first_name_male()}'
            ]

            for village_name in villages:
                safe_add_or_get(Village, name=village_name, directorate_id=directorate.id)

    try:
        db.session.commit()
    except:
        db.session.rollback()

    print("✅ تم إنشاء البيانات الجغرافية")

def create_all_reference_data():
    """إنشاء جميع البيانات المرجعية"""
    print("🔄 إنشاء جميع البيانات المرجعية...")

    # الرتب العسكرية
    ranks_data = [
        ('مشير', 'مشير'), ('فريق أول', 'فريق أول'), ('فريق', 'فريق'),
        ('لواء', 'لواء'), ('عميد', 'عميد'), ('عقيد', 'عقيد'),
        ('مقدم', 'مقدم'), ('رائد', 'رائد'), ('نقيب', 'نقيب'),
        ('ملازم أول', 'ملازم أول'), ('ملازم', 'ملازم'),
        ('رقيب أول', 'رقيب أول'), ('رقيب', 'رقيب'),
        ('عريف أول', 'عريف أول'), ('عريف', 'عريف'),
        ('جندي أول', 'جندي أول'), ('جندي', 'جندي')
    ]

    for name, code in ranks_data:
        safe_add_or_get(MilitaryRank, name=name, code=code)

    # فئات الدورات
    categories_data = [
        'دورات عسكرية', 'دورات أمنية', 'دورات إدارية', 'دورات تقنية',
        'دورات طبية', 'دورات قانونية', 'دورات مالية', 'دورات لغات',
        'دورات حاسوب', 'دورات قيادية', 'دورات تخصصية', 'دورات تطويرية'
    ]

    for category in categories_data:
        safe_add_or_get(CourseCategory, name=category)

    # مستويات الدورات
    levels_data = ['مبتدئ', 'متوسط', 'متقدم', 'خبير', 'تأسيسي', 'تخصصي', 'قيادي', 'استراتيجي']

    for level in levels_data:
        safe_add_or_get(CourseLevel, name=level)

    # مسارات الدورات
    paths_data = [
        ('المسار العسكري', 'تدريب عسكري متخصص'),
        ('المسار الأمني', 'تدريب أمني وحماية'),
        ('المسار الإداري', 'تطوير المهارات الإدارية'),
        ('المسار التقني', 'تدريب تقني ومهني'),
        ('المسار القيادي', 'إعداد القادة'),
        ('المسار الطبي', 'تدريب طبي متخصص'),
        ('المسار القانوني', 'تدريب قانوني'),
        ('المسار المالي', 'تدريب مالي ومحاسبي'),
        ('مسار اللغات', 'تعلم اللغات الأجنبية'),
        ('مسار الحاسوب', 'تدريب تقنية المعلومات')
    ]

    for name, description in paths_data:
        safe_add_or_get(CoursePath, name=name, description=description)

    # مستويات المسارات
    path_levels_data = [
        'المستوى الأول', 'المستوى الثاني', 'المستوى الثالث',
        'المستوى الرابع', 'المستوى المتقدم', 'المستوى الخبير'
    ]

    for level in path_levels_data:
        safe_add_or_get(CoursePathLevel, name=level)

    # أنواع البطاقات
    card_types_data = ['بطاقة شخصية', 'جواز سفر', 'رخصة قيادة', 'بطاقة عسكرية', 'بطاقة موظف']

    for card_type in card_types_data:
        safe_add_or_get(CardType, name=card_type)

    # فصائل الدم
    blood_types_data = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']

    for blood_type in blood_types_data:
        safe_add_or_get(BloodType, name=blood_type)

    # الحالة الاجتماعية
    marital_statuses_data = ['أعزب', 'متزوج', 'مطلق', 'أرمل']

    for status in marital_statuses_data:
        safe_add_or_get(MaritalStatus, name=status)

    # أنواع المؤهلات
    qualification_types_data = [
        'ابتدائي', 'إعدادي', 'ثانوي', 'دبلوم', 'بكالوريوس',
        'ماجستير', 'دكتوراه', 'دورات تدريبية', 'شهادات مهنية'
    ]

    for qualification in qualification_types_data:
        safe_add_or_get(QualificationType, name=qualification)

    # أنواع المشاركين
    participant_types_data = ['ضابط', 'ضابط صف', 'جندي', 'موظف مدني', 'متدرب خارجي']

    for participant_type in participant_types_data:
        safe_add_or_get(ParticipantType, name=participant_type)

    # التصنيفات القتالية
    force_classifications_data = [
        'مشاة', 'مدرعات', 'مدفعية', 'مهندسين', 'إشارة', 'طيران',
        'بحرية', 'دفاع جوي', 'استطلاع', 'قوات خاصة', 'حرس حدود',
        'أمن', 'شرطة عسكرية', 'لوجستيات', 'طبي', 'إداري'
    ]

    for classification in force_classifications_data:
        safe_add_or_get(ForceClassification, name=classification)

    # التخصصات
    specializations_data = [
        'قيادة وأركان', 'عمليات عسكرية', 'استخبارات عسكرية', 'لوجستيات عسكرية',
        'إدارة عسكرية', 'طب عسكري', 'هندسة عسكرية', 'طيران عسكري',
        'بحرية عسكرية', 'إشارة عسكرية', 'مدفعية', 'مدرعات', 'مشاة',
        'قوات خاصة', 'أمن وحماية', 'شرطة عسكرية', 'دفاع مدني',
        'حرس حدود', 'مكافحة إرهاب', 'أمن سيبراني', 'تكنولوجيا معلومات'
    ]

    for specialization in specializations_data:
        safe_add_or_get(Specialization, name=specialization)

    try:
        db.session.commit()
    except:
        db.session.rollback()

    print("✅ تم إنشاء جميع البيانات المرجعية")

def generate_arabic_names():
    """توليد أسماء عربية متنوعة"""
    first_names = [
        'محمد', 'أحمد', 'علي', 'حسن', 'حسين', 'عبدالله', 'عبدالرحمن', 'عبدالعزيز',
        'خالد', 'سعد', 'فهد', 'عبدالمجيد', 'عبدالكريم', 'عبدالرحيم', 'عبدالحميد',
        'يوسف', 'إبراهيم', 'إسماعيل', 'موسى', 'عيسى', 'داود', 'سليمان', 'يعقوب',
        'زكريا', 'يحيى', 'عمر', 'عثمان', 'طلحة', 'الزبير', 'سعيد', 'أبو بكر',
        'بلال', 'عمار', 'حمزة', 'جعفر', 'زيد', 'أسامة', 'معاذ', 'أنس',
        'عبدالوهاب', 'عبدالصمد', 'عبدالقادر', 'عبدالناصر', 'عبدالسلام', 'عبدالحكيم',
        'نور الدين', 'صلاح الدين', 'سيف الدين', 'عماد الدين', 'شمس الدين',
        'فيصل', 'نايف', 'بندر', 'تركي', 'سلطان', 'مشعل', 'متعب', 'عبدالإله'
    ]

    family_names = [
        'الأحمد', 'المحمد', 'العلي', 'الحسن', 'الحسين', 'السعد', 'الخالد',
        'الفهد', 'العبدالله', 'الإبراهيم', 'اليوسف', 'الموسى', 'العيسى',
        'الداود', 'السليمان', 'اليعقوب', 'الزكريا', 'اليحيى', 'العمر',
        'العثمان', 'الطلحة', 'الزبير', 'السعيد', 'البكر', 'البلال',
        'العمار', 'الحمزة', 'الجعفر', 'الزيد', 'الأسامة', 'المعاذ',
        'الأنس', 'الوهاب', 'الصمد', 'القادر', 'الناصر', 'السلام',
        'الحكيم', 'النور', 'الصلاح', 'السيف', 'العماد', 'الشمس',
        'الفيصل', 'النايف', 'البندر', 'التركي', 'السلطان', 'المشعل',
        'المتعب', 'الإله', 'الرحمن', 'الرحيم', 'الكريم', 'المجيد',
        'الحميد', 'العزيز', 'الغني', 'الحليم', 'الصبور', 'الشكور'
    ]

    return first_names, family_names

def create_persons(count=1000):
    """إنشاء أشخاص بأسماء عربية متنوعة"""
    print(f"🔄 إنشاء {count} شخص...")

    first_names, family_names = generate_arabic_names()

    # جلب البيانات المرجعية
    governorates = Governorate.query.all()
    directorates = Directorate.query.all()
    villages = Village.query.all()
    blood_types = BloodType.query.all()
    marital_statuses = MaritalStatus.query.all()
    qualification_types = QualificationType.query.all()

    # التحقق من عدد الأشخاص الموجودين
    existing_count = PersonData.query.count()
    print(f"   ℹ️ عدد الأشخاص الموجودين: {existing_count}")

    if existing_count >= count:
        print(f"   ✅ يوجد بالفعل {existing_count} شخص في قاعدة البيانات")
        return

    # عدد الأشخاص المطلوب إنشاؤهم
    to_create = count - existing_count
    print(f"   🔄 سيتم إنشاء {to_create} شخص جديد")

    created_count = 0
    attempts = 0
    max_attempts = to_create * 3

    while created_count < to_create and attempts < max_attempts:
        attempts += 1

        # توليد اسم فريد
        first_name = random.choice(first_names)
        father_name = random.choice(first_names)
        grandfather_name = random.choice(first_names)
        family_name = random.choice(family_names)

        full_name = f"{first_name} {father_name} {grandfather_name} {family_name}"

        # التحقق من عدم وجود الاسم
        if PersonData.query.filter_by(full_name=full_name).first():
            continue

        # اختيار بيانات عشوائية
        governorate = random.choice(governorates) if governorates else None

        # اختيار مديرية من نفس المحافظة
        available_directorates = [d for d in directorates if d.governorate_id == governorate.id] if governorate and directorates else []
        directorate = random.choice(available_directorates) if available_directorates else None

        # اختيار قرية من نفس المديرية
        available_villages = [v for v in villages if v.directorate_id == directorate.id] if directorate and villages else []
        village = random.choice(available_villages) if available_villages else None

        person = PersonData(
            full_name=full_name,
            nickname=first_name,
            age=random.randint(18, 60),
            governorate=governorate.name if governorate else None,
            directorate=directorate.name if directorate else None,
            village=village.name if village else None,
            qualification=random.choice(qualification_types).name if qualification_types else None,
            marital_status=random.choice(marital_statuses).name if marital_statuses else None,
            phone=f"77{random.randint(1000000, 9999999)}",
            national_number=f"{random.randint(100000000, 999999999)}",
            military_number=f"M{random.randint(100000, 999999)}",
            job=random.choice(['ضابط', 'ضابط صف', 'جندي', 'موظف مدني']),
            agency=random.choice(['وزارة الدفاع', 'وزارة الداخلية', 'وزارة التعليم']),
            work_place=f"مكتب {random.choice(['الإدارة', 'التدريب', 'العمليات'])}"
        )

        try:
            db.session.add(person)
            db.session.flush()
            created_count += 1

            if created_count % 100 == 0:
                print(f"   ✅ تم إنشاء {created_count} شخص...")
                db.session.commit()
        except Exception as e:
            db.session.rollback()
            print(f"   ❌ خطأ: {e}")
            continue

    db.session.commit()
    print(f"✅ تم إنشاء {created_count} شخص جديد (الإجمالي: {existing_count + created_count})")

def create_courses(count=200):
    """إنشاء دورات متنوعة"""
    print(f"🔄 إنشاء الدورات...")

    # التحقق من عدد الدورات الموجودة
    existing_count = Course.query.count()
    print(f"   ℹ️ عدد الدورات الموجودة: {existing_count}")

    if existing_count >= count:
        print(f"   ✅ يوجد بالفعل {existing_count} دورة في قاعدة البيانات")
        return

    # عدد الدورات المطلوب إنشاؤها
    to_create = count - existing_count
    print(f"   🔄 سيتم إنشاء {to_create} دورة جديدة")

    # أسماء الدورات
    course_titles = [
        'دورة القيادة والإدارة',
        'دورة التخطيط الاستراتيجي',
        'دورة إدارة المشاريع',
        'دورة الأمن السيبراني',
        'دورة مكافحة الإرهاب',
        'دورة التدريب العسكري المتقدم',
        'دورة الطب العسكري',
        'دورة الهندسة العسكرية',
        'دورة الطيران العسكري',
        'دورة البحرية العسكرية',
        'دورة الاستخبارات العسكرية',
        'دورة العمليات الخاصة',
        'دورة حفظ السلام',
        'دورة إدارة الأزمات',
        'دورة التفاوض',
        'دورة اللغة الإنجليزية',
        'دورة اللغة الفرنسية',
        'دورة الحاسوب المتقدم',
        'دورة الشبكات',
        'دورة البرمجة',
        'دورة قواعد البيانات',
        'دورة الذكاء الاصطناعي',
        'دورة المحاسبة',
        'دورة المالية',
        'دورة القانون العسكري',
        'دورة حقوق الإنسان',
        'دورة الإسعافات الأولية',
        'دورة السلامة المهنية',
        'دورة إدارة الموارد البشرية',
        'دورة التسويق',
        'دورة العلاقات العامة',
        'دورة الإعلام العسكري',
        'دورة التصوير',
        'دورة المونتاج',
        'دورة التصميم الجرافيكي'
    ]

    # جلب البيانات المرجعية
    agencies = Agency.query.all()
    training_centers = TrainingCenter.query.all()
    categories = CourseCategory.query.all()
    levels = CourseLevel.query.all()
    paths = CoursePath.query.all()
    path_levels = CoursePathLevel.query.all()

    created_count = 0

    for i in range(to_create):
        # توليد رقم دورة فريد
        course_number = f"C{datetime.now().year}{random.randint(1000, 9999)}"

        # التحقق من عدم وجود الرقم
        if Course.query.filter_by(course_number=course_number).first():
            course_number = f"C{datetime.now().year}{random.randint(10000, 99999)}"

        # اختيار عنوان عشوائي
        title = random.choice(course_titles)
        if random.choice([True, False]):
            title += f" - المستوى {random.choice(['الأول', 'الثاني', 'الثالث', 'المتقدم'])}"

        # تواريخ عشوائية
        start_date = fake.date_between(start_date='-2y', end_date='+1y')
        duration_days = random.randint(5, 90)
        end_date = start_date + timedelta(days=duration_days)

        # جلب أول مستخدم كمدرب
        trainer = User.query.first()
        trainer_id = trainer.id if trainer else 1

        # تحويل التواريخ إلى هجري (تقريبي)
        start_date_hijri = f"144{random.randint(5, 7)}/{random.randint(1, 12):02d}/{random.randint(1, 28):02d}"
        end_date_hijri = f"144{random.randint(5, 7)}/{random.randint(1, 12):02d}/{random.randint(1, 28):02d}"

        course = Course(
            course_number=course_number,
            agency_course_number=f"AG{random.randint(100, 999)}",
            place_course_number=f"PL{random.randint(100, 999)}",
            title=title,
            description=f"وصف تفصيلي لدورة {title}. تهدف هذه الدورة إلى تطوير مهارات المشاركين في مجال التخصص.",
            category=random.choice(categories).name if categories else 'دورات عامة',
            level=random.choice(levels).name if levels else 'متوسط',
            start_date=start_date,
            end_date=end_date,
            start_date_hijri=start_date_hijri,
            end_date_hijri=end_date_hijri,
            duration_days=duration_days,
            trainer_id=trainer_id,
            path_id=random.choice(paths).id if paths else None,
            path_level_id=random.choice(path_levels).id if path_levels else None,
            agency_id=random.choice(agencies).id if agencies else None,
            center_id=random.choice(training_centers).id if training_centers else None,
            place_name=f"قاعة {random.choice(['التدريب', 'المحاضرات', 'الاجتماعات'])}"
        )

        try:
            db.session.add(course)
            db.session.flush()
            created_count += 1

            if created_count % 50 == 0:
                print(f"   ✅ تم إنشاء {created_count} دورة...")
                db.session.commit()
        except Exception as e:
            db.session.rollback()
            print(f"   ❌ خطأ في إنشاء الدورة: {e}")
            continue

    db.session.commit()
    print(f"✅ تم إنشاء {created_count} دورة جديدة (الإجمالي: {existing_count + created_count})")

def create_enrollments():
    """إنشاء مشاركين في الدورات"""
    print("🔄 إنشاء مشاركين في الدورات...")

    # التحقق من عدد المشاركين الموجودين
    existing_count = CourseParticipant.query.count()
    print(f"   ℹ️ عدد المشاركين الموجودين: {existing_count}")

    # جلب جميع الدورات والأشخاص
    courses = Course.query.all()
    persons = PersonData.query.all()

    if not courses or not persons:
        print("❌ لا توجد دورات أو أشخاص لإنشاء المشاركين")
        return

    print(f"   📚 عدد الدورات: {len(courses)}")
    print(f"   👥 عدد الأشخاص: {len(persons)}")

    created_count = 0

    for course in courses:
        # تحديد عدد المشاركين للدورة (بين 10 و 50)
        num_participants = random.randint(10, min(50, len(persons)))

        # اختيار مشاركين عشوائيين
        selected_persons = random.sample(persons, num_participants)

        for person in selected_persons:
            # التحقق من عدم وجود مشاركة مسبقة
            existing = CourseParticipant.query.filter_by(
                course_id=course.id,
                personal_data_id=person.id
            ).first()

            if existing:
                continue

            participant = CourseParticipant(
                course_id=course.id,
                personal_data_id=person.id,
                entry_date=course.start_date - timedelta(days=random.randint(1, 5)),
                exit_date=course.end_date + timedelta(days=random.randint(0, 2)),
                status=random.choice(['active', 'completed', 'dropped']),
                daily_allowance=random.uniform(50, 200),
                transportation_allowance=random.uniform(20, 100),
                accommodation_allowance=random.uniform(100, 300),
                payment_status=random.choice(['pending', 'paid', 'cancelled']),
                notes=f"ملاحظات حول أداء {person.full_name} في الدورة"
            )

            try:
                db.session.add(participant)
                created_count += 1
            except Exception as e:
                db.session.rollback()
                continue

        if created_count % 100 == 0:
            print(f"   ✅ تم إنشاء {created_count} مشارك...")
            db.session.commit()

    db.session.commit()
    print(f"✅ تم إنشاء {created_count} مشارك جديد (الإجمالي: {existing_count + created_count})")

def main():
    """الوظيفة الرئيسية لإنشاء جميع البيانات"""
    print("=" * 80)
    print("🚀 بدء إنشاء البيانات الشاملة للنظام - الإصدار الآمن")
    print("=" * 80)

    with app.app_context():
        try:
            # إنشاء الجداول إذا لم تكن موجودة
            db.create_all()

            # إنشاء البيانات المرجعية
            create_reference_data()
            create_geographic_data()
            create_all_reference_data()

            # إنشاء الأشخاص والدورات
            create_persons(1000)
            create_courses(200)

            # إنشاء التسجيلات
            create_enrollments()

            print("\n" + "=" * 80)
            print("🎉 تم إنشاء جميع البيانات بنجاح!")
            print("=" * 80)

            # إحصائيات نهائية
            print("📊 الإحصائيات النهائية:")
            print(f"   👥 الأشخاص: {PersonData.query.count()}")
            print(f"   📚 الدورات: {Course.query.count()}")
            print(f"   📝 المشاركين في الدورات: {CourseParticipant.query.count()}")
            print(f"   🏢 الجهات: {Agency.query.count()}")
            print(f"   🏫 مراكز التدريب: {TrainingCenter.query.count()}")
            print(f"   🌍 المحافظات: {Governorate.query.count()}")
            print(f"   📍 المديريات: {Directorate.query.count()}")
            print(f"   🏘️ القرى: {Village.query.count()}")
            print(f"   🎖️ الرتب العسكرية: {MilitaryRank.query.count()}")
            print(f"   📋 فئات الدورات: {CourseCategory.query.count()}")
            print(f"   📈 مستويات الدورات: {CourseLevel.query.count()}")
            print(f"   🛤️ مسارات الدورات: {CoursePath.query.count()}")
            print(f"   🩸 فصائل الدم: {BloodType.query.count()}")
            print(f"   💍 الحالات الاجتماعية: {MaritalStatus.query.count()}")
            print(f"   🎓 أنواع المؤهلات: {QualificationType.query.count()}")

            print("\n💡 يمكنك الآن:")
            print("   • تشغيل النظام ومشاهدة التقارير")
            print("   • استكشاف البيانات في الجداول المختلفة")
            print("   • إنشاء تقارير متقدمة")
            print("   • اختبار جميع وظائف النظام")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    main()
