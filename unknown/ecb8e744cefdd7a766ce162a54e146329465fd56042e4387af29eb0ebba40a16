{% extends "layout.html" %}

{% block styles %}
<style>
    .report-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        text-align: center;
    }
    
    .report-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .report-subtitle {
        font-size: 1.2rem;
        opacity: 0.8;
    }
    
    .stats-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
        transition: all 0.3s;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 10px 0;
    }
    
    .stats-title {
        font-size: 1.2rem;
        color: #6c757d;
    }
    
    .stats-icon {
        font-size: 3rem;
        margin-bottom: 15px;
    }
    
    .stats-courses .stats-icon {
        color: #4a6bff;
    }
    
    .stats-participants .stats-icon {
        color: #28a745;
    }
    
    .stats-graduates .stats-icon {
        color: #17a2b8;
    }
    
    .stats-dropouts .stats-icon {
        color: #dc3545;
    }
    
    .stats-allowance .stats-icon {
        color: #ffc107;
    }
    
    .data-card {
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        border: none;
        margin-bottom: 30px;
    }
    
    .data-card-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 15px 20px;
        font-weight: bold;
        border-radius: 15px 15px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .month-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
        transition: all 0.3s;
    }
    
    .month-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
    
    .month-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 15px 20px;
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .month-body {
        padding: 20px;
    }
    
    .month-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
    }
    
    .month-stat {
        text-align: center;
        padding: 10px;
        border-radius: 10px;
        background-color: #f8f9fa;
        flex: 1;
        margin: 0 5px;
    }
    
    .month-stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .month-stat-title {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .month-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
    }
    
    .btn-view {
        background-color: #4a6bff;
        color: white;
        border: none;
        border-radius: 10px;
        padding: 8px 15px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-view:hover {
        background-color: #2541b2;
        color: white;
    }
    
    .btn-export {
        background-color: #28a745;
        color: white;
        border: none;
        border-radius: 10px;
        padding: 8px 15px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-export:hover {
        background-color: #218838;
        color: white;
    }
    
    .month-courses {
        margin-top: 15px;
    }
    
    .month-course {
        padding: 10px;
        border-radius: 10px;
        background-color: #f8f9fa;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .month-course-title {
        font-weight: bold;
    }
    
    .month-course-info {
        font-size: 0.9rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="report-header">
        <div class="report-title">تقارير الدورات التدريبية</div>
        <div class="report-subtitle">إحصائيات وتقارير عن الدورات التدريبية والمشاركين</div>
    </div>
    
    <div class="row mb-5">
        <div class="col-md-4">
            <div class="stats-card stats-courses">
                <div class="stats-icon"><i class="fas fa-chalkboard-teacher"></i></div>
                <div class="stats-number">{{ total_courses }}</div>
                <div class="stats-title">إجمالي الدورات</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card stats-participants">
                <div class="stats-icon"><i class="fas fa-users"></i></div>
                <div class="stats-number">{{ total_participants }}</div>
                <div class="stats-title">إجمالي المشاركين</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card stats-graduates">
                <div class="stats-icon"><i class="fas fa-graduation-cap"></i></div>
                <div class="stats-number">{{ total_graduates }}</div>
                <div class="stats-title">إجمالي الخريجين</div>
            </div>
        </div>
    </div>
    
    <div class="row mb-5">
        <div class="col-md-6">
            <div class="stats-card stats-dropouts">
                <div class="stats-icon"><i class="fas fa-user-slash"></i></div>
                <div class="stats-number">{{ total_dropouts }}</div>
                <div class="stats-title">إجمالي المنسحبين</div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="stats-card stats-allowance">
                <div class="stats-icon"><i class="fas fa-money-bill-wave"></i></div>
                <div class="stats-number">{{ total_allowance }}</div>
                <div class="stats-title">إجمالي المبالغ المالية</div>
            </div>
        </div>
    </div>
    
    <div class="data-card">
        <div class="data-card-header">
            <span><i class="fas fa-calendar-alt me-2"></i> تقارير الدورات حسب الشهر</span>
        </div>
        <div class="card-body">
            <div class="row">
                {% for month_year in sorted_months %}
                <div class="col-md-6">
                    <div class="month-card">
                        <div class="month-header">
                            {% set year, month = month_year.split('-') %}
                            {% set month_names = {
                                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
                            } %}
                            <span>{{ month_names[month] }} {{ year }}</span>
                            <span class="badge bg-light text-dark">{{ courses_by_month[month_year]|length }} دورة</span>
                        </div>
                        <div class="month-body">
                            <div class="month-stats">
                                <div class="month-stat">
                                    <div class="month-stat-number">{{ courses_by_month[month_year]|length }}</div>
                                    <div class="month-stat-title">الدورات</div>
                                </div>
                                <div class="month-stat">
                                    <div class="month-stat-number">
                                        {% set participants_count = 0 %}
                                        {% for course in courses_by_month[month_year] %}
                                            {% set participants_count = participants_count + (course.total_participants or 0) %}
                                        {% endfor %}
                                        {{ participants_count }}
                                    </div>
                                    <div class="month-stat-title">المشاركين</div>
                                </div>
                                <div class="month-stat">
                                    <div class="month-stat-number">
                                        {% set total = 0 %}
                                        {% for course in courses_by_month[month_year] %}
                                            {% set total = total + (course.total_allowance or 0) %}
                                        {% endfor %}
                                        {{ total }}
                                    </div>
                                    <div class="month-stat-title">المبالغ</div>
                                </div>
                            </div>
                            
                            <div class="month-courses">
                                {% for course in courses_by_month[month_year][:3] %}
                                <div class="month-course">
                                    <div>
                                        <div class="month-course-title">{{ course.title }}</div>
                                        <div class="month-course-info">{{ course.course_number }} | {{ course.start_date.strftime('%Y-%m-%d') }}</div>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary">{{ course.total_participants or 0 }} مشارك</span>
                                    </div>
                                </div>
                                {% endfor %}
                                
                                {% if courses_by_month[month_year]|length > 3 %}
                                <div class="text-center mt-2">
                                    <small class="text-muted">{{ courses_by_month[month_year]|length - 3 }} دورات أخرى...</small>
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="month-actions">
                                <a href="{{ url_for('monthly_course_report', month_year=month_year) }}" class="btn btn-view">
                                    <i class="fas fa-eye me-1"></i> عرض التقرير
                                </a>
                                <a href="{{ url_for('export_course_report', month_year=month_year) }}" class="btn btn-export">
                                    <i class="fas fa-file-export me-1"></i> تصدير CSV
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
