@echo off
chcp 65001 >nul
echo ========================================
echo    بناء ملف EXE للنظام
echo    Building EXE for Training System
echo ========================================
echo.

cd /d "E:\app\TRINING"

echo 🔧 تفعيل البيئة الافتراضية...
call .venv\Scripts\activate.bat

echo.
echo 📦 تثبيت PyInstaller...
pip install pyinstaller

echo.
echo 🔨 بناء ملف EXE...
echo هذه العملية قد تستغرق عدة دقائق...
echo This process may take several minutes...
echo.

pyinstaller build_exe.spec

echo.
if exist "dist\TrainingSystem" (
    echo ✅ تم بناء ملف EXE بنجاح!
    echo ✅ EXE built successfully!
    echo.
    echo 📁 الملفات متوفرة في: dist\TrainingSystem\
    echo 📁 Files available in: dist\TrainingSystem\
    echo.
    echo 🚀 لتشغيل النظام:
    echo 🚀 To run the system:
    echo    cd dist\TrainingSystem
    echo    TrainingSystem.exe
    echo.
) else (
    echo ❌ فشل في بناء ملف EXE
    echo ❌ Failed to build EXE
    echo تحقق من الأخطاء أعلاه
    echo Check errors above
)

echo.
echo 📋 ملاحظات مهمة:
echo 📋 Important notes:
echo.
echo 1. تأكد من وجود جميع الملفات في مجلد dist
echo    Make sure all files are in dist folder
echo.
echo 2. انسخ مجلد templates و static إذا لم يتم نسخهما
echo    Copy templates and static folders if not copied
echo.
echo 3. انسخ قاعدة البيانات training_system.db
echo    Copy training_system.db database
echo.
pause
