#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار النظام الديناميكي للتقييم
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db

def test_dynamic_evaluation():
    """اختبار النظام الديناميكي"""
    
    with app.app_context():
        print("🔍 اختبار النظام الديناميكي للتقييم...")
        
        # فحص الجداول الجديدة
        tables = [
            'criteria_types',
            'evaluation_criteria', 
            'evaluation_items',
            'course_evaluation_criteria',
            'participant_evaluations',
            'evaluation_item_scores'
        ]
        
        for table in tables:
            try:
                result = db.engine.execute(f"SELECT COUNT(*) as count FROM {table}").fetchone()
                print(f"📊 {table}: {result['count']} سجل")
            except Exception as e:
                print(f"❌ خطأ في جدول {table}: {str(e)}")
        
        # فحص معايير الدورة الأولى
        print(f"\n🔍 معايير الدورة الأولى:")
        
        try:
            criteria_results = db.engine.execute("""
                SELECT DISTINCT ec.id, ec.name, ec.description, ec.order_index
                FROM evaluation_criteria ec
                JOIN course_evaluation_criteria cec ON ec.id = cec.criteria_id
                WHERE cec.course_id = 1 AND cec.is_active = 1 AND ec.is_active = 1
                ORDER BY ec.order_index
            """).fetchall()
            
            total_items = 0
            total_max_score = 0
            
            for criteria in criteria_results:
                print(f"   📝 {criteria['name']}")
                
                # جلب البنود
                items_results = db.engine.execute("""
                    SELECT id, name, max_score, order_index
                    FROM evaluation_items
                    WHERE criteria_id = ? AND is_active = 1
                    ORDER BY order_index
                """, (criteria['id'],)).fetchall()
                
                for item in items_results:
                    print(f"      - {item['name']}: {item['max_score']} درجة")
                    total_items += 1
                    total_max_score += item['max_score']
            
            print(f"\n📊 الإجمالي:")
            print(f"   - عدد المعايير: {len(criteria_results)}")
            print(f"   - عدد البنود: {total_items}")
            print(f"   - الدرجة العظمى: {total_max_score}")
            
            # اختبار URL
            print(f"\n🔗 رابط اختبار التقييم:")
            print(f"   http://127.0.0.1:5000/course/1/participants")
            
        except Exception as e:
            print(f"❌ خطأ في فحص المعايير: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_dynamic_evaluation()
