#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تسجيل الدخول والوصول لصفحة إدارة المستخدمين
Test Login and Access to Users Management Page
"""

import requests
import sys

def test_login_and_users_page():
    """اختبار تسجيل الدخول والوصول لصفحة المستخدمين"""
    
    session = requests.Session()
    base_url = 'http://localhost:5000'
    
    print("🧪 اختبار نظام إدارة المستخدمين")
    print("=" * 50)
    
    try:
        # 1. اختبار الوصول للصفحة الرئيسية
        print("1️⃣ اختبار الوصول للصفحة الرئيسية...")
        response = session.get(base_url)
        if response.status_code == 200:
            print("   ✅ الصفحة الرئيسية تعمل")
        else:
            print(f"   ❌ خطأ في الصفحة الرئيسية: {response.status_code}")
            return False
        
        # 2. اختبار صفحة تسجيل الدخول
        print("2️⃣ اختبار صفحة تسجيل الدخول...")
        response = session.get(f'{base_url}/login')
        if response.status_code == 200:
            print("   ✅ صفحة تسجيل الدخول تعمل")
        else:
            print(f"   ❌ خطأ في صفحة تسجيل الدخول: {response.status_code}")
            return False
        
        # 3. اختبار تسجيل الدخول
        print("3️⃣ اختبار تسجيل الدخول بالمدير...")

        # الحصول على CSRF token من صفحة تسجيل الدخول
        login_page = session.get(f'{base_url}/login')
        if 'csrf_token' in login_page.text:
            import re
            csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
            csrf_token = csrf_match.group(1) if csrf_match else None
        else:
            csrf_token = None

        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123'
        }

        if csrf_token:
            login_data['csrf_token'] = csrf_token

        response = session.post(f'{base_url}/login', data=login_data)
        if response.status_code == 200 and ('dashboard' in response.url or 'dashboard' in response.text):
            print("   ✅ تم تسجيل الدخول بنجاح")
        elif response.status_code == 302:
            # إعادة توجيه - قد يكون نجح
            print("   ✅ تم تسجيل الدخول (إعادة توجيه)")
        else:
            print(f"   ❌ فشل تسجيل الدخول: {response.status_code}")
            print(f"   URL: {response.url}")
            # طباعة جزء من المحتوى للتشخيص
            if 'فشل تسجيل الدخول' in response.text:
                print("   💡 بيانات تسجيل الدخول غير صحيحة")
            return False
        
        # 4. اختبار الوصول لصفحة إدارة المستخدمين
        print("4️⃣ اختبار الوصول لصفحة إدارة المستخدمين...")
        response = session.get(f'{base_url}/admin/users')
        if response.status_code == 200:
            print("   ✅ صفحة إدارة المستخدمين تعمل")
            
            # فحص محتوى الصفحة
            if 'إدارة المستخدمين' in response.text:
                print("   ✅ محتوى الصفحة صحيح")
            else:
                print("   ⚠️ محتوى الصفحة قد يكون غير مكتمل")
                
        elif response.status_code == 403:
            print("   ❌ ليس لديك صلاحية للوصول")
            return False
        else:
            print(f"   ❌ خطأ في صفحة إدارة المستخدمين: {response.status_code}")
            return False
        
        # 5. اختبار إنشاء مستخدم جديد
        print("5️⃣ اختبار إنشاء مستخدم جديد...")
        new_user_data = {
            'username': 'test_user_123',
            'email': '<EMAIL>',
            'password': 'test123',
            'confirm_password': 'test123',
            'role': 'viewer',
            'first_name': 'مستخدم',
            'last_name': 'اختبار',
            'department': 'قسم الاختبار',
            'is_active': True
        }
        
        response = session.post(
            f'{base_url}/admin/users/create',
            json=new_user_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ تم إنشاء المستخدم بنجاح")
            else:
                print(f"   ❌ فشل إنشاء المستخدم: {result.get('message', 'خطأ غير معروف')}")
        else:
            print(f"   ❌ خطأ في إنشاء المستخدم: {response.status_code}")
        
        # 6. اختبار تسجيل الخروج
        print("6️⃣ اختبار تسجيل الخروج...")
        response = session.get(f'{base_url}/logout')
        if response.status_code == 200 or response.status_code == 302:
            print("   ✅ تم تسجيل الخروج بنجاح")
        else:
            print(f"   ❌ خطأ في تسجيل الخروج: {response.status_code}")
        
        print("\n🎉 انتهى الاختبار بنجاح!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم. تأكد من تشغيل النظام على localhost:5000")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_different_users():
    """اختبار مستخدمين مختلفين"""
    
    users_to_test = [
        {'email': '<EMAIL>', 'password': 'manager123', 'role': 'مدير'},
        {'email': '<EMAIL>', 'password': 'trainer123', 'role': 'مدرب'},
        {'email': '<EMAIL>', 'password': 'data123', 'role': 'مدخل بيانات'},
        {'email': '<EMAIL>', 'password': 'viewer123', 'role': 'مشاهد'}
    ]
    
    print("\n👥 اختبار مستخدمين مختلفين")
    print("=" * 50)
    
    for user in users_to_test:
        print(f"\n🔐 اختبار {user['role']}: {user['email']}")
        
        session = requests.Session()
        
        try:
            # تسجيل الدخول
            login_data = {
                'email': user['email'],
                'password': user['password']
            }
            
            response = session.post('http://localhost:5000/login', data=login_data)
            if response.status_code == 200:
                print(f"   ✅ تم تسجيل الدخول")
                
                # اختبار الوصول لصفحة إدارة المستخدمين
                response = session.get('http://localhost:5000/admin/users')
                if response.status_code == 200:
                    print(f"   ✅ يمكن الوصول لصفحة إدارة المستخدمين")
                elif response.status_code == 403:
                    print(f"   ⚠️ ليس لديه صلاحية للوصول (طبيعي)")
                else:
                    print(f"   ❌ خطأ: {response.status_code}")
                
                # تسجيل الخروج
                session.get('http://localhost:5000/logout')
                
            else:
                print(f"   ❌ فشل تسجيل الدخول: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    
    # اختبار أساسي
    success = test_login_and_users_page()
    
    if success:
        # اختبار مستخدمين مختلفين
        test_different_users()
        
        print("\n📊 ملخص الاختبار:")
        print("✅ النظام يعمل بشكل صحيح")
        print("✅ صفحة إدارة المستخدمين تعمل")
        print("✅ تسجيل الدخول والخروج يعمل")
        print("✅ إنشاء المستخدمين يعمل")
        
        print("\n🔗 روابط مهمة:")
        print("   الصفحة الرئيسية: http://localhost:5000")
        print("   تسجيل الدخول: http://localhost:5000/login")
        print("   إدارة المستخدمين: http://localhost:5000/admin/users")
        print("   لوحة التحكم: http://localhost:5000/dashboard")
        
        print("\n👤 بيانات تسجيل الدخول:")
        print("   المدير: <EMAIL> / admin123")
        print("   مدير التدريب: <EMAIL> / manager123")
        print("   المدرب: <EMAIL> / trainer123")
        
    else:
        print("\n❌ فشل الاختبار. يرجى مراجعة الأخطاء أعلاه.")
        sys.exit(1)

if __name__ == '__main__':
    main()
