#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير شجرة النظام والصلاحيات المتقدم
Advanced System Tree and Permissions Manager
"""

import json
from datetime import datetime

class SystemTreeManager:
    """مدير شجرة النظام"""
    
    # شجرة النظام الكاملة مع جميع الشاشات والأزرار
    SYSTEM_TREE = {
        "dashboard": {
            "name": "لوحة التحكم",
            "icon": "fas fa-tachometer-alt",
            "permissions": {
                "view": "عرض لوحة التحكم",
                "export": "تصدير إحصائيات لوحة التحكم"
            }
        },
        "users_management": {
            "name": "إدارة المستخدمين",
            "icon": "fas fa-users",
            "permissions": {
                "view": "عرض المستخدمين",
                "add": "إضافة مستخدم جديد",
                "edit": "تعديل بيانات المستخدم",
                "delete": "حذف المستخدم",
                "reset_password": "إعادة تعيين كلمة المرور",
                "activate_deactivate": "تفعيل/إلغاء تفعيل المستخدم",
                "export": "تصدير قائمة المستخدمين",
                "import": "استيراد المستخدمين",
                "print": "طباعة قائمة المستخدمين"
            }
        },
        "roles_management": {
            "name": "إدارة الأدوار والصلاحيات",
            "icon": "fas fa-shield-alt",
            "permissions": {
                "view": "عرض الأدوار",
                "add": "إضافة دور جديد",
                "edit": "تعديل الدور",
                "delete": "حذف الدور",
                "assign_permissions": "تعيين الصلاحيات للدور",
                "export": "تصدير الأدوار",
                "print": "طباعة الأدوار"
            }
        },
        "courses_management": {
            "name": "إدارة الدورات",
            "icon": "fas fa-graduation-cap",
            "permissions": {
                "view": "عرض الدورات",
                "add": "إضافة دورة جديدة",
                "edit": "تعديل الدورة",
                "delete": "حذف الدورة",
                "duplicate": "نسخ الدورة",
                "export": "تصدير الدورات",
                "import": "استيراد الدورات",
                "print": "طباعة الدورات"
            },
            "sub_modules": {
                "course_participants": {
                    "name": "إدارة المشاركين",
                    "icon": "fas fa-user-friends",
                    "permissions": {
                        "view": "عرض المشاركين",
                        "add": "إضافة مشارك",
                        "edit": "تعديل بيانات المشارك",
                        "delete": "حذف المشارك",
                        "export": "تصدير المشاركين",
                        "print": "طباعة قائمة المشاركين",
                        "send_certificates": "إرسال الشهادات"
                    }
                },
                "course_schedule": {
                    "name": "جدولة الدورات",
                    "icon": "fas fa-calendar-alt",
                    "permissions": {
                        "view": "عرض الجدول",
                        "add": "إضافة موعد",
                        "edit": "تعديل الموعد",
                        "delete": "حذف الموعد",
                        "export": "تصدير الجدول",
                        "print": "طباعة الجدول"
                    }
                }
            }
        },
        "persons_management": {
            "name": "إدارة بيانات الأشخاص",
            "icon": "fas fa-address-book",
            "permissions": {
                "view": "عرض بيانات الأشخاص",
                "add": "إضافة شخص جديد",
                "edit": "تعديل بيانات الشخص",
                "delete": "حذف الشخص",
                "export": "تصدير البيانات",
                "import": "استيراد البيانات",
                "print": "طباعة البيانات",
                "advanced_search": "البحث المتقدم"
            }
        },
        "reports": {
            "name": "التقارير",
            "icon": "fas fa-chart-bar",
            "permissions": {
                "view": "عرض التقارير",
                "export": "تصدير التقارير",
                "print": "طباعة التقارير",
                "schedule": "جدولة التقارير"
            },
            "sub_modules": {
                "users_reports": {
                    "name": "تقارير المستخدمين",
                    "icon": "fas fa-users",
                    "permissions": {
                        "view": "عرض تقارير المستخدمين",
                        "export": "تصدير تقارير المستخدمين",
                        "print": "طباعة تقارير المستخدمين"
                    }
                },
                "courses_reports": {
                    "name": "تقارير الدورات",
                    "icon": "fas fa-graduation-cap",
                    "permissions": {
                        "view": "عرض تقارير الدورات",
                        "export": "تصدير تقارير الدورات",
                        "print": "طباعة تقارير الدورات"
                    }
                },
                "financial_reports": {
                    "name": "التقارير المالية",
                    "icon": "fas fa-dollar-sign",
                    "permissions": {
                        "view": "عرض التقارير المالية",
                        "export": "تصدير التقارير المالية",
                        "print": "طباعة التقارير المالية"
                    }
                }
            }
        },
        "reference_tables": {
            "name": "الجداول الترميزية",
            "icon": "fas fa-table",
            "permissions": {
                "view": "عرض الجداول الترميزية",
                "add": "إضافة جدول ترميزي",
                "edit": "تعديل الجدول الترميزي",
                "delete": "حذف الجدول الترميزي",
                "export": "تصدير الجداول",
                "print": "طباعة الجداول"
            },
            "sub_modules": {
                "course_paths": {
                    "name": "مسارات التدريب",
                    "icon": "fas fa-route",
                    "permissions": {
                        "view": "عرض المسارات",
                        "add": "إضافة مسار",
                        "edit": "تعديل المسار",
                        "delete": "حذف المسار"
                    }
                },
                "locations": {
                    "name": "المواقع",
                    "icon": "fas fa-map-marker-alt",
                    "permissions": {
                        "view": "عرض المواقع",
                        "add": "إضافة موقع",
                        "edit": "تعديل الموقع",
                        "delete": "حذف الموقع"
                    }
                }
            }
        },
        "system_settings": {
            "name": "إعدادات النظام",
            "icon": "fas fa-cog",
            "permissions": {
                "view": "عرض الإعدادات",
                "edit": "تعديل الإعدادات",
                "backup": "إنشاء نسخة احتياطية",
                "restore": "استعادة نسخة احتياطية",
                "logs": "عرض سجلات النظام",
                "maintenance": "صيانة النظام"
            }
        }
    }
    
    @staticmethod
    def get_system_tree():
        """الحصول على شجرة النظام الكاملة"""
        return SystemTreeManager.SYSTEM_TREE
    
    @staticmethod
    def get_all_permissions():
        """الحصول على جميع الصلاحيات في النظام"""
        permissions = {}
        
        def extract_permissions(modules, parent_key=""):
            for module_key, module_data in modules.items():
                module_permissions = module_data.get('permissions', {})
                
                for perm_key, perm_name in module_permissions.items():
                    full_key = f"{module_key}.{perm_key}"
                    permissions[full_key] = {
                        'name': perm_name,
                        'module': module_data['name'],
                        'module_key': module_key
                    }
                
                # معالجة الوحدات الفرعية
                if 'sub_modules' in module_data:
                    extract_permissions(module_data['sub_modules'], module_key)
        
        extract_permissions(SystemTreeManager.SYSTEM_TREE)
        return permissions
    
    @staticmethod
    def get_permissions_by_module(module_key):
        """الحصول على صلاحيات وحدة معينة"""
        tree = SystemTreeManager.SYSTEM_TREE
        
        if module_key in tree:
            return tree[module_key].get('permissions', {})
        
        # البحث في الوحدات الفرعية
        for main_module in tree.values():
            if 'sub_modules' in main_module and module_key in main_module['sub_modules']:
                return main_module['sub_modules'][module_key].get('permissions', {})
        
        return {}
    
    @staticmethod
    def create_role_permissions_structure():
        """إنشاء هيكل صلاحيات الدور للحفظ في قاعدة البيانات"""
        structure = {}
        
        def build_structure(modules, parent_key=""):
            for module_key, module_data in modules.items():
                structure[module_key] = {
                    'name': module_data['name'],
                    'icon': module_data['icon'],
                    'permissions': {},
                    'sub_modules': {}
                }
                
                # إضافة الصلاحيات
                for perm_key, perm_name in module_data.get('permissions', {}).items():
                    structure[module_key]['permissions'][perm_key] = {
                        'name': perm_name,
                        'enabled': False  # افتراضياً معطل
                    }
                
                # معالجة الوحدات الفرعية
                if 'sub_modules' in module_data:
                    for sub_key, sub_data in module_data['sub_modules'].items():
                        structure[module_key]['sub_modules'][sub_key] = {
                            'name': sub_data['name'],
                            'icon': sub_data['icon'],
                            'permissions': {}
                        }
                        
                        for perm_key, perm_name in sub_data.get('permissions', {}).items():
                            structure[module_key]['sub_modules'][sub_key]['permissions'][perm_key] = {
                                'name': perm_name,
                                'enabled': False
                            }
        
        build_structure(SystemTreeManager.SYSTEM_TREE)
        return structure
    
    @staticmethod
    def get_role_permissions_json(role_permissions):
        """تحويل صلاحيات الدور إلى JSON للحفظ"""
        return json.dumps(role_permissions, ensure_ascii=False, indent=2)
    
    @staticmethod
    def parse_role_permissions_json(json_string):
        """تحليل JSON صلاحيات الدور"""
        try:
            return json.loads(json_string)
        except:
            return SystemTreeManager.create_role_permissions_structure()

if __name__ == '__main__':
    # اختبار النظام
    tree = SystemTreeManager.get_system_tree()
    permissions = SystemTreeManager.get_all_permissions()
    
    print("🌳 شجرة النظام:")
    for module_key, module_data in tree.items():
        print(f"📁 {module_data['name']} ({module_key})")
        for perm_key, perm_name in module_data.get('permissions', {}).items():
            print(f"   🔘 {perm_name} ({perm_key})")
        
        if 'sub_modules' in module_data:
            for sub_key, sub_data in module_data['sub_modules'].items():
                print(f"   📂 {sub_data['name']} ({sub_key})")
                for perm_key, perm_name in sub_data.get('permissions', {}).items():
                    print(f"      🔘 {perm_name} ({perm_key})")
    
    print(f"\n📊 إجمالي الصلاحيات: {len(permissions)}")
