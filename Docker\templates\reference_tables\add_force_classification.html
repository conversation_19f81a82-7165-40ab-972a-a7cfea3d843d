{% extends "layout.html" %}
{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">إضافة تصنيف قوة جديد</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                {{ form.hidden_tag() }}
                <div class="form-group">
                    {{ form.name.label(class="form-control-label") }}
                    {% if form.name.errors %}
                        {{ form.name(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.name.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.name(class="form-control") }}
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.description.label(class="form-control-label") }}
                    {% if form.description.errors %}
                        {{ form.description(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.description(class="form-control") }}
                    {% endif %}
                </div>
                <div class="form-group mt-4">
                    {{ form.submit(class="btn btn-primary") }}
                    <a href="{{ url_for('force_classifications') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
