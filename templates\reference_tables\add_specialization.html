{% extends "layout.html" %}

{% block styles %}
<style>
    .form-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .form-header {
        background-color: #28a745;
        color: white;
        padding: 20px;
    }

    .form-title {
        margin: 0;
        font-size: 1.5rem;
    }

    .form-body {
        padding: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-control {
        border-radius: 5px;
        border: 1px solid #ced4da;
        padding: 10px 15px;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .form-label {
        font-weight: bold;
        margin-bottom: 8px;
    }

    .btn-submit {
        background-color: #28a745;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        transition: all 0.3s;
    }

    .btn-submit:hover {
        background-color: #218838;
        transform: translateY(-2px);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    .btn-cancel {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        transition: all 0.3s;
    }

    .btn-cancel:hover {
        background-color: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('reference_tables') }}">الجداول الترميزية</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('specializations') }}">التخصصات</a></li>
            <li class="breadcrumb-item active" aria-current="page">إضافة تخصص</li>
        </ol>
    </nav>

    <div class="form-card">
        <div class="form-header">
            <h3 class="form-title">إضافة تخصص جديد</h3>
        </div>
        <div class="form-body">
            <form method="POST" action="">
                {{ form.hidden_tag() }}
                <div class="form-group">
                    {{ form.name.label(class="form-label") }}
                    {% if form.name.errors %}
                        {{ form.name(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.name.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.name(class="form-control") }}
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.description.label(class="form-label") }}
                    {% if form.description.errors %}
                        {{ form.description(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.description(class="form-control", rows=3) }}
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.field.label(class="form-label") }}
                    {% if form.field.errors %}
                        {{ form.field(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.field.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.field(class="form-control") }}
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.code.label(class="form-label") }}
                    {% if form.code.errors %}
                        {{ form.code(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.code.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.code(class="form-control") }}
                    {% endif %}
                </div>
                <div class="form-group d-flex justify-content-between">
                    <a href="{{ url_for('specializations') }}" class="btn btn-cancel">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                    {{ form.submit(class="btn btn-submit") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
