('E:\\app\\TRINING\\TRINING_EXE\\build\\trining_exe\\trining_exe.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\app\\TRINING\\TRINING_EXE\\build\\trining_exe\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\app\\TRINING\\TRINING_EXE\\build\\trining_exe\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\app\\TRINING\\TRINING_EXE\\build\\trining_exe\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\app\\TRINING\\TRINING_EXE\\build\\trining_exe\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\app\\TRINING\\TRINING_EXE\\build\\trining_exe\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\app\\TRINING\\TRINING_EXE\\build\\trining_exe\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('app', 'E:\\app\\TRINING\\TRINING_EXE\\app.py', 'PYSOURCE'),
  ('python313.dll', 'C:\\Python313\\python313.dll', 'BINARY'),
  ('_decimal.pyd', 'C:\\Python313\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python313\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python313\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python313\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python313\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python313\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python313\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python313\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'C:\\Python313\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Python313\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python313\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\Python313\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Python313\\DLLs\\libffi-8.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python313\\VCRUNTIME140_1.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Python313\\DLLs\\sqlite3.dll', 'BINARY'),
  ('database\\training_system.db',
   'E:\\app\\TRINING\\TRINING_EXE\\database\\training_system.db',
   'DATA'),
  ('static\\css\\dashboard-style.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\dashboard-style.css',
   'DATA'),
  ('static\\css\\dashboard.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\dashboard.css',
   'DATA'),
  ('static\\css\\login.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\login.css',
   'DATA'),
  ('static\\css\\main.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\main.css',
   'DATA'),
  ('static\\css\\reports.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\reports.css',
   'DATA'),
  ('static\\css\\smart-search.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\css\\smart-search.css',
   'DATA'),
  ('static\\img\\README.md',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\README.md',
   'DATA'),
  ('static\\img\\default_course.jpg',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\default_course.jpg',
   'DATA'),
  ('static\\img\\training-bg.jpg',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\training-bg.jpg',
   'DATA'),
  ('static\\img\\user-avatar.png',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\img\\user-avatar.png',
   'DATA'),
  ('static\\js\\main.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\js\\main.js',
   'DATA'),
  ('static\\js\\smart-search.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\js\\smart-search.js',
   'DATA'),
  ('static\\libs\\bootstrap\\bootstrap.bundle.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\bootstrap\\bootstrap.bundle.min.js',
   'DATA'),
  ('static\\libs\\bootstrap\\bootstrap.rtl.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\bootstrap\\bootstrap.rtl.min.css',
   'DATA'),
  ('static\\libs\\chartjs\\chart.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\chartjs\\chart.min.js',
   'DATA'),
  ('static\\libs\\datatables\\buttons.bootstrap5.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.bootstrap5.min.css',
   'DATA'),
  ('static\\libs\\datatables\\buttons.bootstrap5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.bootstrap5.min.js',
   'DATA'),
  ('static\\libs\\datatables\\buttons.html5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.html5.min.js',
   'DATA'),
  ('static\\libs\\datatables\\buttons.print.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\buttons.print.min.js',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.bootstrap5.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.bootstrap5.min.css',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.bootstrap5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.bootstrap5.min.js',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.buttons.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.buttons.min.js',
   'DATA'),
  ('static\\libs\\datatables\\dataTables.responsive.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\dataTables.responsive.min.js',
   'DATA'),
  ('static\\libs\\datatables\\jquery.dataTables.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\jquery.dataTables.min.js',
   'DATA'),
  ('static\\libs\\datatables\\responsive.bootstrap5.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\responsive.bootstrap5.min.css',
   'DATA'),
  ('static\\libs\\datatables\\responsive.bootstrap5.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\datatables\\responsive.bootstrap5.min.js',
   'DATA'),
  ('static\\libs\\devextreme\\dx.light.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\devextreme\\dx.light.css',
   'DATA'),
  ('static\\libs\\fontawesome\\all.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\all.min.css',
   'DATA'),
  ('static\\libs\\fontawesome\\webfonts\\fa-brands-400.woff2',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\webfonts\\fa-brands-400.woff2',
   'DATA'),
  ('static\\libs\\fontawesome\\webfonts\\fa-regular-400.woff2',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\webfonts\\fa-regular-400.woff2',
   'DATA'),
  ('static\\libs\\fontawesome\\webfonts\\fa-solid-900.woff2',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\fontawesome\\webfonts\\fa-solid-900.woff2',
   'DATA'),
  ('static\\libs\\jquery\\jquery-3.6.0.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\jquery\\jquery-3.6.0.min.js',
   'DATA'),
  ('static\\libs\\libs_config.py',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\libs_config.py',
   'DATA'),
  ('static\\libs\\other\\jspdf.umd.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\other\\jspdf.umd.min.js',
   'DATA'),
  ('static\\libs\\other\\jszip.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\other\\jszip.min.js',
   'DATA'),
  ('static\\libs\\other\\xlsx.full.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\other\\xlsx.full.min.js',
   'DATA'),
  ('static\\libs\\select2\\select2-bootstrap-5-theme.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\select2\\select2-bootstrap-5-theme.min.css',
   'DATA'),
  ('static\\libs\\select2\\select2.min.css',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\select2\\select2.min.css',
   'DATA'),
  ('static\\libs\\select2\\select2.min.js',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\libs\\select2\\select2.min.js',
   'DATA'),
  ('static\\participants_complete_sample.xlsx',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\participants_complete_sample.xlsx',
   'DATA'),
  ('static\\participants_sample.xlsx',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\participants_sample.xlsx',
   'DATA'),
  ('static\\uploads\\courses\\ibb2.bmp',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\courses\\ibb2.bmp',
   'DATA'),
  ('static\\uploads\\courses\\images.jpeg',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\courses\\images.jpeg',
   'DATA'),
  ('static\\uploads\\materials\\1\\22-_720P_HD.mp4',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\materials\\1\\22-_720P_HD.mp4',
   'DATA'),
  ('static\\uploads\\materials\\1\\46_ERP720P_HD.mp4',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\materials\\1\\46_ERP720P_HD.mp4',
   'DATA'),
  ('static\\uploads\\materials\\1\\720P_HD.mp4',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\uploads\\materials\\1\\720P_HD.mp4',
   'DATA'),
  ('static\\welcome.html',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\welcome.html',
   'DATA'),
  ('static\\قالب_بيانات_الأشخاص_نموذج.xlsx',
   'E:\\app\\TRINING\\TRINING_EXE\\static\\قالب_بيانات_الأشخاص_نموذج.xlsx',
   'DATA'),
  ('templates\\add_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\add_course.html',
   'DATA'),
  ('templates\\add_course_participant.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\add_course_participant.html',
   'DATA'),
  ('templates\\add_material.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\add_material.html',
   'DATA'),
  ('templates\\backup\\backup.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\backup\\backup.html',
   'DATA'),
  ('templates\\backup\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\backup\\index.html',
   'DATA'),
  ('templates\\batches.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\batches.html',
   'DATA'),
  ('templates\\batches\\add_batch.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\batches\\add_batch.html',
   'DATA'),
  ('templates\\batches\\edit_batch.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\batches\\edit_batch.html',
   'DATA'),
  ('templates\\course_details.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_details.html',
   'DATA'),
  ('templates\\course_import_detailed_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_import_detailed_results.html',
   'DATA'),
  ('templates\\course_import_participants.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_import_participants.html',
   'DATA'),
  ('templates\\course_import_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_import_results.html',
   'DATA'),
  ('templates\\course_participants.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_participants.html',
   'DATA'),
  ('templates\\course_participants_with_evaluation.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_participants_with_evaluation.html',
   'DATA'),
  ('templates\\course_reports.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_reports.html',
   'DATA'),
  ('templates\\course_schedule.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_schedule.html',
   'DATA'),
  ('templates\\course_smart_analysis.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\course_smart_analysis.html',
   'DATA'),
  ('templates\\courses.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\courses.html',
   'DATA'),
  ('templates\\dashboard.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\dashboard.html',
   'DATA'),
  ('templates\\dynamic_evaluation_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\dynamic_evaluation_form.html',
   'DATA'),
  ('templates\\edit_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_course.html',
   'DATA'),
  ('templates\\edit_course_participant.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_course_participant.html',
   'DATA'),
  ('templates\\edit_material.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_material.html',
   'DATA'),
  ('templates\\edit_schedule_item.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\edit_schedule_item.html',
   'DATA'),
  ('templates\\evaluation_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\evaluation_form.html',
   'DATA'),
  ('templates\\evaluation_print.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\evaluation_print.html',
   'DATA'),
  ('templates\\graduates.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\graduates.html',
   'DATA'),
  ('templates\\hello.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\hello.html',
   'DATA'),
  ('templates\\home.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\home.html',
   'DATA'),
  ('templates\\home_new.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\home_new.html',
   'DATA'),
  ('templates\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\index.html',
   'DATA'),
  ('templates\\layout.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\layout.html',
   'DATA'),
  ('templates\\layout_local.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\layout_local.html',
   'DATA'),
  ('templates\\login.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\login.html',
   'DATA'),
  ('templates\\manage_course_participants.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\manage_course_participants.html',
   'DATA'),
  ('templates\\monthly_course_report.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\monthly_course_report.html',
   'DATA'),
  ('templates\\name_analysis_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\name_analysis_course.html',
   'DATA'),
  ('templates\\person_data.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data.html',
   'DATA'),
  ('templates\\person_data\\add_correction.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\add_correction.html',
   'DATA'),
  ('templates\\person_data\\duplicate_names.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\duplicate_names.html',
   'DATA'),
  ('templates\\person_data\\excel_management.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\excel_management.html',
   'DATA'),
  ('templates\\person_data\\manage_corrections.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\manage_corrections.html',
   'DATA'),
  ('templates\\person_data\\name_analysis.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_backup_before_enhancement.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_backup_before_enhancement.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results_backup.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results_backup.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results_backup_before_charts.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results_backup_before_charts.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_data_results_backup_before_font.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_data_results_backup_before_font.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_evaluation_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_evaluation_results.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_evaluation_results_backup_before_charts.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_evaluation_results_backup_before_charts.html',
   'DATA'),
  ('templates\\person_data\\name_analysis_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\name_analysis_results.html',
   'DATA'),
  ('templates\\person_data\\test_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data\\test_form.html',
   'DATA'),
  ('templates\\person_data_dashboard.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data_dashboard.html',
   'DATA'),
  ('templates\\person_data_table.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data_table.html',
   'DATA'),
  ('templates\\person_data_table_new.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\person_data_table_new.html',
   'DATA'),
  ('templates\\personal_data\\add.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\add.html',
   'DATA'),
  ('templates\\personal_data\\add_course.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\add_course.html',
   'DATA'),
  ('templates\\personal_data\\advanced_excel_view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\advanced_excel_view.html',
   'DATA'),
  ('templates\\personal_data\\compare_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\compare_results.html',
   'DATA'),
  ('templates\\personal_data\\devextreme_view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\devextreme_view.html',
   'DATA'),
  ('templates\\personal_data\\excel_devextreme.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\excel_devextreme.html',
   'DATA'),
  ('templates\\personal_data\\excel_view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\excel_view.html',
   'DATA'),
  ('templates\\personal_data\\existing_records.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\existing_records.html',
   'DATA'),
  ('templates\\personal_data\\import_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\import_results.html',
   'DATA'),
  ('templates\\personal_data\\list.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\list.html',
   'DATA'),
  ('templates\\personal_data\\new_add.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\new_add.html',
   'DATA'),
  ('templates\\personal_data\\view.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\personal_data\\view.html',
   'DATA'),
  ('templates\\reference_tables\\add_card_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_card_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_category.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_category.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_level.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_path.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_path.html',
   'DATA'),
  ('templates\\reference_tables\\add_course_path_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_course_path_level.html',
   'DATA'),
  ('templates\\reference_tables\\add_department.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_department.html',
   'DATA'),
  ('templates\\reference_tables\\add_directorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_directorate.html',
   'DATA'),
  ('templates\\reference_tables\\add_force_classification.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_force_classification.html',
   'DATA'),
  ('templates\\reference_tables\\add_governorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_governorate.html',
   'DATA'),
  ('templates\\reference_tables\\add_injury_cause.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_injury_cause.html',
   'DATA'),
  ('templates\\reference_tables\\add_injury_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_injury_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_location.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_location.html',
   'DATA'),
  ('templates\\reference_tables\\add_military_rank.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_military_rank.html',
   'DATA'),
  ('templates\\reference_tables\\add_participant_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_participant_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_qualification_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_qualification_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_specialization.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_specialization.html',
   'DATA'),
  ('templates\\reference_tables\\add_training_center.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_training_center.html',
   'DATA'),
  ('templates\\reference_tables\\add_training_center_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_training_center_type.html',
   'DATA'),
  ('templates\\reference_tables\\add_village.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\add_village.html',
   'DATA'),
  ('templates\\reference_tables\\agencies.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\agencies.html',
   'DATA'),
  ('templates\\reference_tables\\agencies_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\agencies_tree.html',
   'DATA'),
  ('templates\\reference_tables\\blood_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\blood_types.html',
   'DATA'),
  ('templates\\reference_tables\\card_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\card_types.html',
   'DATA'),
  ('templates\\reference_tables\\course_categories.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_categories.html',
   'DATA'),
  ('templates\\reference_tables\\course_levels.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_levels.html',
   'DATA'),
  ('templates\\reference_tables\\course_path_levels.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_path_levels.html',
   'DATA'),
  ('templates\\reference_tables\\course_paths.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_paths.html',
   'DATA'),
  ('templates\\reference_tables\\course_paths_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\course_paths_tree.html',
   'DATA'),
  ('templates\\reference_tables\\departments.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\departments.html',
   'DATA'),
  ('templates\\reference_tables\\departments_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\departments_tree.html',
   'DATA'),
  ('templates\\reference_tables\\directorates.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\directorates.html',
   'DATA'),
  ('templates\\reference_tables\\edit_agency.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_agency.html',
   'DATA'),
  ('templates\\reference_tables\\edit_blood_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_blood_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_category.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_category.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_level.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_path.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_path.html',
   'DATA'),
  ('templates\\reference_tables\\edit_course_path_level.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_course_path_level.html',
   'DATA'),
  ('templates\\reference_tables\\edit_department.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_department.html',
   'DATA'),
  ('templates\\reference_tables\\edit_directorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_directorate.html',
   'DATA'),
  ('templates\\reference_tables\\edit_force_classification.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_force_classification.html',
   'DATA'),
  ('templates\\reference_tables\\edit_governorate.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_governorate.html',
   'DATA'),
  ('templates\\reference_tables\\edit_injury_cause.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_injury_cause.html',
   'DATA'),
  ('templates\\reference_tables\\edit_injury_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_injury_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_location.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_location.html',
   'DATA'),
  ('templates\\reference_tables\\edit_marital_status.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_marital_status.html',
   'DATA'),
  ('templates\\reference_tables\\edit_military_rank.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_military_rank.html',
   'DATA'),
  ('templates\\reference_tables\\edit_participant_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_participant_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_qualification_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_qualification_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_specialization.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_specialization.html',
   'DATA'),
  ('templates\\reference_tables\\edit_training_center.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_training_center.html',
   'DATA'),
  ('templates\\reference_tables\\edit_training_center_type.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_training_center_type.html',
   'DATA'),
  ('templates\\reference_tables\\edit_village.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\edit_village.html',
   'DATA'),
  ('templates\\reference_tables\\force_classifications.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\force_classifications.html',
   'DATA'),
  ('templates\\reference_tables\\governorates.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\governorates.html',
   'DATA'),
  ('templates\\reference_tables\\governorates_tree.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\governorates_tree.html',
   'DATA'),
  ('templates\\reference_tables\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\index.html',
   'DATA'),
  ('templates\\reference_tables\\injury_causes.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\injury_causes.html',
   'DATA'),
  ('templates\\reference_tables\\injury_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\injury_types.html',
   'DATA'),
  ('templates\\reference_tables\\locations.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\locations.html',
   'DATA'),
  ('templates\\reference_tables\\marital_statuses.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\marital_statuses.html',
   'DATA'),
  ('templates\\reference_tables\\military_ranks.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\military_ranks.html',
   'DATA'),
  ('templates\\reference_tables\\participant_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\participant_types.html',
   'DATA'),
  ('templates\\reference_tables\\qualification_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\qualification_types.html',
   'DATA'),
  ('templates\\reference_tables\\specializations.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\specializations.html',
   'DATA'),
  ('templates\\reference_tables\\training_center_types.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\training_center_types.html',
   'DATA'),
  ('templates\\reference_tables\\training_centers.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\training_centers.html',
   'DATA'),
  ('templates\\reference_tables\\villages.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reference_tables\\villages.html',
   'DATA'),
  ('templates\\reports.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reports.html',
   'DATA'),
  ('templates\\reports_dashboard.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\reports_dashboard.html',
   'DATA'),
  ('templates\\search_courses.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\search_courses.html',
   'DATA'),
  ('templates\\search_results.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\search_results.html',
   'DATA'),
  ('templates\\simple_data\\index.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_data\\index.html',
   'DATA'),
  ('templates\\simple_data_basic.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_data_basic.html',
   'DATA'),
  ('templates\\simple_evaluation_form.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_evaluation_form.html',
   'DATA'),
  ('templates\\simple_person_list.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_person_list.html',
   'DATA'),
  ('templates\\simple_test.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\simple_test.html',
   'DATA'),
  ('templates\\trainers.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\trainers.html',
   'DATA'),
  ('templates\\users.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\users.html',
   'DATA'),
  ('templates\\video_player.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\video_player.html',
   'DATA'),
  ('templates\\welcome.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome.html',
   'DATA'),
  ('templates\\welcome_direct.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_direct.html',
   'DATA'),
  ('templates\\welcome_final.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_final.html',
   'DATA'),
  ('templates\\welcome_new.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_new.html',
   'DATA'),
  ('templates\\welcome_simple.html',
   'E:\\app\\TRINING\\TRINING_EXE\\templates\\welcome_simple.html',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('flask-2.3.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA'),
  ('flask-2.3.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-2.3.3.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'DATA'),
  ('flask-2.3.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('base_library.zip',
   'E:\\app\\TRINING\\TRINING_EXE\\build\\trining_exe\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
