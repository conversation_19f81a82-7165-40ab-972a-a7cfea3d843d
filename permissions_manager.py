#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة الصلاحيات والأدوار
Permissions and Roles Management System
"""

from functools import wraps
from flask import request, jsonify, session, redirect, url_for, flash, abort
from flask_login import current_user
from datetime import datetime, timezone
import json

class PermissionsManager:
    """مدير الصلاحيات"""
    
    # تعريف جميع الصلاحيات في النظام
    PERMISSIONS = {
        # إدارة المستخدمين
        'users.view': 'عرض المستخدمين',
        'users.create': 'إنشاء مستخدمين جدد',
        'users.edit': 'تعديل بيانات المستخدمين',
        'users.delete': 'حذف المستخدمين',
        'users.manage_roles': 'إدارة أدوار المستخدمين',
        'users.manage_permissions': 'إدارة صلاحيات المستخدمين',
        'users.reset_password': 'إعادة تعيين كلمات المرور',
        'users.lock_unlock': 'قفل وإلغاء قفل المستخدمين',
        
        # إدارة الدورات
        'courses.view': 'عرض الدورات',
        'courses.create': 'إنشاء دورات جديدة',
        'courses.edit': 'تعديل الدورات',
        'courses.delete': 'حذف الدورات',
        'courses.manage_participants': 'إدارة المشاركين في الدورات',
        'courses.view_financial': 'عرض البيانات المالية للدورات',
        'courses.edit_financial': 'تعديل البيانات المالية للدورات',
        
        # إدارة بيانات الأشخاص
        'persons.view': 'عرض بيانات الأشخاص',
        'persons.create': 'إضافة بيانات أشخاص جدد',
        'persons.edit': 'تعديل بيانات الأشخاص',
        'persons.delete': 'حذف بيانات الأشخاص',
        'persons.import': 'استيراد بيانات الأشخاص',
        'persons.export': 'تصدير بيانات الأشخاص',
        
        # التقارير
        'reports.view': 'عرض التقارير',
        'reports.create': 'إنشاء تقارير جديدة',
        'reports.export': 'تصدير التقارير',
        'reports.advanced': 'التقارير المتقدمة',
        'reports.financial': 'التقارير المالية',
        
        # الجداول الترميزية
        'reference_tables.view': 'عرض الجداول الترميزية',
        'reference_tables.edit': 'تعديل الجداول الترميزية',
        'reference_tables.create': 'إنشاء جداول ترميزية جديدة',
        'reference_tables.delete': 'حذف الجداول الترميزية',
        
        # النسخ الاحتياطي
        'backup.create': 'إنشاء نسخ احتياطية',
        'backup.restore': 'استعادة النسخ الاحتياطية',
        'backup.download': 'تحميل النسخ الاحتياطية',
        'backup.delete': 'حذف النسخ الاحتياطية',
        
        # إعدادات النظام
        'system.settings': 'إدارة إعدادات النظام',
        'system.logs': 'عرض سجلات النظام',
        'system.maintenance': 'صيانة النظام',
        'system.database': 'إدارة قاعدة البيانات',
        
        # الصلاحيات والأدوار
        'roles.view': 'عرض الأدوار',
        'roles.create': 'إنشاء أدوار جديدة',
        'roles.edit': 'تعديل الأدوار',
        'roles.delete': 'حذف الأدوار',
        'permissions.view': 'عرض الصلاحيات',
        'permissions.manage': 'إدارة الصلاحيات',
    }
    
    # الأدوار الافتراضية
    DEFAULT_ROLES = {
        'admin': {
            'display_name': 'مدير النظام',
            'description': 'مدير النظام له جميع الصلاحيات',
            'permissions': list(PERMISSIONS.keys()),  # جميع الصلاحيات
            'is_system_role': True
        },
        'manager': {
            'display_name': 'مدير',
            'description': 'مدير له صلاحيات إدارية محدودة',
            'permissions': [
                'users.view', 'users.edit',
                'courses.view', 'courses.create', 'courses.edit', 'courses.manage_participants',
                'persons.view', 'persons.create', 'persons.edit', 'persons.import', 'persons.export',
                'reports.view', 'reports.create', 'reports.export',
                'reference_tables.view', 'reference_tables.edit',
                'backup.create', 'backup.download'
            ],
            'is_system_role': True
        },
        'trainer': {
            'display_name': 'مدرب',
            'description': 'مدرب له صلاحيات إدارة الدورات التي يدربها',
            'permissions': [
                'courses.view', 'courses.edit', 'courses.manage_participants',
                'persons.view', 'persons.create', 'persons.edit',
                'reports.view',
                'reference_tables.view'
            ],
            'is_system_role': True
        },
        'data_entry': {
            'display_name': 'مدخل بيانات',
            'description': 'مدخل بيانات له صلاحيات إدخال وتعديل البيانات',
            'permissions': [
                'courses.view',
                'persons.view', 'persons.create', 'persons.edit', 'persons.import',
                'reference_tables.view'
            ],
            'is_system_role': True
        },
        'viewer': {
            'display_name': 'مشاهد',
            'description': 'مشاهد له صلاحيات العرض فقط',
            'permissions': [
                'courses.view',
                'persons.view',
                'reports.view',
                'reference_tables.view'
            ],
            'is_system_role': True
        }
    }
    
    @staticmethod
    def init_permissions(app, db):
        """تهيئة الصلاحيات والأدوار الافتراضية"""
        try:
            # استيراد النماذج مباشرة
            import sqlite3
            conn = sqlite3.connect('training_system.db')
            cursor = conn.cursor()

            # إنشاء الصلاحيات
            for perm_name, perm_display in PermissionsManager.PERMISSIONS.items():
                cursor.execute("SELECT id FROM permission WHERE name = ?", (perm_name,))
                if not cursor.fetchone():
                    category = perm_name.split('.')[0]
                    cursor.execute('''
                        INSERT INTO permission (name, display_name, category, is_active, created_at)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (perm_name, perm_display, category, 1, datetime.now().isoformat()))

            # إنشاء الأدوار
            for role_name, role_data in PermissionsManager.DEFAULT_ROLES.items():
                cursor.execute("SELECT id FROM role WHERE name = ?", (role_name,))
                existing_role = cursor.fetchone()
                if not existing_role:
                    cursor.execute('''
                        INSERT INTO role (name, display_name, description, is_system_role, is_active, created_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (role_name, role_data['display_name'], role_data['description'],
                          role_data['is_system_role'], 1, datetime.now().isoformat()))

                    role_id = cursor.lastrowid

                    # إضافة صلاحيات الدور
                    for perm_name in role_data['permissions']:
                        cursor.execute('''
                            INSERT INTO role_permission (role_id, permission_name, is_active)
                            VALUES (?, ?, ?)
                        ''', (role_id, perm_name, 1))

            conn.commit()
            conn.close()
            print("✅ تم تهيئة الصلاحيات والأدوار بنجاح")

        except Exception as e:
            print(f"⚠️ خطأ في تهيئة الصلاحيات: {e}")
            if 'conn' in locals():
                conn.close()

def require_permission(permission_name):
    """ديكوريتر للتحقق من الصلاحيات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                if request.is_json:
                    return jsonify({'error': 'يجب تسجيل الدخول أولاً'}), 401
                flash('يجب تسجيل الدخول أولاً', 'error')
                return redirect(url_for('login'))
            
            if not current_user.has_permission(permission_name):
                if request.is_json:
                    return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الصفحة'}), 403
                flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_role(role_name):
    """ديكوريتر للتحقق من الأدوار"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                if request.is_json:
                    return jsonify({'error': 'يجب تسجيل الدخول أولاً'}), 401
                flash('يجب تسجيل الدخول أولاً', 'error')
                return redirect(url_for('login'))
            
            # فحص الدور
            has_role = False
            if current_user.role == role_name:  # الدور الأساسي
                has_role = True
            else:
                # فحص الأدوار الإضافية
                from app import UserRole, Role
                user_role = UserRole.query.filter_by(
                    user_id=current_user.id,
                    is_active=True
                ).join(Role).filter(Role.name == role_name, Role.is_active == True).first()
                if user_role:
                    has_role = True
            
            if not has_role:
                if request.is_json:
                    return jsonify({'error': 'ليس لديك الدور المطلوب للوصول إلى هذه الصفحة'}), 403
                flash('ليس لديك الدور المطلوب للوصول إلى هذه الصفحة', 'error')
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def log_user_action(action, resource_type=None, resource_id=None, details=None):
    """تسجيل عمليات المستخدم"""
    try:
        from app import db, AuditLog
        
        log_entry = AuditLog(
            user_id=current_user.id if current_user.is_authenticated else None,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=details,
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string
        )
        db.session.add(log_entry)
        db.session.commit()
    except Exception as e:
        print(f"خطأ في تسجيل العملية: {e}")
