{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<style>
    .analysis-card {
        border: 2px solid #007bff;
        border-radius: 15px;
        box-shadow: 0 4px 8px rgba(0,123,255,0.1);
        transition: all 0.3s ease;
    }

    .analysis-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0,123,255,0.2);
    }

    .course-selector {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
    }

    .course-card {
        background: white;
        color: #333;
        border-radius: 10px;
        padding: 15px;
        margin: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .course-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        border-color: #007bff;
    }

    .course-card.selected {
        border-color: #28a745;
        background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    }

    .stats-card {
        background: white;
        color: #333;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        margin-bottom: 20px;
    }

    .category-card {
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .person-item {
        background: white;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
        border: 1px solid #e0e0e0;
    }

    .field-display {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.85em;
        margin: 2px;
        display: inline-block;
    }

    .field-display.filled {
        background: #d4edda;
        border: 1px solid #28a745;
        color: #155724;
    }

    .btn-smart {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        padding: 12px 30px;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 25px;
        transition: all 0.3s ease;
        color: white;
    }

    .btn-smart:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        color: white;
    }

    .info-badge {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        margin: 5px;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- عنوان الصفحة -->
            <div class="text-center mb-4">
                <h1 class="display-4 text-primary mb-3">
                    <i class="fas fa-brain"></i> التحليل الذكي للدورات
                </h1>
                <p class="lead text-muted">
                    نظام ذكي لتحليل ومقايسة الأسماء مع قاعدة البيانات وإضافة المشاركين للدورات
                </p>
            </div>

            <!-- قسم رفع الملف -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-file-excel"></i> رفع ملف Excel للتحليل الذكي
                    </h3>
                </div>
                <div class="card-body">
                    <form id="smartImportForm" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

                        <div class="upload-area mb-4" id="uploadZone">
                            <div class="feature-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h4 class="text-primary mb-3">اختر ملف Excel للتحليل الذكي</h4>
                            <input type="file" class="form-control form-control-lg" id="excelFile" name="excel_file"
                                   accept=".xlsx,.xls" required style="max-width: 400px; margin: 0 auto;">
                            <p class="text-muted mt-3">
                                <i class="fas fa-info-circle"></i>
                                يجب أن يحتوي الملف على عمود "الاسم الشخصي" مع البيانات الكاملة
                            </p>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-smart btn-lg" id="smartAnalyzeBtn">
                                <i class="fas fa-brain"></i> بدء التحليل الذكي للدورات
                            </button>
                        </div>

                        <!-- مؤشر التقدم -->
                        <div class="mt-4" id="progressContainer" style="display: none;">
                            <div class="progress" style="height: 25px; border-radius: 15px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                                     role="progressbar" style="width: 0%" id="progressBar">
                                    <span id="progressText" style="font-weight: bold;">0%</span>
                                </div>
                            </div>
                            <p class="text-center mt-2 text-muted" id="progressMessage">جاري التحليل الذكي...</p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- اختيار الدورة -->
            <div class="course-selector">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">
                            <i class="fas fa-graduation-cap"></i> اختر الدورة لإضافة المشاركين
                        </h2>
                        <p class="mb-0">اختر الدورة التي تريد إضافة المشاركين إليها من القائمة أدناه</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="stats-card">
                            <h3 class="text-primary mb-2">{{ results.statistics.total_count }}</h3>
                            <p class="mb-0"><i class="fas fa-users text-primary"></i> إجمالي الأشخاص</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة الدورات -->
            <div class="row mb-4" id="courses-list">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>

            <!-- إحصائيات التحليل -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); border-left: 4px solid #28a745;">
                        <h3 class="text-success">{{ results.statistics.new_count }}</h3>
                        <p class="mb-0"><i class="fas fa-user-plus"></i> أشخاص جدد</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-left: 4px solid #007bff;">
                        <h3 class="text-primary">{{ results.statistics.existing_count }}</h3>
                        <p class="mb-0"><i class="fas fa-user-check"></i> موجودين في قاعدة البيانات</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border-left: 4px solid #ffc107;">
                        <h3 class="text-warning" id="available-count">-</h3>
                        <p class="mb-0"><i class="fas fa-user-times"></i> متاحين للإضافة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-left: 4px solid #dc3545;">
                        <h3 class="text-danger">{{ results.statistics.corrected_count }}</h3>
                        <p class="mb-0"><i class="fas fa-edit"></i> أسماء مصححة</p>
                    </div>
                </div>
            </div>

            <!-- أدوات التحكم الرئيسية -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0"><i class="fas fa-tools"></i> مركز التحكم الذكي</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                                        <button class="btn btn-success" onclick="selectAll()" style="border-radius: 20px; padding: 8px 16px;">
                                            <i class="fas fa-check-double"></i> اختيار الكل
                                        </button>
                                        <button class="btn btn-success" onclick="selectCategory('new_people')" style="border-radius: 20px; padding: 8px 16px;">
                                            <i class="fas fa-user-plus"></i> اختيار الجدد
                                        </button>
                                        <button class="btn btn-primary" onclick="selectCategory('existing_available')" style="border-radius: 20px; padding: 8px 16px;">
                                            <i class="fas fa-user-check"></i> اختيار المتاحين
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <button class="btn btn-success btn-lg" onclick="addSelectedToCourse()" id="addBtn" disabled>
                                        <i class="fas fa-plus-circle"></i> إضافة المختارين للدورة
                                    </button>
                                    <button class="btn btn-warning btn-lg ms-2" onclick="clearSelection()">
                                        <i class="fas fa-times-circle"></i> إلغاء الاختيار
                                    </button>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="alert alert-info" id="course-selection-alert">
                                        <i class="fas fa-info-circle"></i> يرجى اختيار دورة أولاً لتفعيل أزرار الإضافة
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عرض البيانات -->
            <div id="results-display">
                <!-- سيتم ملء هذا القسم بـ JavaScript -->
            </div>

            <!-- أزرار التنقل -->
            <div class="text-center mt-4">
                <a href="/name_analysis" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-arrow-right"></i> العودة للتحليل العادي
                </a>
                <a href="/person_data/" class="btn btn-outline-primary btn-lg ms-3">
                    <i class="fas fa-database"></i> إدارة البيانات
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
{% if results %}
// بيانات النتائج من الخادم
const results = {{ results | tojson }};
let selectedCourseId = null;
let availableCourses = [];

console.log('📊 Course Analysis Data:', results);

// تحميل قائمة الدورات
function loadCourses() {
    $.ajax({
        url: '/api/courses',
        type: 'GET',
        success: function(courses) {
            availableCourses = courses;
            displayCourses(courses);
        },
        error: function(xhr, status, error) {
            console.error('❌ Error loading courses:', error);
            // استخدام بيانات وهمية للاختبار
            const dummyCourses = [
                {id: 1, title: 'دورة البرمجة المتقدمة', start_date: '2024-01-15', duration: 30, location: 'صنعاء', participants_count: 25},
                {id: 2, title: 'دورة إدارة المشاريع', start_date: '2024-02-01', duration: 20, location: 'عدن', participants_count: 18},
                {id: 3, title: 'دورة التسويق الرقمي', start_date: '2024-02-15', duration: 15, location: 'تعز', participants_count: 12}
            ];
            displayCourses(dummyCourses);
        }
    });
}

// عرض قائمة الدورات
function displayCourses(courses) {
    let html = '';

    if (courses.length === 0) {
        html = `
            <div class="col-12">
                <div class="alert alert-warning text-center">
                    <h4><i class="fas fa-graduation-cap"></i> لا توجد دورات متاحة</h4>
                    <p>يرجى إنشاء دورة أولاً قبل إضافة المشاركين</p>
                    <a href="/course/add" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إنشاء دورة جديدة
                    </a>
                </div>
            </div>
        `;
    } else {
        courses.forEach(course => {
            const statusBadge = course.status === 'نشط' ?
                '<span class="badge bg-success">نشط</span>' :
                '<span class="badge bg-primary">مجدول</span>';

            const levelBadge = course.level ?
                `<span class="badge bg-info">${course.level}</span>` : '';

            html += `
                <div class="col-md-6 col-lg-4">
                    <div class="course-card" data-course-id="${course.id}" onclick="selectCourse(${course.id})">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="text-primary mb-0">
                                <i class="fas fa-graduation-cap"></i> ${course.title}
                            </h5>
                            ${statusBadge}
                        </div>

                        <div class="mb-2">
                            <small class="text-muted">${course.description || 'لا يوجد وصف'}</small>
                        </div>

                        <div class="row mb-2">
                            <div class="col-6">
                                <small class="text-muted">
                                    <i class="fas fa-hashtag"></i> ${course.course_number}
                                </small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">
                                    <i class="fas fa-layer-group"></i> ${course.category}
                                </small>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-6">
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> ${course.start_date || 'غير محدد'}
                                </small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i> ${course.duration_days || 0} أيام
                                </small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt"></i> ${course.location || 'غير محدد'}
                                </small>
                            </div>
                            <div class="col-6">
                                <small class="text-success">
                                    <i class="fas fa-users"></i> ${course.participants_count || 0} مشارك
                                </small>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-user-tie"></i> ${course.trainer}
                                </small>
                                ${levelBadge}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }

    $('#courses-list').html(html);
}

// اختيار دورة
function selectCourse(courseId) {
    selectedCourseId = courseId;

    // تحديث واجهة المستخدم
    $('.course-card').removeClass('selected');
    $(`.course-card[data-course-id="${courseId}"]`).addClass('selected');

    // تفعيل أزرار الإضافة
    $('#addBtn').prop('disabled', false);
    $('#course-selection-alert').hide();

    // تحديث الإحصائيات للدورة المختارة
    updateStatsForCourse(courseId);

    console.log('🎯 Selected course:', courseId);
}

// تحديث الإحصائيات للدورة المختارة
function updateStatsForCourse(courseId) {
    // محاكاة تحديث الإحصائيات
    const availableCount = Math.floor(Math.random() * 50) + 10;
    $('#available-count').text(availableCount);

    console.log('📊 Updated stats for course:', {
        courseId,
        availableToAdd: availableCount
    });
}

// دوال التحكم في الاختيار
function selectAll() {
    $('.person-checkbox').prop('checked', true);
    updateSelectionCount();
}

function selectCategory(category) {
    $('.person-checkbox').prop('checked', false);
    $(`.person-checkbox[data-category="${category}"]`).prop('checked', true);
    updateSelectionCount();
}

function clearSelection() {
    $('.person-checkbox').prop('checked', false);
    updateSelectionCount();
}

function updateSelectionCount() {
    const selectedCount = $('.person-checkbox:checked').length;
    console.log('📋 Selected count:', selectedCount);
}

// إضافة المختارين للدورة
function addSelectedToCourse() {
    if (!selectedCourseId) {
        alert('يرجى اختيار دورة أولاً');
        return;
    }

    const selectedNew = [];
    const selectedExisting = [];

    $('.person-checkbox:checked').each(function() {
        const category = $(this).data('category');
        const personId = $(this).data('person-id');
        const name = $(this).val();

        if (category === 'new_people') {
            // البحث عن بيانات الشخص الجديد
            const person = results.new_people[personId - 1];
            if (person) {
                selectedNew.push(person);
            }
        } else if (category === 'existing_available') {
            // البحث عن بيانات الشخص الموجود
            const person = results.existing_people.find(p => p.person_id == personId);
            if (person) {
                selectedExisting.push(personId);
            }
        }
    });

    if (selectedNew.length === 0 && selectedExisting.length === 0) {
        alert('يرجى اختيار أشخاص للإضافة أولاً');
        return;
    }

    console.log('🚀 Adding to course:', {
        courseId: selectedCourseId,
        new_people: selectedNew,
        existing_people: selectedExisting
    });

    // إرسال الطلب للخادم
    $.ajax({
        url: `/add_selected_to_course`,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            course_id: selectedCourseId,
            selected_new: selectedNew,
            selected_existing: selectedExisting
        }),
        success: function(response) {
            if (response.success) {
                alert(`✅ تم بنجاح!\n📊 تم إضافة ${response.results.added_new} شخص جديد\n👥 تم إضافة ${response.results.added_existing} شخص موجود\n📈 إجمالي المضافين: ${response.results.added_new + response.results.added_existing}`);

                // إعادة توجيه لصفحة إدارة المشاركين
                window.location.href = `/manage_participants/${selectedCourseId}/`;
            } else {
                alert('❌ خطأ: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ AJAX Error:', {xhr, status, error});
            alert('حدث خطأ في الإضافة: ' + error);
        }
    });
}

// عرض البيانات (نسخة من course_smart_analysis)
function displayResults() {
    let html = '';

    // الأشخاص الجدد
    if (results.new_people && results.new_people.length > 0) {
        html += `
        <div class="category-card" style="background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); border-left: 4px solid #28a745;">
            <h4 class="text-success mb-3">
                <i class="fas fa-user-plus"></i> أشخاص جدد (${results.statistics.new_count})
                <small class="text-muted">- سيتم إضافتهم لقاعدة البيانات والدورة</small>
            </h4>`;

        results.new_people.forEach((person, index) => {
            html += `
            <div class="person-item">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <input type="checkbox" class="form-check-input person-checkbox"
                               data-category="new_people" data-person-id="${index + 1}"
                               value="${person.name}">
                    </div>
                    <div class="col-md-3">
                        <strong>${person.name}</strong>
                        ${person.original_name ? `<br><small class="text-muted">الأصلي: ${person.original_name}</small>` : ''}
                    </div>
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                ${person.nickname ? `<span class="field-display filled">${person.nickname}</span>` : ''}
                                ${person.age ? `<span class="field-display filled">${person.age} سنة</span>` : ''}
                                ${person.governorate ? `<span class="field-display filled">${person.governorate}</span>` : ''}
                                ${person.directorate ? `<span class="field-display filled">${person.directorate}</span>` : ''}
                                ${person.village ? `<span class="field-display filled">${person.village}</span>` : ''}
                            </div>
                            <div class="col-md-6">
                                ${person.qualification ? `<span class="field-display filled">${person.qualification}</span>` : ''}
                                ${person.job ? `<span class="field-display filled">${person.job}</span>` : ''}
                                ${person.national_id ? `<span class="field-display filled">${person.national_id}</span>` : ''}
                                ${person.military_number ? `<span class="field-display filled">${person.military_number}</span>` : ''}
                                ${person.phone ? `<span class="field-display filled">${person.phone}</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>`;
        });

        html += '</div>';
    }

    // الأشخاص الموجودين (متاحين للإضافة)
    if (results.existing_people && results.existing_people.length > 0) {
        html += `
        <div class="category-card" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-left: 4px solid #007bff;">
            <h4 class="text-primary mb-3">
                <i class="fas fa-user-check"></i> موجودين في قاعدة البيانات (${results.statistics.existing_count})
                <small class="text-muted">- يمكن إضافتهم للدورة المختارة</small>
            </h4>`;

        results.existing_people.forEach((person) => {
            html += `
            <div class="person-item">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <input type="checkbox" class="form-check-input person-checkbox"
                               data-category="existing_available" data-person-id="${person.person_id}"
                               value="${person.name}">
                    </div>
                    <div class="col-md-3">
                        <strong>${person.name}</strong>
                        ${person.original_name ? `<br><small class="text-muted">الأصلي: ${person.original_name}</small>` : ''}
                    </div>
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                ${person.nickname ? `<span class="field-display filled">${person.nickname}</span>` : ''}
                                ${person.age ? `<span class="field-display filled">${person.age} سنة</span>` : ''}
                                ${person.governorate ? `<span class="field-display filled">${person.governorate}</span>` : ''}
                                ${person.directorate ? `<span class="field-display filled">${person.directorate}</span>` : ''}
                                ${person.village ? `<span class="field-display filled">${person.village}</span>` : ''}
                            </div>
                            <div class="col-md-6">
                                ${person.qualification ? `<span class="field-display filled">${person.qualification}</span>` : ''}
                                ${person.job ? `<span class="field-display filled">${person.job}</span>` : ''}
                                ${person.national_id ? `<span class="field-display filled">${person.national_id}</span>` : ''}
                                ${person.military_number ? `<span class="field-display filled">${person.military_number}</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>`;
        });

        html += '</div>';
    }

    // الأسماء المصححة
    if (results.corrected_names && results.corrected_names.length > 0) {
        html += `
        <div class="category-card" style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-left: 4px solid #dc3545;">
            <h4 class="text-danger mb-3">
                <i class="fas fa-edit"></i> أسماء تم تصحيحها (${results.statistics.corrected_count})
                <small class="text-muted">- تصحيحات تلقائية للأخطاء الإملائية</small>
            </h4>`;

        results.corrected_names.forEach((correction) => {
            html += `
            <div class="person-item">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <i class="fas fa-edit text-danger"></i>
                    </div>
                    <div class="col-md-5">
                        <span class="badge bg-danger">${correction.original}</span>
                    </div>
                    <div class="col-md-1 text-center">
                        <i class="fas fa-arrow-left text-success"></i>
                    </div>
                    <div class="col-md-5">
                        <span class="badge bg-success">${correction.corrected}</span>
                    </div>
                </div>
            </div>`;
        });

        html += '</div>';
    }

    $('#results-display').html(html);

    // تحديث عداد الاختيار عند تغيير أي checkbox
    $('.person-checkbox').change(function() {
        updateSelectionCount();
    });
}



// معالجة رفع الملف والتحليل الذكي
$('#smartImportForm').submit(function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const fileInput = $('#excelFile')[0];

    if (!fileInput.files.length) {
        alert('يرجى اختيار ملف Excel أولاً');
        return;
    }

    // تغيير حالة الزر
    $('#smartAnalyzeBtn').html('<i class="fas fa-spinner fa-spin"></i> جاري التحليل الذكي...');
    $('#smartAnalyzeBtn').prop('disabled', true);

    // إظهار مؤشر التقدم
    $('#progressContainer').show();

    // محاكاة تقدم التحليل
    let progress = 0;
    const interval = setInterval(function() {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;

        $('#progressBar').css('width', progress + '%');
        $('#progressText').text(Math.round(progress) + '%');

        if (progress >= 90) {
            clearInterval(interval);
            $('#progressMessage').text('جاري إنهاء التحليل...');
        }
    }, 500);

    // إرسال الطلب للتحليل الذكي
    $.ajax({
        url: '/person_data/name_analysis',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            clearInterval(interval);
            $('#progressBar').css('width', '100%');
            $('#progressText').text('100%');
            $('#progressMessage').text('تم التحليل بنجاح!');

            // إعادة تحميل الصفحة لعرض النتائج
            setTimeout(function() {
                location.reload();
            }, 1000);
        },
        error: function(xhr, status, error) {
            clearInterval(interval);
            console.error('❌ AJAX Error:', {xhr, status, error});
            alert('حدث خطأ في التحليل: ' + error);
            resetSmartForm();
        }
    });
});

function resetSmartForm() {
    $('#smartAnalyzeBtn').html('<i class="fas fa-brain"></i> بدء التحليل الذكي للدورات');
    $('#smartAnalyzeBtn').prop('disabled', false);
    $('#progressContainer').hide();
    $('#progressBar').css('width', '0%');
}

// تحسين تجربة رفع الملف
$('#excelFile').change(function() {
    var fileName = $(this).val().split('\\').pop();
    if (fileName) {
        $('.upload-area h4').text('تم اختيار: ' + fileName);
        $('.upload-area').addClass('border-success').removeClass('border-primary');
        $('.feature-icon i').removeClass('fa-cloud-upload-alt').addClass('fa-check-circle text-success');
    }
});

// تأثير السحب والإفلات
$('#uploadZone').on('dragover', function(e) {
    e.preventDefault();
    $(this).addClass('border-success');
});

$('#uploadZone').on('dragleave', function(e) {
    e.preventDefault();
    $(this).removeClass('border-success');
});

$('#uploadZone').on('drop', function(e) {
    e.preventDefault();
    $(this).removeClass('border-success');

    const files = e.originalEvent.dataTransfer.files;
    if (files.length > 0) {
        $('#excelFile')[0].files = files;
        $('#excelFile').trigger('change');
    }
});

$(document).ready(function() {
    loadCourses();
    displayResults();
    console.log('✅ Course Smart Analysis page loaded');
    console.log('📊 Statistics:', results.statistics);
});

{% endif %}
</script>
{% endblock %}