import os
import shutil
import sqlite3
import datetime
import zipfile
import threading
import time
import atexit
import signal
import sys
from flask import current_app
import logging

def create_backup(backup_path=None, backup_name=None, include_personal_data=True, include_courses=True, include_uploads=True):
    """
    إنشاء نسخة احتياطية من قاعدة البيانات والملفات المرفقة

    Args:
        backup_path (str): مسار حفظ النسخة الاحتياطية (اختياري)
        backup_name (str): اسم ملف النسخة الاحتياطية (اختياري)
        include_personal_data (bool): تضمين البيانات الشخصية
        include_courses (bool): تضمين بيانات الدورات
        include_uploads (bool): تضمين الملفات المرفقة

    Returns:
        tuple: (نجاح العملية, رسالة, مسار الملف)
    """
    try:
        # الحصول على مسار قاعدة البيانات الحالية
        db_uri = current_app.config['SQLALCHEMY_DATABASE_URI']
        db_path = db_uri.replace('sqlite:///', '')

        # إذا كان المسار نسبيًا، قم بتحويله إلى مسار مطلق
        if not os.path.isabs(db_path):
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)

        # التأكد من وجود قاعدة البيانات
        if not os.path.exists(db_path):
            return False, "قاعدة البيانات غير موجودة", None

        # إنشاء مسار النسخة الاحتياطية إذا لم يتم تحديده
        if not backup_path:
            backup_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجودًا
        os.makedirs(backup_path, exist_ok=True)

        # إنشاء اسم الملف إذا لم يتم تحديده
        if not backup_name:
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"backup_{timestamp}.zip"
        elif not backup_name.endswith('.zip'):
            backup_name += '.zip'

        # المسار الكامل للنسخة الاحتياطية
        backup_file = os.path.join(backup_path, backup_name)

        # إنشاء نسخة احتياطية مضغوطة
        with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # إضافة قاعدة البيانات
            zipf.write(db_path, os.path.basename(db_path))

            # إضافة ملف معلومات النسخة الاحتياطية
            backup_info = {
                'timestamp': datetime.datetime.now().strftime('%Y%m%d_%H%M%S'),
                'date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'options': {
                    'include_personal_data': include_personal_data,
                    'include_courses': include_courses,
                    'include_uploads': include_uploads
                }
            }
            zipf.writestr('backup_info.json', str(backup_info))

            # إضافة الملفات المرفقة إذا تم تحديد ذلك
            if include_uploads:
                uploads_dir = os.path.join(current_app.root_path, 'static', 'uploads')
                if os.path.exists(uploads_dir):
                    for root, dirs, files in os.walk(uploads_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, current_app.root_path)
                            zipf.write(file_path, arcname)

        return True, "تم إنشاء النسخة الاحتياطية بنجاح", backup_file

    except Exception as e:
        return False, f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}", None

def restore_backup(backup_file):
    """
    استعادة قاعدة البيانات والملفات المرفقة من نسخة احتياطية

    Args:
        backup_file (str): مسار ملف النسخة الاحتياطية

    Returns:
        tuple: (نجاح العملية, رسالة)
    """
    try:
        # التأكد من وجود ملف النسخة الاحتياطية
        if not os.path.exists(backup_file):
            return False, "ملف النسخة الاحتياطية غير موجود"

        # الحصول على مسار قاعدة البيانات الحالية
        db_uri = current_app.config['SQLALCHEMY_DATABASE_URI']
        db_path = db_uri.replace('sqlite:///', '')

        # إذا كان المسار نسبيًا، قم بتحويله إلى مسار مطلق
        if not os.path.isabs(db_path):
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)

        # إنشاء مجلد مؤقت لاستخراج النسخة الاحتياطية
        temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp_backup')
        os.makedirs(temp_dir, exist_ok=True)

        # استخراج النسخة الاحتياطية
        with zipfile.ZipFile(backup_file, 'r') as zipf:
            # قراءة معلومات النسخة الاحتياطية
            backup_info = {}
            if 'backup_info.json' in zipf.namelist():
                try:
                    backup_info_str = zipf.read('backup_info.json').decode('utf-8')
                    backup_info = eval(backup_info_str)
                except:
                    pass

            # استخراج جميع الملفات
            zipf.extractall(temp_dir)

        # الحصول على اسم ملف قاعدة البيانات
        db_name = os.path.basename(db_path)
        extracted_db = os.path.join(temp_dir, db_name)

        # التأكد من وجود الملف المستخرج
        if not os.path.exists(extracted_db):
            shutil.rmtree(temp_dir)
            return False, "ملف قاعدة البيانات غير موجود في النسخة الاحتياطية"

        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
        if os.path.exists(db_path):
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_before_restore = f"{db_path}.{timestamp}.bak"
            shutil.copy2(db_path, backup_before_restore)

        # استبدال قاعدة البيانات الحالية بالنسخة المستعادة
        shutil.copy2(extracted_db, db_path)

        # حذف المجلد المؤقت
        shutil.rmtree(temp_dir)

        return True, "تم استعادة قاعدة البيانات بنجاح"

    except Exception as e:
        return False, f"حدث خطأ أثناء استعادة قاعدة البيانات: {str(e)}"

def list_backups(backup_path=None):
    """
    الحصول على قائمة النسخ الاحتياطية المتوفرة

    Args:
        backup_path (str): مسار مجلد النسخ الاحتياطية (اختياري)

    Returns:
        list: قائمة بملفات النسخ الاحتياطية
    """
    try:
        # إنشاء مسار النسخة الاحتياطية إذا لم يتم تحديده
        if not backup_path:
            backup_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')

        # التأكد من وجود المجلد
        if not os.path.exists(backup_path):
            return []

        # الحصول على قائمة ملفات النسخ الاحتياطية
        backups = []
        for file in os.listdir(backup_path):
            if file.endswith('.zip'):
                file_path = os.path.join(backup_path, file)
                file_size = os.path.getsize(file_path)
                file_date = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))

                # محاولة قراءة معلومات إضافية من النسخة الاحتياطية
                backup_info = None
                try:
                    import zipfile
                    with zipfile.ZipFile(file_path, 'r') as zipf:
                        if 'backup_info.json' in zipf.namelist():
                            backup_info_str = zipf.read('backup_info.json').decode('utf-8')
                            backup_info = eval(backup_info_str)
                except:
                    pass

                backups.append({
                    'name': file,
                    'path': file_path,
                    'size': file_size,
                    'date': file_date,
                    'formatted_date': file_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'formatted_date_ar': file_date.strftime('%d/%m/%Y الساعة %H:%M'),
                    'info': backup_info
                })

        # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x['date'], reverse=True)

        return backups

    except Exception as e:
        print(f"حدث خطأ أثناء قراءة النسخ الاحتياطية: {str(e)}")
        return []

# ===== نظام النسخ الاحتياطي الذكي والتلقائي =====

class SmartBackupManager:
    """
    مدير النسخ الاحتياطي الذكي والتلقائي
    يقوم بإنشاء نسخ احتياطية تلقائية دون تدخل المستخدم
    """

    def __init__(self, app=None, config=None):
        self.app = app
        self.backup_thread = None
        self.stop_backup = False

        # تحميل الإعدادات
        if config is None:
            try:
                from smart_backup_config import current_config
                self.config = current_config
            except ImportError:
                # إعدادات افتراضية إذا لم يتم العثور على ملف الإعدادات
                class DefaultConfig:
                    BACKUP_INTERVAL_MINUTES = 30
                    MAX_BACKUPS = 10
                    BACKUP_FOLDER = 'backups'
                    LOG_FILE = 'backup_log.txt'
                self.config = DefaultConfig
        else:
            self.config = config

        self.backup_interval = self.config.BACKUP_INTERVAL_MINUTES * 60
        self.max_backups = self.config.MAX_BACKUPS
        self.last_backup_time = None
        self.db_last_modified = None

        # إعداد التسجيل
        self.logger = logging.getLogger('SmartBackup')
        self.logger.setLevel(logging.INFO)

        # إنشاء معالج للملف إذا لم يكن موجوداً
        if not self.logger.handlers:
            log_file = getattr(self.config, 'LOG_FILE', 'backup_log.txt')
            handler = logging.FileHandler(log_file, encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        if app:
            self.init_app(app)

    def init_app(self, app):
        """تهيئة المدير مع تطبيق Flask"""
        self.app = app

        # تسجيل دوال التنظيف عند إغلاق التطبيق
        atexit.register(self.cleanup_on_exit)
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        # بدء النظام التلقائي
        self.start_automatic_backup()

    def signal_handler(self, signum, frame):
        """معالج إشارات النظام لإيقاف النسخ الاحتياطي بأمان"""
        self.logger.info(f"تم استلام إشارة النظام {signum}")
        try:
            self.cleanup_on_exit()
        except Exception as e:
            self.logger.error(f"خطأ في معالج الإشارة: {str(e)}")
        finally:
            sys.exit(0)

    def cleanup_on_exit(self):
        """تنظيف وإنشاء نسخة احتياطية أخيرة عند الخروج"""
        self.logger.info("🛑 بدء عملية التنظيف والنسخ الاحتياطي الأخير...")
        self.stop_backup = True

        # إنشاء نسخة احتياطية أخيرة
        try:
            with self.app.app_context():
                success, message, backup_file = self.create_smart_backup(backup_type="exit")
                if success:
                    self.logger.info(f"✅ تم إنشاء النسخة الاحتياطية الأخيرة: {backup_file}")
                else:
                    self.logger.error(f"❌ فشل في إنشاء النسخة الاحتياطية الأخيرة: {message}")
        except Exception as e:
            self.logger.error(f"❌ خطأ في النسخة الاحتياطية الأخيرة: {str(e)}")

        # انتظار انتهاء خيط النسخ الاحتياطي
        if self.backup_thread and self.backup_thread.is_alive():
            self.backup_thread.join(timeout=5)

        self.logger.info("🏁 تم الانتهاء من عملية التنظيف")

    def get_db_path(self):
        """الحصول على مسار قاعدة البيانات"""
        try:
            db_uri = self.app.config['SQLALCHEMY_DATABASE_URI']
            db_path = db_uri.replace('sqlite:///', '')

            if not os.path.isabs(db_path):
                db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)

            return db_path
        except:
            return 'training_system.db'

    def check_db_changes(self):
        """فحص ما إذا كانت قاعدة البيانات قد تغيرت"""
        try:
            db_path = self.get_db_path()
            if not os.path.exists(db_path):
                return False

            current_modified = os.path.getmtime(db_path)

            if self.db_last_modified is None:
                self.db_last_modified = current_modified
                return True

            if current_modified > self.db_last_modified:
                self.db_last_modified = current_modified
                return True

            return False
        except Exception as e:
            self.logger.error(f"خطأ في فحص تغييرات قاعدة البيانات: {str(e)}")
            return False

    def create_smart_backup(self, backup_type="auto"):
        """إنشاء نسخة احتياطية ذكية"""
        try:
            # إنشاء اسم النسخة الاحتياطية
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"smart_backup_{backup_type}_{timestamp}.zip"

            # إنشاء النسخة الاحتياطية
            success, message, backup_file = create_backup(backup_name=backup_name)

            if success:
                self.last_backup_time = datetime.datetime.now()
                self.logger.info(f"✅ تم إنشاء نسخة احتياطية {backup_type}: {backup_file}")

                # تنظيف النسخ القديمة
                self.cleanup_old_backups()

                return True, message, backup_file
            else:
                self.logger.error(f"❌ فشل في إنشاء النسخة الاحتياطية: {message}")
                return False, message, None

        except Exception as e:
            error_msg = f"خطأ في إنشاء النسخة الاحتياطية الذكية: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None

    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = list_backups()

            # الاحتفاظ بالنسخ الذكية فقط
            smart_backups = [b for b in backups if 'smart_backup' in b['name']]

            if len(smart_backups) > self.max_backups:
                # حذف النسخ الزائدة (الأقدم)
                backups_to_delete = smart_backups[self.max_backups:]

                for backup in backups_to_delete:
                    try:
                        os.remove(backup['path'])
                        self.logger.info(f"🗑️ تم حذف النسخة الاحتياطية القديمة: {backup['name']}")
                    except Exception as e:
                        self.logger.error(f"❌ فشل في حذف النسخة الاحتياطية {backup['name']}: {str(e)}")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")

    def backup_worker(self):
        """العامل الذي يعمل في الخلفية لإنشاء النسخ الاحتياطية"""
        self.logger.info("🚀 بدء عامل النسخ الاحتياطي التلقائي")

        # إنشاء نسخة احتياطية عند البدء
        try:
            with self.app.app_context():
                self.create_smart_backup(backup_type="startup")
        except Exception as e:
            self.logger.error(f"خطأ في النسخة الاحتياطية عند البدء: {str(e)}")

        while not self.stop_backup:
            try:
                # انتظار لفترة قصيرة للتحقق من التوقف
                for _ in range(60):  # فحص كل دقيقة
                    if self.stop_backup:
                        break
                    time.sleep(1)

                if self.stop_backup:
                    break

                # فحص ما إذا كان الوقت قد حان لإنشاء نسخة احتياطية
                current_time = datetime.datetime.now()

                should_backup = False

                # فحص الفترة الزمنية
                if self.last_backup_time is None:
                    should_backup = True
                elif (current_time - self.last_backup_time).total_seconds() >= self.backup_interval:
                    should_backup = True

                # فحص تغييرات قاعدة البيانات
                if should_backup and self.check_db_changes():
                    with self.app.app_context():
                        self.create_smart_backup(backup_type="auto")

            except Exception as e:
                self.logger.error(f"خطأ في عامل النسخ الاحتياطي: {str(e)}")
                time.sleep(60)  # انتظار دقيقة قبل المحاولة مرة أخرى

        self.logger.info("🛑 تم إيقاف عامل النسخ الاحتياطي التلقائي")

    def start_automatic_backup(self):
        """بدء النظام التلقائي للنسخ الاحتياطي"""
        if self.backup_thread is None or not self.backup_thread.is_alive():
            self.stop_backup = False
            self.backup_thread = threading.Thread(target=self.backup_worker, daemon=True)
            self.backup_thread.start()
            self.logger.info("✅ تم بدء النظام التلقائي للنسخ الاحتياطي")

    def stop_automatic_backup(self):
        """إيقاف النظام التلقائي للنسخ الاحتياطي"""
        self.stop_backup = True
        if self.backup_thread and self.backup_thread.is_alive():
            self.backup_thread.join(timeout=5)
        self.logger.info("🛑 تم إيقاف النظام التلقائي للنسخ الاحتياطي")

    def force_backup(self):
        """إجبار إنشاء نسخة احتياطية فورية"""
        try:
            with self.app.app_context():
                return self.create_smart_backup(backup_type="manual")
        except Exception as e:
            error_msg = f"خطأ في النسخة الاحتياطية الفورية: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None

    def get_backup_status(self):
        """الحصول على حالة النظام الاحتياطي"""
        return {
            'is_running': self.backup_thread is not None and self.backup_thread.is_alive(),
            'last_backup_time': self.last_backup_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_backup_time else None,
            'backup_interval_minutes': self.backup_interval // 60,
            'max_backups': self.max_backups,
            'total_backups': len([b for b in list_backups() if 'smart_backup' in b['name']])
        }

# إنشاء مثيل عام للمدير
smart_backup_manager = SmartBackupManager()
