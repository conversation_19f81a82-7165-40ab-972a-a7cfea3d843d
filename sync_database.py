#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, Course, CourseParticipant, PersonData, User
from datetime import datetime

def sync_database():
    with app.app_context():
        print("🔄 تحديث وتزامن قاعدة البيانات...")
        print("=" * 60)
        
        # 1. فحص الدورات الموجودة
        print("📋 الدورات الموجودة حالياً:")
        courses = Course.query.all()
        for course in courses:
            participants_count = CourseParticipant.query.filter_by(course_id=course.id).count()
            print(f"   - ID: {course.id}, رقم: {course.course_number}, اسم: {course.title}")
            print(f"     المشاركين: {participants_count}")
        
        print(f"\nإجمالي الدورات في قاعدة البيانات: {len(courses)}")
        
        # 2. إنشاء الدورات المفقودة
        required_courses = [
            {
                'course_number': '3455667',
                'title': 'المبيعات',
                'description': 'دورة تدريبية في المبيعات',
                'participants': [
                    'فاطمة صالح محمد الحميري',
                    'علي أحمد محمد قاسم', 
                    'علي أحمد علي قاسم',
                    'فاطمة صالح محمد عبدالله',
                    'علي عبدالله علي عبدالله',
                    'عبدالله قاسم محمد صالح',
                    'محمد أحمد علي الحميري',
                    'سارة محمد قاسم الزهراني',
                    'خالد عبدالله صالح المقطري',
                    'نورا أحمد محمد العامري',
                    'عبدالرحمن علي أحمد الشهري',
                    'فاطمة قاسم عبدالله الحربي',
                    'أحمد محمد علي القحطاني',
                    'مريم عبدالله أحمد الدوسري'
                ]
            },
            {
                'course_number': '2024001',
                'title': 'إدارة المشاريع',
                'description': 'دورة تدريبية في إدارة المشاريع',
                'participants': [
                    'سعد علي محمد الغامدي',
                    'هند أحمد قاسم العتيبي',
                    'عبدالعزيز محمد علي الرشيد',
                    'نوال قاسم عبدالله الخالدي',
                    'يوسف أحمد علي المالكي',
                    'رنا محمد قاسم الشمري'
                ]
            }
        ]
        
        print("\n🆕 إنشاء الدورات المطلوبة...")
        
        # جلب أول مستخدم كمدرب
        trainer = User.query.first()
        if not trainer:
            print("❌ لا يوجد مستخدمين في النظام")
            return
        
        for course_data in required_courses:
            # التحقق من وجود الدورة
            existing_course = Course.query.filter_by(course_number=course_data['course_number']).first()
            
            if existing_course:
                print(f"✅ الدورة {course_data['course_number']} موجودة بالفعل")
                course = existing_course
            else:
                # إنشاء دورة جديدة
                try:
                    course = Course()
                    course.course_number = course_data['course_number']
                    course.title = course_data['title']
                    course.description = course_data['description']
                    course.category = 'فنية'
                    course.level = 'متوسط'
                    course.start_date = datetime.now()
                    course.end_date = datetime.now()
                    course.start_date_hijri = '1446/05/25'
                    course.end_date_hijri = '1446/05/25'
                    course.duration_days = 1
                    course.trainer_id = trainer.id
                    course.total_participants = 0
                    course.total_graduates = 0
                    course.total_dropouts = 0
                    
                    db.session.add(course)
                    db.session.flush()
                    print(f"✅ تم إنشاء الدورة {course_data['course_number']}")
                except Exception as e:
                    print(f"❌ خطأ في إنشاء الدورة {course_data['course_number']}: {str(e)}")
                    continue
            
            # إضافة المشاركين
            print(f"👥 إضافة المشاركين للدورة {course_data['course_number']}...")
            added_count = 0
            
            for participant_name in course_data['participants']:
                try:
                    # البحث عن الشخص أو إنشاؤه
                    person = PersonData.query.filter_by(full_name=participant_name).first()
                    if not person:
                        person = PersonData(full_name=participant_name)
                        db.session.add(person)
                        db.session.flush()
                    
                    # التحقق من عدم وجود المشارك في الدورة
                    existing_participant = CourseParticipant.query.filter_by(
                        course_id=course.id,
                        personal_data_id=person.id
                    ).first()
                    
                    if not existing_participant:
                        participant = CourseParticipant(
                            course_id=course.id,
                            personal_data_id=person.id,
                            status='active',
                            entry_date=datetime.now()
                        )
                        db.session.add(participant)
                        added_count += 1
                
                except Exception as e:
                    print(f"❌ خطأ في إضافة {participant_name}: {str(e)}")
                    continue
            
            # تحديث عدد المشاركين
            total_participants = CourseParticipant.query.filter_by(course_id=course.id).count()
            course.total_participants = total_participants
            
            print(f"   ✅ تم إضافة {added_count} مشارك جديد")
            print(f"   📊 إجمالي المشاركين: {total_participants}")
        
        # حفظ جميع التغييرات
        try:
            db.session.commit()
            print("\n✅ تم حفظ جميع التغييرات بنجاح!")
        except Exception as e:
            db.session.rollback()
            print(f"\n❌ خطأ في حفظ التغييرات: {str(e)}")
            return
        
        # 3. عرض النتيجة النهائية
        print("\n" + "=" * 60)
        print("📊 الوضع النهائي لقاعدة البيانات:")
        
        final_courses = Course.query.all()
        for course in final_courses:
            participants_count = CourseParticipant.query.filter_by(course_id=course.id).count()
            print(f"\n🎓 الدورة ID: {course.id}")
            print(f"   رقم الدورة: {course.course_number}")
            print(f"   اسم الدورة: {course.title}")
            print(f"   إجمالي المشاركين: {participants_count}")
            print(f"   رابط الدورة: http://127.0.0.1:5000/course/{course.id}/participants")

if __name__ == '__main__':
    sync_database()
