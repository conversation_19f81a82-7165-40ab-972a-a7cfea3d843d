<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مثال عملي - نظام إدارة الصلاحيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .permission-demo {
            border: 2px dashed #007bff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .permission-label {
            background-color: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            margin-bottom: 10px;
            display: inline-block;
        }
        .role-badge {
            background-color: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            margin: 5px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-shield-alt text-primary"></i>
                    مثال عملي - نظام إدارة الصلاحيات
                </h1>
                
                <!-- محاكي المستخدم الحالي -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-user"></i> المستخدم الحالي (محاكاة)</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="userRole" class="form-label">اختر الدور:</label>
                                <select id="userRole" class="form-select" onchange="changeUserRole()">
                                    <option value="admin">admin - مدير النظام</option>
                                    <option value="manager">manager - مدير</option>
                                    <option value="trainer">trainer - مدرب</option>
                                    <option value="data_entry">data_entry - مدخل بيانات</option>
                                    <option value="viewer">viewer - مشاهد</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الصلاحيات الحالية:</label>
                                <div id="currentPermissions"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مثال 1: إدارة المستخدمين -->
        <div class="row">
            <div class="col-12">
                <div class="permission-demo">
                    <div class="permission-label">يتطلب صلاحية: users.view</div>
                    <h4><i class="fas fa-users"></i> إدارة المستخدمين</h4>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>قائمة المستخدمين</span>
                        
                        <!-- أزرار تظهر حسب الصلاحيات -->
                        <div class="btn-group">
                            <button id="btnAddUser" class="btn btn-primary" style="display: none;">
                                <i class="fas fa-plus"></i> إضافة مستخدم
                            </button>
                            <button id="btnImportUsers" class="btn btn-info" style="display: none;">
                                <i class="fas fa-upload"></i> استيراد
                            </button>
                            <button id="btnExportUsers" class="btn btn-success" style="display: none;">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                        </div>
                    </div>
                    
                    <!-- جدول المستخدمين -->
                    <div id="usersSection" style="display: none;">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>أحمد محمد</td>
                                    <td><EMAIL></td>
                                    <td><span class="role-badge">مدير</span></td>
                                    <td>
                                        <button id="btnViewUser1" class="btn btn-sm btn-info" style="display: none;">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button id="btnEditUser1" class="btn btn-sm btn-warning" style="display: none;">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button id="btnResetPassword1" class="btn btn-sm btn-secondary" style="display: none;">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button id="btnDeleteUser1" class="btn btn-sm btn-danger" style="display: none;">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>فاطمة علي</td>
                                    <td><EMAIL></td>
                                    <td><span class="role-badge">مدربة</span></td>
                                    <td>
                                        <button id="btnViewUser2" class="btn btn-sm btn-info" style="display: none;">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button id="btnEditUser2" class="btn btn-sm btn-warning" style="display: none;">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button id="btnResetPassword2" class="btn btn-sm btn-secondary" style="display: none;">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button id="btnDeleteUser2" class="btn btn-sm btn-danger" style="display: none;">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div id="noUsersPermission" class="alert alert-warning" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        ليس لديك صلاحية لعرض المستخدمين
                    </div>
                </div>
            </div>
        </div>

        <!-- مثال 2: إدارة الدورات -->
        <div class="row">
            <div class="col-12">
                <div class="permission-demo">
                    <div class="permission-label">يتطلب صلاحية: courses.view</div>
                    <h4><i class="fas fa-graduation-cap"></i> إدارة الدورات</h4>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>قائمة الدورات</span>
                        
                        <div class="btn-group">
                            <button id="btnAddCourse" class="btn btn-primary" style="display: none;">
                                <i class="fas fa-plus"></i> إضافة دورة
                            </button>
                            <button id="btnManageParticipants" class="btn btn-info" style="display: none;">
                                <i class="fas fa-users"></i> إدارة المشاركين
                            </button>
                        </div>
                    </div>
                    
                    <div id="coursesSection" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">دورة البرمجة المتقدمة</h5>
                                        <p class="card-text">دورة تدريبية في البرمجة</p>
                                        <div class="btn-group">
                                            <button id="btnEditCourse1" class="btn btn-sm btn-warning" style="display: none;">تعديل</button>
                                            <button id="btnDeleteCourse1" class="btn btn-sm btn-danger" style="display: none;">حذف</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">دورة إدارة المشاريع</h5>
                                        <p class="card-text">دورة في إدارة المشاريع</p>
                                        <div class="btn-group">
                                            <button id="btnEditCourse2" class="btn btn-sm btn-warning" style="display: none;">تعديل</button>
                                            <button id="btnDeleteCourse2" class="btn btn-sm btn-danger" style="display: none;">حذف</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="noCoursesPermission" class="alert alert-warning" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        ليس لديك صلاحية لعرض الدورات
                    </div>
                </div>
            </div>
        </div>

        <!-- مثال 3: التقارير -->
        <div class="row">
            <div class="col-12">
                <div class="permission-demo">
                    <div class="permission-label">يتطلب صلاحية: reports.view</div>
                    <h4><i class="fas fa-chart-bar"></i> التقارير</h4>
                    
                    <div id="reportsSection" style="display: none;">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5>تقرير المستخدمين</h5>
                                        <button id="btnExportUsersReport" class="btn btn-light btn-sm" style="display: none;">
                                            <i class="fas fa-download"></i> تصدير
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5>تقرير الدورات</h5>
                                        <button id="btnExportCoursesReport" class="btn btn-light btn-sm" style="display: none;">
                                            <i class="fas fa-download"></i> تصدير
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h5>التقرير المالي</h5>
                                        <button id="btnExportFinancialReport" class="btn btn-light btn-sm" style="display: none;">
                                            <i class="fas fa-download"></i> تصدير
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="noReportsPermission" class="alert alert-warning" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        ليس لديك صلاحية لعرض التقارير
                    </div>
                </div>
            </div>
        </div>

        <!-- مثال 4: إعدادات النظام -->
        <div class="row">
            <div class="col-12">
                <div class="permission-demo">
                    <div class="permission-label">يتطلب دور: admin</div>
                    <h4><i class="fas fa-cog"></i> إعدادات النظام</h4>
                    
                    <div id="systemSettingsSection" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">إعدادات عامة</div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <label>اسم النظام</label>
                                            <input type="text" class="form-control" value="نظام إدارة التدريب">
                                        </div>
                                        <button class="btn btn-primary">حفظ</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">النسخ الاحتياطي</div>
                                    <div class="card-body">
                                        <button class="btn btn-success">إنشاء نسخة احتياطية</button>
                                        <button class="btn btn-warning">استعادة نسخة</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="noSystemPermission" class="alert alert-danger" style="display: none;">
                        <i class="fas fa-ban"></i>
                        فقط المديرين يمكنهم الوصول لإعدادات النظام
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعريف الصلاحيات لكل دور
        const rolePermissions = {
            admin: [
                'users.view', 'users.create', 'users.edit', 'users.delete', 'users.reset_password',
                'courses.view', 'courses.create', 'courses.edit', 'courses.delete', 'courses.manage_participants',
                'reports.view', 'reports.create', 'reports.export', 'reports.financial',
                'system.settings', 'backup.create'
            ],
            manager: [
                'users.view', 'users.edit',
                'courses.view', 'courses.create', 'courses.edit', 'courses.manage_participants',
                'reports.view', 'reports.create', 'reports.export'
            ],
            trainer: [
                'courses.view', 'courses.edit', 'courses.manage_participants',
                'reports.view'
            ],
            data_entry: [
                'courses.view',
                'persons.view', 'persons.create', 'persons.edit'
            ],
            viewer: [
                'courses.view',
                'reports.view'
            ]
        };

        let currentRole = 'admin';
        let currentPermissions = rolePermissions[currentRole];

        function hasPermission(permission) {
            return currentPermissions.includes(permission);
        }

        function hasRole(role) {
            return currentRole === role;
        }

        function changeUserRole() {
            currentRole = document.getElementById('userRole').value;
            currentPermissions = rolePermissions[currentRole];
            updatePermissionsDisplay();
            updateUIBasedOnPermissions();
        }

        function updatePermissionsDisplay() {
            const permissionsDiv = document.getElementById('currentPermissions');
            permissionsDiv.innerHTML = '';
            
            currentPermissions.forEach(permission => {
                const badge = document.createElement('span');
                badge.className = 'badge bg-secondary me-1 mb-1';
                badge.textContent = permission;
                permissionsDiv.appendChild(badge);
            });
        }

        function updateUIBasedOnPermissions() {
            // إدارة المستخدمين
            if (hasPermission('users.view')) {
                document.getElementById('usersSection').style.display = 'block';
                document.getElementById('noUsersPermission').style.display = 'none';
            } else {
                document.getElementById('usersSection').style.display = 'none';
                document.getElementById('noUsersPermission').style.display = 'block';
            }

            // أزرار إدارة المستخدمين
            document.getElementById('btnAddUser').style.display = hasPermission('users.create') ? 'inline-block' : 'none';
            document.getElementById('btnImportUsers').style.display = hasPermission('users.import') ? 'inline-block' : 'none';
            document.getElementById('btnExportUsers').style.display = hasPermission('users.export') ? 'inline-block' : 'none';

            // أزرار إجراءات المستخدمين
            ['1', '2'].forEach(num => {
                document.getElementById(`btnViewUser${num}`).style.display = hasPermission('users.view') ? 'inline-block' : 'none';
                document.getElementById(`btnEditUser${num}`).style.display = hasPermission('users.edit') ? 'inline-block' : 'none';
                document.getElementById(`btnResetPassword${num}`).style.display = hasPermission('users.reset_password') ? 'inline-block' : 'none';
                document.getElementById(`btnDeleteUser${num}`).style.display = hasPermission('users.delete') ? 'inline-block' : 'none';
            });

            // إدارة الدورات
            if (hasPermission('courses.view')) {
                document.getElementById('coursesSection').style.display = 'block';
                document.getElementById('noCoursesPermission').style.display = 'none';
            } else {
                document.getElementById('coursesSection').style.display = 'none';
                document.getElementById('noCoursesPermission').style.display = 'block';
            }

            // أزرار إدارة الدورات
            document.getElementById('btnAddCourse').style.display = hasPermission('courses.create') ? 'inline-block' : 'none';
            document.getElementById('btnManageParticipants').style.display = hasPermission('courses.manage_participants') ? 'inline-block' : 'none';

            ['1', '2'].forEach(num => {
                document.getElementById(`btnEditCourse${num}`).style.display = hasPermission('courses.edit') ? 'inline-block' : 'none';
                document.getElementById(`btnDeleteCourse${num}`).style.display = hasPermission('courses.delete') ? 'inline-block' : 'none';
            });

            // التقارير
            if (hasPermission('reports.view')) {
                document.getElementById('reportsSection').style.display = 'block';
                document.getElementById('noReportsPermission').style.display = 'none';
            } else {
                document.getElementById('reportsSection').style.display = 'none';
                document.getElementById('noReportsPermission').style.display = 'block';
            }

            // أزرار تصدير التقارير
            document.getElementById('btnExportUsersReport').style.display = hasPermission('reports.export') ? 'inline-block' : 'none';
            document.getElementById('btnExportCoursesReport').style.display = hasPermission('reports.export') ? 'inline-block' : 'none';
            document.getElementById('btnExportFinancialReport').style.display = hasPermission('reports.financial') ? 'inline-block' : 'none';

            // إعدادات النظام
            if (hasRole('admin')) {
                document.getElementById('systemSettingsSection').style.display = 'block';
                document.getElementById('noSystemPermission').style.display = 'none';
            } else {
                document.getElementById('systemSettingsSection').style.display = 'none';
                document.getElementById('noSystemPermission').style.display = 'block';
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updatePermissionsDisplay();
            updateUIBasedOnPermissions();
        });
    </script>
</body>
</html>
