# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['E:/app/TRINING\app.py'],
    pathex=['E:/app/TRINING'],
    binaries=[],
    datas=[],
    hiddenimports=['', 'wmi', 'PyQt5', 'olefile', 'os,', 'this', 'gc', 'errno', '_operator', 'pathlib', 'being', 'orjson', 'packaging', 'ipywidgets', 'traceback', 'asyncpg', 'pstats', 'selectors', 'psycopg2', 'those', '__pypy__', 'asyncio', 'xml', 'httpcore', 'ast', 'urllib3_secure_extra', 'HTMLParser', 'rich', 'typing_extensions', '%(module)s', 'environment', 'binascii', 'threading', 'rapidfuzz', 'posixpath', 'ipaddress', 'multiprocessing', 'encodings', 'six', 'string', 'lingua', 'abc', 'unittest', '``0', 'zoneinfo', 'pty', 'importlib_metadata', 'pickle', 'xlrd', 'brotlicffi', 'anyio', 'zipimport', '_testbuffer', '_ctypes', 'UserDict', 'datetime', 'idna', '_string', 'zipfile', 'xlsxwriter', 'openpyxl', 'pyannotate_runtime', 'myapp', 'bs4', 'a', 'platform', 'limited_api_latest', 'dask', '_dummy_thread', 'PyInstaller', 'engine_kwargs', 'ntlm', 'struct', 'shlex', 'matplotlib', 'calculations', 'ntpath', 'setuptools_scm', 'numba', 'what', '`formats`,', 'one', 'pythoncom', 'dns', 'gettext', 'traitlets', 'fnmatch', 'Excel', 'warnings', 'Queue', 'yourapplication', 'token', 'adbc_driver_postgresql', 'elastic_transport', 'cgi', 'async_timeout', 'mymodule', '``namedtuple``', '`y`', '_frozen_importlib', 'contextvars', 'termios', 'pymysql', 'winreg', 'trio', 'cffi', 'array', 'limited_api1', 'sets', 'floats', 'os', 'colorsys', 'inflect', '_aix_support', 'fcntl', 'numbers', '_manylinux', 'the', 'simsimd', 'cx_Oracle', 'mako', 'difflib', 'time', 'calendar', 'yaml', 'json,', 'numexpr', 'win32pdh', 'tables', 'email_validator', 'tqdm', 'gzip', 'xmlrpclib', 'flask_login', 'adbc_driver_sqlite', '*all*', 'adbc_driver_manager', 'tarfile', 'copy', 'StringIO', 'an', 'fileinput', 'thread', ':meth:`_make_session_factory`', 'Oracle', 'backports', 'dotenv', 'threadpoolctl', '0', 'mypy', 'flask_sqlalchemy', 'keyring', 'attr', 'uuid', 'relative', 'psycopg', 'secrets', 'android', 'typing', 'flask_migrate', 'sysconfig', '``obj=[0]``', 'pyodbc', 'comparing', 'dataclasses', 'given', 'psutil', 'outside', '_csv', 'argmin', 'imp', 'charset_normalizer', 'pandas', 'name', 'PyQt4', 'annotationlib', 'asgiref', 'python_calamine', 'lxml', 'pyxlsb', 'slack_sdk', 'mysql', 'pip', '`unstack`)', 'requests', 'inspect', 'socket', 'scipy', 'sphinx', '_ast', 'all', 'code', 'queue', 'importlib_resources', 'flask_wtf', 'tty', 'cython', 'zstandard', 'pickletools', 'oracledb', 'IPython', 'itsdangerous', 'importlib', 'source', 'cmath', 'index', 'logging', 'ctypes', '_typeshed', 'compileall', 'pydoc', ':class:`CliRunner`', 'http', '_frozen_importlib_external', 'AppKit', 'cProfile', 'pytz', 'pyarrow', 'visitor', '__builtin__', 'reprlib', 'htmlentitydefs', 'h2', 'bidi', 'httpx', 'magic', 'reports_generator', 'rapidfuzz_capi', 'weakref', 'qtpy', 'botocore', 'collections', 'pkgutil', 'certifi', 'itertools', 'alembic', 'dominate', 'locale', 'elasticsearch', '{bad_lib}', 'plistlib', 'timeit', 'greenlet', 'resource', 'which', 'tomllib', 'codecs', 'chardet', 'argparse', 'person_data_routes', 'linecache', 'values', '__future__', 'builtins', 'subprocess', 'simplejson', 'hashlib', 'tokenize', 'getpass', 'pickle5', 'email', 'objgraph', 'hiredis', 'aioquic', 'fsspec', '_pytest', 'sqlite3', 'et_xmlfile', 'opentelemetry', 'pyperclip', 're', 'webbrowser', 'urllib', 'js', 'that', 'concurrent', '_socket', 'unavailable', 'each', 'hmac', '``info``;', 'line', '_abcoll', 'tracemalloc', 'dogpile', 'arabic_reshaper', '__main__', 'blinker', 'code_generators', 'filelock', 'sqlcipher_compatible_driver', 'colorama', 'heapq', 'numpy_distutils', 'optparse', 'functools', 'bisect', 'B2', 'unicodedata', 'segments', 'tempfile', 'aiohttp', 'babel', 'its', 'array_interface_testing', 'urllib3', 'watchdog', '_multibytecodec', 'py_compile', 'docutils', 'PIL', 'redis', 'dummy_threading', 'sklearn', 'PyQt6', 'multiple', 'faker', 'textwrap', 'asyncmy', '_osx_support', '_imp', 'xarray', 'ssl', 'rlcompleter', 'runpy', 'on', 'sniffio', 'java', 'gevent', 'app', 'numpy', 'fontTools', 'limited_api2', 'select', 'cycler', '``', 'google', '_ssl', 'serializable', 'stat', 'pairs', 'backup_utils', 'mpl_toolkits', 'OpenSSL', 'socketserver', 'signal', '1', 'operator', 'click', 'dependencies', 'mem_policy', 'marshal', 'zlib', 'fractions', 'hypothesis', 'mimetypes', 'urllib2', 'configparser', 'coercing', 'brotli', 'smart_backup_config', 'fast_search_engine', 'dummy_thread', 'jwt', 'Foundation', ':mod:`contextlib`', 'pylab', 'dateutil', 'io', 'enum', 'lzma', 'contextlib2', 'mock', 'ConfigParser', 'setuptools', 'boto3', 'shutil', 'xmlrpc', 'msvcrt', 'pytest', 'ASCII', 'decimal', 'lowest', 'tkinter', 'jinja2', '_thread', 'logger', 'foomodule', 'defusedxml', 'random', 'pprint', 'sqlalchemy', 'Cython', 'tensorflow', 'PySide6', 'urlparse', 'glob', 'argmax', 'mmap', 'json', 'sqla_plugin_base', 'base64', 'pysqlcipher3', 'keras', 'checks', 'readline', 'wtforms', 'math', 'types', 'netrc', 'httplib', 'contextlib', 'fastparquet', 'sqlcipher3', 'csv', 'distutils', 'PyPI', 'pdfkit', 'or', 'keyword', 'flask', '``TextAsFrom``,', 'inside', 'odf', '`get_tokens_unprocessed()`,', 'py', 'sys', ':file:`', 'cryptography', 'xmltodict', 'scipy_doctest', 'doctest', 'jnius', 'within', 'left', 'azure', 'socks', '``Engineer``', 'flask_babel', 'beaker', 'site', 'profile', 'associated', 'pyodide', '``pyproject', 'atexit', 'markupsafe', 's3fs', 'pkg_resources', 'werkzeug', 'pygments', 'html', 'copyreg', 'bz2', '`numpy'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# إضافة ملفات البيانات
a.datas += [('training_system.db', 'E:/app/TRINING/training_system.db', 'DATA')]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='app',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    
)