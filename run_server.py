#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف تشغيل الخادم المبسط
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        print("🔄 تحميل التطبيق...")
        
        # استيراد التطبيق
        from app import app, db
        
        print("✅ تم تحميل التطبيق بنجاح")
        
        # إنشاء الجداول إذا لم تكن موجودة
        print("🔄 التحقق من قاعدة البيانات...")
        with app.app_context():
            db.create_all()
        print("✅ قاعدة البيانات جاهزة")
        
        # تشغيل الخادم
        print("🚀 بدء تشغيل الخادم على http://127.0.0.1:5000")
        print("📝 للوصول إلى التقارير: http://127.0.0.1:5000/reports/dashboard")
        print("🛑 لإيقاف الخادم اضغط Ctrl+C")
        print("-" * 50)
        
        app.run(
            debug=True,
            host='127.0.0.1',
            port=5000,
            threaded=True,
            use_reloader=False  # تعطيل إعادة التحميل التلقائي لتجنب المشاكل
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        import traceback
        traceback.print_exc()
        
        # محاولة تشغيل مبسطة
        print("\n🔄 محاولة تشغيل مبسطة...")
        try:
            from app import app
            app.run(host='127.0.0.1', port=5000, debug=False)
        except Exception as e2:
            print(f"❌ فشل في التشغيل المبسط أيضاً: {e2}")

if __name__ == '__main__':
    main()
