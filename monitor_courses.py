#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مراقب مستمر للدورات - فحص الدورات في القاعدة والنظام
"""

import sqlite3
import requests
import time
from datetime import datetime

def check_database_courses():
    """فحص الدورات في قاعدة البيانات"""
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM course")
        courses_count = cursor.fetchone()[0]
        
        if courses_count > 0:
            cursor.execute("""
                SELECT course_number, title, trainer_id, start_date
                FROM course
                ORDER BY id
            """)
            courses = cursor.fetchall()
            conn.close()
            return courses_count, courses
        
        conn.close()
        return 0, []
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")
        return 0, []

def check_system_courses():
    """فحص الدورات في النظام"""
    try:
        # محاولة الوصول إلى صفحة الدورات
        response = requests.get('http://127.0.0.1:5000/courses', 
                              allow_redirects=False, 
                              timeout=5)
        
        if response.status_code == 200:
            content = response.text
            # البحث عن أسماء الدورات في المحتوى
            courses_found = []
            if 'المبيعات2' in content:
                courses_found.append('المبيعات2')
            if 'تدريب الذكاء الاصطناعي' in content:
                courses_found.append('تدريب الذكاء الاصطناعي')
            if 'تدريب الذكاء الاصطناعي 2' in content:
                courses_found.append('تدريب الذكاء الاصطناعي 2')
            
            return True, len(courses_found), courses_found
        elif response.status_code == 302:
            return True, -1, ["يحتاج تسجيل دخول"]
        else:
            return False, 0, []
            
    except Exception as e:
        return False, 0, [f"خطأ: {str(e)}"]

def check_server_status():
    """فحص حالة الخادم"""
    try:
        response = requests.get('http://127.0.0.1:5000/', timeout=3)
        return True, response.status_code
    except:
        return False, 0

def print_status_report(db_count, db_courses, sys_working, sys_count, sys_courses, server_ok, server_status):
    """طباعة تقرير الحالة"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"\n{'='*60}")
    print(f"📊 تقرير الحالة - {timestamp}")
    print(f"{'='*60}")
    
    # حالة الخادم
    if server_ok:
        print(f"🟢 الخادم: يعمل (كود: {server_status})")
    else:
        print(f"🔴 الخادم: متوقف")
    
    # حالة قاعدة البيانات
    print(f"💾 قاعدة البيانات: {db_count} دورة")
    if db_courses:
        for course in db_courses:
            print(f"   - {course[0]}: {course[1]}")
    
    # حالة النظام
    if sys_working:
        if sys_count == -1:
            print(f"🔐 النظام: يعمل (يحتاج تسجيل دخول)")
        else:
            print(f"🌐 النظام: {sys_count} دورة ظاهرة")
            for course in sys_courses:
                print(f"   - {course}")
    else:
        print(f"🔴 النظام: لا يعمل")
        for error in sys_courses:
            print(f"   - {error}")
    
    # تحليل الحالة
    print(f"\n📋 التحليل:")
    if db_count > 0 and sys_working:
        if sys_count == -1:
            print("✅ الدورات موجودة في القاعدة والنظام يعمل")
            print("💡 تحتاج تسجيل دخول لرؤية الدورات")
        elif sys_count > 0:
            print("✅ الدورات موجودة ومرئية في النظام")
        else:
            print("⚠️ الدورات موجودة في القاعدة لكن غير مرئية في النظام")
    elif db_count > 0 and not sys_working:
        print("⚠️ الدورات موجودة في القاعدة لكن النظام لا يعمل")
    elif db_count == 0:
        print("❌ لا توجد دورات في قاعدة البيانات")
    else:
        print("❓ حالة غير واضحة")

def continuous_monitor(interval=30):
    """مراقبة مستمرة"""
    print("🔄 بدء المراقبة المستمرة للدورات")
    print(f"⏱️ فترة الفحص: كل {interval} ثانية")
    print("⏹️ اضغط Ctrl+C للتوقف")
    
    try:
        while True:
            # فحص قاعدة البيانات
            db_count, db_courses = check_database_courses()
            
            # فحص النظام
            sys_working, sys_count, sys_courses = check_system_courses()
            
            # فحص الخادم
            server_ok, server_status = check_server_status()
            
            # طباعة التقرير
            print_status_report(db_count, db_courses, sys_working, sys_count, sys_courses, server_ok, server_status)
            
            # انتظار
            print(f"\n⏳ انتظار {interval} ثانية...")
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف المراقبة")

def single_check():
    """فحص واحد"""
    print("🔍 فحص واحد للدورات")
    
    # فحص قاعدة البيانات
    db_count, db_courses = check_database_courses()
    
    # فحص النظام
    sys_working, sys_count, sys_courses = check_system_courses()
    
    # فحص الخادم
    server_ok, server_status = check_server_status()
    
    # طباعة التقرير
    print_status_report(db_count, db_courses, sys_working, sys_count, sys_courses, server_ok, server_status)
    
    return db_count, sys_working, sys_count

def fix_suggestions():
    """اقتراحات الإصلاح"""
    print(f"\n{'='*60}")
    print("🔧 اقتراحات الإصلاح:")
    print(f"{'='*60}")
    
    db_count, sys_working, sys_count = single_check()
    
    print(f"\n💡 التوصيات:")
    
    if db_count == 0:
        print("1. تشغيل سكريبت إنشاء الدورات:")
        print("   python direct_sql_courses.py")
    
    if not sys_working:
        print("2. تشغيل الخادم:")
        print("   python simple_run.py")
    
    if db_count > 0 and sys_working and sys_count == 0:
        print("3. فحص مسار الدورات في app.py")
        print("4. فحص قالب courses.html")
        print("5. تسجيل الدخول كمدير")
    
    if sys_count == -1:
        print("3. تسجيل الدخول:")
        print("   http://127.0.0.1:5000/login")
        print("   المستخدم: admin")
        print("   كلمة المرور: admin")
    
    print(f"\n🔗 روابط مفيدة:")
    print("   📚 الدورات: http://127.0.0.1:5000/courses")
    print("   🏠 لوحة التحكم: http://127.0.0.1:5000/dashboard")
    print("   🔐 تسجيل الدخول: http://127.0.0.1:5000/login")

def main():
    """الدالة الرئيسية"""
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'monitor':
            interval = int(sys.argv[2]) if len(sys.argv) > 2 else 30
            continuous_monitor(interval)
        elif sys.argv[1] == 'fix':
            fix_suggestions()
        else:
            print("الاستخدام:")
            print("  python monitor_courses.py          # فحص واحد")
            print("  python monitor_courses.py monitor  # مراقبة مستمرة")
            print("  python monitor_courses.py monitor 10  # مراقبة كل 10 ثواني")
            print("  python monitor_courses.py fix      # اقتراحات الإصلاح")
    else:
        single_check()

if __name__ == "__main__":
    main()
