/* 
🔧 إصلاح نهائي وقوي لحجم خط وصف الدورات
Final Strong Fix for Course Description Font Size
*/

/* CSS قوي جداً لضمان تطبيق حجم الخط الصغير */
html body .courses-page .course-card .course-description,
html body .courses-page .course-description,
html body .course-card .course-description,
html body p.course-description,
html body .card .course-description,
html body .courses-page .card .course-description,
html body .courses-page .course-card p.course-description,
.courses-page .course-card .course-description,
.courses-page .course-description,
.course-card .course-description,
p.course-description {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
    max-height: 28px !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    text-overflow: ellipsis !important;
    color: #9ca3af !important;
    font-weight: 400 !important;
    margin-bottom: 8px !important;
    padding: 0 6px !important;
    text-align: center !important;
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    hyphens: auto !important;
}

/* تأكيد إضافي مع specificity عالي */
.courses-page .row .col-md-4 .course-card .course-body .course-description,
.courses-page .row .col-md-4 .course-card .course-body p.course-description {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
    max-height: 28px !important;
}

/* للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    html body .courses-page .course-card .course-description,
    html body .courses-page .course-description,
    html body .course-card .course-description,
    html body p.course-description {
        font-size: 0.55rem !important;
        height: 28px !important;
        line-height: 1.2 !important;
        max-height: 28px !important;
    }
}

/* للشاشات الكبيرة */
@media (min-width: 1200px) and (max-width: 1399px) {
    html body .courses-page .course-card .course-description,
    html body .courses-page .course-description,
    html body .course-card .course-description,
    html body p.course-description {
        font-size: 0.55rem !important;
        height: 28px !important;
        line-height: 1.2 !important;
        max-height: 28px !important;
    }
}

/* للشاشات المتوسطة الكبيرة */
@media (min-width: 992px) and (max-width: 1199px) {
    html body .courses-page .course-card .course-description,
    html body .courses-page .course-description,
    html body .course-card .course-description,
    html body p.course-description {
        font-size: 0.53rem !important;
        height: 26px !important;
        line-height: 1.1 !important;
        max-height: 26px !important;
    }
}

/* للشاشات المتوسطة */
@media (min-width: 768px) and (max-width: 991px) {
    html body .courses-page .course-card .course-description,
    html body .courses-page .course-description,
    html body .course-card .course-description,
    html body p.course-description {
        font-size: 0.52rem !important;
        height: 26px !important;
        line-height: 1.1 !important;
        max-height: 26px !important;
    }
}

/* للأجهزة اللوحية */
@media (min-width: 576px) and (max-width: 767px) {
    html body .courses-page .course-card .course-description,
    html body .courses-page .course-description,
    html body .course-card .course-description,
    html body p.course-description {
        font-size: 0.51rem !important;
        height: 24px !important;
        line-height: 1.1 !important;
        max-height: 24px !important;
        -webkit-line-clamp: 2 !important;
    }
}

/* للهواتف */
@media (max-width: 575px) {
    html body .courses-page .course-card .course-description,
    html body .courses-page .course-description,
    html body .course-card .course-description,
    html body p.course-description {
        font-size: 0.5rem !important;
        height: 22px !important;
        line-height: 1.0 !important;
        max-height: 22px !important;
        -webkit-line-clamp: 2 !important;
        padding: 0 4px !important;
    }
}

/* CSS إضافي للتأكد من عدم تداخل الأنماط */
.courses-page .course-card .course-description:not(.custom-size) {
    font-size: 0.55rem !important;
}

/* إزالة أي تنسيقات متضاربة */
.courses-page .course-card .course-description {
    font-size: 0.55rem !important;
    min-height: unset !important;
    max-width: 100% !important;
}

/* تأكيد نهائي */
body.courses-page .course-card .course-description,
.courses-page body .course-card .course-description {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
}

/* CSS للتأكد من عدم تأثير أي ملفات أخرى */
.courses-page .course-card .course-description[class*="course-description"] {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
    max-height: 28px !important;
}

/* تأكيد أخير مع أعلى specificity */
html body div.courses-page div.row div.col-md-4 div.course-card div.course-body p.course-description {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
    max-height: 28px !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
}

/* إصلاح أي تضارب محتمل */
.courses-page * .course-description {
    font-size: 0.55rem !important;
}

/* تأكيد للعناصر المتداخلة */
.courses-page .main-container .row .col-md-4 .course-card .course-body .course-description {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
}

/* CSS نهائي للتأكد التام */
[class*="courses-page"] [class*="course-description"],
[class*="courses-page"] p[class*="course-description"] {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
    max-height: 28px !important;
}

/* تأكيد للعناصر التي قد تحتوي على inline styles */
.courses-page .course-description[style] {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
}

/* CSS للتغلب على أي !important آخر */
.courses-page .course-card .course-description {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
    max-height: 28px !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    text-overflow: ellipsis !important;
    color: #9ca3af !important;
    font-weight: 400 !important;
    margin-bottom: 8px !important;
    padding: 0 6px !important;
    text-align: center !important;
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    hyphens: auto !important;
}

/* تأكيد نهائي مطلق */
* .courses-page * .course-description {
    font-size: 0.55rem !important;
}

/* CSS للتأكد من التطبيق حتى مع JavaScript */
.courses-page .course-description,
.courses-page .course-description * {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
}

/* إصلاح أي تداخل مع Bootstrap */
.courses-page .card .course-description.text-muted,
.courses-page .card .course-description.small,
.courses-page .card .course-description.lead {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
}

/* CSS نهائي للتأكد من عدم تأثير أي ملف آخر */
.courses-page .course-card .course-description,
.courses-page .course-card .course-description:before,
.courses-page .course-card .course-description:after {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
}

/* تأكيد أخير للعناصر المولدة ديناميكياً */
.courses-page [data-course-description],
.courses-page .course-card [data-description] {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
}
