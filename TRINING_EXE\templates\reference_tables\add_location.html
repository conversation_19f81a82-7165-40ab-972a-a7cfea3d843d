{% extends "layout.html" %}

{% block content %}
<div class="container">
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">إضافة موقع</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                {{ form.hidden_tag() }}
                <div class="mb-3">
                    {{ form.name.label(class="form-label") }}
                    {% if form.name.errors %}
                        {{ form.name(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.name.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.name(class="form-control") }}
                    {% endif %}
                </div>
                <div class="mb-3">
                    {{ form.address.label(class="form-label") }}
                    {% if form.address.errors %}
                        {{ form.address(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.address.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.address(class="form-control") }}
                    {% endif %}
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        {{ form.governorate_id.label(class="form-label") }}
                        {% if form.governorate_id.errors %}
                            {{ form.governorate_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.governorate_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.governorate_id(class="form-select") }}
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {{ form.directorate_id.label(class="form-label") }}
                        {% if form.directorate_id.errors %}
                            {{ form.directorate_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.directorate_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.directorate_id(class="form-select") }}
                        {% endif %}
                    </div>
                </div>
                <div class="d-flex justify-content-between">
                    {{ form.submit(class="btn btn-primary") }}
                    <a href="{{ url_for('locations') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // الحصول على عناصر المحافظة والمديرية
        const governorateSelect = document.getElementById('governorate_id');
        const directorateSelect = document.getElementById('directorate_id');
        
        // إضافة مستمع حدث لتغيير المحافظة
        governorateSelect.addEventListener('change', function() {
            const governorateId = this.value;
            
            // إفراغ قائمة المديريات
            directorateSelect.innerHTML = '<option value="0">اختر المديرية</option>';
            
            // إذا تم اختيار محافظة
            if (governorateId != 0) {
                // إرسال طلب AJAX للحصول على المديريات
                fetch(`/api/directorates/${governorateId}`)
                    .then(response => response.json())
                    .then(data => {
                        // إضافة المديريات إلى القائمة المنسدلة
                        data.forEach(directorate => {
                            const option = document.createElement('option');
                            option.value = directorate.id;
                            option.textContent = directorate.name;
                            directorateSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error fetching directorates:', error));
            }
        });
    });
</script>
{% endblock %}
