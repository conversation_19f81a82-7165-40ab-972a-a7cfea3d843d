/* 
🎓 تحسينات طباعة صفحة الدورات
Courses Typography Enhancements
*/

/* تحسين عام للخطوط في صفحة الدورات */
.courses-page * {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
}

/* تصميم بطاقات الدورات المحسن - مشابه للصورة */
.course-card {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    height: 420px !important; /* ارتفاع ثابت */
    background: white !important;
    border: 1px solid #e5e7eb !important;
    margin-bottom: 20px !important;
}

.course-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.course-card .course-image {
    height: 160px !important; /* ارتفاع أصغر للصورة */
    background-size: cover !important;
    background-position: center !important;
    position: relative !important;
}

.course-card .course-title {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    line-height: 1.4 !important;
    color: #1e40af !important;
    margin-bottom: 8px !important;
    text-align: center !important;
    padding: 0 10px !important;
}

.course-card .course-instructor {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 500 !important;
    font-size: 0.85rem !important;
    color: #6b7280 !important;
    margin-bottom: 8px !important;
    text-align: center !important;
}

.course-card .course-description {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 400 !important;
    font-size: 0.8rem !important;
    line-height: 1.4 !important;
    color: #6b7280 !important;
    margin-bottom: 12px !important;
    text-align: center !important;
    padding: 0 15px !important;
    height: 60px !important; /* ارتفاع ثابت للوصف */
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
}

/* تحسين body البطاقة */
.course-card .course-body {
    padding: 15px !important;
    display: flex !important;
    flex-direction: column !important;
    height: 200px !important; /* ارتفاع ثابت للمحتوى */
}

.course-card .course-students {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 500 !important;
    font-size: 0.75rem !important;
    color: #9ca3af !important;
    text-align: center !important;
}

.course-card .badge {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 600 !important;
    font-size: 0.7rem !important;
    padding: 4px 8px !important;
    letter-spacing: 0.02em !important;
}

.course-card .btn {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 600 !important;
    font-size: 0.85rem !important;
    letter-spacing: 0.01em !important;
    padding: 8px 16px !important;
    border-radius: 8px !important;
}

/* تحسين meta section */
.course-card .course-meta {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-top: auto !important;
    padding-top: 10px !important;
    border-top: 1px solid #f3f4f6 !important;
}

/* تحسين footer البطاقة */
.course-card .card-footer {
    padding: 12px 15px !important;
    background: #f9fafb !important;
    border-top: 1px solid #f3f4f6 !important;
    text-align: center !important;
}

/* تحسين العنوان الرئيسي */
.courses-main-title {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 800 !important;
    font-size: 3.2rem !important;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    filter: drop-shadow(0 4px 8px rgba(30, 64, 175, 0.4)) !important;
    text-shadow: 0 4px 8px rgba(30, 64, 175, 0.3) !important;
    line-height: 1.2 !important;
    margin-bottom: 30px !important;
}

/* تحسين قسم الفلترة */
.filter-section .form-label {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    color: #1e40af !important;
    margin-bottom: 10px !important;
    text-shadow: 0 2px 4px rgba(30, 64, 175, 0.3) !important;
}

.filter-section .form-control,
.filter-section .form-select {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
}

/* تحسين الأزرار */
.courses-page .btn {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 700 !important;
    font-size: 1.05rem !important;
    letter-spacing: 0.02em !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.courses-page .btn-primary {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
    border: none !important;
    box-shadow: 0 8px 20px rgba(30, 64, 175, 0.3) !important;
}

.courses-page .btn-info {
    background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%) !important;
    border: none !important;
    box-shadow: 0 8px 20px rgba(8, 145, 178, 0.3) !important;
}

/* تحسين ترقيم الصفحات */
.pagination .page-link {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
}

/* تحسين التنبيهات */
.courses-page .alert {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 500 !important;
    font-size: 1.1rem !important;
    line-height: 1.6 !important;
}

/* تحسين الشريط الجانبي */
.courses-page .sidebar-link {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
}

.courses-page .sidebar .text-center h5 {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 800 !important;
    font-size: 1.4rem !important;
}

.courses-page .sidebar .badge {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 700 !important;
    font-size: 1rem !important;
}

/* تحسين الشبكة والتخطيط */
.courses-page .row {
    margin: 0 -10px !important;
}

.courses-page .col-md-4 {
    padding: 0 10px !important;
    margin-bottom: 20px !important;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .courses-main-title {
        font-size: 2rem !important;
        text-align: center !important;
    }

    .course-card {
        height: 380px !important;
    }

    .course-card .course-image {
        height: 140px !important;
    }

    .course-card .course-body {
        height: 180px !important;
    }

    .course-card .course-title {
        font-size: 1rem !important;
    }

    .course-card .course-description {
        font-size: 0.75rem !important;
        height: 50px !important;
        -webkit-line-clamp: 2 !important;
    }

    .filter-section .form-label {
        font-size: 0.9rem !important;
    }

    .courses-page .btn {
        font-size: 0.8rem !important;
        padding: 8px 14px !important;
    }
}

/* تحسين للشاشات المتوسطة */
@media (min-width: 769px) and (max-width: 1199px) {
    .course-card {
        height: 400px !important;
    }

    .course-card .course-image {
        height: 150px !important;
    }

    .course-card .course-title {
        font-size: 1.05rem !important;
    }

    .course-card .course-description {
        font-size: 0.8rem !important;
    }
}

/* تحسين للشاشات الكبيرة */
@media (min-width: 1200px) {
    .courses-main-title {
        font-size: 3rem !important;
    }

    .course-card {
        height: 440px !important;
    }

    .course-card .course-image {
        height: 170px !important;
    }

    .course-card .course-body {
        height: 210px !important;
    }

    .course-card .course-title {
        font-size: 1.15rem !important;
    }

    .course-card .course-description {
        font-size: 0.85rem !important;
        height: 65px !important;
    }
}

/* تحسين التباعد والمحاذاة - تصميم مدمج */
.courses-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 20px !important;
    padding: 20px 0 !important;
}

/* تحسين عام للبطاقات */
.course-card-wrapper {
    display: flex !important;
    height: 100% !important;
}

/* تحسين التنسيق الداخلي */
.course-card .course-body {
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    flex-grow: 1 !important;
}

/* تحسين المحتوى */
.course-content {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
}

.course-info-section {
    margin-top: auto !important;
}

/* تحسين الألوان والتباين */
.course-card .course-description {
    color: #374151 !important;
}

.course-card .course-instructor {
    color: #6b7280 !important;
}

.course-card .course-students {
    color: #6b7280 !important;
}

/* تحسين الشارات والعناصر الخاصة */
.course-category {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    font-weight: 600 !important;
    font-size: 0.7rem !important;
    letter-spacing: 0.02em !important;
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    padding: 4px 8px !important;
    border-radius: 12px !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* ألوان الشارات المحسنة */
.course-category.bg-primary { background: rgba(59, 130, 246, 0.9) !important; }
.course-category.bg-success { background: rgba(34, 197, 94, 0.9) !important; }
.course-category.bg-warning { background: rgba(245, 158, 11, 0.9) !important; }
.course-category.bg-danger { background: rgba(239, 68, 68, 0.9) !important; }
.course-category.bg-secondary { background: rgba(107, 114, 128, 0.9) !important; }
.course-category.bg-info { background: rgba(14, 165, 233, 0.9) !important; }
.course-category.bg-dark { background: rgba(55, 65, 81, 0.9) !important; }

/* تحسين التأثيرات البصرية */
.course-card:hover .course-title {
    color: #1d4ed8 !important;
    transition: color 0.3s ease !important;
}

.course-card:hover .course-description {
    color: #1f2937 !important;
    transition: color 0.3s ease !important;
}

/* تحسين النصوص العربية */
.course-card [dir="rtl"] {
    text-align: right !important;
    font-family: 'Cairo', 'Poppins', sans-serif !important;
}

/* تحسين النصوص الإنجليزية */
.course-card [dir="ltr"] {
    text-align: left !important;
    font-family: 'Poppins', 'Cairo', sans-serif !important;
}

/* تحسين الأرقام */
.course-card .number,
.course-card .count {
    font-family: 'Poppins', 'Cairo', sans-serif !important;
    font-weight: 600 !important;
    font-variant-numeric: tabular-nums !important;
}

/* تحسين التواريخ */
.course-card .date,
.course-card .time {
    font-family: 'Poppins', 'Cairo', sans-serif !important;
    font-weight: 500 !important;
    font-variant-numeric: tabular-nums !important;
}

/* تحسين عام للوضوح */
.courses-page * {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    text-rendering: optimizeLegibility !important;
}

/* تحسين التباعد بين الأحرف */
.course-card .course-title {
    letter-spacing: -0.01em !important;
}

.course-card .course-description {
    letter-spacing: 0.01em !important;
}

.course-card .btn {
    letter-spacing: 0.02em !important;
}

/* تحسين ارتفاع الأسطر */
.course-card .course-title {
    line-height: 1.3 !important;
}

.course-card .course-description {
    line-height: 1.7 !important;
}

.course-card .course-instructor,
.course-card .course-students {
    line-height: 1.5 !important;
}
