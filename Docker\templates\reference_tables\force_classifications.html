{% extends "layout.html" %}
{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h3 class="mb-0">تصنيفات القوة</h3>
            <a href="{{ url_for('add_force_classification') }}" class="btn btn-light">إضافة تصنيف جديد</a>
        </div>
        <div class="card-body">
            {% if classifications %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">الاسم</th>
                            <th scope="col">الوصف</th>
                            <th scope="col">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for classification in classifications %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ classification.name }}</td>
                            <td>{{ classification.description }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('edit_force_classification', classification_id=classification.id) }}" class="btn btn-sm btn-primary">تعديل</a>
                                    <a href="{{ url_for('delete_force_classification', classification_id=classification.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا التصنيف؟')">حذف</a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                لا توجد تصنيفات قوة مضافة حتى الآن.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
