@echo off
chcp 65001 > nul
title نظام التدريب والتأهيل - Docker - المنفذ 5001

echo.
echo ========================================
echo  نظام التدريب والتأهيل - المنفذ 5001
echo ========================================
echo.

echo 🔍 التحقق من Docker...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker غير مشغل - يرجى تشغيل Docker Desktop أولاً
    pause
    exit /b 1
)

echo ✅ Docker متاح
echo.

echo 🐳 تشغيل النظام على المنفذ 5001...
docker-compose -f docker-compose-5001.yml up -d --build

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ تم تشغيل النظام بنجاح!
    echo ========================================
    echo.
    echo 🌐 الوصول إلى النظام:
    echo    http://localhost:5001
    echo.
    echo 🔑 بيانات تسجيل الدخول:
    echo    الإيميل: <EMAIL>
    echo    كلمة المرور: admin123
    echo.
    echo ⏰ سيتم فتح المتصفح خلال 5 ثوان...
    timeout /t 5 /nobreak > nul
    start http://localhost:5001
) else (
    echo ❌ فشل في تشغيل النظام
)

echo.
pause
