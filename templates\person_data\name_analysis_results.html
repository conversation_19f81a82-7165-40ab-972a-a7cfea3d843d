{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<!-- التصميم الموحد الحديث -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/modern-unified.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/charts-universal.css') }}">
<style>
    /* تخصيص صفحة النتائج */
    .page-header {
        background: var(--white);
        border-radius: var(--radius-xl);
        padding: var(--spacing-8);
        margin-bottom: var(--spacing-8);
        box-shadow: var(--shadow-lg);
        text-align: center;
    }

    .page-title {
        font-size: var(--text-3xl);
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: var(--spacing-4);
    }

    .stats-card {
        background: var(--primary-color);
        color: var(--white);
        padding: var(--spacing-6);
        border-radius: var(--radius-lg);
        text-align: center;
        margin-bottom: var(--spacing-6);
        box-shadow: var(--shadow-lg);
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .stat-number {
        font-size: var(--text-4xl);
        font-weight: 700;
        margin: var(--spacing-4) 0;
        color: var(--white);
    }

    .stat-label {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--white);
    }

    .result-section {
        background: var(--white);
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-8);
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
    }

    .section-header {
        background: var(--gray-600);
        color: var(--white);
        padding: var(--spacing-5);
        margin-bottom: 0;
        font-weight: 600;
        font-size: var(--text-lg);
    }

    .name-item {
        background: var(--gray-50);
        border-radius: var(--radius-lg);
        padding: var(--spacing-5);
        margin-bottom: var(--spacing-4);
        border-right: 4px solid var(--success-color);
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
    }

    .name-item:hover {
        transform: translateX(8px);
        box-shadow: var(--shadow-md);
    }

    .corrected-name {
        border-right-color: var(--warning-color);
        background: #fef3c7;
    }

    .similar-name {
        border-right-color: var(--info-color);
        background: #cffafe;
    }

    .new-name {
        border-right-color: var(--danger-color);
        background: #fee2e2;
    }


</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Header Section -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-chart-line"></i> نتائج تحليل الأسماء
        </h1>
        <p class="lead">
            <i class="fas fa-file-excel"></i> الملف: <strong>{{ excel_filename }}</strong>
        </p>
        {% if selected_course %}
        <div class="alert alert-info">
            <i class="fas fa-graduation-cap"></i>
            <strong>الدورة المختارة:</strong> {{ selected_course.course_number }} - {{ selected_course.title }}
        </div>
        {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="text-center mb-6">
        <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-success">
            <i class="fas fa-download"></i> تصدير النتائج الكاملة
        </a>
        <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-success">
            <i class="fas fa-file-excel"></i> تصدير الأسماء الجديدة فقط
        </a>
        {% if results.new_names or results.corrected_names %}
        <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            {% if selected_course %}
            <button type="submit" class="btn btn-warning"
                    onclick="return confirm('هل تريد استيراد الأسماء الجديدة إلى قاعدة البيانات والدورة {{ selected_course.course_number }}؟')">
                <i class="fas fa-database"></i> استيراد للقاعدة والدورة
            </button>
            {% else %}
            <button type="submit" class="btn btn-warning"
                    onclick="return confirm('هل تريد استيراد الأسماء الجديدة إلى قاعدة البيانات؟')">
                <i class="fas fa-database"></i> استيراد الأسماء الجديدة
            </button>
            {% endif %}
        </form>
        {% endif %}
        <a href="{{ url_for('person_data.name_analysis') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> تحليل جديد
        </a>
    </div>
        </div>
    </div>



    <!-- Statistics Cards with Modern Design -->
    <div class="row mb-5">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-list-alt fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.statistics.get('total_processed', 0) }}</div>
                <div class="stat-label">إجمالي السجلات المعالجة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-spell-check fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.statistics.get('corrected_count', 0) }}</div>
                <div class="stat-label">الأسماء المصححة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-user-check fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.duplicate_in_course|length if results.duplicate_in_course else 0 }}</div>
                <div class="stat-label">موجود في القاعدة + موجود في الدورة</div>
                <div class="mt-2">
                    <small class="text-white-50">لن يتم إضافتهم</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-user-plus fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.in_db_not_in_course|length if results.in_db_not_in_course else 0 }}</div>
                <div class="stat-label">موجود في القاعدة + غير مضاف في الدورة</div>
                <div class="mt-2">
                    <small class="text-white-50">سيتم إضافتهم</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-plus-circle fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.smart_statistics.not_in_db_count if results.smart_statistics else results.statistics.get('new_records_count', 0) }}</div>
                <div class="stat-label">غير موجود في قاعدة البيانات</div>
            </div>
        </div>
        {% if results.smart_statistics %}
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-graduation-cap fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.smart_statistics.in_course_count }}</div>
                <div class="stat-label">موجود في الدورة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-user-plus fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.smart_statistics.not_in_course_count }}</div>
                <div class="stat-label">سيتم إضافته للدورة</div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Advanced Duplicate Detection Cards -->
    {% if results.statistics.get('has_national_id_column', False) or results.statistics.get('has_phone_column', False) or results.statistics.get('has_military_id_column', False) %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-search"></i> نتائج فحص التطابق المتقدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="card text-center border-danger">
                                <div class="card-body">
                                    <h5 class="text-danger">{{ results.statistics.get('blocked_duplicates_count', 0) }}</h5>
                                    <small class="text-muted">سجلات مرفوضة</small>
                                    <br><small class="text-danger">تطابق في البيانات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card text-center border-success">
                                <div class="card-body">
                                    <h5 class="text-success">{{ results.statistics.get('allowed_duplicates_count', 0) }}</h5>
                                    <small class="text-muted">أسماء مكررة مسموحة</small>
                                    <br><small class="text-success">بيانات مختلفة</small>
                                </div>
                            </div>
                        </div>
                        {% if results.statistics.get('has_national_id_column', False) %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.get('name_national_id_matches', 0) + results.statistics.get('national_id_only_matches', 0) }}</h5>
                                    <small class="text-muted">تطابق رقم وطني</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if results.statistics.get('has_phone_column', False) %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.get('name_phone_matches', 0) + results.statistics.get('phone_only_matches', 0) }}</h5>
                                    <small class="text-muted">تطابق رقم هاتف</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if results.statistics.get('has_military_id_column', False) %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.get('name_military_id_matches', 0) + results.statistics.get('military_id_only_matches', 0) }}</h5>
                                    <small class="text-muted">تطابق رقم عسكري</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if results.course_analysis and results.course_analysis.selected_course %}
    <!-- تحليل الدورة المختارة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap"></i> تحليل الدورة المختارة
                    </h5>
                </div>
                <div class="card-body">
                    <!-- معلومات الدورة -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-primary">معلومات الدورة:</h6>
                            <p class="mb-1"><strong>رقم الدورة:</strong> {{ results.course_analysis.selected_course.course_number }}</p>
                            <p class="mb-1"><strong>اسم الدورة:</strong> {{ results.course_analysis.selected_course.title }}</p>
                            <p class="mb-1"><strong>الجهة:</strong> {{ results.course_analysis.selected_course.agency or 'غير محدد' }}</p>
                            <p class="mb-0"><strong>المركز:</strong> {{ results.course_analysis.selected_course.center_name or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">إحصائيات المشاركين:</h6>
                            {% if results.course_analysis.participants_summary %}
                            <p class="mb-1"><strong>المشاركين الحاليين:</strong>
                                <span class="badge bg-info">{{ results.course_analysis.participants_summary.current_participants_count }}</span>
                            </p>
                            <p class="mb-1"><strong>المشاركين الجدد:</strong>
                                <span class="badge bg-success">{{ results.course_analysis.participants_summary.new_participants_count }}</span>
                            </p>
                            <p class="mb-1"><strong>المشاركين المكررين:</strong>
                                <span class="badge bg-warning">{{ results.course_analysis.participants_summary.duplicate_participants_count }}</span>
                            </p>
                            <p class="mb-0"><strong>إجمالي بعد الاستيراد:</strong>
                                <span class="badge bg-primary">{{ results.course_analysis.participants_summary.total_after_import }}</span>
                            </p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- تحذيرات المشاركين المكررين -->
                    {% if results.course_analysis.duplicate_participants %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> تم العثور على {{ results.course_analysis.duplicate_participants|length }} مشارك مكرر في كشف الدورة.
                        هؤلاء المشاركين موجودين مسبقاً في الدورة ولن يتم إضافتهم مرة أخرى.
                    </div>

                    <!-- عرض المشاركين المكررين -->
                    <div class="mb-3">
                        <h6 class="text-warning">المشاركين المكررين في الدورة:</h6>
                        <div class="row">
                            {% for duplicate in results.course_analysis.duplicate_participants[:5] %}
                            <div class="col-md-6 mb-2">
                                <div class="card border-warning">
                                    <div class="card-body p-2">
                                        <h6 class="card-title text-warning mb-1">{{ duplicate.name }}</h6>
                                        <small class="text-muted">{{ duplicate.reason }}</small>
                                        <br><small class="badge bg-warning">موجود في الصف {{ duplicate.excel_record.row_index }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if results.course_analysis.duplicate_participants|length > 5 %}
                        <p class="text-muted text-center mt-2">
                            <i class="fas fa-info-circle"></i>
                            يتم عرض أول 5 مشاركين مكررين فقط. للاطلاع على القائمة الكاملة، قم بتصدير النتائج.
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- المشاركين الجدد للدورة -->
                    {% if results.course_analysis.new_participants %}
                    <div class="mb-3">
                        <h6 class="text-success">المشاركين الجدد الذين سيتم إضافتهم للدورة:</h6>
                        <div class="row">
                            {% for new_participant in results.course_analysis.new_participants[:5] %}
                            <div class="col-md-6 mb-2">
                                <div class="card border-success">
                                    <div class="card-body p-2">
                                        <h6 class="card-title text-success mb-1">{{ new_participant.name }}</h6>
                                        <small class="badge bg-success">{{ new_participant.type }}</small>
                                        <br><small class="text-muted">من الصف {{ new_participant.excel_record.row_index }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if results.course_analysis.new_participants|length > 5 %}
                        <p class="text-muted text-center mt-2">
                            <i class="fas fa-info-circle"></i>
                            يتم عرض أول 5 مشاركين جدد فقط. للاطلاع على القائمة الكاملة، قم بتصدير النتائج.
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Similarity Statistics -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.statistics.get('triple_similarity_count', 0) }}</div>
                <div class="sub-stat-label">تشابه ثلاثي</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.statistics.get('quadruple_similarity_count', 0) }}</div>
                <div class="sub-stat-label">تشابه رباعي</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.statistics.get('quintuple_similarity_count', 0) }}</div>
                <div class="sub-stat-label">تشابه خماسي</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.statistics.get('full_with_title_count', 0) }}</div>
                <div class="sub-stat-label">تشابه كامل</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.statistics.get('six_plus_count', 0) }}</div>
                <div class="sub-stat-label">أسماء طويلة</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.total_db_names }}</div>
                <div class="sub-stat-label">في قاعدة البيانات</div>
            </div>
        </div>
    </div>

    <!-- Corrected Names Section -->
    {% if results.corrected_names %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-spell-check"></i> الأسماء المصححة
                <span class="badge bg-warning">{{ results.corrected_names|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="row">
                {% for correction in results.corrected_names[:10] %}
                <div class="col-md-6 mb-2">
                    <div class="name-item corrected-name">
                        <strong>الأصلي:</strong> {{ correction.original }}<br>
                        <strong>المصحح:</strong> <span class="text-success">{{ correction.corrected }}</span>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if results.corrected_names|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 أسماء فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Smart Categories - الفئات الذكية -->

    <!-- موجود في قاعدة البيانات ومكرر في الدورة -->
    {% if results.duplicate_in_course %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-exclamation-triangle"></i> موجود في قاعدة البيانات + مكرر في الدورة
                <span class="badge bg-light text-dark">{{ results.duplicate_in_course|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-danger">
                <i class="fas fa-ban"></i>
                <strong>تحذير:</strong> هؤلاء الأشخاص موجودين مسبق<|im_start|> في الدورة ولن يتم إضافتهم مرة أخرى
            </div>
            {% for record in results.duplicate_in_course[:10] %}
            <div class="card mb-2 border-danger">
                <div class="card-body p-2">
                    <div class="row">
                        <div class="col-md-8">
                            <strong class="text-danger">{{ record.corrected_name }}</strong>
                            <small class="text-muted d-block">الصف: {{ record.row_index }}</small>
                            <small class="text-danger">{{ record.action_needed }}</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-danger">مكرر في الدورة</span>
                            {% if record.course_match and record.course_match.created_at %}
                            <br><small class="text-muted">تاريخ الدخول: {{ record.course_match.created_at.strftime('%Y-%m-%d') }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.duplicate_in_course|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 سجلات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- موجود في قاعدة البيانات وسيتم إضافته للدورة -->
    {% if results.in_db_not_in_course %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-user-plus"></i> موجود في قاعدة البيانات - سيتم إضافته للدورة
                <span class="badge bg-light text-dark">{{ results.in_db_not_in_course|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <strong>إجراء:</strong> هؤلاء الأشخاص موجودين في قاعدة البيانات وسيتم إضافتهم للدورة
            </div>
            {% for record in results.in_db_not_in_course[:10] %}
            <div class="card mb-2 border-success">
                <div class="card-body p-2">
                    <div class="row">
                        <div class="col-md-8">
                            <strong class="text-success">{{ record.corrected_name }}</strong>
                            <small class="text-muted d-block">الصف: {{ record.row_index }}</small>
                            <small class="text-success">{{ record.action_needed }}</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-success">سيتم إضافته للدورة</span>
                            {% if record.db_match %}
                            <br><small class="text-muted">ID: {{ record.db_match.id }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.in_db_not_in_course|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 سجلات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Exact Matches Section -->
    {% if results.exact_matches %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-check-circle"></i> موجود في قاعدة البيانات
                <span class="badge bg-success">{{ results.exact_matches|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="row">
                {% for match in results.exact_matches[:10] %}
                <div class="col-md-12 mb-3">
                    <div class="card border-success">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-check-circle"></i> من ملف Excel:
                                    </h6>
                                    <p class="mb-1"><strong>{{ match.excel_name }}</strong></p>
                                    <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                                    {% if match.excel_record.national_id %}
                                    <br><small class="badge bg-info">رقم وطني: {{ match.excel_record.national_id }}</small>
                                    {% endif %}
                                    {% if match.excel_record.phone %}
                                    <br><small class="badge bg-success">هاتف: {{ match.excel_record.phone }}</small>
                                    {% endif %}
                                    {% if match.excel_record.military_id %}
                                    <br><small class="badge bg-warning">عسكري: {{ match.excel_record.military_id }}</small>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-2">
                                        <i class="fas fa-database"></i> من قاعدة البيانات:
                                    </h6>
                                    <p class="mb-1"><strong>{{ match.db_name }}</strong></p>
                                    <small class="text-muted">ID: {{ match.db_record.id }}</small>
                                    {% if match.db_record.national_id %}
                                    <br><small class="badge bg-info">رقم وطني: {{ match.db_record.national_id }}</small>
                                    {% endif %}
                                    {% if match.db_record.phone %}
                                    <br><small class="badge bg-success">هاتف: {{ match.db_record.phone }}</small>
                                    {% endif %}
                                    {% if match.db_record.military_id %}
                                    <br><small class="badge bg-warning">عسكري: {{ match.db_record.military_id }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if results.exact_matches|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 مطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Blocked Duplicates Section -->
    {% if results.blocked_duplicates %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-ban"></i> السجلات المرفوضة (تطابق في البيانات الشخصية)
                <span class="badge bg-light text-dark">{{ results.blocked_duplicates|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>تحذير:</strong> هذه السجلات لن يتم استيرادها بسبب التطابق في البيانات الشخصية
            </div>
            {% for blocked in results.blocked_duplicates[:10] %}
            <div class="card mb-3 border-danger">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-danger">السجل من Excel:</h6>
                            <p><strong>الاسم:</strong> {{ blocked.corrected_name }}</p>
                            {% if blocked.excel_record.national_id %}
                            <p><strong>الرقم الوطني:</strong> {{ blocked.excel_record.national_id }}</p>
                            {% endif %}
                            {% if blocked.excel_record.phone %}
                            <p><strong>رقم الهاتف:</strong> {{ blocked.excel_record.phone }}</p>
                            {% endif %}
                            {% if blocked.excel_record.military_id %}
                            <p><strong>الرقم العسكري:</strong> {{ blocked.excel_record.military_id }}</p>
                            {% endif %}
                            <small class="text-muted">الصف: {{ blocked.excel_record.row_index }}</small>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">سبب الرفض:</h6>
                            <p class="text-danger">{{ blocked.reason }}</p>
                            <div class="mt-2">
                                {% for dup_type in blocked.duplicate_types %}
                                <span class="badge bg-danger me-1">{{ dup_type }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.blocked_duplicates|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 سجلات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Allowed Duplicates Section -->
    {% if results.allowed_duplicates %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-check-circle"></i> الأسماء المكررة المسموحة (بيانات مختلفة)
                <span class="badge bg-light text-dark">{{ results.allowed_duplicates|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-success">
                <i class="fas fa-info-circle"></i>
                <strong>ملاحظة:</strong> هذه الأسماء مكررة لكن البيانات الشخصية مختلفة، لذا يمكن إضافتها
            </div>
            {% for allowed in results.allowed_duplicates[:10] %}
            <div class="card mb-3 border-success">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="text-success">السجل المسموح:</h6>
                            <p><strong>الاسم:</strong> {{ allowed.corrected_name }}</p>
                            {% if allowed.excel_record.national_id %}
                            <p><strong>الرقم الوطني:</strong> {{ allowed.excel_record.national_id }}</p>
                            {% endif %}
                            {% if allowed.excel_record.phone %}
                            <p><strong>رقم الهاتف:</strong> {{ allowed.excel_record.phone }}</p>
                            {% endif %}
                            {% if allowed.excel_record.military_id %}
                            <p><strong>الرقم العسكري:</strong> {{ allowed.excel_record.military_id }}</p>
                            {% endif %}
                            <small class="text-muted">الصف: {{ allowed.excel_record.row_index }}</small>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-success">السبب:</h6>
                            <p class="text-success">{{ allowed.reason }}</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.allowed_duplicates|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 سجلات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- New Records Section -->
    {% if results.new_records %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-plus-circle"></i> غير موجود في قاعدة البيانات
                <span class="badge bg-primary">{{ results.new_records|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            {% for record in results.new_records[:20] %}
            <div class="card mb-2 border-primary">
                <div class="card-body p-2">
                    <div class="row">
                        <div class="col-md-8">
                            <strong>{{ record.corrected_name }}</strong>
                            <small class="text-muted d-block">الصف: {{ record.excel_record.row_index }}</small>
                        </div>
                        <div class="col-md-4 text-end">
                            {% if record.excel_record.national_id %}
                            <small class="badge bg-info">رقم وطني: {{ record.excel_record.national_id }}</small>
                            {% endif %}
                            {% if record.excel_record.phone %}
                            <small class="badge bg-success">هاتف: {{ record.excel_record.phone }}</small>
                            {% endif %}
                            {% if record.excel_record.military_id %}
                            <small class="badge bg-warning">عسكري: {{ record.excel_record.military_id }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.new_records|length > 20 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 20 سجل فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Advanced Duplicate Matches Sections -->
    {% for match_type, matches in results.duplicate_matches.items() %}
    {% if matches %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-exclamation-triangle"></i>
                {% if match_type == 'name_national_id' %}
                تطابق في الاسم والرقم الوطني
                {% elif match_type == 'name_phone' %}
                تطابق في الاسم ورقم الهاتف
                {% elif match_type == 'name_military_id' %}
                تطابق في الاسم والرقم العسكري
                {% elif match_type == 'national_id_only' %}
                تطابق في الرقم الوطني فقط (أسماء مختلفة)
                {% elif match_type == 'phone_only' %}
                تطابق في رقم الهاتف فقط (أسماء مختلفة)
                {% elif match_type == 'military_id_only' %}
                تطابق في الرقم العسكري فقط (أسماء مختلفة)
                {% endif %}
                <span class="badge bg-light text-dark">{{ matches|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-warning">
                <i class="fas fa-info-circle"></i>
                <strong>تنبيه:</strong> {{ matches[0].match_details if matches }}
            </div>
            {% for match in matches[:10] %}
            <div class="card mb-3 border-warning">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">السجل من Excel:</h6>
                            <p><strong>الاسم:</strong> {{ match.excel_record.corrected_name }}</p>
                            {% if match.excel_record.national_id %}
                            <p><strong>الرقم الوطني:</strong> {{ match.excel_record.national_id }}</p>
                            {% endif %}
                            {% if match.excel_record.phone %}
                            <p><strong>رقم الهاتف:</strong> {{ match.excel_record.phone }}</p>
                            {% endif %}
                            {% if match.excel_record.military_id %}
                            <p><strong>الرقم العسكري:</strong> {{ match.excel_record.military_id }}</p>
                            {% endif %}
                            <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">السجل من قاعدة البيانات:</h6>
                            <p><strong>الاسم:</strong> {{ match.db_record.name }}</p>
                            {% if match.db_record.national_id %}
                            <p><strong>الرقم الوطني:</strong> {{ match.db_record.national_id }}</p>
                            {% endif %}
                            {% if match.db_record.phone %}
                            <p><strong>رقم الهاتف:</strong> {{ match.db_record.phone }}</p>
                            {% endif %}
                            {% if match.db_record.military_id %}
                            <p><strong>الرقم العسكري:</strong> {{ match.db_record.military_id }}</p>
                            {% endif %}
                            <small class="text-muted">ID: {{ match.db_record.id }}</small>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12">
                            <span class="badge bg-warning text-dark">{{ match.match_details }}</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if matches|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 تطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}
    {% endfor %}

    <!-- Similarity Sections -->
    {% for similarity_type, similarity_data in results.similarity_matches.items() %}
    {% if similarity_data and similarity_type != 'six_plus' %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-layer-group"></i>
                {% if similarity_type == 'triple' %}التشابه الثلاثي{% endif %}
                {% if similarity_type == 'quadruple' %}التشابه الرباعي{% endif %}
                {% if similarity_type == 'quintuple' %}التشابه الخماسي{% endif %}
                {% if similarity_type == 'full_with_title' %}التشابه الكامل مع اللقب{% endif %}
                <span class="badge bg-info">{{ similarity_data|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            {% for match in similarity_data[:5] %}
            <div class="name-item similar-name mb-3">
                <div class="row">
                    <div class="col-md-5">
                        <strong>من Excel:</strong><br>
                        {{ match.excel_name }}
                    </div>
                    <div class="col-md-5">
                        <strong>من قاعدة البيانات:</strong><br>
                        {{ match.db_name }}
                    </div>
                    <div class="col-md-2">
                        <span class="badge badge-custom bg-info">
                            {{ match.common_parts|length }} أجزاء مشتركة
                        </span>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if similarity_data|length > 5 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 5 تشابهات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}
    {% endfor %}

    <!-- Long Names Section -->
    {% if results.similarity_matches.six_plus %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-text-width"></i> الأسماء الطويلة (أكثر من 6 أجزاء)
                <span class="badge bg-warning">{{ results.similarity_matches.six_plus|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            {% for item in results.similarity_matches.six_plus[:10] %}
            <div class="name-item mb-2">
                <strong>{{ item.name }}</strong>
                <span class="badge bg-secondary">{{ item.parts_count }} أجزاء</span>
                <br>
                <small class="text-muted">{{ item.parts|join(', ') }}</small>
            </div>
            {% endfor %}
            {% if results.similarity_matches.six_plus|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 أسماء فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- الرسوم البيانية والمخططات -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie"></i> الرسوم البيانية لنتائج تحليل كشف الدورة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- الرسم البياني الدائري -->
                        <div class="col-lg-6 mb-4">
                            <h6 class="text-center mb-3">
                                <i class="fas fa-chart-pie"></i> توزيع النتائج
                            </h6>
                            <div class="chart-container">
                                <canvas id="courseAnalysisChart"></canvas>
                            </div>
                        </div>

                        <!-- الرسم البياني العمودي -->
                        <div class="col-lg-6 mb-4">
                            <h6 class="text-center mb-3">
                                <i class="fas fa-chart-bar"></i> إحصائيات مفصلة
                            </h6>
                            <div class="chart-container">
                                <canvas id="courseStatsChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- المخطط الخطي لمقارنة النتائج -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-center mb-3">
                                <i class="fas fa-chart-line"></i> مقارنة شاملة للنتائج
                            </h6>
                            <div class="chart-container">
                                <canvas id="courseLineChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- مؤشرات الأداء -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-center mb-3">
                                <i class="fas fa-tachometer-alt"></i> مؤشرات الأداء
                            </h6>
                            <div class="performance-indicators">
                                <div class="indicator">
                                    <div class="indicator-label">معدل النجاح</div>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-success" role="progressbar"
                                             style="width: {{ ((results.in_db_not_in_course|length if results.in_db_not_in_course else 0) + (results.smart_statistics.not_in_db_count if results.smart_statistics else results.statistics.get('new_records_count', 0))) / results.statistics.get('total_processed', 1) * 100 }}%">
                                        </div>
                                    </div>
                                    <div class="indicator-value">{{ "%.1f"|format(((results.in_db_not_in_course|length if results.in_db_not_in_course else 0) + (results.smart_statistics.not_in_db_count if results.smart_statistics else results.statistics.get('new_records_count', 0))) / results.statistics.get('total_processed', 1) * 100) }}%</div>
                                </div>

                                <div class="indicator">
                                    <div class="indicator-label">معدل التكرار</div>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-warning" role="progressbar"
                                             style="width: {{ (results.duplicate_in_course|length if results.duplicate_in_course else 0) / results.statistics.get('total_processed', 1) * 100 }}%">
                                        </div>
                                    </div>
                                    <div class="indicator-value">{{ "%.1f"|format((results.duplicate_in_course|length if results.duplicate_in_course else 0) / results.statistics.get('total_processed', 1) * 100) }}%</div>
                                </div>

                                <div class="indicator">
                                    <div class="indicator-label">معدل التصحيح</div>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-info" role="progressbar"
                                             style="width: {{ results.statistics.get('corrected_count', 0) / results.statistics.get('total_processed', 1) * 100 }}%">
                                        </div>
                                    </div>
                                    <div class="indicator-value">{{ "%.1f"|format(results.statistics.get('corrected_count', 0) / results.statistics.get('total_processed', 1) * 100) }}%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Section -->
    <div class="mt-5 mb-4">
        <div class="card">
            <div class="card-body">
                <h5 class="text-primary text-center mb-4">
                    <i class="fas fa-file-excel"></i> خيارات التصدير والاستيراد
                </h5>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-download text-primary mb-3" style="font-size: 2rem;"></i>
                                <h6>التقرير الكامل</h6>
                                <p class="text-muted small">جميع النتائج والإحصائيات في ملف Excel منظم</p>
                                <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-primary">
                                    <i class="fas fa-download"></i> تحميل
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-file-excel text-success mb-3" style="font-size: 2rem;"></i>
                                <h6>الأسماء الجديدة فقط</h6>
                                <p class="text-muted small">الأسماء غير الموجودة في قاعدة البيانات للمراجعة</p>
                                <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-success">
                                    <i class="fas fa-file-excel"></i> تحميل
                                </a>
                            </div>
                        </div>
                    </div>

                    {% if results.new_records or results.allowed_duplicates or results.corrected_names %}
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-database text-warning mb-3" style="font-size: 2rem;"></i>
                                <h6>استيراد مباشر</h6>
                                <p class="text-muted small">إضافة السجلات الجديدة والمسموحة مباشرة إلى قاعدة البيانات</p>
                                <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-warning"
                                            onclick="return confirm('هل تريد استيراد {{ (results.new_records|length if results.new_records else 0) + (results.allowed_duplicates|length if results.allowed_duplicates else 0) + (results.corrected_names|length if results.corrected_names else 0) }} سجل جديد إلى قاعدة البيانات{% if selected_course %} وإلى الدورة {{ selected_course.title }}{% endif %}؟')">
                                        <i class="fas fa-database"></i> استيراد
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- إحصائيات التحليل -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="text-primary mb-3 text-center">
                        <i class="fas fa-chart-pie"></i> ملخص التحليل بالنسب المئوية
                    </h6>
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-success">{{ "%.1f"|format((results.statistics.new_records_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">سجلات جديدة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-info">{{ "%.1f"|format((results.statistics.exact_matches_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">مطابقات تامة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-warning">{{ "%.1f"|format((results.statistics.get('corrected_count', 0) / results.statistics.get('total_processed', 1) * 100) if results.statistics.get('total_processed', 0) > 0 else 0) }}%</h5>
                                <small class="text-muted">أسماء مصححة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h5 class="text-primary">{{ "%.1f"|format(((results.statistics.get('triple_similarity_count', 0) + results.statistics.get('quadruple_similarity_count', 0) + results.statistics.get('quintuple_similarity_count', 0)) / results.statistics.get('total_processed', 1) * 100) if results.statistics.get('total_processed', 0) > 0 else 0) }}%</h5>
                            <small class="text-muted">تشابهات</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- مكتبات الرسوم البيانية -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
<script src="{{ url_for('static', filename='js/charts-universal.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // البيانات من النتائج
    const totalProcessed = {{ results.statistics.get('total_processed', 0) }};
    const duplicateInCourse = {{ results.duplicate_in_course|length if results.duplicate_in_course else 0 }};
    const inDbNotInCourse = {{ results.in_db_not_in_course|length if results.in_db_not_in_course else 0 }};
    const notInDb = {{ results.smart_statistics.not_in_db_count if results.smart_statistics else results.statistics.get('new_records_count', 0) }};
    const correctedCount = {{ results.statistics.get('corrected_count', 0) }};

    // الرسم البياني الدائري
    const pieCtx = document.getElementById('courseAnalysisChart');
    if (pieCtx) {
        new Chart(pieCtx, {
            type: 'pie',
            data: {
                labels: [
                    'موجود في القاعدة + موجود في الدورة',
                    'موجود في القاعدة + غير مضاف في الدورة',
                    'غير موجود في قاعدة البيانات',
                    'أسماء مصححة'
                ],
                datasets: [{
                    data: [duplicateInCourse, inDbNotInCourse, notInDb, correctedCount],
                    backgroundColor: [
                        '#dc3545', // أحمر للمكررين
                        '#28a745', // أخضر للذين سيتم إضافتهم
                        '#17a2b8', // أزرق فاتح للجدد
                        '#ffc107'  // أصفر للمصححين
                    ],
                    borderColor: [
                        '#c82333',
                        '#1e7e34',
                        '#138496',
                        '#e0a800'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 1,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                family: 'Cairo, Tajawal, sans-serif',
                                size: 11
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const percentage = ((value / totalProcessed) * 100).toFixed(1);
                                return label + ': ' + value + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }

    // الرسم البياني العمودي
    const barCtx = document.getElementById('courseStatsChart');
    if (barCtx) {
        new Chart(barCtx, {
            type: 'bar',
            data: {
                labels: [
                    'مكرر في الدورة',
                    'سيتم إضافته للدورة',
                    'جديد في القاعدة',
                    'أسماء مصححة'
                ],
                datasets: [{
                    label: 'عدد السجلات',
                    data: [duplicateInCourse, inDbNotInCourse, notInDb, correctedCount],
                    backgroundColor: [
                        'rgba(220, 53, 69, 0.8)',   // أحمر شفاف
                        'rgba(40, 167, 69, 0.8)',   // أخضر شفاف
                        'rgba(23, 162, 184, 0.8)',  // أزرق شفاف
                        'rgba(255, 193, 7, 0.8)'    // أصفر شفاف
                    ],
                    borderColor: [
                        '#dc3545',
                        '#28a745',
                        '#17a2b8',
                        '#ffc107'
                    ],
                    borderWidth: 2,
                    borderRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 1.5,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed.y;
                                const percentage = ((value / totalProcessed) * 100).toFixed(1);
                                return 'العدد: ' + value + ' (' + percentage + '%)';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1,
                            font: {
                                family: 'Cairo, Tajawal, sans-serif',
                                size: 10
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Cairo, Tajawal, sans-serif',
                                size: 10
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // المخطط الخطي لمقارنة النتائج
    const lineCtx = document.getElementById('courseLineChart');
    if (lineCtx) {
        new Chart(lineCtx, {
            type: 'line',
            data: {
                labels: [
                    'بداية التحليل',
                    'موجود في قاعدة البيانات',
                    'مكرر في الدورة',
                    'أسماء مصححة',
                    'سيتم إضافته للدورة',
                    'نتائج نهائية'
                ],
                datasets: [{
                    label: 'مسار معالجة البيانات',
                    data: [
                        totalProcessed,
                        duplicateInCourse + inDbNotInCourse,
                        duplicateInCourse,
                        correctedCount,
                        inDbNotInCourse + notInDb,
                        inDbNotInCourse + notInDb
                    ],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: [
                        '#6c757d',  // بداية التحليل
                        '#007bff',  // موجود في قاعدة البيانات
                        '#dc3545',  // مكرر في الدورة
                        '#ffc107',  // أسماء مصححة
                        '#28a745',  // سيتم إضافته للدورة
                        '#17a2b8'   // نتائج نهائية
                    ],
                    pointBorderColor: [
                        '#495057',
                        '#0056b3',
                        '#c82333',
                        '#e0a800',
                        '#1e7e34',
                        '#138496'
                    ],
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 2.5,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed.y;
                                const percentage = ((value / totalProcessed) * 100).toFixed(1);
                                return context.label + ': ' + value + ' سجل (' + percentage + '%)';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: Math.max(totalProcessed * 1.1, 10),
                        ticks: {
                            stepSize: Math.ceil(totalProcessed / 10),
                            font: {
                                family: 'Cairo, Tajawal, sans-serif',
                                size: 11
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        title: {
                            display: true,
                            text: 'عدد السجلات',
                            font: {
                                family: 'Cairo, Tajawal, sans-serif',
                                size: 12,
                                weight: 'bold'
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Cairo, Tajawal, sans-serif',
                                size: 10
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                elements: {
                    point: {
                        hoverBorderWidth: 3
                    }
                }
            }
        });
    }

    // تحسين عرض الرسوم البيانية
    function resizeCharts() {
        Chart.helpers.each(Chart.instances, function(instance) {
            instance.resize();
        });
    }

    // إعادة تحجيم الرسوم عند تغيير حجم النافذة
    window.addEventListener('resize', resizeCharts);

    // تحديث مؤشرات الأداء بشكل تفاعلي
    const indicators = document.querySelectorAll('.indicator');
    indicators.forEach((indicator, index) => {
        setTimeout(() => {
            indicator.style.opacity = '0';
            indicator.style.transform = 'translateY(20px)';

            setTimeout(() => {
                indicator.style.transition = 'all 0.5s ease';
                indicator.style.opacity = '1';
                indicator.style.transform = 'translateY(0)';
            }, 100);
        }, index * 200);
    });

    // إضافة تأثيرات للبطاقات الإحصائية
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
            this.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.2)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1)';
        });
    });

    console.log('📊 تم تحميل الرسوم البيانية لتحليل كشف الدورة بنجاح');
    console.log('📈 البيانات:', {
        'إجمالي السجلات': totalProcessed,
        'مكرر في الدورة': duplicateInCourse,
        'سيتم إضافته للدورة': inDbNotInCourse,
        'جديد في القاعدة': notInDb,
        'أسماء مصححة': correctedCount
    });
});
</script>
{% endblock %}
