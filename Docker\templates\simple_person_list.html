{% extends "layout.html" %}

{% block styles %}
<style>
    .person-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s;
    }
    
    .person-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .person-name {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .person-info {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .info-item {
        background: #f8f9fa;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 0.9rem;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
    }
    
    .search-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        color: white;
    }
    
    .search-input {
        border: none;
        border-radius: 25px;
        padding: 12px 20px;
        font-size: 16px;
        width: 100%;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .btn-action {
        margin: 2px;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 0.8rem;
    }
    
    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 30px;
    }
    
    .pagination-btn {
        background: #667eea;
        color: white;
        border: none;
        padding: 10px 15px;
        margin: 0 5px;
        border-radius: 5px;
        cursor: pointer;
    }
    
    .pagination-btn:hover {
        background: #5a67d8;
    }
    
    .pagination-btn.active {
        background: #4c51bf;
    }
    
    .pagination-btn:disabled {
        background: #cbd5e0;
        cursor: not-allowed;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-users"></i> قائمة الأشخاص
            </h2>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="totalCount">{{ total_count }}</div>
                <div>إجمالي الأشخاص</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="currentCount">{{ people|length }}</div>
                <div>المعروض حالياً</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ current_page }}</div>
                <div>الصفحة الحالية</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">{{ total_pages }}</div>
                <div>إجمالي الصفحات</div>
            </div>
        </div>
    </div>

    <!-- البحث -->
    <div class="search-box">
        <h4><i class="fas fa-search me-2"></i>البحث السريع</h4>
        <form method="GET" action="{{ url_for('simple_person_list') }}">
            <div class="row">
                <div class="col-md-8">
                    <input type="text" name="search" class="search-input" 
                           placeholder="ابحث بالاسم أو الرقم الوطني أو الرقم العسكري..."
                           value="{{ request.args.get('search', '') }}">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-light btn-lg w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="row mb-4">
        <div class="col-md-6">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addPersonModal">
                <i class="fas fa-plus"></i> إضافة شخص جديد
            </button>
            <a href="{{ url_for('person_data_import') }}" class="btn btn-info">
                <i class="fas fa-file-import"></i> استيراد من Excel
            </a>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ url_for('person_data_export') }}" class="btn btn-secondary">
                <i class="fas fa-file-export"></i> تصدير البيانات
            </a>
        </div>
    </div>

    <!-- قائمة الأشخاص -->
    <div class="row">
        {% if people %}
            {% for person in people %}
            <div class="col-md-6 col-lg-4">
                <div class="person-card">
                    <div class="person-name">
                        <i class="fas fa-user"></i> {{ person.full_name }}
                    </div>
                    
                    <div class="person-info">
                        {% if person.nickname %}
                        <div class="info-item">
                            <span class="info-label">الكنية:</span> {{ person.nickname }}
                        </div>
                        {% endif %}
                        
                        {% if person.age %}
                        <div class="info-item">
                            <span class="info-label">العمر:</span> {{ person.age }}
                        </div>
                        {% endif %}
                        
                        {% if person.governorate %}
                        <div class="info-item">
                            <span class="info-label">المحافظة:</span> {{ person.governorate }}
                        </div>
                        {% endif %}
                        
                        {% if person.national_number %}
                        <div class="info-item">
                            <span class="info-label">الرقم الوطني:</span> {{ person.national_number }}
                        </div>
                        {% endif %}
                        
                        {% if person.military_number %}
                        <div class="info-item">
                            <span class="info-label">الرقم العسكري:</span> {{ person.military_number }}
                        </div>
                        {% endif %}
                        
                        {% if person.phone %}
                        <div class="info-item">
                            <span class="info-label">الهاتف:</span> {{ person.phone }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="text-end">
                        <button class="btn btn-primary btn-action" onclick="editPerson({{ person.id }})">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-danger btn-action" onclick="deletePerson({{ person.id }}, '{{ person.full_name }}')">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-users fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد بيانات للعرض</h4>
                    <p class="text-muted">يمكنك إضافة أشخاص جدد أو استيراد بيانات من ملف Excel</p>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addPersonModal">
                        <i class="fas fa-plus"></i> إضافة أول شخص
                    </button>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- التنقل بين الصفحات -->
    {% if total_pages > 1 %}
    <div class="pagination-container">
        {% if current_page > 1 %}
        <a href="{{ url_for('simple_person_list', page=current_page-1, search=request.args.get('search', '')) }}" 
           class="pagination-btn">
            <i class="fas fa-chevron-right"></i> السابق
        </a>
        {% endif %}
        
        {% for page_num in range(1, total_pages + 1) %}
            {% if page_num == current_page %}
            <span class="pagination-btn active">{{ page_num }}</span>
            {% else %}
            <a href="{{ url_for('simple_person_list', page=page_num, search=request.args.get('search', '')) }}" 
               class="pagination-btn">{{ page_num }}</a>
            {% endif %}
        {% endfor %}
        
        {% if current_page < total_pages %}
        <a href="{{ url_for('simple_person_list', page=current_page+1, search=request.args.get('search', '')) }}" 
           class="pagination-btn">
            التالي <i class="fas fa-chevron-left"></i>
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- نافذة إضافة شخص جديد -->
<div class="modal fade" id="addPersonModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة شخص جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('simple_add_person') }}">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" name="full_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الكنية</label>
                                <input type="text" class="form-control" name="nickname">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">العمر</label>
                                <input type="number" class="form-control" name="age">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">المحافظة</label>
                                <input type="text" class="form-control" name="governorate">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">المديرية</label>
                                <input type="text" class="form-control" name="directorate">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الرقم الوطني</label>
                                <input type="text" class="form-control" name="national_number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الرقم العسكري</label>
                                <input type="text" class="form-control" name="military_number">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" name="phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المؤهل العلمي</label>
                                <input type="text" class="form-control" name="qualification">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editPerson(id) {
    window.location.href = `/person_edit/${id}`;
}

function deletePerson(id, name) {
    if (confirm(`هل أنت متأكد من حذف ${name}؟`)) {
        fetch(`/person_delete/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم الحذف بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الحذف');
            console.error(error);
        });
    }
}
</script>
{% endblock %}
