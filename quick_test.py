import requests
import re

session = requests.Session()

# Login
login_page = session.get('http://localhost:5000/login')
csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
csrf_token = csrf_match.group(1) if csrf_match else None

login_data = {'email': '<EMAIL>', 'password': 'admin123'}
if csrf_token:
    login_data['csrf_token'] = csrf_token

login_response = session.post('http://localhost:5000/login', data=login_data)
print(f"Login: {'Success' if 'dashboard' in login_response.url else 'Failed'}")

# Test APIs
tree_response = session.get('http://localhost:5000/api/system-tree')
print(f"System Tree: {tree_response.status_code}")
if tree_response.status_code == 200:
    try:
        data = tree_response.json()
        print(f"Modules: {len(data)}")
    except:
        print("J<PERSON><PERSON> Error")

roles_response = session.get('http://localhost:5000/api/roles')
print(f"Roles: {roles_response.status_code}")
if roles_response.status_code == 200:
    try:
        data = roles_response.json()
        print(f"Roles count: {len(data)}")
    except:
        print("JSON Error")
