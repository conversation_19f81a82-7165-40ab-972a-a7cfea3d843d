#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي شامل للنظام
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, CourseParticipant, PersonData, Course

def final_system_test():
    """
    اختبار نهائي شامل للنظام
    """
    with app.app_context():
        try:
            print("🧪 اختبار نهائي شامل للنظام...")
            print("=" * 60)
            
            # اختبار 1: جلب الدورات
            print("📋 اختبار 1: جلب الدورات")
            courses = Course.query.all()
            print(f"   ✅ تم جلب {len(courses)} دورة بنجاح")
            
            if courses:
                course = courses[0]
                print(f"   - الدورة الأولى: {course.title}")
                print(f"   - تاريخ البدء: {course.start_date}")
                print(f"   - تاريخ النهاية: {course.end_date}")
            
            # اختبار 2: جلب المشاركين
            print("\n📋 اختبار 2: جلب المشاركين")
            participants = CourseParticipant.query.filter_by(course_id=1).all()
            print(f"   ✅ تم جلب {len(participants)} مشارك في الدورة 1")
            
            for i, participant in enumerate(participants, 1):
                person_name = participant.personal_data.full_name
                print(f"   {i}. {person_name} (ID: {participant.personal_data_id})")
            
            # اختبار 3: جلب الأشخاص المتاحين
            print("\n📋 اختبار 3: جلب الأشخاص المتاحين للإضافة")
            existing_ids = [p.personal_data_id for p in participants]
            available_people = PersonData.query.filter(~PersonData.id.in_(existing_ids)).all()
            print(f"   ✅ تم جلب {len(available_people)} شخص متاح للإضافة")
            
            # اختبار 4: إنشاء قائمة الخيارات
            print("\n📋 اختبار 4: إنشاء قائمة الخيارات")
            choices = [(0, 'اختر مشارك')] + [(p.id, p.full_name) for p in available_people]
            print(f"   ✅ تم إنشاء {len(choices)} خيار للقائمة المنسدلة")
            
            # اختبار 5: التحقق من صحة البيانات
            print("\n📋 اختبار 5: التحقق من صحة البيانات")
            total_people = PersonData.query.count()
            expected_available = total_people - len(participants)
            actual_available = len(available_people)
            
            print(f"   - إجمالي الأشخاص: {total_people}")
            print(f"   - المشاركين المضافين: {len(participants)}")
            print(f"   - المتوقع متاح: {expected_available}")
            print(f"   - الفعلي متاح: {actual_available}")
            
            if expected_available == actual_available:
                print("   ✅ البيانات صحيحة!")
            else:
                print("   ❌ خطأ في البيانات!")
                return False
            
            # اختبار 6: اختبار العلاقات
            print("\n📋 اختبار 6: اختبار العلاقات")
            for participant in participants:
                try:
                    name = participant.personal_data.full_name
                    phone = participant.personal_data.phone
                    print(f"   ✅ العلاقة تعمل: {name} - {phone}")
                except Exception as e:
                    print(f"   ❌ خطأ في العلاقة: {e}")
                    return False
            
            print("\n" + "=" * 60)
            print("🎉 جميع الاختبارات نجحت!")
            print("✅ النظام يعمل بشكل مثالي!")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار النهائي: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    success = final_system_test()
    if success:
        print("\n🚀 النظام جاهز للاستخدام!")
    else:
        print("\n💥 يحتاج النظام إلى إصلاحات إضافية!")
