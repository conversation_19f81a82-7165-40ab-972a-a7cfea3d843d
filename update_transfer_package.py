#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 تحديث حزمة النقل مع معلومات تسجيل الدخول الصحيحة
Update Transfer Package with Correct Login Information
"""

import os
import shutil
import zipfile
from datetime import datetime

def update_transfer_package():
    """تحديث حزمة النقل مع المعلومات الصحيحة"""
    
    print("🔄 تحديث حزمة النقل...")
    print("=" * 50)
    
    # المجلد الحالي
    current_dir = "TRAINING_SYSTEM_TRANSFER_20250615_010636"
    
    if not os.path.exists(current_dir):
        print(f"❌ المجلد غير موجود: {current_dir}")
        return
    
    # إنشاء ملف ZIP جديد
    new_zip = f"TRAINING_SYSTEM_COMPLETE_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
    
    print(f"📦 إنشاء ملف مضغوط جديد: {new_zip}")
    
    with zipfile.ZipFile(new_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(current_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, current_dir)
                zipf.write(file_path, arc_name)
                print(f"✅ إضافة: {arc_name}")
    
    # حساب حجم الملف
    size_mb = os.path.getsize(new_zip) / (1024 * 1024)
    print(f"✅ تم إنشاء الملف المضغوط - الحجم: {size_mb:.1f} MB")
    
    print("\n" + "=" * 50)
    print("✅ تم تحديث حزمة النقل بنجاح!")
    print(f"📦 الملف الجديد: {new_zip}")
    print("📋 معلومات تسجيل الدخول المحدثة:")
    print("   🌐 الرابط: http://localhost:5000")
    print("   👤 المستخدم: <EMAIL>")
    print("   🔑 كلمة المرور: admin123")
    print("=" * 50)
    
    return new_zip

if __name__ == "__main__":
    try:
        new_package = update_transfer_package()
        
        print("\n🎯 الخطوات التالية:")
        print("1. انقل الملف المضغوط الجديد إلى الجهاز الآخر")
        print("2. انقل مثبت Python من مجلد PROGRAM_PYTHON-313-AMD64")
        print("3. اتبع التعليمات في ملف 'تعليمات_التشغيل.md'")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
