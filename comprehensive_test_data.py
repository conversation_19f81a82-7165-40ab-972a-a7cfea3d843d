#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إدخال بيانات تجريبية شاملة واختبار النظام
Comprehensive Test Data and System Testing
"""

import sqlite3
import requests
import json
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import random
import re

class ComprehensiveSystemTester:
    def __init__(self):
        self.base_url = 'http://localhost:5000'
        self.session = requests.Session()
        self.test_results = []
        
    def add_comprehensive_users(self):
        """إضافة مستخدمين شاملين"""
        print("👥 إضافة مستخدمين تجريبيين شاملين...")
        
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        comprehensive_users = [
            # مديرين
            {
                'username': 'ceo_ahmed',
                'email': '<EMAIL>',
                'password': 'ceo123',
                'role': 'admin',
                'first_name': 'أحمد',
                'last_name': 'المدير العام',
                'department': 'الإدارة العليا',
                'position': 'المدير التنفيذي',
                'phone': '777111111',
                'is_active': 1,
                'notes': 'المدير التنفيذي للشركة'
            },
            {
                'username': 'hr_manager',
                'email': '<EMAIL>',
                'password': 'hr123',
                'role': 'manager',
                'first_name': 'فاطمة',
                'last_name': 'الموارد البشرية',
                'department': 'الموارد البشرية',
                'position': 'مدير الموارد البشرية',
                'phone': '777222222',
                'is_active': 1,
                'notes': 'مسؤولة عن إدارة الموارد البشرية'
            },
            {
                'username': 'it_manager',
                'email': '<EMAIL>',
                'password': 'it123',
                'role': 'manager',
                'first_name': 'محمد',
                'last_name': 'تقنية المعلومات',
                'department': 'تقنية المعلومات',
                'position': 'مدير تقنية المعلومات',
                'phone': '777333333',
                'is_active': 1,
                'notes': 'مسؤول عن البنية التقنية'
            },
            
            # مدربين متخصصين
            {
                'username': 'trainer_tech',
                'email': '<EMAIL>',
                'password': 'tech123',
                'role': 'trainer',
                'first_name': 'علي',
                'last_name': 'التقني',
                'department': 'التدريب التقني',
                'position': 'مدرب تقنية المعلومات',
                'phone': '777444444',
                'is_active': 1,
                'notes': 'متخصص في تدريب التقنيات الحديثة'
            },
            {
                'username': 'trainer_soft',
                'email': '<EMAIL>',
                'password': 'soft123',
                'role': 'trainer',
                'first_name': 'نورا',
                'last_name': 'المهارات الناعمة',
                'department': 'التدريب الإداري',
                'position': 'مدربة المهارات الناعمة',
                'phone': '777555555',
                'is_active': 1,
                'notes': 'متخصصة في تدريب المهارات الإدارية'
            },
            {
                'username': 'trainer_lang',
                'email': '<EMAIL>',
                'password': 'lang123',
                'role': 'trainer',
                'first_name': 'سارة',
                'last_name': 'اللغات',
                'department': 'تدريب اللغات',
                'position': 'مدربة اللغات',
                'phone': '777666666',
                'is_active': 1,
                'notes': 'متخصصة في تدريب اللغات الأجنبية'
            },
            
            # مدخلي بيانات
            {
                'username': 'data_senior',
                'email': '<EMAIL>',
                'password': 'data123',
                'role': 'data_entry',
                'first_name': 'خالد',
                'last_name': 'البيانات الأول',
                'department': 'إدخال البيانات',
                'position': 'مدخل بيانات أول',
                'phone': '777777777',
                'is_active': 1,
                'notes': 'مدخل بيانات خبير'
            },
            {
                'username': 'data_junior',
                'email': '<EMAIL>',
                'password': 'data123',
                'role': 'data_entry',
                'first_name': 'مريم',
                'last_name': 'البيانات المساعدة',
                'department': 'إدخال البيانات',
                'position': 'مدخلة بيانات',
                'phone': '777888888',
                'is_active': 1,
                'notes': 'مدخلة بيانات جديدة'
            },
            
            # مشاهدين ومراجعين
            {
                'username': 'auditor',
                'email': '<EMAIL>',
                'password': 'audit123',
                'role': 'viewer',
                'first_name': 'عبدالله',
                'last_name': 'المراجع',
                'department': 'المراجعة الداخلية',
                'position': 'مراجع داخلي',
                'phone': '777999999',
                'is_active': 1,
                'notes': 'مراجع داخلي للعمليات'
            },
            {
                'username': 'consultant',
                'email': '<EMAIL>',
                'password': 'consult123',
                'role': 'viewer',
                'first_name': 'ليلى',
                'last_name': 'الاستشارية',
                'department': 'الاستشارات',
                'position': 'مستشارة خارجية',
                'phone': '777000000',
                'is_active': 1,
                'notes': 'مستشارة خارجية للتطوير'
            },
            
            # مستخدمين معطلين للاختبار
            {
                'username': 'suspended_user',
                'email': '<EMAIL>',
                'password': 'susp123',
                'role': 'viewer',
                'first_name': 'مستخدم',
                'last_name': 'معلق',
                'department': 'اختبار',
                'position': 'مختبر',
                'phone': '777111000',
                'is_active': 0,
                'notes': 'مستخدم معلق للاختبار'
            },
            {
                'username': 'temp_user',
                'email': '<EMAIL>',
                'password': 'temp123',
                'role': 'data_entry',
                'first_name': 'مؤقت',
                'last_name': 'للاختبار',
                'department': 'اختبار',
                'position': 'مؤقت',
                'phone': '777222000',
                'is_active': 0,
                'notes': 'مستخدم مؤقت للاختبار'
            }
        ]
        
        created_count = 0
        
        try:
            for user_data in comprehensive_users:
                # التحقق من عدم وجود المستخدم
                cursor.execute("SELECT id FROM user WHERE username = ? OR email = ?", 
                             (user_data['username'], user_data['email']))
                if cursor.fetchone():
                    print(f"⚠️ المستخدم {user_data['username']} موجود بالفعل")
                    continue
                
                # تشفير كلمة المرور
                hashed_password = generate_password_hash(user_data['password'])
                
                # إدخال المستخدم
                cursor.execute('''
                    INSERT INTO user (
                        username, email, password, role, first_name, last_name,
                        department, position, phone, is_active, notes, created_at,
                        login_attempts
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    user_data['username'],
                    user_data['email'],
                    hashed_password,
                    user_data['role'],
                    user_data['first_name'],
                    user_data['last_name'],
                    user_data['department'],
                    user_data['position'],
                    user_data['phone'],
                    user_data['is_active'],
                    user_data['notes'],
                    datetime.now().isoformat(),
                    0
                ))
                
                created_count += 1
                print(f"✅ تم إنشاء: {user_data['username']} ({user_data['first_name']} {user_data['last_name']})")
            
            conn.commit()
            print(f"\n🎉 تم إنشاء {created_count} مستخدم جديد!")
            
        except Exception as e:
            print(f"❌ خطأ في إضافة المستخدمين: {e}")
            conn.rollback()
        finally:
            conn.close()
            
        return created_count

    def test_login_all_users(self):
        """اختبار تسجيل الدخول لجميع المستخدمين"""
        print("\n🔐 اختبار تسجيل الدخول لجميع المستخدمين...")
        
        # قائمة المستخدمين للاختبار
        test_users = [
            ('<EMAIL>', 'admin123', 'المدير الأساسي'),
            ('<EMAIL>', 'ceo123', 'المدير التنفيذي'),
            ('<EMAIL>', 'hr123', 'مدير الموارد البشرية'),
            ('<EMAIL>', 'it123', 'مدير تقنية المعلومات'),
            ('<EMAIL>', 'tech123', 'مدرب التقنية'),
            ('<EMAIL>', 'soft123', 'مدربة المهارات'),
            ('<EMAIL>', 'data123', 'مدخل البيانات الأول'),
            ('<EMAIL>', 'audit123', 'المراجع الداخلي'),
            ('<EMAIL>', 'susp123', 'المستخدم المعلق')
        ]
        
        successful_logins = 0
        failed_logins = 0
        
        for email, password, description in test_users:
            try:
                # إنشاء جلسة جديدة
                session = requests.Session()
                
                # الحصول على صفحة تسجيل الدخول
                login_page = session.get(f'{self.base_url}/login')
                
                # استخراج CSRF token
                csrf_token = None
                if 'csrf_token' in login_page.text:
                    csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
                    csrf_token = csrf_match.group(1) if csrf_match else None
                
                # بيانات تسجيل الدخول
                login_data = {
                    'email': email,
                    'password': password
                }
                
                if csrf_token:
                    login_data['csrf_token'] = csrf_token
                
                # محاولة تسجيل الدخول
                response = session.post(f'{self.base_url}/login', data=login_data)
                
                if response.status_code in [200, 302] and ('dashboard' in response.url or 'dashboard' in response.text):
                    print(f"   ✅ {description}: نجح تسجيل الدخول")
                    successful_logins += 1
                    
                    # اختبار الوصول لصفحة إدارة المستخدمين
                    users_page = session.get(f'{self.base_url}/admin/users')
                    if users_page.status_code == 200:
                        print(f"      ✅ يمكن الوصول لصفحة إدارة المستخدمين")
                    elif users_page.status_code == 403:
                        print(f"      ⚠️ ليس لديه صلاحية (طبيعي)")
                    else:
                        print(f"      ❌ خطأ في الوصول: {users_page.status_code}")
                    
                    # تسجيل الخروج
                    session.get(f'{self.base_url}/logout')
                    
                else:
                    print(f"   ❌ {description}: فشل تسجيل الدخول ({response.status_code})")
                    failed_logins += 1
                    
            except Exception as e:
                print(f"   ❌ {description}: خطأ - {e}")
                failed_logins += 1
        
        print(f"\n📊 نتائج اختبار تسجيل الدخول:")
        print(f"   ✅ نجح: {successful_logins}")
        print(f"   ❌ فشل: {failed_logins}")
        print(f"   📈 معدل النجاح: {(successful_logins/(successful_logins+failed_logins))*100:.1f}%")
        
        return successful_logins, failed_logins

    def test_user_management_functions(self):
        """اختبار وظائف إدارة المستخدمين"""
        print("\n⚙️ اختبار وظائف إدارة المستخدمين...")
        
        # تسجيل الدخول كمدير
        session = requests.Session()
        
        # الحصول على CSRF token
        login_page = session.get(f'{self.base_url}/login')
        csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
        csrf_token = csrf_match.group(1) if csrf_match else None
        
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123'
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token
        
        login_response = session.post(f'{self.base_url}/login', data=login_data)
        
        if login_response.status_code not in [200, 302]:
            print("❌ فشل تسجيل الدخول كمدير")
            return False
        
        print("✅ تم تسجيل الدخول كمدير")
        
        # 1. اختبار إنشاء مستخدم جديد
        print("\n1️⃣ اختبار إنشاء مستخدم جديد...")
        new_user_data = {
            'username': f'test_user_{datetime.now().strftime("%H%M%S")}',
            'email': f'test_{datetime.now().strftime("%H%M%S")}@test.com',
            'password': 'test123',
            'confirm_password': 'test123',
            'role': 'viewer',
            'first_name': 'مستخدم',
            'last_name': 'اختبار جديد',
            'department': 'قسم الاختبار',
            'position': 'مختبر',
            'phone': '777123000',
            'is_active': True
        }
        
        create_response = session.post(
            f'{self.base_url}/admin/users/create',
            json=new_user_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if create_response.status_code == 200:
            result = create_response.json()
            if result.get('success'):
                print(f"   ✅ تم إنشاء المستخدم: {new_user_data['username']}")
            else:
                print(f"   ❌ فشل إنشاء المستخدم: {result.get('message')}")
        else:
            print(f"   ❌ خطأ في إنشاء المستخدم: {create_response.status_code}")
        
        # 2. اختبار إعادة تعيين كلمة المرور
        print("\n2️⃣ اختبار إعادة تعيين كلمة المرور...")
        reset_response = session.post(f'{self.base_url}/admin/users/2/reset-password')
        
        if reset_response.status_code == 200:
            result = reset_response.json()
            if result.get('success'):
                print(f"   ✅ تم إعادة تعيين كلمة المرور: {result.get('new_password')}")
            else:
                print(f"   ❌ فشل إعادة تعيين كلمة المرور: {result.get('message')}")
        else:
            print(f"   ❌ خطأ في إعادة تعيين كلمة المرور: {reset_response.status_code}")
        
        # 3. اختبار تغيير حالة المستخدم
        print("\n3️⃣ اختبار تغيير حالة المستخدم...")
        toggle_response = session.post(
            f'{self.base_url}/admin/users/12/toggle-status',
            json={'status': True},
            headers={'Content-Type': 'application/json'}
        )
        
        if toggle_response.status_code == 200:
            result = toggle_response.json()
            if result.get('success'):
                print(f"   ✅ تم تغيير حالة المستخدم")
            else:
                print(f"   ❌ فشل تغيير حالة المستخدم: {result.get('message')}")
        else:
            print(f"   ❌ خطأ في تغيير حالة المستخدم: {toggle_response.status_code}")
        
        return True

    def check_database_integrity(self):
        """فحص سلامة قاعدة البيانات"""
        print("\n🔍 فحص سلامة قاعدة البيانات...")
        
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        try:
            # فحص الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = [
                'user', 'permission', 'role', 'user_role', 
                'user_permission', 'role_permission', 'user_session', 'audit_log'
            ]
            
            print("📋 فحص الجداول:")
            for table in required_tables:
                if table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"   ✅ {table}: {count} سجل")
                else:
                    print(f"   ❌ {table}: غير موجود")
            
            # فحص المستخدمين
            cursor.execute("SELECT COUNT(*) FROM user WHERE is_active = 1")
            active_users = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM user WHERE is_active = 0")
            inactive_users = cursor.fetchone()[0]
            
            print(f"\n👥 إحصائيات المستخدمين:")
            print(f"   النشطين: {active_users}")
            print(f"   المعطلين: {inactive_users}")
            print(f"   الإجمالي: {active_users + inactive_users}")
            
            # فحص الأدوار
            cursor.execute("SELECT COUNT(*) FROM role WHERE is_active = 1")
            active_roles = cursor.fetchone()[0]
            print(f"   الأدوار النشطة: {active_roles}")
            
            # فحص الصلاحيات
            cursor.execute("SELECT COUNT(*) FROM permission WHERE is_active = 1")
            active_permissions = cursor.fetchone()[0]
            print(f"   الصلاحيات النشطة: {active_permissions}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
            return False
        finally:
            conn.close()

    def run_comprehensive_test(self):
        """تشغيل اختبار شامل للنظام"""
        print("🚀 بدء الاختبار الشامل لنظام إدارة المستخدمين والصلاحيات")
        print("=" * 80)
        
        # 1. إضافة بيانات تجريبية
        users_added = self.add_comprehensive_users()
        
        # 2. فحص سلامة قاعدة البيانات
        db_ok = self.check_database_integrity()
        
        # 3. اختبار تسجيل الدخول
        successful, failed = self.test_login_all_users()
        
        # 4. اختبار وظائف إدارة المستخدمين
        functions_ok = self.test_user_management_functions()
        
        # 5. ملخص النتائج
        print("\n" + "="*80)
        print("📊 ملخص الاختبار الشامل")
        print("="*80)
        
        print(f"👥 المستخدمين المضافين: {users_added}")
        print(f"🔍 سلامة قاعدة البيانات: {'✅ سليمة' if db_ok else '❌ مشاكل'}")
        print(f"🔐 تسجيل الدخول: {successful} نجح، {failed} فشل")
        print(f"⚙️ وظائف الإدارة: {'✅ تعمل' if functions_ok else '❌ مشاكل'}")
        
        overall_success = db_ok and (successful > failed) and functions_ok
        
        if overall_success:
            print("\n🎉 النظام يعمل بشكل ممتاز!")
            print("\n🔗 روابط مهمة:")
            print("   الصفحة الرئيسية: http://localhost:5000")
            print("   تسجيل الدخول: http://localhost:5000/login")
            print("   إدارة المستخدمين: http://localhost:5000/admin/users")
            print("   لوحة التحكم: http://localhost:5000/dashboard")
        else:
            print("\n⚠️ هناك مشاكل تحتاج إلى إصلاح")
        
        return overall_success

def main():
    """الدالة الرئيسية"""
    tester = ComprehensiveSystemTester()
    
    try:
        # التحقق من تشغيل الخادم
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code != 200:
            print("❌ الخادم غير متاح. تأكد من تشغيل النظام أولاً.")
            return
    except requests.exceptions.RequestException:
        print("❌ لا يمكن الوصول للخادم. تأكد من تشغيل النظام على localhost:5000")
        return
    
    # تشغيل الاختبار الشامل
    success = tester.run_comprehensive_test()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
    else:
        print("\n❌ بعض الاختبارات فشلت!")

if __name__ == '__main__':
    main()
