@echo off
chcp 65001 >nul
title نظام التدريب والتأهيل

echo ================================================================================
echo 🎯 نظام التدريب والتأهيل
echo ================================================================================
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 🎯 بدء تشغيل النظام...
echo 🌐 الرابط: http://localhost:5000
echo 👤 اسم المستخدم: admin
echo 🔐 كلمة المرور: admin
echo 🛑 لإيقاف النظام اضغط Ctrl+C
echo ================================================================================
echo.

REM محاولة تشغيل الملف المحدث أولاً
if exist "تشغيل_النظام_المحدث.py" (
    echo 🔄 تشغيل الإصدار المحدث...
    python "تشغيل_النظام_المحدث.py"
) else if exist "تشغيل_النظام.py" (
    echo 🔄 تشغيل الإصدار الأصلي...
    python "تشغيل_النظام.py"
) else if exist "run_server.py" (
    echo 🔄 تشغيل الخادم...
    python run_server.py
) else (
    echo ❌ لم يتم العثور على ملفات التشغيل
    echo تأكد من وجود أحد الملفات التالية:
    echo - تشغيل_النظام_المحدث.py
    echo - تشغيل_النظام.py  
    echo - run_server.py
)

echo.
echo 🛑 تم إيقاف النظام
pause
