#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل نظام التدريب والتأهيل - الإصدار المحدث والمحسن
"""

import os
import sys
import subprocess
import time
import webbrowser
from threading import Timer

def check_python_version():
    """التحقق من إصدار Python"""
    print("🔍 التحقق من إصدار Python...")
    try:
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            print(f"❌ يتطلب Python 3.8 أو أحدث")
            print(f"الإصدار الحالي: {python_version.major}.{python_version.minor}.{python_version.micro}")
            return False
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        return True
    except Exception as e:
        print(f"❌ خطأ في فحص Python: {e}")
        return False

def check_and_install_requirements():
    """التحقق من المتطلبات وتثبيتها إذا لزم الأمر"""
    print("🔍 التحقق من المتطلبات...")
    
    required_packages = [
        ('flask', 'flask'),
        ('flask_sqlalchemy', 'flask-sqlalchemy'),
        ('flask_login', 'flask-login'),
        ('flask_wtf', 'flask-wtf'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('arabic_reshaper', 'arabic-reshaper'),
        ('bidi', 'python-bidi'),
        ('wtforms', 'wtforms'),
        ('werkzeug', 'werkzeug'),
        ('tqdm', 'tqdm')
    ]
    
    missing_packages = []
    
    for import_name, install_name in required_packages:
        try:
            if import_name == 'flask_sqlalchemy':
                __import__('flask_sqlalchemy')
            elif import_name == 'flask_login':
                __import__('flask_login')
            elif import_name == 'flask_wtf':
                __import__('flask_wtf')
            else:
                __import__(import_name)
            print(f"✅ {import_name}")
        except ImportError:
            missing_packages.append(install_name)
            print(f"❌ {import_name} غير مثبت")
    
    if missing_packages:
        print(f"\n🔧 تثبيت {len(missing_packages)} حزمة مفقودة...")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '--upgrade'
            ] + missing_packages)
            print("✅ تم تثبيت جميع المتطلبات بنجاح")
            time.sleep(2)
            return True
        except Exception as e:
            print(f"❌ خطأ في تثبيت المتطلبات: {e}")
            print("\n🔧 يرجى تثبيت المتطلبات يدوياً:")
            print("pip install " + " ".join(missing_packages))
            return False
    
    return True

def check_system_files():
    """التحقق من وجود الملفات المطلوبة"""
    print("🔍 التحقق من ملفات النظام...")
    
    required_files = ['app.py']
    optional_files = ['training_system.db', 'instance/training_system.db']
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            missing_files.append(file)
            print(f"❌ {file} غير موجود")
    
    # التحقق من قاعدة البيانات
    db_found = False
    for db_file in optional_files:
        if os.path.exists(db_file):
            print(f"✅ قاعدة البيانات: {db_file}")
            db_found = True
            break
    
    if not db_found:
        print("⚠️ لم يتم العثور على قاعدة البيانات - سيتم إنشاؤها تلقائياً")
    
    if missing_files:
        print(f"\n❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    return True

def open_browser():
    """فتح المتصفح تلقائياً"""
    urls_to_try = [
        'http://localhost:5000',
        'http://127.0.0.1:5000'
    ]
    
    for url in urls_to_try:
        try:
            print(f"🌐 فتح المتصفح: {url}")
            webbrowser.open(url)
            break
        except Exception as e:
            print(f"تعذر فتح {url}: {e}")
            continue

def main():
    print("=" * 80)
    print("🚀 نظام التدريب والتأهيل - الإصدار المحدث")
    print("=" * 80)
    print()
    
    # التحقق من Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من المتطلبات
    if not check_and_install_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من ملفات النظام
    if not check_system_files():
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل النظام
    print("\n" + "=" * 80)
    print("🚀 بدء تشغيل النظام...")
    print("📍 الرابط الرئيسي: http://localhost:5000")
    print("🔑 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin")
    print()
    print("📋 الصفحات المهمة:")
    print("   • الصفحة الرئيسية: http://localhost:5000")
    print("   • لوحة التقارير: http://localhost:5000/reports/dashboard")
    print("   • إدارة البيانات: http://localhost:5000/person_data")
    print("   • جدول البيانات: http://localhost:5000/person_data_table")
    print()
    print("🛑 لإيقاف النظام اضغط Ctrl+C")
    print("=" * 80)
    
    try:
        # استيراد التطبيق
        print("🔄 تحميل التطبيق...")
        from app import app, db
        print("✅ تم تحميل التطبيق بنجاح")
        
        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        print("🔄 التحقق من قاعدة البيانات...")
        with app.app_context():
            db.create_all()
        print("✅ قاعدة البيانات جاهزة")
        
        print("🌐 النظام جاهز للاستخدام!")
        print("⏰ سيتم فتح المتصفح تلقائياً خلال 3 ثوان...")
        print()
        
        # فتح المتصفح تلقائياً بعد 3 ثوان
        Timer(3.0, open_browser).start()
        
        # تشغيل الخادم
        app.run(
            host='localhost',
            port=5000,
            debug=False,
            threaded=True,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف النظام بواسطة المستخدم")
        print("شكراً لاستخدام نظام التدريب والتأهيل!")
        
    except ImportError as e:
        print(f"\n❌ خطأ في استيراد الوحدات: {e}")
        print("تأكد من تثبيت جميع المتطلبات")
        
    except OSError as e:
        if "Address already in use" in str(e):
            print("\n❌ المنفذ 5000 مستخدم من برنامج آخر")
            print("🔧 أغلق البرنامج الآخر أو أعد تشغيل الحاسوب")
        else:
            print(f"\n❌ خطأ في الشبكة: {e}")
            
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        print("\n🔧 تأكد من:")
        print("1. تثبيت جميع المتطلبات")
        print("2. وجود ملف app.py في نفس المجلد")
        print("3. عدم استخدام المنفذ 5000 من تطبيق آخر")
        print("4. صحة ملف قاعدة البيانات")
        import traceback
        traceback.print_exc()
    
    input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
