# نظام إدارة التدريب - تشغيل النظام
# Training Management System - Start System

param(
    [switch]$Setup,
    [switch]$Quick,
    [switch]$Help
)

function Show-Help {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "    نظام إدارة التدريب - المساعدة" -ForegroundColor Yellow
    Write-Host "    Training System - Help" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "الاستخدام:" -ForegroundColor Green
    Write-Host "Usage:" -ForegroundColor Green
    Write-Host ""
    Write-Host "  .\start_system.ps1 -Setup    # إعداد النظام لأول مرة" -ForegroundColor Cyan
    Write-Host "  .\start_system.ps1 -Quick    # تشغيل سريع" -ForegroundColor Cyan
    Write-Host "  .\start_system.ps1 -Help     # عرض المساعدة" -ForegroundColor Cyan
    Write-Host "  .\start_system.ps1           # تشغيل عادي" -ForegroundColor Cyan
    Write-Host ""
}

function Test-Prerequisites {
    Write-Host "🔧 فحص المتطلبات الأساسية..." -ForegroundColor Blue
    
    # فحص Python
    try {
        $pythonVersion = python --version 2>&1
        Write-Host "✅ Python: $pythonVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ Python غير مثبت!" -ForegroundColor Red
        return $false
    }
    
    # فحص البيئة الافتراضية
    if (Test-Path ".venv") {
        Write-Host "✅ البيئة الافتراضية موجودة" -ForegroundColor Green
    } else {
        Write-Host "⚠️ البيئة الافتراضية غير موجودة" -ForegroundColor Yellow
        return $false
    }
    
    # فحص الملفات الأساسية
    $requiredFiles = @("app.py", "templates", "static")
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-Host "✅ $file موجود" -ForegroundColor Green
        } else {
            Write-Host "❌ $file مفقود" -ForegroundColor Red
            return $false
        }
    }
    
    return $true
}

function Setup-System {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "    إعداد نظام إدارة التدريب" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    # إنشاء البيئة الافتراضية
    if (!(Test-Path ".venv")) {
        Write-Host "📦 إنشاء البيئة الافتراضية..." -ForegroundColor Blue
        python -m venv .venv
        Write-Host "✅ تم إنشاء البيئة الافتراضية" -ForegroundColor Green
    }
    
    # تفعيل البيئة الافتراضية
    Write-Host "🔧 تفعيل البيئة الافتراضية..." -ForegroundColor Blue
    & ".venv\Scripts\Activate.ps1"
    
    # تحديث pip
    Write-Host "📦 تحديث pip..." -ForegroundColor Blue
    python -m pip install --upgrade pip
    
    # تثبيت المكتبات
    Write-Host "📦 تثبيت المكتبات..." -ForegroundColor Blue
    pip install Flask Flask-SQLAlchemy Flask-Login Flask-WTF WTForms pandas openpyxl xlsxwriter SQLAlchemy arabic-reshaper python-bidi tqdm email-validator
    
    # إنشاء المجلدات
    Write-Host "📁 إنشاء المجلدات..." -ForegroundColor Blue
    $folders = @("static", "static\css", "static\js", "static\libs", "uploads", "exports")
    foreach ($folder in $folders) {
        if (!(Test-Path $folder)) {
            New-Item -ItemType Directory -Path $folder -Force | Out-Null
        }
    }
    
    Write-Host "✅ تم إعداد النظام بنجاح!" -ForegroundColor Green
}

function Start-System {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "    تشغيل نظام إدارة التدريب" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    # تفعيل البيئة الافتراضية
    Write-Host "🔧 تفعيل البيئة الافتراضية..." -ForegroundColor Blue
    & ".venv\Scripts\Activate.ps1"
    
    # تشغيل النظام
    Write-Host "🚀 تشغيل النظام..." -ForegroundColor Green
    Write-Host "🌐 النظام متاح على: http://localhost:5000" -ForegroundColor Cyan
    Write-Host "⏹️ للإيقاف: اضغط Ctrl+C" -ForegroundColor Yellow
    Write-Host ""
    
    python app.py
}

# المنطق الرئيسي
if ($Help) {
    Show-Help
    exit
}

if ($Setup) {
    Setup-System
    Write-Host ""
    Write-Host "هل تريد تشغيل النظام الآن؟ (y/n): " -NoNewline -ForegroundColor Yellow
    $response = Read-Host
    if ($response -eq 'y' -or $response -eq 'Y' -or $response -eq 'yes') {
        Start-System
    }
    exit
}

if ($Quick) {
    if (Test-Prerequisites) {
        Start-System
    } else {
        Write-Host ""
        Write-Host "❌ المتطلبات غير مكتملة. يرجى تشغيل الإعداد أولاً:" -ForegroundColor Red
        Write-Host ".\start_system.ps1 -Setup" -ForegroundColor Cyan
    }
    exit
}

# التشغيل العادي
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    نظام إدارة التدريب" -ForegroundColor Yellow
Write-Host "    Training Management System" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

if (Test-Prerequisites) {
    Start-System
} else {
    Write-Host ""
    Write-Host "❌ المتطلبات غير مكتملة." -ForegroundColor Red
    Write-Host "هل تريد إعداد النظام الآن؟ (y/n): " -NoNewline -ForegroundColor Yellow
    $response = Read-Host
    if ($response -eq 'y' -or $response -eq 'Y' -or $response -eq 'yes') {
        Setup-System
        Write-Host ""
        Start-System
    } else {
        Write-Host "يرجى تشغيل الإعداد لاحقاً:" -ForegroundColor Yellow
        Write-Host ".\start_system.ps1 -Setup" -ForegroundColor Cyan
    }
}
