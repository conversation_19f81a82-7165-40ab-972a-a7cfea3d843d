#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الدورات مع تسجيل الدخول
"""

import requests
from requests.sessions import Session

def login_and_test_courses():
    """تسجيل الدخول واختبار الدورات"""
    
    # إنشاء جلسة للحفاظ على الكوكيز
    session = Session()
    
    try:
        print("🔐 محاولة تسجيل الدخول...")
        
        # الحصول على صفحة تسجيل الدخول أولاً للحصول على CSRF token
        login_page = session.get('http://127.0.0.1:5000/login')
        
        if login_page.status_code != 200:
            print(f"❌ فشل في الوصول إلى صفحة تسجيل الدخول: {login_page.status_code}")
            return False
        
        # محاولة تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin'
        }
        
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data=login_data,
                                    allow_redirects=False)
        
        print(f"📋 نتيجة تسجيل الدخول: {login_response.status_code}")
        
        if login_response.status_code == 302:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print("❌ فشل في تسجيل الدخول")
            print(f"المحتوى: {login_response.text[:200]}...")
            return False
        
        # الآن اختبار صفحة الدورات
        print("\n📚 اختبار صفحة الدورات...")
        courses_response = session.get('http://127.0.0.1:5000/courses')
        
        if courses_response.status_code == 200:
            print("✅ تم الوصول إلى صفحة الدورات بنجاح")
            
            content = courses_response.text
            
            # البحث عن الدورات
            courses_found = []
            if 'المبيعات2' in content:
                courses_found.append('المبيعات2')
                print("✅ تم العثور على دورة المبيعات2")
            
            if 'تدريب الذكاء الاصطناعي' in content:
                courses_found.append('تدريب الذكاء الاصطناعي')
                print("✅ تم العثور على دورة الذكاء الاصطناعي")
            
            if 'تدريب الذكاء الاصطناعي 2' in content:
                courses_found.append('تدريب الذكاء الاصطناعي 2')
                print("✅ تم العثور على دورة الذكاء الاصطناعي 2")
            
            # فحص رسالة "لا توجد دورات"
            if 'لا توجد دورات تدريبية متاحة حالياً' in content:
                print("❌ ظهرت رسالة 'لا توجد دورات'")
                return False
            
            print(f"\n📊 النتيجة: تم العثور على {len(courses_found)} دورة")
            
            if len(courses_found) == 3:
                print("🎉 جميع الدورات الثلاث ظاهرة في النظام!")
                return True
            elif len(courses_found) > 0:
                print("⚠️ بعض الدورات ظاهرة لكن ليس جميعها")
                return False
            else:
                print("❌ لا توجد دورات ظاهرة في النظام")
                return False
        else:
            print(f"❌ فشل في الوصول إلى صفحة الدورات: {courses_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def test_dashboard():
    """اختبار لوحة التحكم"""
    session = Session()
    
    try:
        print("\n🏠 اختبار لوحة التحكم...")
        
        # تسجيل الدخول أولاً
        login_page = session.get('http://127.0.0.1:5000/login')
        login_data = {'username': 'admin', 'password': 'admin'}
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data=login_data,
                                    allow_redirects=False)
        
        if login_response.status_code != 302:
            print("❌ فشل في تسجيل الدخول للوحة التحكم")
            return False
        
        # اختبار لوحة التحكم
        dashboard_response = session.get('http://127.0.0.1:5000/dashboard')
        
        if dashboard_response.status_code == 200:
            print("✅ تم الوصول إلى لوحة التحكم بنجاح")
            return True
        else:
            print(f"❌ فشل في الوصول إلى لوحة التحكم: {dashboard_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار لوحة التحكم: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الدورات مع تسجيل الدخول")
    print("=" * 50)
    
    # اختبار الدورات
    courses_ok = login_and_test_courses()
    
    # اختبار لوحة التحكم
    dashboard_ok = test_dashboard()
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    print("📋 ملخص الاختبار:")
    
    if courses_ok:
        print("✅ الدورات تظهر بشكل صحيح في النظام")
    else:
        print("❌ مشكلة في عرض الدورات")
    
    if dashboard_ok:
        print("✅ لوحة التحكم تعمل بشكل صحيح")
    else:
        print("❌ مشكلة في لوحة التحكم")
    
    if courses_ok and dashboard_ok:
        print("\n🎉 النظام يعمل بشكل مثالي!")
        print("🔗 يمكنك الآن:")
        print("   1. تسجيل الدخول: http://127.0.0.1:5000/login")
        print("   2. زيارة الدورات: http://127.0.0.1:5000/courses")
        print("   3. زيارة لوحة التحكم: http://127.0.0.1:5000/dashboard")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى إصلاح")

if __name__ == "__main__":
    main()
