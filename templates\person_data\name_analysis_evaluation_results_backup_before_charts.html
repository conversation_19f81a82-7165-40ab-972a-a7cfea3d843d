{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<style>
    .stats-card {
        background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .result-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-right: 4px solid #ff6b6b;
    }

    .name-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 10px;
        border-right: 3px solid #28a745;
    }

    .corrected-name {
        border-right-color: #ffc107;
    }

    .similar-name {
        border-right-color: #17a2b8;
    }

    .new-name {
        border-right-color: #dc3545;
    }

    .btn-export {
        background: linear-gradient(45deg, #ff6b6b, #ff9a56);
        border: none;
        padding: 12px 30px;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 25px;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-export:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(255,107,107,0.3);
        color: white;
    }

    .section-header {
        background: linear-gradient(45deg, #ff6b6b, #ff9a56);
        color: white;
        padding: 15px;
        border-radius: 10px 10px 0 0;
        margin-bottom: 0;
    }

    .badge-custom {
        font-size: 0.8rem;
        padding: 5px 10px;
        border-radius: 15px;
    }

    .progress-custom {
        height: 8px;
        border-radius: 10px;
        background-color: #e9ecef;
    }

    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .evaluation-header {
        background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin-bottom: 20px;
    }

    .evaluation-stats {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-danger mb-2">
                        <i class="fas fa-star"></i> نتائج تحليل كشف التقييمات
                    </h1>
                    <p class="text-muted">
                        <i class="fas fa-file-excel"></i> الملف: {{ excel_filename }}
                    </p>
                    {% if selected_course %}
                    <div class="alert alert-warning">
                        <i class="fas fa-graduation-cap"></i>
                        <strong>الدورة المختارة:</strong> {{ selected_course.course_number }} - {{ selected_course.title }}
                    </div>
                    {% endif %}
                </div>
                <div>
                    <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-export me-2">
                        <i class="fas fa-download"></i> تصدير النتائج الكاملة
                    </a>
                    <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-success me-2">
                        <i class="fas fa-file-excel"></i> تصدير التقييمات الجديدة فقط
                    </a>
                    {% if results.new_records or results.allowed_duplicates or results.corrected_names %}
                    <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        {% if selected_course %}
                        <button type="submit" class="btn btn-warning me-2"
                                onclick="return confirm('هل تريد إضافة {{ (results.new_records|length if results.new_records else 0) + (results.allowed_duplicates|length if results.allowed_duplicates else 0) }} تقييم جديد إلى قاعدة البيانات والدورة {{ selected_course.course_number }}؟')">
                            <i class="fas fa-database"></i> إضافة للقاعدة والدورة
                        </button>
                        {% else %}
                        <button type="submit" class="btn btn-warning me-2"
                                onclick="return confirm('هل تريد إضافة {{ (results.new_records|length if results.new_records else 0) + (results.allowed_duplicates|length if results.allowed_duplicates else 0) }} تقييم جديد إلى قاعدة البيانات؟')">
                            <i class="fas fa-database"></i> إضافة التقييمات الجديدة
                        </button>
                        {% endif %}
                    </form>
                    {% endif %}
                    <a href="{{ url_for('person_data.name_analysis') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> تحليل جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stat-number">{{ results.statistics.total_processed }}</div>
                <div class="stat-label">إجمالي التقييمات المعالجة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stat-number">{{ results.statistics.corrected_count }}</div>
                <div class="stat-label">الأسماء المصححة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stat-number">{{ results.statistics.exact_matches_count }}</div>
                <div class="stat-label">موجود في قاعدة البيانات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stat-number">{{ results.statistics.new_records_count }}</div>
                <div class="stat-label">غير موجود في قاعدة البيانات</div>
            </div>
        </div>
    </div>

    <!-- Advanced Duplicate Detection Cards -->
    {% if results.statistics.has_national_id_column or results.statistics.has_phone_column or results.statistics.has_military_id_column %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-search"></i> نتائج فحص التطابق المتقدم للتقييمات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="card text-center border-danger">
                                <div class="card-body">
                                    <h5 class="text-danger">{{ results.statistics.blocked_duplicates_count }}</h5>
                                    <small class="text-muted">تقييمات مرفوضة</small>
                                    <br><small class="text-danger">تطابق في البيانات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card text-center border-success">
                                <div class="card-body">
                                    <h5 class="text-success">{{ results.statistics.allowed_duplicates_count }}</h5>
                                    <small class="text-muted">أسماء مكررة مسموحة</small>
                                    <br><small class="text-success">بيانات مختلفة</small>
                                </div>
                            </div>
                        </div>
                        {% if results.statistics.has_national_id_column %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.name_national_id_matches + results.statistics.national_id_only_matches }}</h5>
                                    <small class="text-muted">تطابق رقم وطني</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if results.statistics.has_phone_column %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.name_phone_matches + results.statistics.phone_only_matches }}</h5>
                                    <small class="text-muted">تطابق رقم هاتف</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if results.statistics.has_military_id_column %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.name_military_id_matches + results.statistics.military_id_only_matches }}</h5>
                                    <small class="text-muted">تطابق رقم عسكري</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if results.course_analysis and results.course_analysis.selected_course %}
    <!-- تحليل التقييمات للدورة المختارة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-star"></i> تحليل التقييمات للدورة
                    </h5>
                </div>
                <div class="card-body">
                    <!-- معلومات الدورة -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-warning">معلومات الدورة:</h6>
                            <p class="mb-1"><strong>رقم الدورة:</strong> {{ results.course_analysis.selected_course.course_number }}</p>
                            <p class="mb-1"><strong>اسم الدورة:</strong> {{ results.course_analysis.selected_course.title }}</p>
                            <p class="mb-1"><strong>الجهة:</strong> {{ results.course_analysis.selected_course.agency or 'غير محدد' }}</p>
                            <p class="mb-0"><strong>المركز:</strong> {{ results.course_analysis.selected_course.center_name or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">إحصائيات التقييمات:</h6>
                            {% if results.course_analysis.participants_summary %}
                            <p class="mb-1"><strong>التقييمات الحالية:</strong>
                                <span class="badge bg-info">{{ results.course_analysis.participants_summary.current_participants_count }}</span>
                            </p>
                            <p class="mb-1"><strong>التقييمات الجديدة:</strong>
                                <span class="badge bg-success">{{ results.course_analysis.participants_summary.new_participants_count }}</span>
                            </p>
                            <p class="mb-1"><strong>التقييمات المكررة:</strong>
                                <span class="badge bg-warning">{{ results.course_analysis.participants_summary.duplicate_participants_count }}</span>
                            </p>
                            <p class="mb-0"><strong>إجمالي بعد الاستيراد:</strong>
                                <span class="badge bg-primary">{{ results.course_analysis.participants_summary.total_after_import }}</span>
                            </p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- تحذيرات التقييمات المكررة -->
                    {% if results.course_analysis.duplicate_participants %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> تم العثور على {{ results.course_analysis.duplicate_participants|length }} تقييم مكرر في كشف التقييمات.
                        هذه التقييمات موجودة مسبقاً في الدورة ولن يتم إضافتها مرة أخرى.
                    </div>

                    <!-- عرض التقييمات المكررة -->
                    <div class="mb-3">
                        <h6 class="text-warning">التقييمات المكررة في الدورة:</h6>
                        <div class="row">
                            {% for duplicate in results.course_analysis.duplicate_participants[:5] %}
                            <div class="col-md-6 mb-2">
                                <div class="card border-warning">
                                    <div class="card-body p-2">
                                        <h6 class="card-title text-warning mb-1">{{ duplicate.name }}</h6>
                                        <small class="text-muted">{{ duplicate.reason }}</small>
                                        <br><small class="badge bg-warning">موجود في الصف {{ duplicate.excel_record.row_index }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if results.course_analysis.duplicate_participants|length > 5 %}
                        <p class="text-muted text-center mt-2">
                            <i class="fas fa-info-circle"></i>
                            يتم عرض أول 5 تقييمات مكررة فقط. للاطلاع على القائمة الكاملة، قم بتصدير النتائج.
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- التقييمات الجديدة للدورة -->
                    {% if results.course_analysis.new_participants %}
                    <div class="mb-3">
                        <h6 class="text-success">التقييمات الجديدة التي سيتم إضافتها للدورة:</h6>
                        <div class="row">
                            {% for new_evaluation in results.course_analysis.new_participants[:5] %}
                            <div class="col-md-6 mb-2">
                                <div class="card border-success">
                                    <div class="card-body p-2">
                                        <h6 class="card-title text-success mb-1">{{ new_evaluation.name }}</h6>
                                        <small class="badge bg-success">{{ new_evaluation.type }}</small>
                                        <br><small class="text-muted">من الصف {{ new_evaluation.excel_record.row_index }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if results.course_analysis.new_participants|length > 5 %}
                        <p class="text-muted text-center mt-2">
                            <i class="fas fa-info-circle"></i>
                            يتم عرض أول 5 تقييمات جديدة فقط. للاطلاع على القائمة الكاملة، قم بتصدير النتائج.
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- زر تحديث التقييمات المدخلة -->
                    <div class="text-center mt-4">
                        {% if results.exact_matches %}
                        <button type="button" class="btn btn-info btn-lg me-3" data-bs-toggle="modal" data-bs-target="#updateEvaluationsModal">
                            <i class="fas fa-edit"></i> تحديث التقييمات المدخلة ({{ results.exact_matches|length }})
                        </button>
                        {% endif %}
                        {% if results.course_analysis.new_participants %}
                        <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-success btn-lg"
                                    onclick="return confirm('هل تريد إضافة {{ results.course_analysis.new_participants|length }} تقييم جديد للدورة وقاعدة البيانات؟')">
                                <i class="fas fa-plus-circle"></i> إضافة التقييمات الجديدة
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Corrected Names Section -->
    {% if results.corrected_names %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-spell-check"></i> الأسماء المصححة في التقييمات
                <span class="badge bg-warning">{{ results.corrected_names|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="row">
                {% for correction in results.corrected_names[:10] %}
                <div class="col-md-6 mb-2">
                    <div class="name-item corrected-name">
                        <strong>الأصلي:</strong> {{ correction.original }}<br>
                        <strong>المصحح:</strong> <span class="text-success">{{ correction.corrected }}</span>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if results.corrected_names|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 أسماء فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Exact Matches Section - موجود في قاعدة البيانات -->
    {% if results.exact_matches %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-user-check"></i> موجود في قاعدة البيانات - يحتاج تحديث تقييم
                <span class="badge bg-success">{{ results.exact_matches|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>ملاحظة:</strong> هؤلاء الأشخاص موجودين في قاعدة البيانات ولديهم تقييمات مدخلة مسبقاً. يمكنك تحديث تقييماتهم أو إضافة تقييمات جديدة لهم.
            </div>
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> تحديث التقييمات سيؤثر على البيانات الموجودة
                    </div>
                </div>
                <div class="col-md-6">
                    <button type="button" class="btn btn-warning btn-lg w-100" data-bs-toggle="modal" data-bs-target="#updateEvaluationsModal">
                        <i class="fas fa-edit"></i> تحديث التقييمات المدخلة
                    </button>
                </div>
            </div>
            <div class="row">
                {% for match in results.exact_matches[:10] %}
                <div class="col-md-12 mb-3">
                    <div class="card border-success">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-star"></i> من ملف التقييمات:
                                    </h6>
                                    <p class="mb-1"><strong>{{ match.excel_name }}</strong></p>
                                    <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                                    {% if match.excel_record.national_id %}
                                    <br><small class="badge bg-info">رقم وطني: {{ match.excel_record.national_id }}</small>
                                    {% endif %}
                                    {% if match.excel_record.phone %}
                                    <br><small class="badge bg-success">هاتف: {{ match.excel_record.phone }}</small>
                                    {% endif %}
                                    {% if match.excel_record.military_id %}
                                    <br><small class="badge bg-warning">عسكري: {{ match.excel_record.military_id }}</small>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-2">
                                        <i class="fas fa-database"></i> من قاعدة البيانات:
                                    </h6>
                                    <p class="mb-1"><strong>{{ match.db_name }}</strong></p>
                                    <small class="text-muted">ID: {{ match.db_record.id }}</small>
                                    {% if match.db_record.national_id %}
                                    <br><small class="badge bg-info">رقم وطني: {{ match.db_record.national_id }}</small>
                                    {% endif %}
                                    {% if match.db_record.phone %}
                                    <br><small class="badge bg-success">هاتف: {{ match.db_record.phone }}</small>
                                    {% endif %}
                                    {% if match.db_record.military_id %}
                                    <br><small class="badge bg-warning">عسكري: {{ match.db_record.military_id }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if results.exact_matches|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 مطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- New Records Section - غير موجود في قاعدة البيانات -->
    {% if results.new_records %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-user-plus"></i> غير موجود في قاعدة البيانات - سيتم إدخال تقييم لهم
                <span class="badge bg-primary">{{ results.new_records|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-success">
                <i class="fas fa-user-plus"></i>
                <strong>إجراء مطلوب:</strong> هؤلاء الأشخاص غير موجودين في قاعدة البيانات. سيتم إضافتهم كأشخاص جدد مع تقييماتهم عند الاستيراد.
            </div>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>ملاحظة:</strong> سيتم إنشاء ملفات شخصية جديدة لهؤلاء الأشخاص وإدخال تقييماتهم في نفس الوقت.
            </div>
            {% for record in results.new_records[:20] %}
            <div class="card mb-2 border-primary">
                <div class="card-body p-2">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>{{ record.corrected_name }}</strong>
                            <small class="text-muted d-block">الصف: {{ record.excel_record.row_index }}</small>
                            {% if record.excel_record.national_id %}
                            <small class="badge bg-info">رقم وطني: {{ record.excel_record.national_id }}</small>
                            {% endif %}
                            {% if record.excel_record.phone %}
                            <br><small class="badge bg-success">هاتف: {{ record.excel_record.phone }}</small>
                            {% endif %}
                            {% if record.excel_record.military_id %}
                            <br><small class="badge bg-warning">عسكري: {{ record.excel_record.military_id }}</small>
                            {% endif %}
                        </div>
                        <div class="col-md-3">
                            {% if record.excel_record.grade %}
                            <small class="text-muted">الدرجة:</small>
                            <br><strong class="text-primary">{{ record.excel_record.grade }}</strong>
                            {% endif %}
                            {% if record.excel_record.percentage %}
                            <br><small class="text-muted">النسبة:</small>
                            <br><strong class="text-success">{{ record.excel_record.percentage }}%</strong>
                            {% endif %}
                        </div>
                        <div class="col-md-3 text-end">
                            <span class="badge bg-success">
                                <i class="fas fa-plus"></i> سيتم إضافة تقييم
                            </span>
                            <br><small class="text-muted mt-1">شخص جديد + تقييم</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.new_records|length > 20 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 20 سجل فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

            <!-- التقييمات المكررة -->
            {% if results.evaluation_analysis.duplicate_evaluations %}
            <div class="card mb-4 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        التقييمات المكررة ({{ results.evaluation_analysis.duplicate_evaluations|length }})
                    </h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>الاسم من Excel</th>
                                    <th>الاسم الموجود</th>
                                    <th>تاريخ التقييم الموجود</th>
                                    <th>السبب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for duplicate in results.evaluation_analysis.duplicate_evaluations %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ duplicate.name }}</td>
                                    <td>{{ duplicate.existing_evaluation.name }}</td>
                                    <td>{{ duplicate.existing_evaluation.evaluation_date or '-' }}</td>
                                    <td><span class="badge bg-warning text-dark">{{ duplicate.reason }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- الأسماء المصححة -->
            {% if results.corrected_names %}
            <div class="card mb-4 border-info">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-edit"></i>
                        الأسماء المصححة ({{ results.corrected_names|length }})
                    </h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>الاسم الأصلي</th>
                                    <th>الاسم المصحح</th>
                                    <th>رقم الصف في Excel</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for correction in results.corrected_names %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td><span class="text-danger">{{ correction.original }}</span></td>
                                    <td><span class="text-success"><strong>{{ correction.corrected }}</strong></span></td>
                                    <td>{{ correction.row_index }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- السجلات المرفوضة -->
            {% if results.blocked_duplicates %}
            <div class="card mb-4 border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-ban"></i>
                        السجلات المرفوضة ({{ results.blocked_duplicates|length }})
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> هذه السجلات مرفوضة بسبب وجود تطابق في البيانات الشخصية مع سجلات موجودة في قاعدة البيانات
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>الاسم</th>
                                    <th>الرقم الوطني</th>
                                    <th>رقم الهاتف</th>
                                    <th>السبب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for blocked in results.blocked_duplicates %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ blocked.corrected_name }}</td>
                                    <td>{{ blocked.excel_record.national_id or '-' }}</td>
                                    <td>{{ blocked.excel_record.phone or '-' }}</td>
                                    <td><span class="badge bg-danger">{{ blocked.reason }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- الأسماء المكررة المسموحة -->
            {% if results.allowed_duplicates %}
            <div class="card mb-4 border-success">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-check-circle"></i>
                        التقييمات المكررة المسموحة ({{ results.allowed_duplicates|length }})
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> هذه الأسماء مكررة لكن البيانات الشخصية مختلفة، لذا يمكن إضافة تقييمات لها
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>الاسم</th>
                                    <th>الرقم الوطني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الرقم العسكري</th>
                                    <th>الدرجة</th>
                                    <th>السبب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for allowed in results.allowed_duplicates %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td><strong>{{ allowed.corrected_name }}</strong></td>
                                    <td>{{ allowed.excel_record.national_id or '-' }}</td>
                                    <td>{{ allowed.excel_record.phone or '-' }}</td>
                                    <td>{{ allowed.excel_record.military_id or '-' }}</td>
                                    <td>{{ allowed.excel_record.grade or '-' }}</td>
                                    <td><span class="badge bg-success">{{ allowed.reason }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

    <!-- Export Section -->
    <div class="mt-5 mb-4">
        <div class="card">
            <div class="card-body">
                <h5 class="text-danger text-center mb-4">
                    <i class="fas fa-star"></i> خيارات التصدير والاستيراد للتقييمات
                </h5>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-danger">
                            <div class="card-body text-center">
                                <i class="fas fa-download text-danger mb-3" style="font-size: 2rem;"></i>
                                <h6>التقرير الكامل للتقييمات</h6>
                                <p class="text-muted small">
                                    • كشف من لديهم تقييم مدخل<br>
                                    • كشف من سيتم إدخال تقييم لهم<br>
                                    • كشف من يحتاج تحديث تقييم<br>
                                    • جميع الإحصائيات والتفاصيل
                                </p>
                                <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-danger">
                                    <i class="fas fa-download"></i> تحميل
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-file-excel text-success mb-3" style="font-size: 2rem;"></i>
                                <h6>التقييمات الجديدة فقط</h6>
                                <p class="text-muted small">
                                    الأشخاص الذين سيتم إدخال تقييمات لهم<br>
                                    (غير موجودين في قاعدة البيانات)
                                </p>
                                <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-success">
                                    <i class="fas fa-file-excel"></i> تحميل
                                </a>
                            </div>
                        </div>
                    </div>

                    {% if results.new_records or results.allowed_duplicates or results.corrected_names %}
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-database text-warning mb-3" style="font-size: 2rem;"></i>
                                <h6>إضافة التقييمات الجديدة</h6>
                                <p class="text-muted small">
                                    إضافة الأشخاص الجدد مع تقييماتهم<br>
                                    إلى قاعدة البيانات والدورة
                                </p>
                                <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-warning"
                                            onclick="return confirm('هل تريد إضافة {{ (results.new_records|length if results.new_records else 0) + (results.allowed_duplicates|length if results.allowed_duplicates else 0) + (results.corrected_names|length if results.corrected_names else 0) }} شخص جديد مع تقييماتهم إلى قاعدة البيانات{% if selected_course %} وإلى الدورة {{ selected_course.title }}{% endif %}؟')">
                                        <i class="fas fa-user-plus"></i> إضافة الأشخاص والتقييمات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- إحصائيات التحليل -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="text-danger mb-3 text-center">
                        <i class="fas fa-chart-pie"></i> ملخص تحليل التقييمات بالنسب المئوية
                    </h6>
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-success">{{ "%.1f"|format((results.statistics.new_records_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">سيتم إدخال تقييم لهم</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-info">{{ "%.1f"|format((results.statistics.exact_matches_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">يحتاج تحديث تقييم</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-warning">{{ "%.1f"|format((results.statistics.corrected_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">أسماء مصححة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h5 class="text-danger">{{ "%.1f"|format((results.statistics.blocked_duplicates_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                            <small class="text-muted">تقييمات مرفوضة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تحديث التقييمات -->
{% if results.exact_matches %}
<div class="modal fade" id="updateEvaluationsModal" tabindex="-1" aria-labelledby="updateEvaluationsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="updateEvaluationsModalLabel">
                    <i class="fas fa-edit"></i> تحديث التقييمات المدخلة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> يمكنك تحديث بيانات التقييمات للأشخاص الموجودين في قاعدة البيانات.
                </div>

                <form method="post" action="{{ url_for('person_data.update_evaluations') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>الاسم</th>
                                    <th>الرقم الوطني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الرقم العسكري</th>
                                    <th>الإجراء</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for match in results.exact_matches %}
                                <tr>
                                    <td>
                                        <input type="checkbox" name="selected_evaluations" value="{{ match.db_record.id }}" class="form-check-input evaluation-checkbox">
                                    </td>
                                    <td><strong>{{ match.db_name }}</strong></td>
                                    <td>{{ match.db_record.national_id or '-' }}</td>
                                    <td>{{ match.db_record.phone or '-' }}</td>
                                    <td>{{ match.db_record.military_id or '-' }}</td>
                                    <td>
                                        <select name="action_{{ match.db_record.id }}" class="form-select form-select-sm">
                                            <option value="update_data">تحديث البيانات الشخصية</option>
                                            <option value="add_evaluation">إضافة تقييم جديد</option>
                                            <option value="update_evaluation">تحديث التقييم الحالي</option>
                                        </select>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save"></i> تحديث التقييمات المختارة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تأثيرات بصرية للبطاقات
        $('.stats-card, .result-section').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // إضافة تأثير للأزرار
        $('.btn-export').hover(
            function() {
                $(this).find('i').addClass('fa-bounce');
            },
            function() {
                $(this).find('i').removeClass('fa-bounce');
            }
        );

        // تحديد الكل في modal التحديث
        $('#selectAll').change(function() {
            $('.evaluation-checkbox').prop('checked', this.checked);
        });

        // تحديث حالة "تحديد الكل" عند تغيير الاختيارات الفردية
        $('.evaluation-checkbox').change(function() {
            var total = $('.evaluation-checkbox').length;
            var checked = $('.evaluation-checkbox:checked').length;
            $('#selectAll').prop('checked', total === checked);
        });
    });
</script>
{% endblock %}
