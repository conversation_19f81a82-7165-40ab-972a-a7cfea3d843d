#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
استعادة الدورات الثلاث الأصلية
"""

from app import app, db, Course, User, CourseParticipant, PersonData
from datetime import datetime

def check_current_status():
    """فحص الوضع الحالي"""
    with app.app_context():
        print("🔍 فحص الوضع الحالي:")
        print("=" * 40)
        
        # فحص الدورات
        courses_count = Course.query.count()
        print(f"📚 الدورات: {courses_count}")
        
        # فحص المستخدمين
        users_count = User.query.count()
        print(f"👥 المستخدمين: {users_count}")
        
        # فحص الأشخاص
        persons_count = PersonData.query.count()
        print(f"👤 الأشخاص: {persons_count}")
        
        # فحص المشاركين
        participants_count = CourseParticipant.query.count()
        print(f"🎯 المشاركين: {participants_count}")
        
        return courses_count, users_count, persons_count, participants_count

def create_courses():
    """إنشاء الدورات الثلاث"""
    with app.app_context():
        print("\n📚 إنشاء الدورات الثلاث...")
        
        # التأكد من وجود مستخدم admin
        admin_user = User.query.filter_by(role='admin').first()
        if not admin_user:
            print("❌ لا يوجد مستخدم admin")
            return False
        
        print(f"✅ العثور على مستخدم admin: {admin_user.username}")
        
        # حذف الدورات الموجودة (إن وجدت)
        existing_courses = Course.query.all()
        if existing_courses:
            print(f"🗑️ حذف {len(existing_courses)} دورة موجودة...")
            for course in existing_courses:
                db.session.delete(course)
            db.session.commit()
        
        # بيانات الدورات الثلاث
        courses_data = [
            {
                'course_number': '3455667',
                'title': 'المبيعات2',
                'description': 'دورة تدريبية في المبيعات والتسويق',
                'category': 'management',
                'level': 'intermediate',
                'start_date': datetime(2025, 5, 22),
                'end_date': datetime(2025, 5, 31),
                'start_date_hijri': '1446/11/22',
                'end_date_hijri': '1446/12/01',
                'duration_days': 10,
                'trainer_id': admin_user.id,
                'max_participants': 30,
                'location': 'قاعة التدريب الرئيسية',
                'status': 'active'
            },
            {
                'course_number': '10054',
                'title': 'تدريب الذكاء الاصطناعي',
                'description': 'دورة تدريبية في الذكاء الاصطناعي والتعلم الآلي',
                'category': 'technology',
                'level': 'advanced',
                'start_date': datetime(2025, 5, 22),
                'end_date': datetime(2025, 6, 20),
                'start_date_hijri': '1446/11/22',
                'end_date_hijri': '1446/12/20',
                'duration_days': 30,
                'trainer_id': admin_user.id,
                'max_participants': 25,
                'location': 'مختبر الحاسوب',
                'status': 'active'
            },
            {
                'course_number': '1100255',
                'title': 'تدريب الذكاء الاصطناعي 2',
                'description': 'دورة متقدمة في الذكاء الاصطناعي والتطبيقات العملية',
                'category': 'technology',
                'level': 'expert',
                'start_date': datetime(2025, 5, 22),
                'end_date': datetime(2025, 5, 31),
                'start_date_hijri': '1446/11/22',
                'end_date_hijri': '1446/12/01',
                'duration_days': 10,
                'trainer_id': admin_user.id,
                'max_participants': 20,
                'location': 'مختبر الذكاء الاصطناعي',
                'status': 'active'
            }
        ]
        
        # إنشاء الدورات
        created_courses = []
        for course_data in courses_data:
            try:
                course = Course(**course_data)
                db.session.add(course)
                created_courses.append(course)
                print(f"✅ تم إنشاء دورة: {course_data['course_number']} - {course_data['title']}")
            except Exception as e:
                print(f"❌ خطأ في إنشاء دورة {course_data['course_number']}: {str(e)}")
        
        # حفظ التغييرات
        try:
            db.session.commit()
            print(f"\n🎉 تم إنشاء {len(created_courses)} دورة بنجاح!")
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ الدورات: {str(e)}")
            db.session.rollback()
            return False

def add_participants_to_first_course():
    """إضافة المشاركين إلى الدورة الأولى"""
    with app.app_context():
        print("\n🎯 إضافة المشاركين إلى الدورة الأولى...")
        
        # جلب الدورة الأولى
        first_course = Course.query.filter_by(course_number='3455667').first()
        if not first_course:
            print("❌ لم يتم العثور على الدورة الأولى")
            return False
        
        # جلب جميع الأشخاص
        persons = PersonData.query.all()
        if not persons:
            print("❌ لا يوجد أشخاص في قاعدة البيانات")
            return False
        
        print(f"👥 العثور على {len(persons)} شخص")
        
        # حذف المشاركين الموجودين
        existing_participants = CourseParticipant.query.filter_by(course_id=first_course.id).all()
        if existing_participants:
            print(f"🗑️ حذف {len(existing_participants)} مشارك موجود...")
            for participant in existing_participants:
                db.session.delete(participant)
        
        # إضافة جميع الأشخاص كمشاركين في الدورة الأولى
        added_count = 0
        for person in persons:
            try:
                participant = CourseParticipant(
                    course_id=first_course.id,
                    personal_data_id=person.id,
                    registration_date=datetime.now(),
                    status='enrolled'
                )
                db.session.add(participant)
                added_count += 1
            except Exception as e:
                print(f"❌ خطأ في إضافة {person.full_name}: {str(e)}")
        
        # حفظ التغييرات
        try:
            db.session.commit()
            print(f"✅ تم إضافة {added_count} مشارك إلى الدورة الأولى")
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ المشاركين: {str(e)}")
            db.session.rollback()
            return False

def verify_results():
    """التحقق من النتائج"""
    with app.app_context():
        print("\n📊 التحقق من النتائج:")
        print("=" * 40)
        
        # فحص الدورات
        courses = Course.query.all()
        print(f"📚 الدورات: {len(courses)}")
        for course in courses:
            participants_count = CourseParticipant.query.filter_by(course_id=course.id).count()
            print(f"   - {course.course_number}: {course.title} ({participants_count} مشارك)")
        
        # فحص المشاركين
        total_participants = CourseParticipant.query.count()
        print(f"\n🎯 إجمالي المشاركين: {total_participants}")
        
        # فحص الأشخاص
        total_persons = PersonData.query.count()
        print(f"👤 إجمالي الأشخاص: {total_persons}")
        
        return len(courses), total_participants, total_persons

def main():
    """الدالة الرئيسية"""
    print("🔄 استعادة الدورات الثلاث الأصلية")
    print("=" * 60)
    
    # فحص الوضع الحالي
    check_current_status()
    
    # إنشاء الدورات
    if create_courses():
        print("✅ تم إنشاء الدورات بنجاح")
    else:
        print("❌ فشل في إنشاء الدورات")
        return
    
    # إضافة المشاركين
    if add_participants_to_first_course():
        print("✅ تم إضافة المشاركين بنجاح")
    else:
        print("❌ فشل في إضافة المشاركين")
    
    # التحقق من النتائج
    courses_count, participants_count, persons_count = verify_results()
    
    print("\n" + "=" * 60)
    if courses_count == 3 and participants_count == persons_count:
        print("🎉 تم استعادة الدورات والمشاركين بنجاح!")
        print("✅ النظام جاهز للاستخدام")
    else:
        print("⚠️ هناك مشكلة في البيانات")
    
    print(f"\n📋 الملخص النهائي:")
    print(f"   📚 الدورات: {courses_count}")
    print(f"   👤 الأشخاص: {persons_count}")
    print(f"   🎯 المشاركين: {participants_count}")

if __name__ == "__main__":
    main()
