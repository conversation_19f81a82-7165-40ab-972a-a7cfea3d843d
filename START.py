#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 نظام التدريب والتأهيل - تشغيل مبسط
Simple Start Script for Training System
"""

import os
import sys
import webbrowser
from threading import Timer

def main():
    print("🚀 نظام التدريب والتأهيل")
    print("=" * 40)
    
    try:
        # Import the app
        from app import app, db
        from backup_utils import smart_backup_manager

        # Create database if needed
        with app.app_context():
            db.create_all()

            # تهيئة النظام الذكي للنسخ الاحتياطي
            smart_backup_manager.init_app(app)

        print("✅ النظام جاهز!")
        print("🔒 النظام الذكي للنسخ الاحتياطي مفعل")
        print("🌐 http://localhost:5000")
        print("🔑 admin / admin")
        print("=" * 40)
        
        # Open browser after 2 seconds
        Timer(2.0, lambda: webbrowser.open('http://localhost:5000')).start()
        
        # Run the server
        app.run(host='localhost', port=5000, debug=False, use_reloader=False)
        
    except ImportError:
        print("❌ خطأ: تأكد من تثبيت المتطلبات")
        print("pip install flask flask-sqlalchemy flask-login flask-wtf")
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
