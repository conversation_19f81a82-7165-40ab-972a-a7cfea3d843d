#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف تشغيل الخادم البسيط
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🔄 جاري تحميل التطبيق...")
    from app import app

    print("✅ تم تحميل التطبيق بنجاح")
    print("🚀 بدء تشغيل الخادم...")
    print("📍 الرابط: http://localhost:5000")
    print("🔑 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin")
    print("=" * 50)

    # تشغيل الخادم
    app.run(
        host='localhost',
        port=5000,
        debug=True,  # تفعيل debug mode لرؤية الأخطاء
        threaded=True,
        use_reloader=False
    )

except ImportError as e:
    print(f"❌ خطأ في استيراد التطبيق: {e}")
    print("🔧 تأكد من وجود ملف app.py في نفس المجلد")

except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    print("🔧 تفاصيل الخطأ:")
    import traceback
    traceback.print_exc()

finally:
    input("\n⏸️  اضغط Enter للخروج...")
