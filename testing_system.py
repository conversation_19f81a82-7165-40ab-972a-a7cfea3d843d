#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 نظام الاختبارات الشامل
Comprehensive Testing System
"""

import os
import sys
import unittest
import time
import json
import sqlite3
import requests
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from unittest.mock import Mock, patch
import tempfile
import shutil

class TestRunner:
    """مشغل الاختبارات الشامل"""
    
    def __init__(self):
        self.test_results = {}
        self.test_suite = unittest.TestSuite()
        self.logger = self.setup_logging()
        self.test_data_manager = TestDataManager()
        
    def setup_logging(self):
        """إعداد نظام التسجيل للاختبارات"""
        log_dir = Path("test_logs")
        log_dir.mkdir(exist_ok=True)
        
        logger = logging.getLogger('testing')
        logger.setLevel(logging.DEBUG)
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # معالج ملف للاختبارات
        test_handler = logging.FileHandler(
            log_dir / f"tests_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log",
            encoding='utf-8'
        )
        test_handler.setFormatter(formatter)
        logger.addHandler(test_handler)
        
        # معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء تشغيل الاختبارات الشاملة...")
        print("=" * 60)
        
        start_time = time.time()
        
        # اختبارات الوحدة
        unit_results = self.run_unit_tests()
        
        # اختبارات التكامل
        integration_results = self.run_integration_tests()
        
        # اختبارات الأداء
        performance_results = self.run_performance_tests()
        
        # اختبارات الأمان
        security_results = self.run_security_tests()
        
        # اختبارات الشبكة
        network_results = self.run_network_tests()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # تجميع النتائج
        all_results = {
            'unit_tests': unit_results,
            'integration_tests': integration_results,
            'performance_tests': performance_results,
            'security_tests': security_results,
            'network_tests': network_results,
            'summary': {
                'total_time': total_time,
                'timestamp': datetime.now().isoformat()
            }
        }
        
        # حفظ النتائج
        self.save_test_results(all_results)
        
        # طباعة التقرير
        self.print_test_report(all_results)
        
        return all_results
    
    def run_unit_tests(self):
        """تشغيل اختبارات الوحدة"""
        print("\n🔬 اختبارات الوحدة...")
        
        results = {
            'passed': 0,
            'failed': 0,
            'errors': [],
            'details': []
        }
        
        # اختبار استيراد الوحدات
        modules_to_test = [
            'app',
            'performance_optimizer',
            'reliability_system',
            'network_system'
        ]
        
        for module_name in modules_to_test:
            try:
                __import__(module_name)
                results['passed'] += 1
                results['details'].append(f"✅ {module_name}: استيراد ناجح")
                self.logger.info(f"Module {module_name} imported successfully")
            except Exception as e:
                results['failed'] += 1
                error_msg = f"❌ {module_name}: {str(e)}"
                results['errors'].append(error_msg)
                results['details'].append(error_msg)
                self.logger.error(f"Failed to import {module_name}: {str(e)}")
        
        # اختبار قاعدة البيانات
        db_test = self.test_database_operations()
        if db_test['success']:
            results['passed'] += 1
            results['details'].append("✅ قاعدة البيانات: عمليات أساسية ناجحة")
        else:
            results['failed'] += 1
            results['errors'].append(f"❌ قاعدة البيانات: {db_test['error']}")
            results['details'].append(f"❌ قاعدة البيانات: {db_test['error']}")
        
        return results
    
    def run_integration_tests(self):
        """تشغيل اختبارات التكامل"""
        print("\n🔗 اختبارات التكامل...")
        
        results = {
            'passed': 0,
            'failed': 0,
            'errors': [],
            'details': []
        }
        
        # اختبار تكامل Flask مع قاعدة البيانات
        flask_db_test = self.test_flask_database_integration()
        if flask_db_test['success']:
            results['passed'] += 1
            results['details'].append("✅ Flask + قاعدة البيانات: تكامل ناجح")
        else:
            results['failed'] += 1
            error_msg = f"❌ Flask + قاعدة البيانات: {flask_db_test['error']}"
            results['errors'].append(error_msg)
            results['details'].append(error_msg)
        
        # اختبار تكامل نظام الأداء
        perf_test = self.test_performance_integration()
        if perf_test['success']:
            results['passed'] += 1
            results['details'].append("✅ نظام الأداء: تكامل ناجح")
        else:
            results['failed'] += 1
            error_msg = f"❌ نظام الأداء: {perf_test['error']}"
            results['errors'].append(error_msg)
            results['details'].append(error_msg)
        
        return results
    
    def run_performance_tests(self):
        """تشغيل اختبارات الأداء"""
        print("\n⚡ اختبارات الأداء...")
        
        results = {
            'passed': 0,
            'failed': 0,
            'errors': [],
            'details': [],
            'metrics': {}
        }
        
        # اختبار سرعة قاعدة البيانات
        db_perf = self.test_database_performance()
        results['metrics']['database'] = db_perf
        
        if db_perf['avg_query_time'] < 0.1:  # أقل من 100ms
            results['passed'] += 1
            results['details'].append(f"✅ أداء قاعدة البيانات: {db_perf['avg_query_time']:.3f}s")
        else:
            results['failed'] += 1
            results['details'].append(f"⚠️ أداء قاعدة البيانات بطيء: {db_perf['avg_query_time']:.3f}s")
        
        # اختبار استهلاك الذاكرة
        memory_test = self.test_memory_usage()
        results['metrics']['memory'] = memory_test
        
        if memory_test['peak_memory'] < 100:  # أقل من 100MB
            results['passed'] += 1
            results['details'].append(f"✅ استهلاك الذاكرة: {memory_test['peak_memory']:.1f}MB")
        else:
            results['failed'] += 1
            results['details'].append(f"⚠️ استهلاك ذاكرة عالي: {memory_test['peak_memory']:.1f}MB")
        
        return results
    
    def run_security_tests(self):
        """تشغيل اختبارات الأمان"""
        print("\n🔒 اختبارات الأمان...")
        
        results = {
            'passed': 0,
            'failed': 0,
            'errors': [],
            'details': []
        }
        
        # اختبار SQL Injection
        sql_injection_test = self.test_sql_injection_protection()
        if sql_injection_test['protected']:
            results['passed'] += 1
            results['details'].append("✅ حماية من SQL Injection")
        else:
            results['failed'] += 1
            results['details'].append("❌ عرضة لـ SQL Injection")
        
        # اختبار كلمات المرور
        password_test = self.test_password_security()
        if password_test['secure']:
            results['passed'] += 1
            results['details'].append("✅ أمان كلمات المرور")
        else:
            results['failed'] += 1
            results['details'].append("❌ ضعف في أمان كلمات المرور")
        
        return results
    
    def run_network_tests(self):
        """تشغيل اختبارات الشبكة"""
        print("\n🌐 اختبارات الشبكة...")
        
        results = {
            'passed': 0,
            'failed': 0,
            'errors': [],
            'details': []
        }
        
        # اختبار الاتصال المحلي
        local_connection_test = self.test_local_connection()
        if local_connection_test['success']:
            results['passed'] += 1
            results['details'].append("✅ الاتصال المحلي يعمل")
        else:
            results['failed'] += 1
            results['details'].append(f"❌ فشل الاتصال المحلي: {local_connection_test['error']}")
        
        # اختبار المنافذ
        port_test = self.test_port_availability()
        if port_test['available']:
            results['passed'] += 1
            results['details'].append(f"✅ المنفذ {port_test['port']} متاح")
        else:
            results['failed'] += 1
            results['details'].append(f"❌ المنفذ {port_test['port']} غير متاح")
        
        return results
    
    def test_database_operations(self):
        """اختبار عمليات قاعدة البيانات"""
        try:
            # إنشاء قاعدة بيانات مؤقتة للاختبار
            with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
                test_db_path = tmp_db.name
            
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()
            
            # إنشاء جدول اختبار
            cursor.execute('''
                CREATE TABLE test_table (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إدراج بيانات اختبار
            cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("test_user",))
            
            # استعلام البيانات
            cursor.execute("SELECT * FROM test_table")
            result = cursor.fetchone()
            
            conn.commit()
            conn.close()
            
            # تنظيف
            os.unlink(test_db_path)
            
            return {'success': True, 'result': result}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_flask_database_integration(self):
        """اختبار تكامل Flask مع قاعدة البيانات"""
        try:
            # محاولة استيراد التطبيق
            from app import app, db
            
            with app.app_context():
                # محاولة إنشاء الجداول
                db.create_all()
                
                # فحص وجود الجداول
                from sqlalchemy import inspect
                inspector = inspect(db.engine)
                tables = inspector.get_table_names()
                
                if len(tables) > 0:
                    return {'success': True, 'tables': tables}
                else:
                    return {'success': False, 'error': 'No tables found'}
                    
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_performance_integration(self):
        """اختبار تكامل نظام الأداء"""
        try:
            from performance_optimizer import optimize_performance
            
            @optimize_performance
            def test_function():
                return "test"
            
            result = test_function()
            return {'success': True, 'result': result}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_database_performance(self):
        """اختبار أداء قاعدة البيانات"""
        try:
            with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
                test_db_path = tmp_db.name
            
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE perf_test (
                    id INTEGER PRIMARY KEY,
                    data TEXT
                )
            ''')
            
            # قياس وقت الإدراج
            start_time = time.time()
            for i in range(100):
                cursor.execute("INSERT INTO perf_test (data) VALUES (?)", (f"data_{i}",))
            conn.commit()
            insert_time = time.time() - start_time
            
            # قياس وقت الاستعلام
            start_time = time.time()
            cursor.execute("SELECT COUNT(*) FROM perf_test")
            result = cursor.fetchone()
            query_time = time.time() - start_time
            
            conn.close()
            os.unlink(test_db_path)
            
            return {
                'insert_time': insert_time,
                'query_time': query_time,
                'avg_query_time': (insert_time + query_time) / 2,
                'records_inserted': 100
            }
            
        except Exception as e:
            return {'error': str(e), 'avg_query_time': 999}
    
    def test_memory_usage(self):
        """اختبار استهلاك الذاكرة"""
        try:
            import psutil
            process = psutil.Process()
            
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # محاكاة استهلاك الذاكرة
            test_data = []
            for i in range(1000):
                test_data.append(f"test_data_{i}" * 100)
            
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # تنظيف
            del test_data
            
            return {
                'initial_memory': initial_memory,
                'peak_memory': peak_memory,
                'memory_increase': peak_memory - initial_memory
            }
            
        except ImportError:
            return {'peak_memory': 0, 'error': 'psutil not available'}
        except Exception as e:
            return {'peak_memory': 999, 'error': str(e)}
    
    def test_sql_injection_protection(self):
        """اختبار الحماية من SQL Injection"""
        try:
            with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
                test_db_path = tmp_db.name
            
            conn = sqlite3.connect(test_db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY,
                    username TEXT,
                    password TEXT
                )
            ''')
            
            cursor.execute("INSERT INTO users (username, password) VALUES (?, ?)", 
                         ("admin", "password123"))
            conn.commit()
            
            # محاولة SQL Injection
            malicious_input = "admin'; DROP TABLE users; --"
            
            try:
                cursor.execute("SELECT * FROM users WHERE username = ?", (malicious_input,))
                result = cursor.fetchone()
                
                # فحص ما إذا كان الجدول ما زال موجوداً
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
                table_exists = cursor.fetchone() is not None
                
                conn.close()
                os.unlink(test_db_path)
                
                return {'protected': table_exists}
                
            except Exception:
                conn.close()
                os.unlink(test_db_path)
                return {'protected': True}  # الخطأ يعني الحماية تعمل
                
        except Exception as e:
            return {'protected': False, 'error': str(e)}
    
    def test_password_security(self):
        """اختبار أمان كلمات المرور"""
        try:
            from werkzeug.security import generate_password_hash, check_password_hash
            
            password = "test_password_123"
            hashed = generate_password_hash(password)
            
            # فحص أن كلمة المرور لا تُحفظ كنص خام
            if password in hashed:
                return {'secure': False, 'reason': 'Password stored as plain text'}
            
            # فحص أن التحقق يعمل
            if not check_password_hash(hashed, password):
                return {'secure': False, 'reason': 'Password verification failed'}
            
            return {'secure': True}
            
        except Exception as e:
            return {'secure': False, 'error': str(e)}
    
    def test_local_connection(self):
        """اختبار الاتصال المحلي"""
        try:
            import socket
            
            # اختبار الاتصال بـ localhost
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('localhost', 5000))
            sock.close()
            
            if result == 0:
                return {'success': True}
            else:
                return {'success': False, 'error': 'Connection refused'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_port_availability(self):
        """اختبار توفر المنافذ"""
        try:
            import socket
            
            port = 5000
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            return {'available': result != 0, 'port': port}
            
        except Exception as e:
            return {'available': False, 'port': 5000, 'error': str(e)}
    
    def save_test_results(self, results):
        """حفظ نتائج الاختبارات"""
        results_dir = Path("test_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = results_dir / f"test_results_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Test results saved to: {results_file}")
    
    def print_test_report(self, results):
        """طباعة تقرير الاختبارات"""
        print("\n" + "=" * 60)
        print("📊 تقرير الاختبارات الشامل")
        print("=" * 60)
        
        total_passed = 0
        total_failed = 0
        
        for test_type, test_results in results.items():
            if test_type == 'summary':
                continue
                
            print(f"\n🔍 {test_type.replace('_', ' ').title()}:")
            print(f"   ✅ نجح: {test_results['passed']}")
            print(f"   ❌ فشل: {test_results['failed']}")
            
            total_passed += test_results['passed']
            total_failed += test_results['failed']
            
            if test_results['details']:
                for detail in test_results['details'][:3]:  # أول 3 تفاصيل
                    print(f"   {detail}")
        
        print("\n" + "=" * 60)
        print("📈 الملخص النهائي:")
        print(f"   ✅ إجمالي النجح: {total_passed}")
        print(f"   ❌ إجمالي الفشل: {total_failed}")
        print(f"   📊 معدل النجاح: {(total_passed / (total_passed + total_failed) * 100):.1f}%")
        print(f"   ⏱️  وقت التنفيذ: {results['summary']['total_time']:.2f} ثانية")
        print("=" * 60)

class TestDataManager:
    """مدير بيانات الاختبار"""
    
    def __init__(self):
        self.test_data_dir = Path("test_data")
        self.test_data_dir.mkdir(exist_ok=True)
    
    def create_test_database(self):
        """إنشاء قاعدة بيانات اختبار"""
        test_db_path = self.test_data_dir / "test.db"
        
        conn = sqlite3.connect(test_db_path)
        cursor = conn.cursor()
        
        # إنشاء جداول اختبار
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE,
                email TEXT,
                role TEXT DEFAULT 'user'
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_courses (
                id INTEGER PRIMARY KEY,
                title TEXT,
                description TEXT,
                trainer_id INTEGER
            )
        ''')
        
        # إدراج بيانات اختبار
        test_users = [
            ("test_admin", "<EMAIL>", "admin"),
            ("test_trainer", "<EMAIL>", "trainer"),
            ("test_user", "<EMAIL>", "user")
        ]
        
        cursor.executemany(
            "INSERT OR IGNORE INTO test_users (username, email, role) VALUES (?, ?, ?)",
            test_users
        )
        
        test_courses = [
            ("دورة اختبار 1", "وصف الدورة الأولى", 1),
            ("دورة اختبار 2", "وصف الدورة الثانية", 2)
        ]
        
        cursor.executemany(
            "INSERT OR IGNORE INTO test_courses (title, description, trainer_id) VALUES (?, ?, ?)",
            test_courses
        )
        
        conn.commit()
        conn.close()
        
        return test_db_path

# دوال مساعدة
def run_quick_test():
    """تشغيل اختبار سريع"""
    runner = TestRunner()
    return runner.run_unit_tests()

def run_full_test_suite():
    """تشغيل مجموعة الاختبارات الكاملة"""
    runner = TestRunner()
    return runner.run_all_tests()

# مثال على الاستخدام
if __name__ == "__main__":
    print("🧪 نظام الاختبارات الشامل")
    print("=" * 40)
    
    choice = input("اختر نوع الاختبار:\n1. اختبار سريع\n2. اختبار شامل\nالاختيار: ")
    
    if choice == "1":
        results = run_quick_test()
    else:
        results = run_full_test_suite()
    
    print("\n✅ تم إكمال الاختبارات!")
