import sqlite3
import os

print("🔍 فحص قاعدة البيانات...")

# فحص الملفات
files = ['training_system.db', 'instance/training_system.db']
for file in files:
    if os.path.exists(file):
        print(f"✅ {file} موجود")
        
        conn = sqlite3.connect(file)
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [t[0] for t in cursor.fetchall()]
        print(f"   📊 الجداول: {len(tables)}")
        
        # فحص البيانات
        for table in ['person_data', 'course', 'course_participant']:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   📋 {table}: {count} سجل")
        
        conn.close()
        break
    else:
        print(f"❌ {file} غير موجود")

print("✅ انتهى الفحص")
