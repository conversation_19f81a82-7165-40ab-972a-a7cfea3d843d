{% extends "base.html" %}

{% block title %}تحليل البيانات المفصل - {{ course.title }}{% endblock %}

{% block styles %}
<style>
    .detailed-analysis-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .analysis-header {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        text-align: center;
    }

    .stats-row {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    }

    .stat-box {
        text-align: center;
        padding: 15px;
        border-radius: 10px;
        margin: 5px;
    }

    .stat-box.new { background: linear-gradient(45deg, #4CAF50, #45a049); color: white; }
    .stat-box.existing { background: linear-gradient(45deg, #2196F3, #1976D2); color: white; }
    .stat-box.duplicate { background: linear-gradient(45deg, #FF9800, #F57C00); color: white; }
    .stat-box.total { background: linear-gradient(45deg, #9C27B0, #7B1FA2); color: white; }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .detailed-table-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .analysis-table {
        margin-bottom: 0;
        font-size: 0.9rem;
    }

    .analysis-table th {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 15px 10px;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
    }

    .analysis-table td {
        padding: 12px 10px;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef;
    }

    .analysis-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-new { background: #4CAF50; color: white; }
    .status-existing { background: #2196F3; color: white; }
    .status-duplicate { background: #FF9800; color: white; }

    .corrected-name {
        background: linear-gradient(45deg, #FFE082, #FFCC02);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.8rem;
    }

    .action-buttons {
        text-align: center;
        margin-top: 30px;
    }

    .btn-detailed {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        margin: 0 10px;
        transition: all 0.3s ease;
    }

    .btn-detailed:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }

    .selection-controls {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }

    .file-info-badge {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        margin: 0 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="detailed-analysis-container">
    <div class="container-fluid">
        <!-- رأس التحليل -->
        <div class="analysis-header">
            <h1 class="mb-3">
                <i class="fas fa-table"></i> تحليل البيانات المفصل
            </h1>
            <h4 class="text-muted mb-3">{{ course.title }}</h4>
            <div class="d-flex justify-content-center align-items-center flex-wrap">
                <span class="file-info-badge">
                    <i class="fas fa-file-excel"></i> {{ filename }}
                </span>
                <span class="file-info-badge">
                    <i class="fas fa-clock"></i> {{ moment().format('DD/MM/YYYY HH:mm') }}
                </span>
                <span class="file-info-badge">
                    <i class="fas fa-users"></i> {{ results.statistics.total_processed }} سجل
                </span>
            </div>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="stats-row">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-box new">
                        <div class="stat-number">{{ results.statistics.new_count }}</div>
                        <div class="stat-label">أشخاص جدد</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-box existing">
                        <div class="stat-number">{{ results.statistics.existing_available_count }}</div>
                        <div class="stat-label">موجودين ومتاحين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-box duplicate">
                        <div class="stat-number">{{ results.statistics.already_participants_count }}</div>
                        <div class="stat-label">مكررين</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-box total">
                        <div class="stat-number">{{ results.statistics.total_processed }}</div>
                        <div class="stat-label">الإجمالي</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات التحكم في الاختيار -->
        <div class="detailed-table-container">
            <div class="selection-controls">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h6 class="mb-0">
                            <i class="fas fa-check-square"></i> أدوات الاختيار:
                        </h6>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-sm btn-outline-success" onclick="selectAll('new')">
                            <i class="fas fa-check"></i> اختيار جميع الجدد
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="selectAll('existing')">
                            <i class="fas fa-check"></i> اختيار جميع المتاحين
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearAll()">
                            <i class="fas fa-times"></i> إلغاء الكل
                        </button>
                    </div>
                </div>
            </div>

            <!-- الجدول المفصل -->
            <div class="table-responsive">
                <table class="table analysis-table">
                    <thead>
                        <tr>
                            <th width="3%">#</th>
                            <th width="15%">الاسم الكامل</th>
                            <th width="8%">الحالة</th>
                            <th width="10%">الاسم المستعار</th>
                            <th width="5%">العمر</th>
                            <th width="8%">المحافظة</th>
                            <th width="8%">المديرية</th>
                            <th width="8%">العزلة</th>
                            <th width="8%">الحي/القرية</th>
                            <th width="8%">المؤهل</th>
                            <th width="8%">الحالة الاجتماعية</th>
                            <th width="8%">العمل</th>
                            <th width="10%">الرقم الوطني</th>
                            <th width="3%">اختيار</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set row_counter = [0] %}

                        <!-- الأشخاص الجدد -->
                        {% for person in results.new_people %}
                        {% set _ = row_counter.append(row_counter.pop() + 1) %}
                        <tr class="new-person-row">
                            <td class="text-center"><strong>{{ row_counter[0] }}</strong></td>
                            <td>
                                <strong>{{ person.name }}</strong>
                                {% if person.original_name %}
                                <br><span class="corrected-name">
                                    <i class="fas fa-edit"></i> مصحح من: {{ person.original_name }}
                                </span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="status-badge status-new">جديد</span>
                            </td>
                            <td class="text-center">{{ person.nickname or '-' }}</td>
                            <td class="text-center">{{ person.age or '-' }}</td>
                            <td class="text-center">{{ person.governorate or '-' }}</td>
                            <td class="text-center">{{ person.directorate or '-' }}</td>
                            <td class="text-center">{{ person.isolation or '-' }}</td>
                            <td class="text-center">{{ person.village or '-' }}</td>
                            <td class="text-center">{{ person.qualification or '-' }}</td>
                            <td class="text-center">{{ person.marital_status or '-' }}</td>
                            <td class="text-center">{{ person.job or '-' }}</td>
                            <td class="text-center">{{ person.national_id or 'سيتم إضافته' }}</td>
                            <td class="text-center">
                                <input type="checkbox" class="form-check-input new-person-check"
                                       data-person-index="{{ loop.index0 }}"
                                       data-person-name="{{ person.name }}" checked>
                            </td>
                        </tr>
                        {% endfor %}

                        <!-- الموجودين والمتاحين -->
                        {% for person in results.existing_available %}
                        {% set _ = row_counter.append(row_counter.pop() + 1) %}
                        <tr class="existing-person-row">
                            <td class="text-center"><strong>{{ row_counter[0] }}</strong></td>
                            <td>
                                <strong>{{ person.name }}</strong>
                                {% if person.original_name %}
                                <br><span class="corrected-name">
                                    <i class="fas fa-edit"></i> مصحح من: {{ person.original_name }}
                                </span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="status-badge status-existing">متاح</span>
                            </td>
                            <td class="text-center">{{ person.nickname or '-' }}</td>
                            <td class="text-center">{{ person.age or '-' }}</td>
                            <td class="text-center">{{ person.governorate or '-' }}</td>
                            <td class="text-center">{{ person.directorate or '-' }}</td>
                            <td class="text-center">{{ person.isolation or '-' }}</td>
                            <td class="text-center">{{ person.village or '-' }}</td>
                            <td class="text-center">{{ person.qualification or '-' }}</td>
                            <td class="text-center">{{ person.marital_status or '-' }}</td>
                            <td class="text-center">{{ person.job or '-' }}</td>
                            <td class="text-center">{{ person.national_id or '-' }}</td>
                            <td class="text-center">
                                <input type="checkbox" class="form-check-input existing-person-check"
                                       data-person-index="{{ loop.index0 }}"
                                       data-person-name="{{ person.name }}" checked>
                            </td>
                        </tr>
                        {% endfor %}

                        <!-- المشاركين بالفعل -->
                        {% for person in results.already_participants %}
                        {% set _ = row_counter.append(row_counter.pop() + 1) %}
                        <tr class="duplicate-person-row">
                            <td class="text-center"><strong>{{ row_counter[0] }}</strong></td>
                            <td>
                                <strong>{{ person.name }}</strong>
                                {% if person.original_name %}
                                <br><span class="corrected-name">
                                    <i class="fas fa-edit"></i> مصحح من: {{ person.original_name }}
                                </span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="status-badge status-duplicate">مكرر</span>
                            </td>
                            <td class="text-center">{{ person.nickname or '-' }}</td>
                            <td class="text-center">{{ person.age or '-' }}</td>
                            <td class="text-center">{{ person.governorate or '-' }}</td>
                            <td class="text-center">{{ person.directorate or '-' }}</td>
                            <td class="text-center">{{ person.isolation or '-' }}</td>
                            <td class="text-center">{{ person.village or '-' }}</td>
                            <td class="text-center">{{ person.qualification or '-' }}</td>
                            <td class="text-center">{{ person.marital_status or '-' }}</td>
                            <td class="text-center">{{ person.job or '-' }}</td>
                            <td class="text-center">{{ person.national_id or '-' }}</td>
                            <td class="text-center">
                                <input type="checkbox" class="form-check-input" disabled>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <button class="btn btn-detailed" onclick="executeSelectedAddition()">
                <i class="fas fa-magic"></i> إضافة المختارين
                <span id="selectedCount">({{ results.statistics.new_count + results.statistics.existing_available_count }})</span>
            </button>
            <button class="btn btn-detailed" onclick="executeSmartAddAll()">
                <i class="fas fa-bolt"></i> إضافة ذكية شاملة
            </button>
            <a href="/course/{{ course.id }}/import_participants" class="btn btn-detailed">
                <i class="fas fa-arrow-right"></i> العودة للاستيراد
            </a>
            <a href="/manage_participants/{{ course.id }}/" class="btn btn-detailed">
                <i class="fas fa-users"></i> إدارة المشاركين
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// بيانات النتائج
const results = {{ results | tojson }};

// 🚀 الحل الإبداعي: استخراج course_id من URL أو البيانات
const currentUrl = window.location.pathname;
const courseId = currentUrl.match(/\/course\/(\d+)\//)?.[1] ||
                 currentUrl.match(/manage_participants\/(\d+)\//)?.[1] ||
                 {{ course.id if course else '1' }};

console.log('🎯 Course ID detected:', courseId);

// تحديث عداد المختارين
function updateSelectedCount() {
    const newSelected = $('.new-person-check:checked').length;
    const existingSelected = $('.existing-person-check:checked').length;
    const total = newSelected + existingSelected;

    $('#selectedCount').text(`(${total})`);

    // تحديث حالة الزر
    const addBtn = $('button[onclick="executeSelectedAddition()"]');
    if (total > 0) {
        addBtn.prop('disabled', false);
        addBtn.removeClass('btn-secondary').addClass('btn-detailed');
    } else {
        addBtn.prop('disabled', true);
        addBtn.removeClass('btn-detailed').addClass('btn-secondary');
    }
}

// اختيار جميع العناصر من نوع معين
function selectAll(type) {
    if (type === 'new') {
        $('.new-person-check').prop('checked', true);
    } else if (type === 'existing') {
        $('.existing-person-check').prop('checked', true);
    }
    updateSelectedCount();
}

// إلغاء جميع الاختيارات
function clearAll() {
    $('.new-person-check, .existing-person-check').prop('checked', false);
    updateSelectedCount();
}

// تنفيذ إضافة المختارين
function executeSelectedAddition() {
    const newSelected = [];
    const existingSelected = [];

    // جمع الأشخاص الجدد المختارين
    $('.new-person-check:checked').each(function() {
        const index = $(this).data('person-index');
        newSelected.push(results.new_people[index]);
    });

    // جمع الموجودين المختارين
    $('.existing-person-check:checked').each(function() {
        const index = $(this).data('person-index');
        existingSelected.push(results.existing_available[index]);
    });

    if (newSelected.length === 0 && existingSelected.length === 0) {
        alert('يرجى اختيار أشخاص للإضافة أولاً');
        return;
    }

    const message = `هل تريد إضافة المختارين؟\n\n` +
                   `🆕 جدد: ${newSelected.length}\n` +
                   `👥 متاحين: ${existingSelected.length}\n` +
                   `📊 الإجمالي: ${newSelected.length + existingSelected.length}`;

    if (confirm(message)) {
        executeAddition({
            action: 'smart_add',
            new_people: newSelected,
            existing_available: existingSelected
        });
    }
}

// تنفيذ الإضافة الذكية الشاملة
function executeSmartAddAll() {
    const message = `هل تريد تنفيذ الإضافة الذكية الشاملة؟\n\n` +
                   `🆕 جدد: ${results.statistics.new_count}\n` +
                   `👥 متاحين: ${results.statistics.existing_available_count}\n` +
                   `⚠️ مكررين: ${results.statistics.already_participants_count} (سيتم تجاهلهم)\n` +
                   `📊 سيتم إضافة: ${results.statistics.new_count + results.statistics.existing_available_count}`;

    if (confirm(message)) {
        executeAddition({
            action: 'smart_add',
            new_people: results.new_people,
            existing_available: results.existing_available
        });
    }
}

// تنفيذ الإضافة
function executeAddition(data) {
    // إظهار مؤشر التحميل
    const loadingHtml = `
        <div class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري المعالجة...</span>
            </div>
            <p class="mt-3">جاري إضافة البيانات...</p>
        </div>
    `;

    $('body').append(`
        <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-body">
                        ${loadingHtml}
                    </div>
                </div>
            </div>
        </div>
    `);

    $('#loadingModal').modal('show');

    // تعطيل جميع الأزرار
    $('.btn-detailed').prop('disabled', true);

    // 🎯 بناء URL ديناميكي للمعالجة
    const processUrl = `/course/${courseId}/process_import`;
    console.log('📡 Process URL:', processUrl);

    $.ajax({
        url: processUrl,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            $('#loadingModal').modal('hide');

            if (response.success) {
                // إظهار نتائج النجاح
                const successMessage = `
                    ✅ تم الاستيراد بنجاح!\n\n
                    📊 تم إضافة ${response.added_to_db || 0} شخص لقاعدة البيانات\n
                    👥 تم إضافة ${response.added_to_course || 0} شخص للدورة\n
                    📈 إجمالي المشاركين الآن: ${response.total_participants || 0}
                `;

                alert(successMessage);

                // التوجه لصفحة إدارة المشاركين
                setTimeout(function() {
                    window.location.href = '/manage_participants/' + courseId + '/';
                }, 1000);
            } else {
                alert('خطأ: ' + response.message);
                $('.btn-detailed').prop('disabled', false);
            }
        },
        error: function() {
            $('#loadingModal').modal('hide');
            alert('حدث خطأ في التنفيذ');
            $('.btn-detailed').prop('disabled', false);
        }
    });
}

// تهيئة الصفحة
$(document).ready(function() {
    // مراقبة تغيير الاختيارات
    $('.new-person-check, .existing-person-check').change(updateSelectedCount);

    // تحديث العداد الأولي
    updateSelectedCount();

    // تأثيرات بصرية للجدول
    $('.analysis-table tbody tr').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );
});
</script>
{% endblock %}
