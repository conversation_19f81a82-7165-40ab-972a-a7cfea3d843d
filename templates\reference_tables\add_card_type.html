{% extends "layout.html" %}

{% block styles %}
<style>
    .form-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .form-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: bold;
        margin-bottom: 8px;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #4a6bff;
        box-shadow: 0 0 0 0.25rem rgba(74, 107, 255, 0.25);
    }
    
    .form-text {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 5px;
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
    }
    
    .btn-cancel {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-cancel:hover {
        transform: translateY(-2px);
    }
    
    .invalid-feedback {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>إضافة نوع بطاقة</h2>
        <div>
            <a href="{{ url_for('card_types') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى أنواع البطاقات
            </a>
        </div>
    </div>
    
    <div class="form-card">
        <div class="form-header">
            <h4><i class="fas fa-id-card me-2"></i> إضافة نوع بطاقة جديد</h4>
            <p class="text-muted">أدخل بيانات نوع البطاقة الجديد</p>
        </div>
        
        <form method="POST">
            {{ form.hidden_tag() }}
            
            <div class="form-group">
                {{ form.name.label(class="form-label") }}
                {% if form.name.errors %}
                    {{ form.name(class="form-control is-invalid") }}
                    <div class="invalid-feedback">
                        {% for error in form.name.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.name(class="form-control") }}
                {% endif %}
                <small class="form-text">أدخل اسم نوع البطاقة (مثال: بطاقة شخصية، جواز سفر)</small>
            </div>
            
            <div class="d-flex justify-content-between mt-4">
                <a href="{{ url_for('card_types') }}" class="btn btn-secondary btn-cancel">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
                {{ form.submit(class="btn btn-primary btn-submit") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
