{% extends "layout.html" %}

{% block styles %}
<style>
    .video-container {
        background-color: #000;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        margin-bottom: 30px;
    }
    
    .video-player {
        width: 100%;
        max-height: 70vh;
    }
    
    .video-info {
        background-color: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }
    
    .video-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .video-description {
        color: #6c757d;
        margin-bottom: 20px;
    }
    
    .video-meta {
        display: flex;
        justify-content: space-between;
        border-top: 1px solid #e9ecef;
        padding-top: 15px;
    }
    
    .video-course {
        font-weight: bold;
    }
    
    .video-day {
        color: #6c757d;
    }
    
    .btn-back {
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <a href="{{ url_for('course_details', course_id=material.course_id) }}" class="btn btn-secondary btn-back">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى الدورة
            </a>
            
            <div class="video-container">
                <video class="video-player" controls autoplay>
                    <source src="{{ url_for('view_file', filename=material.file_path) }}" type="video/mp4">
                    متصفحك لا يدعم تشغيل الفيديو.
                </video>
            </div>
            
            <div class="video-info">
                <div class="video-title">{{ material.title }}</div>
                {% if material.description %}
                <div class="video-description">{{ material.description }}</div>
                {% endif %}
                <div class="video-meta">
                    <div class="video-course">{{ material.course.title }}</div>
                    <div class="video-day">اليوم {{ material.day_number }}</div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('download_file', filename=material.file_path) }}" class="btn btn-primary">
                    <i class="fas fa-download me-1"></i> تحميل الفيديو
                </a>
                {% if current_user.id == material.course.trainer_id or current_user.role == 'admin' %}
                <div>
                    <a href="{{ url_for('edit_material', material_id=material.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i> تعديل
                    </a>
                    <a href="{{ url_for('delete_material', material_id=material.id) }}" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الفيديو؟');">
                        <i class="fas fa-trash-alt me-1"></i> حذف
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
