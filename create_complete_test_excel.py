#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
📊 إنشاء ملف Excel شامل بجميع الحقول للاختبار
"""

import pandas as pd
import tempfile
import random

def create_complete_excel_file():
    """إنشاء ملف Excel شامل بجميع الحقول"""
    print("📁 إنشاء ملف Excel شامل بجميع الحقول...")
    
    # البيانات الشاملة
    test_data = {
        'الاسم الشخصي': [
            # أشخاص موجودين ومشاركين (مكررين)
            'علي صالح محمد الحميري1',
            'علي صالح محمد الحميري2',
            
            # أشخاص موجودين ومتاحين
            'فاطمة أحمد علي المقطري2',
            'علي صالح محمد الحميري3',
            
            # أشخاص جدد تماماً
            'محمد عبدالله أحمد الزبيري',
            'سارة محمد علي الشامي',
            'نور الدين عبدالله أحمد',
            'ليلى محمد حسن الأهدل',
            'عبدالرحمن محمد علي الصنعاني',
            'خديجة أحمد سالم المحويتي',
            'يوسف عبدالله محمد التعزي',
            'زينب محمد عبدالله الحديدي',
            
            # أشخاص جدد مع أخطاء إملائية
            'احمد محمد علي الحوثي',      # أحمد
            'فاطمه عبدالله محمد الشامي',  # فاطمة
            'محمود عبدالرحمن احمد العدني' # أحمد
        ],
        
        'الاسم المستعار': [
            'أبو محمد', 'أم أحمد', 'أبو فاطمة', 'أم علي',
            'أبو عبدالله', 'أم سارة', 'أبو نور', 'أم ليلى',
            'أبو عبدالرحمن', 'أم خديجة', 'أبو يوسف', 'أم زينب',
            'أبو أحمد', 'أم فاطمة', 'أبو محمود'
        ],
        
        'العمر': [
            35, 28, 42, 31, 29, 26, 38, 33, 
            27, 34, 30, 25, 36, 32, 39
        ],
        
        'المحافظة': [
            'صنعاء', 'عدن', 'تعز', 'الحديدة', 'إب',
            'ذمار', 'صعدة', 'حجة', 'المحويت', 'الجوف',
            'مأرب', 'البيضاء', 'لحج', 'أبين', 'شبوة'
        ],
        
        'المديرية': [
            'الثورة', 'كريتر', 'صالة', 'الحوك', 'السياني',
            'وصاب السافل', 'باقم', 'حرض', 'الطويلة', 'الحزم',
            'مدينة مأرب', 'رداع', 'تبن', 'خنفر', 'عتق'
        ],
        
        'العزلة': [
            'بني الحارث', 'صيرة', 'الشمايتين', 'الزهرة', 'النادرة',
            'الأهجر', 'الصفراء', 'كعيدنة', 'شبام كوكبان', 'الغيل',
            'الجدعان', 'الصومعة', 'الحبيلين', 'المحفد', 'بيحان'
        ],
        
        'الحي/القرية': [
            'حي الزراعة', 'حي المعلا', 'حي الروضة', 'حي الثورة', 'حي السبعين',
            'قرية الوادي', 'قرية الجبل', 'قرية السهل', 'حي الحصبة', 'حي شعوب',
            'قرية المدار', 'حي الزبيري', 'قرية الضالع', 'حي الانتصار', 'قرية العرش'
        ],
        
        'المؤهل العلمي': [
            'بكالوريوس', 'ثانوية عامة', 'ماجستير', 'دبلوم', 'إعدادية',
            'دكتوراه', 'ثانوية عامة', 'بكالوريوس', 'دبلوم عالي', 'ثانوية تجارية',
            'بكالوريوس', 'ماجستير', 'ثانوية عامة', 'دبلوم', 'بكالوريوس'
        ],
        
        'الحالة الاجتماعية': [
            'متزوج', 'أعزب', 'متزوجة', 'عزباء', 'متزوج',
            'عزباء', 'متزوج', 'متزوجة', 'متزوج', 'متزوجة',
            'أعزب', 'عزباء', 'متزوج', 'متزوجة', 'متزوج'
        ],
        
        'العمل': [
            'مهندس', 'معلمة', 'طبيب', 'محاسبة', 'مبرمج',
            'ممرضة', 'مدير', 'سكرتيرة', 'فني', 'مدرسة',
            'محامي', 'طبيبة', 'مهندس', 'معلمة', 'طبيب'
        ],
        
        'الإدارة': [
            'الهندسة', 'التعليم', 'الصحة', 'المالية', 'تقنية المعلومات',
            'التمريض', 'الإدارة العامة', 'السكرتارية', 'الصيانة', 'التربية',
            'القانونية', 'الطب', 'الهندسة', 'التعليم', 'الصحة'
        ],
        
        'مكان العمل': [
            'وزارة الأشغال', 'وزارة التربية', 'مستشفى الثورة', 'وزارة المالية', 'شركة يمن نت',
            'مستشفى الكويت', 'ديوان الخدمة المدنية', 'جامعة صنعاء', 'شركة النفط', 'مدرسة الأندلس',
            'المحكمة العليا', 'مستشفى الجمهوري', 'شركة الكهرباء', 'معهد المعلمين', 'مركز القلب'
        ],
        
        'الرقم الوطني': [
            '01234567890', '01234567891', '01234567892', '01234567893', '01234567894',
            '01234567895', '01234567896', '01234567897', '01234567898', '01234567899',
            '01234567800', '01234567801', '01234567802', '01234567803', '01234567804'
        ],
        
        'الرقم العسكري': [
            'M001', 'F002', 'M003', 'F004', 'M005',
            'F006', 'M007', 'F008', 'M009', 'F010',
            'M011', 'F012', 'M013', 'F014', 'M015'
        ],
        
        'رقم التلفون': [
            '777123456', '733987654', '770555444', '711222333', '777888999',
            '733111222', '770333444', '711555666', '777777888', '733444555',
            '770666777', '711888999', '777000111', '733222333', '770444555'
        ]
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(test_data)
    
    # إنشاء ملف مؤقت
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
    
    # حفظ الملف مع تنسيق جميل
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='بيانات المشاركين', index=False)
        
        # تنسيق الورقة
        worksheet = writer.sheets['بيانات المشاركين']
        
        # تعديل عرض الأعمدة
        column_widths = {
            'A': 25,  # الاسم الشخصي
            'B': 15,  # الاسم المستعار
            'C': 8,   # العمر
            'D': 12,  # المحافظة
            'E': 15,  # المديرية
            'F': 15,  # العزلة
            'G': 15,  # الحي/القرية
            'H': 15,  # المؤهل العلمي
            'I': 12,  # الحالة الاجتماعية
            'J': 12,  # العمل
            'K': 18,  # الإدارة
            'L': 20,  # مكان العمل
            'M': 15,  # الرقم الوطني
            'N': 12,  # الرقم العسكري
            'O': 12   # رقم التلفون
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
    
    print(f"✅ تم إنشاء ملف Excel الشامل: {temp_file.name}")
    print(f"📊 عدد الأسماء: {len(test_data['الاسم الشخصي'])}")
    print(f"📋 عدد الحقول: {len(test_data.keys())}")
    
    return temp_file.name

def show_file_structure():
    """عرض هيكل الملف"""
    print("\n📋 هيكل ملف Excel الشامل:")
    print("=" * 50)
    
    fields = [
        "1. الاسم الشخصي (مطلوب)",
        "2. الاسم المستعار", 
        "3. العمر",
        "4. المحافظة",
        "5. المديرية",
        "6. العزلة",
        "7. الحي/القرية",
        "8. المؤهل العلمي",
        "9. الحالة الاجتماعية",
        "10. العمل",
        "11. الإدارة", 
        "12. مكان العمل",
        "13. الرقم الوطني",
        "14. الرقم العسكري",
        "15. رقم التلفون"
    ]
    
    for field in fields:
        print(f"   ✅ {field}")

def show_data_samples():
    """عرض عينات من البيانات"""
    print("\n📊 عينات من البيانات:")
    print("=" * 50)
    
    samples = [
        "🆕 أشخاص جدد مع بيانات كاملة",
        "👥 أشخاص موجودين مع تفاصيل إضافية", 
        "⚠️ أشخاص مكررين للاختبار",
        "🔧 أسماء تحتاج تصحيح إملائي",
        "📱 أرقام هواتف متنوعة",
        "🏢 أماكن عمل مختلفة",
        "🎓 مؤهلات علمية متنوعة",
        "🏘️ مناطق جغرافية مختلفة"
    ]
    
    for sample in samples:
        print(f"   {sample}")

def main():
    """الدالة الرئيسية"""
    print("📊 إنشاء ملف Excel شامل بجميع الحقول")
    print("=" * 60)
    
    # إنشاء الملف
    excel_file = create_complete_excel_file()
    
    # عرض المعلومات
    show_file_structure()
    show_data_samples()
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء ملف Excel الشامل بنجاح!")
    print("=" * 60)
    
    print(f"📁 مسار الملف:")
    print(f"   {excel_file}")
    
    print(f"\n🎯 للاستخدام:")
    print(f"   1. احفظ الملف في مكان مناسب")
    print(f"   2. استخدمه في اختبار النظام")
    print(f"   3. ستحصل على تحليل شامل لجميع الحقول")
    
    print(f"\n✨ الملف يحتوي على 15 حقل كامل كما طلبت!")

if __name__ == "__main__":
    main()
