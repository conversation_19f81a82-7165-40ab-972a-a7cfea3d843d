#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مقارنة قواعد البيانات في المجلدين المختلفين
"""

import sqlite3
import os

def check_database_detailed(db_path, db_name):
    """فحص تفصيلي لقاعدة بيانات واحدة"""
    print(f"\n🔍 فحص قاعدة البيانات: {db_name}")
    print(f"📁 المسار: {db_path}")
    
    if not os.path.exists(db_path):
        print("❌ الملف غير موجود")
        return None
    
    file_size = os.path.getsize(db_path)
    print(f"📊 حجم الملف: {file_size:,} بايت")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الجداول المهمة
        important_tables = ['user', 'course', 'person_data', 'personal_data', 'course_participant']
        
        result = {'path': db_path, 'size': file_size, 'data': {}}
        
        for table in important_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                result['data'][table] = count
                
                if count > 0:
                    print(f"✅ {table}: {count:,} سجل")
                    
                    # عرض عينة من البيانات
                    if table == 'course' and count > 0:
                        cursor.execute("SELECT course_number, title FROM course LIMIT 3")
                        courses = cursor.fetchall()
                        print("   📋 عينة من الدورات:")
                        for course in courses:
                            print(f"      - {course[0]}: {course[1]}")
                            
                    elif table == 'person_data' and count > 0:
                        cursor.execute("SELECT full_name, governorate FROM person_data LIMIT 3")
                        persons = cursor.fetchall()
                        print("   👥 عينة من الأشخاص:")
                        for person in persons:
                            print(f"      - {person[0]} ({person[1] or 'غير محدد'})")
                            
                    elif table == 'personal_data' and count > 0:
                        cursor.execute("SELECT full_name FROM personal_data LIMIT 3")
                        persons = cursor.fetchall()
                        print("   👥 عينة من البيانات الشخصية:")
                        for person in persons:
                            print(f"      - {person[0]}")
                            
                else:
                    print(f"⚪ {table}: فارغ")
                    
            except Exception as e:
                print(f"❌ خطأ في جدول {table}: {e}")
                result['data'][table] = f'خطأ: {str(e)}'
        
        conn.close()
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def main():
    print("🔍 مقارنة قواعد البيانات")
    print("=" * 70)
    
    # المسارات المختلفة لقواعد البيانات
    databases = [
        ("E:/app/TRINING/training_system.db", "قاعدة البيانات الرئيسية"),
        ("E:/app/TRINING/instance/training_system.db", "قاعدة البيانات في مجلد instance"),
        ("training_system.db", "قاعدة البيانات المحلية"),
        ("instance/training_system.db", "قاعدة البيانات في instance المحلي")
    ]
    
    results = []
    
    for db_path, db_name in databases:
        result = check_database_detailed(db_path, db_name)
        if result:
            results.append(result)
    
    # تحليل النتائج
    print("\n" + "=" * 70)
    print("🎯 تحليل النتائج:")
    
    if not results:
        print("❌ لم يتم العثور على أي قاعدة بيانات صالحة")
        return
    
    # العثور على أفضل قاعدة بيانات
    best_db = None
    max_total = 0
    
    for result in results:
        total_records = 0
        for table, count in result['data'].items():
            if isinstance(count, int):
                total_records += count
        
        print(f"\n📊 {result['path']}:")
        print(f"   📈 إجمالي السجلات: {total_records:,}")
        print(f"   💾 حجم الملف: {result['size']:,} بايت")
        
        if total_records > max_total:
            max_total = total_records
            best_db = result
    
    if best_db:
        print(f"\n🏆 أفضل قاعدة بيانات:")
        print(f"   📁 المسار: {best_db['path']}")
        print(f"   📈 إجمالي السجلات: {max_total:,}")
        print(f"   💾 حجم الملف: {best_db['size']:,} بايت")
        
        print(f"\n💡 لاستخدام هذه القاعدة:")
        if "instance" in best_db['path']:
            print("   النظام يستخدم قاعدة البيانات في مجلد instance بالفعل")
        else:
            print("   انسخ هذه القاعدة إلى المكان الصحيح")
            print(f"   copy \"{best_db['path']}\" instance/training_system.db")

if __name__ == '__main__':
    main()
