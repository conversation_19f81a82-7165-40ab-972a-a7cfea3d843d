#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء بيانات شاملة للنظام
- أكثر من 1000 اسم للأشخاص
- أكثر من 200 دورة متنوعة
- جميع الجداول الترميزية مع بيانات كاملة
"""

import os
import sys
import random
from datetime import datetime, timedelta
import subprocess

# تثبيت faker إذا لم يكن مثبتاً
try:
    from faker import Faker
except ImportError:
    print("🔄 تثبيت مكتبة Faker...")
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'faker'])
    from faker import Faker

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, Course, PersonData, Enrollment
from app import (
    Agency, TrainingCenter, TrainingCenterType, Governorate, Directorate,
    Village, Location, Department, MilitaryRank, Specialization,
    QualificationType, ParticipantType, CourseCategory, CourseLevel,
    CoursePath, CoursePathLevel, ForceClassification, CardType,
    BloodType, MaritalStatus, InjuryType, InjuryCause
)

# إنشاء مولد البيانات العربية
fake = Faker('ar_SA')
fake_en = Faker('en_US')

def create_reference_data():
    """إنشاء البيانات المرجعية الأساسية"""
    print("🔄 إنشاء البيانات المرجعية...")

    # الجهات
    agencies_data = [
        ('وزارة الدفاع', 'MOD'),
        ('وزارة الداخلية', 'MOI'),
        ('وزارة التعليم', 'MOE'),
        ('وزارة الصحة', 'MOH'),
        ('وزارة المالية', 'MOF'),
        ('وزارة العدل', 'MOJ'),
        ('وزارة الخارجية', 'MOFA'),
        ('وزارة النقل', 'MOT'),
        ('وزارة الاتصالات', 'MOCI'),
        ('وزارة الطاقة', 'MOEN'),
        ('الحرس الوطني', 'NG'),
        ('قوات الأمن الخاصة', 'SSF'),
        ('المخابرات العامة', 'GID'),
        ('الأمن السياسي', 'PSO'),
        ('قوات الطوارئ', 'EF'),
        ('الدفاع المدني', 'CD'),
        ('خفر السواحل', 'CG'),
        ('الشرطة العسكرية', 'MP'),
        ('القوات الجوية', 'AF'),
        ('القوات البحرية', 'NF')
    ]

    for name, code in agencies_data:
        if not Agency.query.filter_by(code=code).first():
            agency = Agency(name=name, code=code)
            db.session.add(agency)

    # أنواع مراكز التدريب
    center_types_data = [
        'مركز تدريب عسكري',
        'مركز تدريب مدني',
        'أكاديمية عسكرية',
        'معهد تدريب متخصص',
        'مركز تطوير القدرات',
        'مركز التدريب المهني',
        'مركز التدريب التقني',
        'مركز التدريب الإداري',
        'مركز التدريب الأمني',
        'مركز التدريب الطبي'
    ]

    for center_type in center_types_data:
        if not TrainingCenterType.query.filter_by(name=center_type).first():
            tc_type = TrainingCenterType(name=center_type)
            db.session.add(tc_type)

    db.session.commit()

    # مراكز التدريب
    centers_data = [
        ('أكاديمية الملك عبدالعزيز الحربية', 'الرياض', 1),
        ('كلية الملك فهد الأمنية', 'الرياض', 2),
        ('معهد الإدارة العامة', 'الرياض', 3),
        ('أكاديمية نايف العربية للعلوم الأمنية', 'الرياض', 4),
        ('مركز التدريب المتقدم', 'جدة', 5),
        ('معهد التدريب التقني', 'الدمام', 6),
        ('مركز التطوير المهني', 'مكة المكرمة', 7),
        ('أكاديمية القيادة والأركان', 'الرياض', 8),
        ('مركز التدريب الطبي العسكري', 'الطائف', 9),
        ('معهد التدريب الفني', 'المدينة المنورة', 10),
        ('مركز التدريب الأمني المتقدم', 'أبها', 1),
        ('أكاديمية الطيران المدني', 'جدة', 2),
        ('مركز التدريب البحري', 'الدمام', 3),
        ('معهد التدريب الإلكتروني', 'الرياض', 4),
        ('مركز التدريب اللوجستي', 'الخبر', 5)
    ]

    for name, location, type_id in centers_data:
        if not TrainingCenter.query.filter_by(name=name).first():
            center = TrainingCenter(
                name=name,
                location=location,
                training_center_type_id=type_id
            )
            db.session.add(center)

    db.session.commit()
    print("✅ تم إنشاء البيانات المرجعية الأساسية")

def create_geographic_data():
    """إنشاء البيانات الجغرافية"""
    print("🔄 إنشاء البيانات الجغرافية...")

    # المحافظات والمديريات
    geographic_data = {
        'صنعاء': ['مديرية الصافية', 'مديرية بني حشيش', 'مديرية همدان', 'مديرية معين', 'مديرية الطيال'],
        'عدن': ['مديرية المعلا', 'مديرية كريتر', 'مديرية الشيخ عثمان', 'مديرية دار سعد', 'مديرية البريقة'],
        'تعز': ['مديرية الوازعية', 'مديرية الشمايتين', 'مديرية صالة', 'مديرية المسراخ', 'مديرية الصلو'],
        'الحديدة': ['مديرية الحوك', 'مديرية الدريهمي', 'مديرية بيت الفقيه', 'مديرية زبيد', 'مديرية التحيتا'],
        'إب': ['مديرية يريم', 'مديرية السياني', 'مديرية المخادر', 'مديرية النادرة', 'مديرية حبيش'],
        'ذمار': ['مديرية عتمة', 'مديرية الحداء', 'مديرية وصاب السافل', 'مديرية جهران', 'مديرية مغرب عنس'],
        'حجة': ['مديرية عبس', 'مديرية حرض', 'مديرية ميدي', 'مديرية حيران', 'مديرية بكيل المير'],
        'صعدة': ['مديرية الصفراء', 'مديرية كتاف', 'مديرية رازح', 'مديرية شدا', 'مديرية منبه'],
        'عمران': ['مديرية ثلا', 'مديرية حبور ظليمة', 'مديرية السودة', 'مديرية خارف', 'مديرية ريدة'],
        'مأرب': ['مديرية صرواح', 'مديرية الجوبة', 'مديرية رحبة', 'مديرية حريب', 'مديرية ماهلية'],
        'الجوف': ['مديرية الحزم', 'مديرية خب والشعف', 'مديرية برط العنان', 'مديرية الغيل', 'مديرية المصلوب'],
        'لحج': ['مديرية الحوطة', 'مديرية تبن', 'مديرية يافع', 'مديرية الحد', 'مديرية ردفان'],
        'أبين': ['مديرية زنجبار', 'مديرية خنفر', 'مديرية لودر', 'مديرية أحور', 'مديرية مودية'],
        'شبوة': ['مديرية عتق', 'مديرية بيحان', 'مديرية مرخة السفلى', 'مديرية حبان', 'مديرية الروضة'],
        'حضرموت': ['مديرية المكلا', 'مديرية سيئون', 'مديرية تريم', 'مديرية الشحر', 'مديرية القطن'],
        'المهرة': ['مديرية الغيضة', 'مديرية قشن', 'مديرية حوف', 'مديرية المسيلة', 'مديرية شحن'],
        'سقطرى': ['مديرية حديبو', 'مديرية قلنسية', 'مديرية الرحة', 'مديرية نوجد', 'مديرية هومهيل'],
        'البيضاء': ['مديرية الزاهر', 'مديرية ولد ربيع', 'مديرية الصومعة', 'مديرية مكيراس', 'مديرية ذي ناعم'],
        'الضالع': ['مديرية الضالع', 'مديرية دمت', 'مديرية قعطبة', 'مديرية الحشاء', 'مديرية جحاف'],
        'ريمة': ['مديرية الجعفرية', 'مديرية بلاد الطعام', 'مديرية كسمة', 'مديرية مزهر', 'مديرية السلفية'],
        'المحويت': ['مديرية الحوك', 'مديرية ملحان', 'مديرية شبام كوكبان', 'مديرية بني سعد', 'مديرية الرجم']
    }

    for gov_name, directorates in geographic_data.items():
        # إنشاء المحافظة
        governorate = Governorate.query.filter_by(name=gov_name).first()
        if not governorate:
            governorate = Governorate(name=gov_name)
            db.session.add(governorate)
            db.session.flush()

        # إنشاء المديريات
        for dir_name in directorates:
            if not Directorate.query.filter_by(name=dir_name, governorate_id=governorate.id).first():
                directorate = Directorate(
                    name=dir_name,
                    governorate_id=governorate.id
                )
                db.session.add(directorate)
                db.session.flush()

                # إنشاء قرى لكل مديرية
                villages = [
                    f'قرية الشهيد {fake.first_name_male()}',
                    f'حي {fake.first_name_male()}',
                    f'منطقة {fake.first_name_male()}',
                    f'عزلة {fake.first_name_male()}',
                    f'حارة {fake.first_name_male()}'
                ]

                for village_name in villages:
                    if not Village.query.filter_by(name=village_name, directorate_id=directorate.id).first():
                        village = Village(
                            name=village_name,
                            directorate_id=directorate.id
                        )
                        db.session.add(village)

    db.session.commit()
    print("✅ تم إنشاء البيانات الجغرافية")

def create_military_data():
    """إنشاء البيانات العسكرية"""
    print("🔄 إنشاء البيانات العسكرية...")

    # الرتب العسكرية
    ranks_data = [
        ('مشير', 'مشير'),
        ('فريق أول', 'فريق أول'),
        ('فريق', 'فريق'),
        ('لواء', 'لواء'),
        ('عميد', 'عميد'),
        ('عقيد', 'عقيد'),
        ('مقدم', 'مقدم'),
        ('رائد', 'رائد'),
        ('نقيب', 'نقيب'),
        ('ملازم أول', 'ملازم أول'),
        ('ملازم', 'ملازم'),
        ('رقيب أول', 'رقيب أول'),
        ('رقيب', 'رقيب'),
        ('عريف أول', 'عريف أول'),
        ('عريف', 'عريف'),
        ('جندي أول', 'جندي أول'),
        ('جندي', 'جندي')
    ]

    for name, code in ranks_data:
        if not MilitaryRank.query.filter_by(name=name).first():
            rank = MilitaryRank(name=name, code=code)
            db.session.add(rank)

    # التصنيفات القتالية
    force_classifications_data = [
        'مشاة',
        'مدرعات',
        'مدفعية',
        'مهندسين',
        'إشارة',
        'طيران',
        'بحرية',
        'دفاع جوي',
        'استطلاع',
        'قوات خاصة',
        'حرس حدود',
        'أمن',
        'شرطة عسكرية',
        'لوجستيات',
        'طبي',
        'إداري',
        'مالي',
        'قانوني',
        'تدريب',
        'استخبارات'
    ]

    for classification in force_classifications_data:
        if not ForceClassification.query.filter_by(name=classification).first():
            fc = ForceClassification(name=classification)
            db.session.add(fc)

    # التخصصات
    specializations_data = [
        'قيادة وأركان',
        'عمليات عسكرية',
        'استخبارات عسكرية',
        'لوجستيات عسكرية',
        'إدارة عسكرية',
        'طب عسكري',
        'هندسة عسكرية',
        'طيران عسكري',
        'بحرية عسكرية',
        'إشارة عسكرية',
        'مدفعية',
        'مدرعات',
        'مشاة',
        'قوات خاصة',
        'أمن وحماية',
        'شرطة عسكرية',
        'دفاع مدني',
        'حرس حدود',
        'مكافحة إرهاب',
        'أمن سيبراني',
        'تكنولوجيا معلومات',
        'إدارة أعمال',
        'محاسبة ومالية',
        'قانون عسكري',
        'علوم سياسية',
        'علاقات دولية'
    ]

    for specialization in specializations_data:
        if not Specialization.query.filter_by(name=specialization).first():
            spec = Specialization(name=specialization)
            db.session.add(spec)

    db.session.commit()
    print("✅ تم إنشاء البيانات العسكرية")

def create_course_reference_data():
    """إنشاء البيانات المرجعية للدورات"""
    print("🔄 إنشاء البيانات المرجعية للدورات...")

    # فئات الدورات
    categories_data = [
        'دورات عسكرية',
        'دورات أمنية',
        'دورات إدارية',
        'دورات تقنية',
        'دورات طبية',
        'دورات قانونية',
        'دورات مالية',
        'دورات لغات',
        'دورات حاسوب',
        'دورات قيادية',
        'دورات تخصصية',
        'دورات تطويرية',
        'دورات تأهيلية',
        'دورات تدريبية'
    ]

    for category in categories_data:
        if not CourseCategory.query.filter_by(name=category).first():
            cat = CourseCategory(name=category)
            db.session.add(cat)

    # مستويات الدورات
    levels_data = [
        'مبتدئ',
        'متوسط',
        'متقدم',
        'خبير',
        'تأسيسي',
        'تخصصي',
        'قيادي',
        'استراتيجي'
    ]

    for level in levels_data:
        if not CourseLevel.query.filter_by(name=level).first():
            lv = CourseLevel(name=level)
            db.session.add(lv)

    # مسارات الدورات
    paths_data = [
        ('المسار العسكري', 'تدريب عسكري متخصص'),
        ('المسار الأمني', 'تدريب أمني وحماية'),
        ('المسار الإداري', 'تطوير المهارات الإدارية'),
        ('المسار التقني', 'تدريب تقني ومهني'),
        ('المسار القيادي', 'إعداد القادة'),
        ('المسار الطبي', 'تدريب طبي متخصص'),
        ('المسار القانوني', 'تدريب قانوني'),
        ('المسار المالي', 'تدريب مالي ومحاسبي'),
        ('مسار اللغات', 'تعلم اللغات الأجنبية'),
        ('مسار الحاسوب', 'تدريب تقنية المعلومات')
    ]

    for name, description in paths_data:
        if not CoursePath.query.filter_by(name=name).first():
            path = CoursePath(name=name, description=description)
            db.session.add(path)

    db.session.commit()

    # مستويات المسارات
    path_levels_data = [
        'المستوى الأول',
        'المستوى الثاني',
        'المستوى الثالث',
        'المستوى الرابع',
        'المستوى المتقدم',
        'المستوى الخبير'
    ]

    for level in path_levels_data:
        if not CoursePathLevel.query.filter_by(name=level).first():
            pl = CoursePathLevel(name=level)
            db.session.add(pl)

    db.session.commit()
    print("✅ تم إنشاء البيانات المرجعية للدورات")

def create_personal_data():
    """إنشاء البيانات الشخصية المرجعية"""
    print("🔄 إنشاء البيانات الشخصية المرجعية...")

    # أنواع البطاقات
    card_types_data = [
        'بطاقة شخصية',
        'جواز سفر',
        'رخصة قيادة',
        'بطاقة عسكرية',
        'بطاقة موظف'
    ]

    for card_type in card_types_data:
        if not CardType.query.filter_by(name=card_type).first():
            ct = CardType(name=card_type)
            db.session.add(ct)

    # فصائل الدم
    blood_types_data = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']

    for blood_type in blood_types_data:
        if not BloodType.query.filter_by(name=blood_type).first():
            bt = BloodType(name=blood_type)
            db.session.add(bt)

    # الحالة الاجتماعية
    marital_statuses_data = [
        'أعزب',
        'متزوج',
        'مطلق',
        'أرمل'
    ]

    for status in marital_statuses_data:
        if not MaritalStatus.query.filter_by(name=status).first():
            ms = MaritalStatus(name=status)
            db.session.add(ms)

    # أنواع المؤهلات
    qualification_types_data = [
        'ابتدائي',
        'إعدادي',
        'ثانوي',
        'دبلوم',
        'بكالوريوس',
        'ماجستير',
        'دكتوراه',
        'دورات تدريبية',
        'شهادات مهنية'
    ]

    for qualification in qualification_types_data:
        if not QualificationType.query.filter_by(name=qualification).first():
            qt = QualificationType(name=qualification)
            db.session.add(qt)

    # أنواع المشاركين
    participant_types_data = [
        'ضابط',
        'ضابط صف',
        'جندي',
        'موظف مدني',
        'متدرب خارجي'
    ]

    for participant_type in participant_types_data:
        if not ParticipantType.query.filter_by(name=participant_type).first():
            pt = ParticipantType(name=participant_type)
            db.session.add(pt)

    db.session.commit()
    print("✅ تم إنشاء البيانات الشخصية المرجعية")

def generate_arabic_names():
    """توليد أسماء عربية متنوعة"""
    first_names = [
        'محمد', 'أحمد', 'علي', 'حسن', 'حسين', 'عبدالله', 'عبدالرحمن', 'عبدالعزيز',
        'خالد', 'سعد', 'فهد', 'عبدالمجيد', 'عبدالكريم', 'عبدالرحيم', 'عبدالحميد',
        'يوسف', 'إبراهيم', 'إسماعيل', 'موسى', 'عيسى', 'داود', 'سليمان', 'يعقوب',
        'زكريا', 'يحيى', 'عمر', 'عثمان', 'طلحة', 'الزبير', 'سعيد', 'أبو بكر',
        'بلال', 'عمار', 'حمزة', 'جعفر', 'زيد', 'أسامة', 'معاذ', 'أنس',
        'عبدالوهاب', 'عبدالصمد', 'عبدالقادر', 'عبدالناصر', 'عبدالسلام', 'عبدالحكيم',
        'نور الدين', 'صلاح الدين', 'سيف الدين', 'عماد الدين', 'شمس الدين',
        'فيصل', 'نايف', 'بندر', 'تركي', 'سلطان', 'مشعل', 'متعب', 'عبدالإله'
    ]

    family_names = [
        'الأحمد', 'المحمد', 'العلي', 'الحسن', 'الحسين', 'السعد', 'الخالد',
        'الفهد', 'العبدالله', 'الإبراهيم', 'اليوسف', 'الموسى', 'العيسى',
        'الداود', 'السليمان', 'اليعقوب', 'الزكريا', 'اليحيى', 'العمر',
        'العثمان', 'الطلحة', 'الزبير', 'السعيد', 'البكر', 'البلال',
        'العمار', 'الحمزة', 'الجعفر', 'الزيد', 'الأسامة', 'المعاذ',
        'الأنس', 'الوهاب', 'الصمد', 'القادر', 'الناصر', 'السلام',
        'الحكيم', 'النور', 'الصلاح', 'السيف', 'العماد', 'الشمس',
        'الفيصل', 'النايف', 'البندر', 'التركي', 'السلطان', 'المشعل',
        'المتعب', 'الإله', 'الرحمن', 'الرحيم', 'الكريم', 'المجيد',
        'الحميد', 'العزيز', 'الغني', 'الحليم', 'الصبور', 'الشكور'
    ]

    return first_names, family_names

def create_persons(count=1000):
    """إنشاء أشخاص بأسماء عربية متنوعة"""
    print(f"🔄 إنشاء {count} شخص...")

    first_names, family_names = generate_arabic_names()

    # جلب البيانات المرجعية
    governorates = Governorate.query.all()
    directorates = Directorate.query.all()
    villages = Village.query.all()
    blood_types = BloodType.query.all()
    marital_statuses = MaritalStatus.query.all()
    qualification_types = QualificationType.query.all()

    created_count = 0
    attempts = 0
    max_attempts = count * 3

    while created_count < count and attempts < max_attempts:
        attempts += 1

        # توليد اسم فريد
        first_name = random.choice(first_names)
        father_name = random.choice(first_names)
        grandfather_name = random.choice(first_names)
        family_name = random.choice(family_names)

        full_name = f"{first_name} {father_name} {grandfather_name} {family_name}"

        # التحقق من عدم وجود الاسم
        if PersonData.query.filter_by(full_name=full_name).first():
            continue

        # اختيار بيانات عشوائية
        governorate = random.choice(governorates) if governorates else None
        directorate = random.choice([d for d in directorates if d.governorate_id == governorate.id]) if governorate and directorates else None
        village = random.choice([v for v in villages if v.directorate_id == directorate.id]) if directorate and villages else None

        person = PersonData(
            full_name=full_name,
            nickname=first_name,
            age=random.randint(18, 60),
            governorate=governorate.name if governorate else None,
            directorate=directorate.name if directorate else None,
            village=village.name if village else None,
            qualification=random.choice(qualification_types).name if qualification_types else None,
            marital_status=random.choice(marital_statuses).name if marital_statuses else None,
            blood_type=random.choice(blood_types).name if blood_types else None,
            phone=f"77{random.randint(1000000, 9999999)}",
            email=f"{first_name.lower()}.{family_name.lower().replace('ال', '')}@example.com",
            national_id=f"{random.randint(100000000, 999999999)}",
            military_number=f"M{random.randint(100000, 999999)}",
            created_at=datetime.now()
        )

        try:
            db.session.add(person)
            db.session.flush()
            created_count += 1

            if created_count % 100 == 0:
                print(f"   ✅ تم إنشاء {created_count} شخص...")
                db.session.commit()
        except Exception as e:
            db.session.rollback()
            continue

    db.session.commit()
    print(f"✅ تم إنشاء {created_count} شخص بنجاح")

def create_courses(count=200):
    """إنشاء دورات متنوعة"""
    print(f"🔄 إنشاء {count} دورة...")

    # أسماء الدورات
    course_titles = [
        'دورة القيادة والإدارة',
        'دورة التخطيط الاستراتيجي',
        'دورة إدارة المشاريع',
        'دورة الأمن السيبراني',
        'دورة مكافحة الإرهاب',
        'دورة التدريب العسكري المتقدم',
        'دورة الطب العسكري',
        'دورة الهندسة العسكرية',
        'دورة الطيران العسكري',
        'دورة البحرية العسكرية',
        'دورة الاستخبارات العسكرية',
        'دورة العمليات الخاصة',
        'دورة حفظ السلام',
        'دورة إدارة الأزمات',
        'دورة التفاوض',
        'دورة اللغة الإنجليزية',
        'دورة اللغة الفرنسية',
        'دورة الحاسوب المتقدم',
        'دورة الشبكات',
        'دورة البرمجة',
        'دورة قواعد البيانات',
        'دورة الذكاء الاصطناعي',
        'دورة المحاسبة',
        'دورة المالية',
        'دورة القانون العسكري',
        'دورة حقوق الإنسان',
        'دورة الإسعافات الأولية',
        'دورة السلامة المهنية',
        'دورة إدارة الموارد البشرية',
        'دورة التسويق',
        'دورة العلاقات العامة',
        'دورة الإعلام العسكري',
        'دورة التصوير',
        'دورة المونتاج',
        'دورة التصميم الجرافيكي'
    ]

    # جلب البيانات المرجعية
    agencies = Agency.query.all()
    training_centers = TrainingCenter.query.all()
    categories = CourseCategory.query.all()
    levels = CourseLevel.query.all()
    paths = CoursePath.query.all()
    path_levels = CoursePathLevel.query.all()

    created_count = 0

    for i in range(count):
        # توليد رقم دورة فريد
        course_number = f"C{datetime.now().year}{random.randint(1000, 9999)}"

        # التحقق من عدم وجود الرقم
        if Course.query.filter_by(course_number=course_number).first():
            course_number = f"C{datetime.now().year}{random.randint(10000, 99999)}"

        # اختيار عنوان عشوائي
        title = random.choice(course_titles)
        if random.choice([True, False]):
            title += f" - المستوى {random.choice(['الأول', 'الثاني', 'الثالث', 'المتقدم'])}"

        # تواريخ عشوائية
        start_date = fake.date_between(start_date='-2y', end_date='+1y')
        duration_days = random.randint(5, 90)
        end_date = start_date + timedelta(days=duration_days)

        course = Course(
            course_number=course_number,
            agency_course_number=f"AG{random.randint(100, 999)}",
            place_course_number=f"PL{random.randint(100, 999)}",
            title=title,
            description=f"وصف تفصيلي لدورة {title}. تهدف هذه الدورة إلى تطوير مهارات المشاركين في مجال التخصص.",
            category=random.choice(categories).name if categories else 'دورات عامة',
            level=random.choice(levels).name if levels else 'متوسط',
            start_date=start_date,
            end_date=end_date,
            duration_days=duration_days,
            agency=random.choice(agencies).name if agencies else 'وزارة الدفاع',
            center_name=random.choice(training_centers).name if training_centers else 'مركز التدريب الرئيسي',
            course_path=random.choice(paths).name if paths else 'المسار العام',
            course_path_level=random.choice(path_levels).name if path_levels else 'المستوى الأول',
            max_participants=random.randint(20, 100),
            total_participants=random.randint(15, 95),
            total_graduates=random.randint(10, 90),
            total_dropouts=random.randint(0, 10),
            daily_allowance=random.uniform(50, 200),
            transportation_allowance=random.uniform(20, 100),
            accommodation_allowance=random.uniform(100, 300),
            status='مكتملة' if start_date < datetime.now().date() else 'قادمة',
            created_at=datetime.now()
        )

        try:
            db.session.add(course)
            db.session.flush()
            created_count += 1

            if created_count % 50 == 0:
                print(f"   ✅ تم إنشاء {created_count} دورة...")
                db.session.commit()
        except Exception as e:
            db.session.rollback()
            print(f"   ❌ خطأ في إنشاء الدورة: {e}")
            continue

    db.session.commit()
    print(f"✅ تم إنشاء {created_count} دورة بنجاح")

def create_enrollments():
    """إنشاء تسجيلات للمشاركين في الدورات"""
    print("🔄 إنشاء تسجيلات المشاركين...")

    # جلب جميع الدورات والأشخاص
    courses = Course.query.all()
    persons = PersonData.query.all()

    if not courses or not persons:
        print("❌ لا توجد دورات أو أشخاص لإنشاء التسجيلات")
        return

    created_count = 0

    for course in courses:
        # تحديد عدد المشاركين للدورة (بين 10 و 50)
        num_participants = random.randint(10, min(50, len(persons)))

        # اختيار مشاركين عشوائيين
        selected_persons = random.sample(persons, num_participants)

        for person in selected_persons:
            # التحقق من عدم وجود تسجيل مسبق
            existing = Enrollment.query.filter_by(
                course_id=course.id,
                person_data_id=person.id
            ).first()

            if existing:
                continue

            enrollment = Enrollment(
                course_id=course.id,
                person_data_id=person.id,
                enrollment_date=course.start_date - timedelta(days=random.randint(1, 30)),
                status=random.choice(['مسجل', 'مكتمل', 'منسحب', 'غائب']),
                grade=random.uniform(60, 100) if random.choice([True, False]) else None,
                attendance_percentage=random.uniform(70, 100),
                notes=f"ملاحظات حول أداء {person.full_name} في الدورة",
                created_at=datetime.now()
            )

            try:
                db.session.add(enrollment)
                created_count += 1
            except Exception as e:
                db.session.rollback()
                continue

        if created_count % 100 == 0:
            print(f"   ✅ تم إنشاء {created_count} تسجيل...")
            db.session.commit()

    db.session.commit()
    print(f"✅ تم إنشاء {created_count} تسجيل بنجاح")

def main():
    """الوظيفة الرئيسية لإنشاء جميع البيانات"""
    print("=" * 80)
    print("🚀 بدء إنشاء البيانات الشاملة للنظام")
    print("=" * 80)

    with app.app_context():
        try:
            # إنشاء الجداول إذا لم تكن موجودة
            db.create_all()

            # إنشاء البيانات المرجعية
            create_reference_data()
            create_geographic_data()
            create_military_data()
            create_course_reference_data()
            create_personal_data()

            # إنشاء الأشخاص والدورات
            create_persons(1000)
            create_courses(200)

            # إنشاء التسجيلات
            create_enrollments()

            print("\n" + "=" * 80)
            print("🎉 تم إنشاء جميع البيانات بنجاح!")
            print("=" * 80)

            # إحصائيات نهائية
            print("📊 الإحصائيات النهائية:")
            print(f"   👥 الأشخاص: {PersonData.query.count()}")
            print(f"   📚 الدورات: {Course.query.count()}")
            print(f"   📝 التسجيلات: {Enrollment.query.count()}")
            print(f"   🏢 الجهات: {Agency.query.count()}")
            print(f"   🏫 مراكز التدريب: {TrainingCenter.query.count()}")
            print(f"   🌍 المحافظات: {Governorate.query.count()}")
            print(f"   📍 المديريات: {Directorate.query.count()}")
            print(f"   🏘️ القرى: {Village.query.count()}")

            print("\n💡 يمكنك الآن:")
            print("   • تشغيل النظام ومشاهدة التقارير")
            print("   • استكشاف البيانات في الجداول المختلفة")
            print("   • إنشاء تقارير متقدمة")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    main()
