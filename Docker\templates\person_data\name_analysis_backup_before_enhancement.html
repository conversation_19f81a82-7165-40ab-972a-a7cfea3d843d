{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<!-- الخط العربي المحسن -->
<link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
<style>
    /* تطبيق الخط العربي المحسن */
    body, h1, h2, h3, h4, h5, h6, p, span, div, label, button, input, select, textarea {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
    }

    /* خلفية متدرجة أزرق إبداعية للصفحة */
    body {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
        min-height: 100vh;
        position: relative;
        line-height: 1.6;
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(66, 165, 245, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    /* تصميم حديث للبطاقات */
    .analysis-card {
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: none;
        overflow: hidden;
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        animation: fadeInUp 0.6s ease-out;
    }

    .analysis-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    }

    .card-header-custom {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 25px;
        font-weight: 600;
        font-size: 1.1rem;
        position: relative;
        overflow: hidden;
    }

    .card-header-custom::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .analysis-card:hover .card-header-custom::before {
        left: 100%;
    }

    /* منطقة الرفع المحسنة */
    .upload-area {
        border: 3px dashed rgba(25, 118, 210, 0.3);
        border-radius: 20px;
        padding: 50px;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        background: linear-gradient(135deg, rgba(227, 242, 253, 0.8) 0%, rgba(187, 222, 251, 0.8) 100%);
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
    }

    .upload-area::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
        opacity: 0;
        transition: opacity 0.3s;
    }

    .upload-area:hover::before {
        opacity: 1;
    }

    .upload-area:hover {
        border-color: #1976d2;
        background: linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(66, 165, 245, 0.1) 100%);
        transform: scale(1.02);
        box-shadow: 0 15px 35px rgba(25, 118, 210, 0.2);
    }

    .feature-icon {
        font-size: 3rem;
        color: #007bff;
        margin-bottom: 1rem;
    }

    .feature-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .btn-analyze {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        padding: 12px 30px;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 25px;
        transition: all 0.3s ease;
    }

    .btn-analyze:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0,123,255,0.3);
    }

    .info-badge {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        margin: 5px;
        display: inline-block;
    }

    /* تصميم الخطوات الصغيرة */
    .step-guide {
        display: flex;
        align-items: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 3px solid #28a745;
        transition: all 0.3s ease;
    }

    .step-guide:hover {
        background: #e8f5e9;
        transform: translateX(3px);
    }

    .step-number-small {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 12px;
        margin-left: 10px;
        flex-shrink: 0;
    }

    .step-text {
        flex: 1;
        font-size: 14px;
    }

    /* أزرار نوع التحليل المحسنة */
    .analysis-type-btn {
        min-width: 200px;
        border-radius: 15px;
        padding: 18px 25px;
        margin: 8px;
        font-weight: 600;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 2px solid rgba(25, 118, 210, 0.2);
        background: rgba(255, 255, 255, 0.9);
        color: #1976d2;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 1px;
        backdrop-filter: blur(10px);
    }

    .analysis-type-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .analysis-type-btn:hover::before {
        left: 100%;
    }

    .analysis-type-btn:hover {
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 15px 30px rgba(25, 118, 210, 0.3);
        border-color: #1976d2;
    }

    .analysis-type-btn.active {
        background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
        color: white;
        border-color: #1976d2;
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 15px 30px rgba(25, 118, 210, 0.4);
    }

    .analysis-type-btn.active[data-type="course"] {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border-color: #007bff;
    }

    .analysis-type-btn.active[data-type="data"] {
        background: linear-gradient(45deg, #28a745, #1e7e34);
        border-color: #28a745;
    }

    .analysis-type-btn.active[data-type="evaluation"] {
        background: linear-gradient(45deg, #ffc107, #d39e00);
        border-color: #ffc107;
        color: #212529;
    }

    .btn-group-toggle {
        justify-content: center;
    }

    /* تحسين العنوان الرئيسي */
    .page-title {
        background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        font-weight: 800;
        font-size: 2.5rem;
        margin-bottom: 30px;
        text-align: center;
        position: relative;
        filter: drop-shadow(0 2px 4px rgba(25, 118, 210, 0.3));
        letter-spacing: 1px;
    }

    .page-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
        border-radius: 2px;
        box-shadow: 0 2px 8px rgba(25, 118, 210, 0.4);
    }

    /* تحسين البطاقات */
    .feature-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
        animation: fadeInUp 0.6s ease-out;
    }

    .feature-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    }

    /* تحسين الأزرار */
    .btn-analyze {
        background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
        border: none;
        padding: 15px 30px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 15px;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .btn-analyze::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-analyze:hover::before {
        left: 100%;
    }

    .btn-analyze:hover {
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 15px 30px rgba(25, 118, 210, 0.4);
    }

    /* تأثيرات الحركة */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    .analysis-card:nth-child(2) { animation-delay: 0.1s; }
    .analysis-card:nth-child(3) { animation-delay: 0.2s; }
    .analysis-card:nth-child(4) { animation-delay: 0.3s; }

    /* تحسين النماذج */
    .form-control, .form-select {
        border-radius: 12px;
        border: 2px solid rgba(25, 118, 210, 0.2);
        padding: 12px 20px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
    }

    .form-control:focus, .form-select:focus {
        border-color: #1976d2;
        box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
        background: rgba(255, 255, 255, 1);
    }

    .form-label {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 10px;
        font-size: 1.1rem;
    }

    /* تحسين العناوين */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        font-weight: 700;
        line-height: 1.4;
    }

    /* تحسين النصوص */
    p, span, div, label, button {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        line-height: 1.6;
    }

    /* تحسين الأزرار */
    .btn, .analysis-type-btn, .btn-analyze {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    /* تحسين النماذج */
    .form-control, .form-select, input, textarea, select {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        font-size: 1rem;
        line-height: 1.5;
    }

    /* تحسين الحاوي الرئيسي */
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 30px;
        margin-top: 20px;
        box-shadow: 0 20px 40px rgba(25, 118, 210, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        position: relative;
        overflow: hidden;
    }

    .main-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, #1976d2, #42a5f5, #29b6f6, #4fc3f7);
        background-size: 200% 100%;
        animation: shimmer 3s ease-in-out infinite;
    }

    @media (max-width: 768px) {
        .analysis-type-btn {
            min-width: 100%;
            margin: 5px 0;
        }

        .page-title {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="main-container">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="page-title">
                        <i class="fas fa-search-plus"></i> تحليل ومقايسة الأسماء الذكي
                    </h1>
                    <p class="lead text-muted">
                        نظام ذكي متطور لتحليل وتصحيح الأسماء العربية ومقايستها مع قاعدة البيانات
                    </p>
                </div>

                <!-- Main Analysis Card -->
                <div class="card analysis-card mb-5">
                    <div class="card-header card-header-custom text-center">
                        <h3 class="mb-0">
                            <i class="fas fa-file-excel"></i> رفع ملف Excel للتحليل الذكي
                        </h3>
                    </div>
                <div class="card-body">
                    <!-- أزرار نوع الفحص -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h5 class="mb-3 text-center">
                                <i class="fas fa-cogs"></i>
                                نوع الفحص المطلوب:
                            </h5>
                            <div class="btn-group-toggle d-flex flex-wrap justify-content-center gap-3" data-toggle="buttons">
                                <label class="btn btn-outline-primary btn-lg analysis-type-btn active" data-type="course">
                                    <input type="radio" name="analysis_type_radio" value="course" checked>
                                    <i class="fas fa-graduation-cap"></i>
                                    فحص كشف دورة
                                </label>
                                <label class="btn btn-outline-success btn-lg analysis-type-btn" data-type="data">
                                    <input type="radio" name="analysis_type_radio" value="data">
                                    <i class="fas fa-database"></i>
                                    فحص كشف بيانات
                                </label>
                                <label class="btn btn-outline-warning btn-lg analysis-type-btn" data-type="evaluation">
                                    <input type="radio" name="analysis_type_radio" value="evaluation">
                                    <i class="fas fa-star"></i>
                                    فحص كشف تقييمات دورة
                                </label>
                            </div>
                        </div>
                    </div>

                    <form action="{{ url_for('person_data.name_analysis') }}" method="post" enctype="multipart/form-data" id="analysisForm">
                        {% if csrf_token %}
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        {% endif %}

                        <!-- حقل مخفي لنوع التحليل -->
                        <input type="hidden" name="analysis_type" id="analysis_type_input" value="course">

                        <!-- اختيار الدورة -->
                        <div class="row mb-4">
                            <div class="col-md-6 mx-auto">
                                <div class="card border-info" id="course-selection-card">
                                    <div class="card-header bg-info text-white text-center">
                                        <h5 class="mb-0">
                                            <i class="fas fa-graduation-cap"></i>
                                            <span id="course-card-title">اختيار الدورة (مطلوب)</span>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="course_id" class="form-label">
                                                <i class="fas fa-list"></i>
                                                <span id="course-label">اختر دورة لفحص كشف الدورة:</span>
                                            </label>
                                            <select class="form-select" name="course_id" id="course_id" required>
                                                <option value="">-- اختر دورة --</option>
                                                {% for course in courses %}
                                                <option value="{{ course.id }}">
                                                    {{ course.course_number }} - {{ course.title }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                            <div class="form-text text-muted" id="course-help-text">
                                                <i class="fas fa-info-circle"></i>
                                                <span id="course-instruction">يجب اختيار دورة لفحص كشف الدورة</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="upload-area mb-4">
                            <div class="feature-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h4 class="text-primary mb-3">اختر ملف Excel للتحليل</h4>
                            <input type="file" class="form-control form-control-lg" name="excel_file"
                                   accept=".xlsx,.xls" required style="max-width: 400px; margin: 0 auto;">
                            <p class="text-muted mt-3">
                                <i class="fas fa-info-circle"></i>
                                يجب أن يحتوي الملف على عمود "الاسم الشخصي"
                            </p>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-analyze" id="analyzeBtn">
                                <i class="fas fa-analytics"></i>
                                <span id="submit-text">بدء تحليل كشف الدورة</span>
                            </button>
                            <a href="{{ url_for('person_data.export_corrections_guide') }}"
                               class="btn btn-success btn-lg ms-3"
                               title="تحميل دليل شامل لجميع التصحيحات المتاحة">
                                <i class="fas fa-book-open"></i> تحميل دليل التصحيحات
                            </a>
                            <a href="{{ url_for('person_data.manage_corrections') }}"
                               class="btn btn-warning btn-lg ms-3"
                               title="إدارة التصحيحات المخصصة">
                                <i class="fas fa-cogs"></i> إدارة التصحيحات
                            </a>

                        </div>

                        <!-- مؤشر التقدم -->
                        <div class="progress mt-3 d-none" id="analysisProgress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                                 role="progressbar" style="width: 0%" id="progressBar">
                                <span id="progressText">0%</span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            </div> <!-- إغلاق main-container -->
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تعريف إعدادات كل نوع فحص
        const analysisTypes = {
            course: {
                cardTitle: 'اختيار الدورة (مطلوب)',
                label: 'اختر دورة لفحص كشف الدورة:',
                submitText: 'بدء تحليل كشف الدورة',
                required: true,
                instruction: 'يجب اختيار دورة لفحص كشف الدورة',
                placeholder: '-- اختر دورة --'
            },
            data: {
                cardTitle: 'اختيار الدورة (اختياري)',
                label: 'اختر دورة لإضافة الأسماء الجديدة إليها (اختياري):',
                submitText: 'بدء تحليل البيانات الشخصية',
                required: false,
                instruction: 'اختيار الدورة اختياري لفحص البيانات العامة',
                placeholder: '-- لا تضيف لأي دورة (اختياري) --'
            },
            evaluation: {
                cardTitle: 'اختيار الدورة (مطلوب)',
                label: 'اختر دورة لفحص كشف تقييمات الدورة:',
                submitText: 'بدء تحليل كشف التقييمات',
                required: true,
                instruction: 'يجب اختيار دورة لفحص كشف تقييمات الدورة',
                placeholder: '-- اختر دورة --'
            }
        };

        // دالة تحديث الواجهة حسب نوع الفحص
        function updateInterface(type) {
            const config = analysisTypes[type];

            // تحديث عنوان بطاقة الدورة
            $('#course-card-title').text(config.cardTitle);

            // تحديث تسمية اختيار الدورة
            $('#course-label').text(config.label);

            // تحديث نص زر الإرسال
            $('#submit-text').text(config.submitText);

            // تحديث حالة اختيار الدورة (مطلوب أم لا)
            $('#course_id').prop('required', config.required);

            // تحديث النص التوضيحي
            $('#course-instruction').text(config.instruction);

            // تحديث placeholder
            $('#course_id option:first').text(config.placeholder);

            // إذا لم يكن مطلوب، امسح الاختيار
            if (!config.required) {
                $('#course_id').val('');
            }

            // تحديث قيمة نوع التحليل المخفية
            $('#analysis_type_input').val(type);

            // تحديث لون بطاقة الدورة
            const cardHeader = $('#course-selection-card .card-header');
            cardHeader.removeClass('bg-info bg-success bg-warning');

            if (type === 'course') {
                cardHeader.addClass('bg-info');
            } else if (type === 'data') {
                cardHeader.addClass('bg-success');
            } else if (type === 'evaluation') {
                cardHeader.addClass('bg-warning text-dark');
            }
        }

        // إضافة مستمعي الأحداث لأزرار نوع الفحص
        $('.analysis-type-btn').on('click', function() {
            // إزالة الفئة النشطة من جميع الأزرار
            $('.analysis-type-btn').removeClass('active');

            // إضافة الفئة النشطة للزر المحدد
            $(this).addClass('active');

            // تحديث الواجهة
            const type = $(this).data('type');
            updateInterface(type);
        });

        // تحديث الواجهة عند التحميل (الافتراضي: فحص دورة)
        updateInterface('course');

        // تحسين تجربة رفع الملف
        $('input[type="file"]').change(function() {
            var fileName = $(this).val().split('\\').pop();
            if (fileName) {
                $('.upload-area h4').text('تم اختيار: ' + fileName);
                $('.upload-area').addClass('border-success').removeClass('border-primary');
                $('.feature-icon i').removeClass('fa-cloud-upload-alt').addClass('fa-check-circle text-success');
            }
        });

        // تأثيرات بصرية للنموذج
        $('#analysisForm').on('submit', function(e) {
            console.log('🔥 تم إرسال النموذج');

            // التحقق من وجود ملف
            var fileInput = $('input[name="excel_file"]')[0];
            if (!fileInput.files || fileInput.files.length === 0) {
                alert('يرجى اختيار ملف Excel أولاً');
                e.preventDefault();
                return false;
            }

            // التحقق من اختيار الدورة إذا كان مطلوب
            const analysisType = $('#analysis_type_input').val();
            const courseRequired = analysisTypes[analysisType].required;
            const courseSelected = $('#course_id').val();

            if (courseRequired && !courseSelected) {
                alert('يرجى اختيار دورة أولاً');
                e.preventDefault();
                return false;
            }

            console.log('📁 الملف المختار:', fileInput.files[0].name);
            console.log('🎯 نوع التحليل:', analysisType);
            console.log('🎓 الدورة المختارة:', courseSelected || 'لا توجد');

            $('.btn-analyze').html('<i class="fas fa-spinner fa-spin"></i> جاري التحليل...');
            $('.btn-analyze').prop('disabled', true);

            // إظهار مؤشر التقدم
            $('#analysisProgress').removeClass('d-none');

            // محاكاة تقدم التحليل
            var progress = 0;
            var interval = setInterval(function() {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;

                $('#progressBar').css('width', progress + '%');
                $('#progressText').text(Math.round(progress) + '%');

                if (progress >= 90) {
                    clearInterval(interval);
                    $('#progressText').text('جاري إنهاء التحليل...');
                }
            }, 500);
        });
    });
</script>
{% endblock %}
