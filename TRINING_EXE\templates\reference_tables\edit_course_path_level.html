{% extends "layout.html" %}
{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">تعديل مستوى مسار تدريبي</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ request.path }}{% if request.args.get('redirect_to') %}?redirect_to={{ request.args.get('redirect_to') }}{% endif %}">
                {{ form.hidden_tag() }}
                {% if redirect_to %}
                <input type="hidden" name="redirect_to" value="{{ redirect_to }}">
                {% endif %}
                <div class="form-group">
                    {{ form.path_id.label(class="form-control-label") }}
                    {% if form.path_id.errors %}
                        {{ form.path_id(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.path_id.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.path_id(class="form-control") }}
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.name.label(class="form-control-label") }}
                    {% if form.name.errors %}
                        {{ form.name(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.name.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.name(class="form-control") }}
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.description.label(class="form-control-label") }}
                    {% if form.description.errors %}
                        {{ form.description(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.description(class="form-control") }}
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.code.label(class="form-control-label") }}
                    {% if form.code.errors %}
                        {{ form.code(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.code.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.code(class="form-control") }}
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.order.label(class="form-control-label") }}
                    {% if form.order.errors %}
                        {{ form.order(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.order.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.order(class="form-control") }}
                    {% endif %}
                </div>
                <div class="form-group mt-4">
                    {{ form.submit(class="btn btn-primary") }}
                    {% if redirect_to == 'tree' %}
                    <a href="{{ url_for('course_paths_tree') }}" class="btn btn-secondary">إلغاء</a>
                    {% else %}
                    <a href="{{ url_for('course_path_levels') }}" class="btn btn-secondary">إلغاء</a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
