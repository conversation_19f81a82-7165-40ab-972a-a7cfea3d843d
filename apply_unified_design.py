#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تطبيق التصميم الموحد على جميع صفحات النظام
"""

import os
import re
from pathlib import Path

def get_all_template_files():
    """الحصول على جميع ملفات القوالب"""
    templates_dir = Path('templates')
    template_files = []
    
    for file_path in templates_dir.rglob('*.html'):
        if file_path.name != 'layout.html':  # تجاهل الملف الأساسي
            template_files.append(file_path)
    
    return template_files

def analyze_template_structure(file_path):
    """تحليل هيكل القالب"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    analysis = {
        'file_path': file_path,
        'extends_layout': '{% extends "layout.html" %}' in content,
        'has_title_block': '{% block title %}' in content,
        'has_styles_block': '{% block styles %}' in content,
        'has_content_block': '{% block content %}' in content,
        'has_scripts_block': '{% block scripts %}' in content,
        'uses_bootstrap_classes': bool(re.search(r'class="[^"]*(?:btn|card|container|row|col)', content)),
        'uses_fontawesome': 'fas fa-' in content or 'far fa-' in content,
        'has_custom_css': 'style=' in content,
        'content_preview': content[:200] + '...' if len(content) > 200 else content
    }
    
    return analysis

def suggest_improvements(analysis):
    """اقتراح تحسينات للقالب"""
    suggestions = []
    
    if not analysis['extends_layout']:
        suggestions.append("❌ لا يمتد من layout.html - يجب إضافة {% extends \"layout.html\" %}")
    
    if not analysis['has_title_block']:
        suggestions.append("⚠️ لا يحتوي على block title - يُنصح بإضافته")
    
    if not analysis['has_styles_block']:
        suggestions.append("💡 يمكن إضافة block styles للتصميم المخصص")
    
    if not analysis['has_content_block']:
        suggestions.append("❌ لا يحتوي على block content - مطلوب")
    
    if not analysis['has_scripts_block']:
        suggestions.append("💡 يمكن إضافة block scripts للسكريبت المخصص")
    
    if analysis['has_custom_css']:
        suggestions.append("🔧 يحتوي على CSS مخصص - يُنصح بنقله لملف منفصل")
    
    if analysis['uses_bootstrap_classes']:
        suggestions.append("✅ يستخدم Bootstrap classes")
    
    if analysis['uses_fontawesome']:
        suggestions.append("✅ يستخدم Font Awesome icons")
    
    return suggestions

def create_unified_template_structure(file_path, analysis):
    """إنشاء هيكل موحد للقالب"""
    
    # قراءة المحتوى الحالي
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # استخراج العنوان من المحتوى
    title_match = re.search(r'<h[1-6][^>]*>([^<]+)</h[1-6]>', content)
    page_title = title_match.group(1).strip() if title_match else file_path.stem.replace('_', ' ')
    
    # إنشاء هيكل موحد
    unified_structure = f'''{{% extends "layout.html" %}}

{{% block title %}}{page_title}{{% endblock %}}

{{% block styles %}}
<link rel="stylesheet" href="{{{{ url_for('static', filename='css/admin-pages.css') }}}}">
<!-- إضافة أي CSS مخصص هنا -->
{{% endblock %}}

{{% block content %}}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title">
            <i class="fas fa-file-alt icon-lg"></i>
            {page_title}
        </div>
        <div class="page-subtitle">
            وصف الصفحة هنا
        </div>
    </div>

    <div class="content-wrapper">
        <!-- محتوى الصفحة هنا -->
        <!-- {content} -->
    </div>
</div>
{{% endblock %}}

{{% block scripts %}}
<!-- إضافة أي JavaScript مخصص هنا -->
{{% endblock %}}
'''
    
    return unified_structure

def apply_unified_design():
    """تطبيق التصميم الموحد"""
    
    print("🎨 تطبيق التصميم الموحد على جميع صفحات النظام")
    print("=" * 60)
    
    # الحصول على جميع ملفات القوالب
    template_files = get_all_template_files()
    
    print(f"📁 تم العثور على {len(template_files)} ملف قالب")
    
    # تحليل كل ملف
    analyses = []
    for file_path in template_files:
        analysis = analyze_template_structure(file_path)
        analyses.append(analysis)
    
    # عرض التحليل
    print("\n📊 تحليل الملفات:")
    print("-" * 60)
    
    for analysis in analyses:
        print(f"\n📄 {analysis['file_path']}")
        print(f"   📝 يمتد من layout: {'✅' if analysis['extends_layout'] else '❌'}")
        print(f"   🏷️ له عنوان: {'✅' if analysis['has_title_block'] else '❌'}")
        print(f"   🎨 له تصميم: {'✅' if analysis['has_styles_block'] else '❌'}")
        print(f"   📄 له محتوى: {'✅' if analysis['has_content_block'] else '❌'}")
        print(f"   📜 له سكريبت: {'✅' if analysis['has_scripts_block'] else '❌'}")
        print(f"   🅱️ يستخدم Bootstrap: {'✅' if analysis['uses_bootstrap_classes'] else '❌'}")
        print(f"   🔤 يستخدم FontAwesome: {'✅' if analysis['uses_fontawesome'] else '❌'}")
        
        # عرض الاقتراحات
        suggestions = suggest_improvements(analysis)
        if suggestions:
            print("   💡 الاقتراحات:")
            for suggestion in suggestions:
                print(f"      {suggestion}")
    
    # إحصائيات عامة
    print(f"\n📈 الإحصائيات العامة:")
    total_files = len(analyses)
    extends_layout = sum(1 for a in analyses if a['extends_layout'])
    has_title = sum(1 for a in analyses if a['has_title_block'])
    has_styles = sum(1 for a in analyses if a['has_styles_block'])
    has_content = sum(1 for a in analyses if a['has_content_block'])
    has_scripts = sum(1 for a in analyses if a['has_scripts_block'])
    uses_bootstrap = sum(1 for a in analyses if a['uses_bootstrap_classes'])
    uses_fontawesome = sum(1 for a in analyses if a['uses_fontawesome'])
    
    print(f"   📁 إجمالي الملفات: {total_files}")
    print(f"   🔗 يمتد من layout: {extends_layout}/{total_files} ({extends_layout/total_files*100:.1f}%)")
    print(f"   🏷️ له عنوان: {has_title}/{total_files} ({has_title/total_files*100:.1f}%)")
    print(f"   🎨 له تصميم: {has_styles}/{total_files} ({has_styles/total_files*100:.1f}%)")
    print(f"   📄 له محتوى: {has_content}/{total_files} ({has_content/total_files*100:.1f}%)")
    print(f"   📜 له سكريبت: {has_scripts}/{total_files} ({has_scripts/total_files*100:.1f}%)")
    print(f"   🅱️ يستخدم Bootstrap: {uses_bootstrap}/{total_files} ({uses_bootstrap/total_files*100:.1f}%)")
    print(f"   🔤 يستخدم FontAwesome: {uses_fontawesome}/{total_files} ({uses_fontawesome/total_files*100:.1f}%)")
    
    # تحديد الملفات التي تحتاج تحسين
    files_need_improvement = []
    for analysis in analyses:
        suggestions = suggest_improvements(analysis)
        critical_issues = [s for s in suggestions if s.startswith('❌')]
        if critical_issues:
            files_need_improvement.append((analysis['file_path'], critical_issues))
    
    if files_need_improvement:
        print(f"\n⚠️ الملفات التي تحتاج تحسين عاجل ({len(files_need_improvement)} ملف):")
        for file_path, issues in files_need_improvement:
            print(f"   📄 {file_path}")
            for issue in issues:
                print(f"      {issue}")
    
    # اقتراح خطة التحسين
    print(f"\n📋 خطة التحسين المقترحة:")
    print("1. 🔧 تحديث الملفات التي لا تمتد من layout.html")
    print("2. 🎨 إضافة نظام التصميم الموحد لجميع الصفحات")
    print("3. 🏷️ توحيد العناوين والأيقونات")
    print("4. 📱 تحسين الاستجابة للشاشات المختلفة")
    print("5. ♿ تحسين إمكانية الوصول")
    print("6. 🚀 تحسين الأداء")
    
    return analyses

def create_design_report(analyses):
    """إنشاء تقرير التصميم"""
    
    report_content = """# تقرير التصميم الموحد

## نظرة عامة
تم تحليل جميع ملفات القوالب في النظام لتقييم مدى توافقها مع معايير التصميم الموحد.

## الإحصائيات العامة
"""
    
    total_files = len(analyses)
    extends_layout = sum(1 for a in analyses if a['extends_layout'])
    has_title = sum(1 for a in analyses if a['has_title_block'])
    has_styles = sum(1 for a in analyses if a['has_styles_block'])
    
    report_content += f"""
- **إجمالي الملفات:** {total_files}
- **يمتد من layout.html:** {extends_layout}/{total_files} ({extends_layout/total_files*100:.1f}%)
- **له عنوان:** {has_title}/{total_files} ({has_title/total_files*100:.1f}%)
- **له تصميم مخصص:** {has_styles}/{total_files} ({has_styles/total_files*100:.1f}%)

## تفاصيل الملفات
"""
    
    for analysis in analyses:
        report_content += f"""
### {analysis['file_path']}
- **يمتد من layout:** {'✅' if analysis['extends_layout'] else '❌'}
- **له عنوان:** {'✅' if analysis['has_title_block'] else '❌'}
- **له تصميم:** {'✅' if analysis['has_styles_block'] else '❌'}
- **يستخدم Bootstrap:** {'✅' if analysis['uses_bootstrap_classes'] else '❌'}
- **يستخدم FontAwesome:** {'✅' if analysis['uses_fontawesome'] else '❌'}

"""
        
        suggestions = suggest_improvements(analysis)
        if suggestions:
            report_content += "**الاقتراحات:**\n"
            for suggestion in suggestions:
                report_content += f"- {suggestion}\n"
            report_content += "\n"
    
    # حفظ التقرير
    with open('design_report.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("📄 تم إنشاء تقرير التصميم: design_report.md")

def main():
    """الدالة الرئيسية"""
    
    try:
        # تطبيق التصميم الموحد
        analyses = apply_unified_design()
        
        # إنشاء تقرير
        create_design_report(analyses)
        
        print(f"\n🎉 تم الانتهاء من تحليل التصميم!")
        print(f"📊 تم تحليل {len(analyses)} ملف قالب")
        print(f"📄 تم إنشاء تقرير مفصل في design_report.md")
        
        print(f"\n🔗 الخطوات التالية:")
        print("1. راجع التقرير المُنشأ")
        print("2. طبق التحسينات المقترحة")
        print("3. اختبر التصميم الموحد")
        print("4. تأكد من الاستجابة للشاشات المختلفة")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == '__main__':
    main()
