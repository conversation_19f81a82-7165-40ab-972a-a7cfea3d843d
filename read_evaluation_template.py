#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
قراءة وتحليل نموذج التقييم الأصلي
"""

import pandas as pd
import openpyxl
from openpyxl import load_workbook
import os

def read_evaluation_template():
    """قراءة نموذج التقييم الأصلي"""
    
    file_path = "نموذج تقييم.xlsx"
    
    if not os.path.exists(file_path):
        print(f"❌ الملف غير موجود: {file_path}")
        return
    
    print(f"📄 قراءة ملف: {file_path}")
    
    try:
        # قراءة باستخدام openpyxl
        print("\n🔍 قراءة باستخدام openpyxl...")
        workbook = load_workbook(file_path, data_only=True)
        
        print(f"📊 أوراق العمل الموجودة: {workbook.sheetnames}")
        
        for sheet_name in workbook.sheetnames:
            print(f"\n📋 ورقة العمل: {sheet_name}")
            worksheet = workbook[sheet_name]
            
            print(f"   - الأبعاد: {worksheet.max_row} صف × {worksheet.max_column} عمود")
            
            # قراءة أول 20 صف
            print("   - المحتوى:")
            for row_num in range(1, min(21, worksheet.max_row + 1)):
                row_data = []
                for col_num in range(1, min(11, worksheet.max_column + 1)):
                    cell = worksheet.cell(row=row_num, column=col_num)
                    if cell.value is not None:
                        row_data.append(str(cell.value))
                    else:
                        row_data.append("")
                
                if any(cell.strip() for cell in row_data):  # إذا كان الصف يحتوي على بيانات
                    print(f"     الصف {row_num}: {' | '.join(row_data)}")
        
        workbook.close()
        
    except Exception as e:
        print(f"❌ خطأ في قراءة openpyxl: {str(e)}")
    
    try:
        # قراءة باستخدام pandas
        print("\n🔍 قراءة باستخدام pandas...")
        
        # قراءة جميع الأوراق
        excel_file = pd.ExcelFile(file_path)
        print(f"📊 أوراق العمل: {excel_file.sheet_names}")
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n📋 ورقة العمل: {sheet_name}")
            
            # قراءة الورقة
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
            print(f"   - الأبعاد: {df.shape[0]} صف × {df.shape[1]} عمود")
            
            # عرض البيانات غير الفارغة
            print("   - المحتوى:")
            for index, row in df.iterrows():
                if index >= 20:  # أول 20 صف فقط
                    break
                
                # تصفية القيم غير الفارغة
                non_empty = [str(val) for val in row if pd.notna(val) and str(val).strip()]
                if non_empty:
                    print(f"     الصف {index + 1}: {' | '.join(non_empty)}")
        
    except Exception as e:
        print(f"❌ خطأ في قراءة pandas: {str(e)}")
    
    # محاولة قراءة كملف نصي
    try:
        print("\n🔍 محاولة قراءة كملف نصي...")
        with open(file_path, 'rb') as f:
            content = f.read(1000)  # أول 1000 بايت
            print(f"   - أول 1000 بايت: {content}")
            
    except Exception as e:
        print(f"❌ خطأ في القراءة النصية: {str(e)}")

def analyze_evaluation_structure():
    """تحليل هيكل التقييم المتوقع"""
    
    print("\n📝 تحليل هيكل التقييم المتوقع...")
    print("🎯 البنود المتوقعة في نموذج التقييم:")
    
    expected_items = [
        "معلومات المتدرب (الاسم، الرقم الوطني، الرقم العسكري)",
        "معلومات الدورة (اسم الدورة، رقم الدورة، التاريخ)",
        "معايير التقييم:",
        "  - الحضور والانضباط",
        "  - المشاركة والتفاعل", 
        "  - الاختبار النظري",
        "  - الاختبار العملي",
        "  - المهارات الشخصية",
        "  - التطبيق العملي",
        "  - المشروع النهائي",
        "الدرجات والنسب المئوية",
        "التقدير النهائي",
        "ملاحظات",
        "التواقيع (المدرب، رئيس القسم، المدير)"
    ]
    
    for i, item in enumerate(expected_items, 1):
        print(f"   {i}. {item}")
    
    print("\n❓ يرجى التأكد من أن النموذج يحتوي على هذه العناصر")

if __name__ == "__main__":
    read_evaluation_template()
    analyze_evaluation_structure()
