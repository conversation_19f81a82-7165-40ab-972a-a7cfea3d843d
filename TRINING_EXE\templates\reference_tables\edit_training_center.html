{% extends "layout.html" %}

{% block styles %}
<style>
    .form-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }

    .form-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: bold;
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4a6bff;
        box-shadow: 0 0 0 0.25rem rgba(74, 107, 255, 0.25);
    }

    .form-text {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .btn-submit {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
    }

    .btn-cancel {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-cancel:hover {
        transform: translateY(-2px);
    }

    .invalid-feedback {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .status-toggle {
        margin-bottom: 20px;
    }

    .form-check-input {
        width: 20px;
        height: 20px;
        margin-left: 10px;
    }

    .form-check-label {
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>تعديل مركز تدريبي</h2>
        <div>
            <a href="{{ url_for('training_centers') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى المراكز التدريبية
            </a>
        </div>
    </div>

    <div class="form-card">
        <div class="form-header">
            <h4><i class="fas fa-edit me-2"></i> تعديل بيانات المركز التدريبي</h4>
            <p class="text-muted">قم بتعديل بيانات المركز التدريبي</p>
        </div>

        <form method="POST">
            {{ form.hidden_tag() }}

            <div class="form-group">
                {{ form.name.label(class="form-label") }}
                {% if form.name.errors %}
                    {{ form.name(class="form-control is-invalid") }}
                    <div class="invalid-feedback">
                        {% for error in form.name.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.name(class="form-control") }}
                {% endif %}
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.center_type_id.label(class="form-label") }}
                        {% if form.center_type_id.errors %}
                            {{ form.center_type_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.center_type_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.center_type_id(class="form-select") }}
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.location_id.label(class="form-label") }}
                        {% if form.location_id.errors %}
                            {{ form.location_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.location_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.location_id(class="form-select") }}
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.governorate_id.label(class="form-label") }}
                        {% if form.governorate_id.errors %}
                            {{ form.governorate_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.governorate_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.governorate_id(class="form-select") }}
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.directorate_id.label(class="form-label") }}
                        {% if form.directorate_id.errors %}
                            {{ form.directorate_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.directorate_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.directorate_id(class="form-select") }}
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.agency_id.label(class="form-label") }}
                        {% if form.agency_id.errors %}
                            {{ form.agency_id(class="form-select is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.agency_id.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.agency_id(class="form-select") }}
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.capacity.label(class="form-label") }}
                        {% if form.capacity.errors %}
                            {{ form.capacity(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.capacity.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.capacity(class="form-control") }}
                        {% endif %}
                        <small class="form-text">أدخل السعة الاستيعابية للمركز التدريبي</small>
                    </div>
                </div>
            </div>

            <div class="form-group status-toggle">
                <div class="form-check">
                    {{ form.is_ready(class="form-check-input") }}
                    {{ form.is_ready.label(class="form-check-label") }}
                </div>
            </div>

            <div class="form-group not-ready-reason" style="display: none;">
                {{ form.not_ready_reason.label(class="form-label") }}
                {% if form.not_ready_reason.errors %}
                    {{ form.not_ready_reason(class="form-control is-invalid", rows=3) }}
                    <div class="invalid-feedback">
                        {% for error in form.not_ready_reason.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.not_ready_reason(class="form-control", rows=3) }}
                {% endif %}
            </div>

            <div class="form-group">
                {{ form.notes.label(class="form-label") }}
                {% if form.notes.errors %}
                    {{ form.notes(class="form-control is-invalid", rows=3) }}
                    <div class="invalid-feedback">
                        {% for error in form.notes.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.notes(class="form-control", rows=3) }}
                {% endif %}
            </div>

            <div class="d-flex justify-content-between mt-4">
                <a href="{{ url_for('training_centers') }}" class="btn btn-secondary btn-cancel">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
                {{ form.submit(class="btn btn-primary btn-submit") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // الحصول على عناصر المحافظة والمديرية
        const governorateSelect = document.getElementById('governorate_id');
        const directorateSelect = document.getElementById('directorate_id');

        // إضافة مستمع حدث لتغيير المحافظة
        governorateSelect.addEventListener('change', function() {
            const governorateId = this.value;

            // إفراغ قائمة المديريات
            directorateSelect.innerHTML = '<option value="0">اختر المديرية</option>';

            // إذا تم اختيار محافظة
            if (governorateId != 0) {
                // إرسال طلب AJAX للحصول على المديريات
                fetch(`/api/directorates/${governorateId}`)
                    .then(response => response.json())
                    .then(data => {
                        // إضافة المديريات إلى القائمة المنسدلة
                        data.forEach(directorate => {
                            const option = document.createElement('option');
                            option.value = directorate.id;
                            option.textContent = directorate.name;
                            directorateSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error fetching directorates:', error));
            }
        });

        // تشغيل حدث تغيير المحافظة عند تحميل الصفحة لتحميل المديريات الحالية
        if (governorateSelect.value != 0) {
            const event = new Event('change');
            governorateSelect.dispatchEvent(event);

            // تحديد المديرية الحالية بعد تحميل المديريات
            const currentDirectorateId = {{ center.directorate_id or 0 }};
            if (currentDirectorateId != 0) {
                setTimeout(() => {
                    directorateSelect.value = currentDirectorateId;
                }, 500);
            }
        }

        // التحكم في ظهور حقل سبب عدم الجهوزية
        const isReadyCheckbox = document.getElementById('is_ready');
        const notReadyReasonDiv = document.querySelector('.not-ready-reason');

        function toggleNotReadyReason() {
            if (isReadyCheckbox.checked) {
                notReadyReasonDiv.style.display = 'none';
            } else {
                notReadyReasonDiv.style.display = 'block';
            }
        }

        // تنفيذ الدالة عند تحميل الصفحة
        toggleNotReadyReason();

        // إضافة مستمع حدث لتغيير حالة الجهوزية
        isReadyCheckbox.addEventListener('change', toggleNotReadyReason);
    });
</script>
{% endblock %}