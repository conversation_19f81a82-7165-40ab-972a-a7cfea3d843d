{% extends "layout.html" %}

{% block title %}إدارة المستخدمين والأدوار المتقدمة{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-pages.css') }}">
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title">
            <i class="fas fa-users-cog icon-lg"></i>
            إدارة المستخدمين والأدوار المتقدمة
        </div>
        <div class="page-subtitle">
            نظام شامل لإدارة المستخدمين والأدوار مع التحكم الدقيق في الصلاحيات
        </div>
        <nav aria-label="breadcrumb" class="mt-3">
            <ol class="breadcrumb bg-transparent p-0 mb-0">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}" class="text-white">الرئيسية</a></li>
                <li class="breadcrumb-item text-white opacity-75">إدارة المستخدمين المتقدمة</li>
            </ol>
        </nav>
    </div>

    <div class="content-wrapper">

        <!-- الكروت الرئيسية -->
        <div class="row g-4">
            <!-- كارت 1: عرض شجرة النظام -->
            <div class="col-lg-4 col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <div class="card-title mb-0">
                            <i class="fas fa-sitemap icon-lg"></i>
                            عرض شجرة النظام
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-4">
                            <i class="fas fa-sitemap fa-4x text-primary opacity-75"></i>
                        </div>
                        <p class="card-text mb-4">
                            عرض شجرة النظام الكاملة مع جميع الشاشات والأزرار المتاحة للاطلاع فقط
                        </p>
                        <button class="btn btn-primary btn-lg w-100" onclick="showSystemTree()">
                            <i class="fas fa-eye"></i>
                            عرض شجرة النظام
                        </button>
                    </div>
                </div>
            </div>

            <!-- كارت 2: إدارة الأدوار -->
            <div class="col-lg-4 col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <div class="card-title mb-0">
                            <i class="fas fa-shield-alt icon-lg"></i>
                            إدارة الأدوار
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-4">
                            <i class="fas fa-shield-alt fa-4x text-success opacity-75"></i>
                        </div>
                        <p class="card-text mb-4">
                            إنشاء وتعديل الأدوار مع تحديد الصلاحيات لكل دور من خلال شجرة النظام
                        </p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="showRolesManagement()">
                                <i class="fas fa-list"></i>
                                عرض الأدوار
                            </button>
                            <button class="btn btn-outline-success" onclick="showAddRoleModal()">
                                <i class="fas fa-plus"></i>
                                إضافة دور جديد
                            </button>
                            <button class="btn btn-info" onclick="checkRolesSystem()">
                                <i class="fas fa-check-circle"></i>
                                فحص نظام الأدوار
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- كارت 3: إدارة المستخدمين -->
            <div class="col-lg-4 col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-warning text-dark">
                        <div class="card-title mb-0">
                            <i class="fas fa-users icon-lg"></i>
                            إدارة المستخدمين
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-4">
                            <i class="fas fa-users fa-4x text-warning opacity-75"></i>
                        </div>
                        <p class="card-text mb-4">
                            إضافة وتعديل المستخدمين مع تعيين الأدوار لكل مستخدم
                        </p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-warning" onclick="showUsersManagement()">
                                <i class="fas fa-list"></i>
                                عرض المستخدمين
                            </button>
                            <button class="btn btn-outline-warning" onclick="showAddUserModal()">
                                <i class="fas fa-user-plus"></i>
                                إضافة مستخدم جديد
                            </button>
                            <button class="btn btn-info" onclick="checkUsersSystem()">
                                <i class="fas fa-user-check"></i>
                                فحص نظام المستخدمين
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- منطقة المحتوى الديناميكي -->
        <div class="mt-5">
            <div class="card">
                <div class="card-body" id="dynamicContent">
                    <!-- سيتم تحميل المحتوى هنا ديناميكياً -->
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-mouse-pointer fa-4x text-muted opacity-50"></i>
                        </div>
                        <h4 class="text-muted mb-3">اختر أحد الخيارات أعلاه للبدء</h4>
                        <p class="text-muted">يمكنك عرض شجرة النظام أو إدارة الأدوار أو إدارة المستخدمين</p>
                        <div class="mt-4">
                            <div class="row g-3 justify-content-center">
                                <div class="col-auto">
                                    <span class="badge badge-primary">🌳 شجرة النظام</span>
                                </div>
                                <div class="col-auto">
                                    <span class="badge badge-success">🛡️ إدارة الأدوار</span>
                                </div>
                                <div class="col-auto">
                                    <span class="badge badge-warning">👥 إدارة المستخدمين</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<!-- Modal: عرض شجرة النظام -->
<div class="modal fade" id="systemTreeModal" tabindex="-1" aria-labelledby="systemTreeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="systemTreeModalLabel">
                    <i class="fas fa-sitemap"></i>
                    شجرة النظام الكاملة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="systemTreeContent">
                    <!-- سيتم تحميل شجرة النظام هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal: إضافة دور جديد -->
<div class="modal fade" id="addRoleModal" tabindex="-1" aria-labelledby="addRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="addRoleModalLabel">
                    <i class="fas fa-plus"></i>
                    إضافة دور جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addRoleForm">
                    <!-- معلومات الدور -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="roleName" class="form-label">اسم الدور <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="roleName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="roleDescription" class="form-label">وصف الدور</label>
                            <input type="text" class="form-control" id="roleDescription">
                        </div>
                    </div>

                    <!-- شجرة الصلاحيات -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-tree"></i>
                            تحديد صلاحيات الدور
                        </h6>
                        <div class="border rounded p-3" style="max-height: 500px; overflow-y: auto;">
                            <div id="rolePermissionsTree">
                                <!-- سيتم تحميل شجرة الصلاحيات هنا -->
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="button" class="btn btn-outline-primary" onclick="selectAllPermissions()">
                                <i class="fas fa-check-double"></i>
                                تحديد الكل
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearAllPermissions()">
                                <i class="fas fa-times"></i>
                                إلغاء تحديد الكل
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="saveRole()">
                    <i class="fas fa-save"></i>
                    حفظ الدور
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal: إضافة مستخدم جديد -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="addUserModalLabel">
                    <i class="fas fa-user-plus"></i>
                    إضافة مستخدم جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="userName" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="userName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="userEmail" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="userEmail" required>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="userPassword" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="userPassword" required>
                        </div>
                        <div class="col-md-6">
                            <label for="userRole" class="form-label">الدور <span class="text-danger">*</span></label>
                            <select class="form-select" id="userRole" required>
                                <option value="">اختر الدور</option>
                                <!-- سيتم تحميل الأدوار ديناميكياً -->
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="userFirstName" class="form-label">الاسم الأول</label>
                            <input type="text" class="form-control" id="userFirstName">
                        </div>
                        <div class="col-md-6">
                            <label for="userLastName" class="form-label">الاسم الأخير</label>
                            <input type="text" class="form-control" id="userLastName">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="userPhone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="userPhone">
                        </div>
                        <div class="col-md-6">
                            <label for="userDepartment" class="form-label">القسم</label>
                            <input type="text" class="form-control" id="userDepartment">
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="userActive" checked>
                            <label class="form-check-label" for="userActive">
                                المستخدم نشط
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="saveUser()">
                    <i class="fas fa-save"></i>
                    حفظ المستخدم
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.permission-tree {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.module-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sub-module-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    margin: 8px 0 8px 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.permission-item {
    padding: 8px 15px;
    margin: 5px 0 5px 40px;
    background-color: #f8f9fa;
    border-left: 3px solid #007bff;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.permission-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
}

.permission-checkbox {
    margin-right: 10px;
    transform: scale(1.2);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}
</style>

<script>
// متغيرات عامة
let systemTree = {};
let availableRoles = [];

// تحميل البيانات عند تحميل الصفحة
$(document).ready(function() {
    console.log('تم تحميل الصفحة، بدء تحميل البيانات...');
    loadSystemTree();
    loadAvailableRoles();
});

// التحقق من تحميل jQuery
if (typeof $ === 'undefined') {
    console.error('jQuery غير محمل!');
    alert('خطأ: jQuery غير محمل. يرجى إعادة تحميل الصفحة.');
}

// تحميل شجرة النظام
function loadSystemTree() {
    if (typeof $ !== 'undefined') {
        $.get('/api/system-tree', function(data) {
            systemTree = data;
            console.log('تم تحميل شجرة النظام:', Object.keys(data).length, 'وحدة');
        }).fail(function(xhr, status, error) {
            console.error('فشل في تحميل شجرة النظام:', status, error);
            console.error('Response:', xhr.responseText);
        });
    } else {
        // استخدام fetch كبديل
        fetch('/api/system-tree')
            .then(response => response.json())
            .then(data => {
                systemTree = data;
                console.log('تم تحميل شجرة النظام (fetch):', Object.keys(data).length, 'وحدة');
            })
            .catch(error => {
                console.error('فشل في تحميل شجرة النظام (fetch):', error);
            });
    }
}

// تحميل الأدوار المتاحة
function loadAvailableRoles() {
    if (typeof $ !== 'undefined') {
        $.get('/api/roles', function(data) {
            availableRoles = data;
            updateRoleSelect();
            console.log('تم تحميل الأدوار:', data.length, 'دور');
        }).fail(function(xhr, status, error) {
            console.error('فشل في تحميل الأدوار:', status, error);
            console.error('Response:', xhr.responseText);
        });
    } else {
        // استخدام fetch كبديل
        fetch('/api/roles')
            .then(response => response.json())
            .then(data => {
                availableRoles = data;
                updateRoleSelect();
                console.log('تم تحميل الأدوار (fetch):', data.length, 'دور');
            })
            .catch(error => {
                console.error('فشل في تحميل الأدوار (fetch):', error);
            });
    }
}

// تحديث قائمة الأدوار في نموذج إضافة المستخدم
function updateRoleSelect() {
    const roleSelect = document.getElementById('userRole');
    if (!roleSelect) {
        console.warn('عنصر userRole غير موجود');
        return;
    }

    roleSelect.innerHTML = '<option value="">اختر الدور</option>';

    if (availableRoles && availableRoles.length > 0) {
        availableRoles.forEach(role => {
            const option = document.createElement('option');
            option.value = role.id;
            option.textContent = role.display_name || role.name;
            roleSelect.appendChild(option);
        });
        console.log('تم تحديث قائمة الأدوار:', availableRoles.length, 'دور');
    } else {
        console.warn('لا توجد أدوار متاحة');
    }
}

// عرض شجرة النظام
function showSystemTree() {
    console.log('عرض شجرة النظام:', systemTree);
    if (!systemTree || Object.keys(systemTree).length === 0) {
        if (typeof Swal !== 'undefined') {
            Swal.fire('تنبيه', 'لم يتم تحميل شجرة النظام بعد. يرجى المحاولة مرة أخرى.', 'warning');
        } else {
            alert('لم يتم تحميل شجرة النظام بعد. يرجى المحاولة مرة أخرى.');
        }
        loadSystemTree();
        return;
    }
    const content = generateSystemTreeHTML(systemTree, true);
    const treeContent = document.getElementById('systemTreeContent');
    if (treeContent) {
        treeContent.innerHTML = content;
    }

    // عرض المودال
    if (typeof $ !== 'undefined') {
        $('#systemTreeModal').modal('show');
    } else {
        // استخدام Bootstrap مباشرة
        const modal = document.getElementById('systemTreeModal');
        if (modal) {
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }
    }
}

// عرض نموذج إضافة دور
function showAddRoleModal() {
    console.log('عرض نموذج إضافة دور:', systemTree);
    if (!systemTree || Object.keys(systemTree).length === 0) {
        if (typeof Swal !== 'undefined') {
            Swal.fire('تنبيه', 'لم يتم تحميل شجرة النظام بعد. يرجى المحاولة مرة أخرى.', 'warning');
        } else {
            alert('لم يتم تحميل شجرة النظام بعد. يرجى المحاولة مرة أخرى.');
        }
        loadSystemTree();
        return;
    }
    const content = generateSystemTreeHTML(systemTree, false);
    const permissionsTree = document.getElementById('rolePermissionsTree');
    if (permissionsTree) {
        permissionsTree.innerHTML = content;
    }

    // عرض المودال
    if (typeof $ !== 'undefined') {
        $('#addRoleModal').modal('show');
    } else {
        const modal = document.getElementById('addRoleModal');
        if (modal) {
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }
    }
}

// عرض نموذج إضافة مستخدم
function showAddUserModal() {
    // التأكد من تحميل الأدوار
    if (!availableRoles || availableRoles.length === 0) {
        loadAvailableRoles();
    }

    // عرض المودال
    if (typeof $ !== 'undefined') {
        $('#addUserModal').modal('show');
    } else {
        const modal = document.getElementById('addUserModal');
        if (modal) {
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }
    }
}

// إنشاء HTML لشجرة النظام
function generateSystemTreeHTML(tree, readOnly = false) {
    let html = '<div class="permission-tree">';
    
    for (const [moduleKey, moduleData] of Object.entries(tree)) {
        html += `
            <div class="module-header">
                <i class="${moduleData.icon}"></i>
                <strong>${moduleData.name}</strong>
            </div>
        `;
        
        // صلاحيات الوحدة الرئيسية
        if (moduleData.permissions) {
            for (const [permKey, permName] of Object.entries(moduleData.permissions)) {
                const checkboxId = `${moduleKey}_${permKey}`;
                html += `
                    <div class="permission-item">
                        <input type="checkbox" class="permission-checkbox" id="${checkboxId}" 
                               ${readOnly ? 'disabled' : ''} data-module="${moduleKey}" data-permission="${permKey}">
                        <label for="${checkboxId}">${permName}</label>
                    </div>
                `;
            }
        }
        
        // الوحدات الفرعية
        if (moduleData.sub_modules) {
            for (const [subKey, subData] of Object.entries(moduleData.sub_modules)) {
                html += `
                    <div class="sub-module-header">
                        <i class="${subData.icon}"></i>
                        <strong>${subData.name}</strong>
                    </div>
                `;
                
                if (subData.permissions) {
                    for (const [permKey, permName] of Object.entries(subData.permissions)) {
                        const checkboxId = `${subKey}_${permKey}`;
                        html += `
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox" id="${checkboxId}" 
                                       ${readOnly ? 'disabled' : ''} data-module="${subKey}" data-permission="${permKey}">
                                <label for="${checkboxId}">${permName}</label>
                            </div>
                        `;
                    }
                }
            }
        }
    }
    
    html += '</div>';
    return html;
}

// تحديد جميع الصلاحيات
function selectAllPermissions() {
    $('#rolePermissionsTree input[type="checkbox"]').prop('checked', true);
}

// إلغاء تحديد جميع الصلاحيات
function clearAllPermissions() {
    $('#rolePermissionsTree input[type="checkbox"]').prop('checked', false);
}

// حفظ الدور
function saveRole() {
    const roleName = $('#roleName').val().trim();
    const roleDescription = $('#roleDescription').val().trim();
    
    if (!roleName) {
        Swal.fire('خطأ', 'يرجى إدخال اسم الدور', 'error');
        return;
    }
    
    // جمع الصلاحيات المحددة
    const permissions = [];
    $('#rolePermissionsTree input[type="checkbox"]:checked').each(function() {
        const module = $(this).data('module');
        const permission = $(this).data('permission');
        permissions.push(`${module}.${permission}`);
    });
    
    const roleData = {
        name: roleName,
        description: roleDescription,
        permissions: permissions
    };
    
    $.ajax({
        url: '/api/roles',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(roleData),
        success: function(response) {
            if (response.success) {
                Swal.fire('نجح', 'تم حفظ الدور بنجاح', 'success');
                $('#addRoleModal').modal('hide');
                $('#addRoleForm')[0].reset();
                loadAvailableRoles();
            } else {
                Swal.fire('خطأ', response.message, 'error');
            }
        },
        error: function() {
            Swal.fire('خطأ', 'فشل في حفظ الدور', 'error');
        }
    });
}

// حفظ المستخدم
function saveUser() {
    const userData = {
        username: $('#userName').val().trim(),
        email: $('#userEmail').val().trim(),
        password: $('#userPassword').val(),
        role_id: $('#userRole').val(),
        first_name: $('#userFirstName').val().trim(),
        last_name: $('#userLastName').val().trim(),
        phone: $('#userPhone').val().trim(),
        department: $('#userDepartment').val().trim(),
        is_active: $('#userActive').is(':checked')
    };
    
    // التحقق من البيانات المطلوبة
    if (!userData.username || !userData.email || !userData.password || !userData.role_id) {
        Swal.fire('خطأ', 'يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    $.ajax({
        url: '/api/users',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(userData),
        success: function(response) {
            if (response.success) {
                Swal.fire('نجح', 'تم حفظ المستخدم بنجاح', 'success');
                $('#addUserModal').modal('hide');
                $('#addUserForm')[0].reset();
            } else {
                Swal.fire('خطأ', response.message, 'error');
            }
        },
        error: function() {
            Swal.fire('خطأ', 'فشل في حفظ المستخدم', 'error');
        }
    });
}

// عرض إدارة الأدوار
function showRolesManagement() {
    const dynamicContent = document.getElementById('dynamicContent');
    if (dynamicContent) {
        dynamicContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><br>جاري التحميل...</div>';
    }

    if (typeof $ !== 'undefined') {
        $.get('/api/roles-management-content', function(data) {
            $('#dynamicContent').html(data);
        }).fail(function() {
            $('#dynamicContent').html('<div class="alert alert-danger">فشل في تحميل إدارة الأدوار</div>');
        });
    } else {
        fetch('/api/roles-management-content')
            .then(response => response.text())
            .then(data => {
                if (dynamicContent) {
                    dynamicContent.innerHTML = data;
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل إدارة الأدوار:', error);
                if (dynamicContent) {
                    dynamicContent.innerHTML = '<div class="alert alert-danger">فشل في تحميل إدارة الأدوار</div>';
                }
            });
    }
}

// عرض إدارة المستخدمين
function showUsersManagement() {
    const dynamicContent = document.getElementById('dynamicContent');
    if (dynamicContent) {
        dynamicContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><br>جاري التحميل...</div>';
    }

    if (typeof $ !== 'undefined') {
        $.get('/api/users-management-content', function(data) {
            $('#dynamicContent').html(data);
        }).fail(function() {
            $('#dynamicContent').html('<div class="alert alert-danger">فشل في تحميل إدارة المستخدمين</div>');
        });
    } else {
        fetch('/api/users-management-content')
            .then(response => response.text())
            .then(data => {
                if (dynamicContent) {
                    dynamicContent.innerHTML = data;
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل إدارة المستخدمين:', error);
                if (dynamicContent) {
                    dynamicContent.innerHTML = '<div class="alert alert-danger">فشل في تحميل إدارة المستخدمين</div>';
                }
            });
    }
}

// ========================================
// وظائف التعديل والحذف والفحص
// ========================================

// تعديل دور
function editRole(roleId) {
    console.log('تعديل الدور:', roleId);

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'تعديل الدور',
            text: 'هذه الميزة قيد التطوير',
            icon: 'info',
            confirmButtonText: 'موافق'
        });
    } else {
        alert('تعديل الدور - هذه الميزة قيد التطوير');
    }
}

// حذف دور
function deleteRole(roleId) {
    console.log('حذف الدور:', roleId);

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'تأكيد الحذف',
            text: 'هل أنت متأكد من حذف هذا الدور؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                // تنفيذ الحذف
                performDeleteRole(roleId);
            }
        });
    } else {
        if (confirm('هل أنت متأكد من حذف هذا الدور؟')) {
            performDeleteRole(roleId);
        }
    }
}

// تنفيذ حذف الدور
function performDeleteRole(roleId) {
    const deleteData = { role_id: roleId };

    if (typeof $ !== 'undefined') {
        $.ajax({
            url: '/api/roles/' + roleId,
            method: 'DELETE',
            contentType: 'application/json',
            data: JSON.stringify(deleteData),
            success: function(response) {
                if (response.success) {
                    if (typeof Swal !== 'undefined') {
                        Swal.fire('تم الحذف', 'تم حذف الدور بنجاح', 'success');
                    } else {
                        alert('تم حذف الدور بنجاح');
                    }
                    showRolesManagement(); // إعادة تحميل القائمة
                } else {
                    if (typeof Swal !== 'undefined') {
                        Swal.fire('خطأ', response.message, 'error');
                    } else {
                        alert('خطأ: ' + response.message);
                    }
                }
            },
            error: function() {
                if (typeof Swal !== 'undefined') {
                    Swal.fire('خطأ', 'فشل في حذف الدور', 'error');
                } else {
                    alert('فشل في حذف الدور');
                }
            }
        });
    } else {
        fetch('/api/roles/' + roleId, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(deleteData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف الدور بنجاح');
                showRolesManagement();
            } else {
                alert('خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('خطأ في حذف الدور:', error);
            alert('فشل في حذف الدور');
        });
    }
}

// تعديل مستخدم
function editUser(userId) {
    console.log('تعديل المستخدم:', userId);

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'تعديل المستخدم',
            text: 'هذه الميزة قيد التطوير',
            icon: 'info',
            confirmButtonText: 'موافق'
        });
    } else {
        alert('تعديل المستخدم - هذه الميزة قيد التطوير');
    }
}

// حذف مستخدم
function deleteUser(userId) {
    console.log('حذف المستخدم:', userId);

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'تأكيد الحذف',
            text: 'هل أنت متأكد من حذف هذا المستخدم؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                performDeleteUser(userId);
            }
        });
    } else {
        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            performDeleteUser(userId);
        }
    }
}

// تنفيذ حذف المستخدم
function performDeleteUser(userId) {
    const deleteData = { user_id: userId };

    if (typeof $ !== 'undefined') {
        $.ajax({
            url: '/api/users/' + userId,
            method: 'DELETE',
            contentType: 'application/json',
            data: JSON.stringify(deleteData),
            success: function(response) {
                if (response.success) {
                    if (typeof Swal !== 'undefined') {
                        Swal.fire('تم الحذف', 'تم حذف المستخدم بنجاح', 'success');
                    } else {
                        alert('تم حذف المستخدم بنجاح');
                    }
                    showUsersManagement(); // إعادة تحميل القائمة
                } else {
                    if (typeof Swal !== 'undefined') {
                        Swal.fire('خطأ', response.message, 'error');
                    } else {
                        alert('خطأ: ' + response.message);
                    }
                }
            },
            error: function() {
                if (typeof Swal !== 'undefined') {
                    Swal.fire('خطأ', 'فشل في حذف المستخدم', 'error');
                } else {
                    alert('فشل في حذف المستخدم');
                }
            }
        });
    } else {
        fetch('/api/users/' + userId, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(deleteData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف المستخدم بنجاح');
                showUsersManagement();
            } else {
                alert('خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('خطأ في حذف المستخدم:', error);
            alert('فشل في حذف المستخدم');
        });
    }
}

// فحص نظام الأدوار
function checkRolesSystem() {
    console.log('فحص نظام الأدوار...');

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'جاري الفحص...',
            text: 'يتم فحص نظام الأدوار',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    }

    if (typeof $ !== 'undefined') {
        $.get('/api/check-roles-system', function(data) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'نتائج فحص نظام الأدوار',
                    html: formatCheckResults(data),
                    icon: data.overall_status === 'success' ? 'success' : 'warning',
                    confirmButtonText: 'موافق'
                });
            } else {
                alert('نتائج الفحص: ' + JSON.stringify(data, null, 2));
            }
        }).fail(function() {
            if (typeof Swal !== 'undefined') {
                Swal.fire('خطأ', 'فشل في فحص نظام الأدوار', 'error');
            } else {
                alert('فشل في فحص نظام الأدوار');
            }
        });
    } else {
        fetch('/api/check-roles-system')
            .then(response => response.json())
            .then(data => {
                alert('نتائج فحص نظام الأدوار: ' + JSON.stringify(data, null, 2));
            })
            .catch(error => {
                console.error('خطأ في فحص نظام الأدوار:', error);
                alert('فشل في فحص نظام الأدوار');
            });
    }
}

// فحص نظام المستخدمين
function checkUsersSystem() {
    console.log('فحص نظام المستخدمين...');

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: 'جاري الفحص...',
            text: 'يتم فحص نظام المستخدمين',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    }

    if (typeof $ !== 'undefined') {
        $.get('/api/check-users-system', function(data) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'نتائج فحص نظام المستخدمين',
                    html: formatCheckResults(data),
                    icon: data.overall_status === 'success' ? 'success' : 'warning',
                    confirmButtonText: 'موافق'
                });
            } else {
                alert('نتائج الفحص: ' + JSON.stringify(data, null, 2));
            }
        }).fail(function() {
            if (typeof Swal !== 'undefined') {
                Swal.fire('خطأ', 'فشل في فحص نظام المستخدمين', 'error');
            } else {
                alert('فشل في فحص نظام المستخدمين');
            }
        });
    } else {
        fetch('/api/check-users-system')
            .then(response => response.json())
            .then(data => {
                alert('نتائج فحص نظام المستخدمين: ' + JSON.stringify(data, null, 2));
            })
            .catch(error => {
                console.error('خطأ في فحص نظام المستخدمين:', error);
                alert('فشل في فحص نظام المستخدمين');
            });
    }
}

// تنسيق نتائج الفحص
function formatCheckResults(data) {
    let html = '<div class="text-start">';

    if (data.checks) {
        data.checks.forEach(check => {
            const icon = check.status === 'success' ? '✅' : '❌';
            const color = check.status === 'success' ? 'text-success' : 'text-danger';
            html += `<div class="${color}"><strong>${icon} ${check.name}:</strong> ${check.message}</div>`;
        });
    }

    if (data.summary) {
        html += '<hr><div class="mt-3"><strong>الملخص:</strong></div>';
        Object.keys(data.summary).forEach(key => {
            html += `<div><strong>${key}:</strong> ${data.summary[key]}</div>`;
        });
    }

    html += '</div>';
    return html;
}
</script>
{% endblock %}
