#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 تحضير المشروع للنقل إلى جهاز آخر
Prepare Project for Transfer to Another Device
"""

import os
import shutil
import zipfile
import sys
from datetime import datetime

def create_transfer_package():
    """إنشاء حزمة نقل كاملة للمشروع"""
    
    print("🚀 تحضير المشروع للنقل...")
    print("=" * 50)
    
    # إنشاء مجلد النقل
    transfer_dir = f"TRAINING_SYSTEM_TRANSFER_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if os.path.exists(transfer_dir):
        shutil.rmtree(transfer_dir)
    
    os.makedirs(transfer_dir)
    print(f"✅ تم إنشاء مجلد النقل: {transfer_dir}")
    
    # قائمة الملفات والمجلدات المطلوبة
    essential_items = [
        # الملفات الأساسية
        'app.py',
        'requirements.txt',
        'reports_generator.py',
        'backup_utils.py',
        'person_data_routes.py',
        'api_routes.py',
        'smart_backup_config.py',
        
        # ملفات التشغيل
        'START.py',
        'GO.py',
        'run_app.py',
        'run_server.py',
        
        # ملفات الإعداد
        'production_config.py',
        '.env.example',
        
        # المجلدات الأساسية
        'templates',
        'static',
        'venv',  # البيئة الافتراضية
        
        # قاعدة البيانات
        'training_system.db',
        'instance/training_system.db',
        
        # ملفات الدعم
        'SETUP.bat',
        'start_app.bat',
        'install_libs.bat',
        'quick_start.bat'
    ]
    
    # نسخ الملفات والمجلدات
    copied_items = []
    for item in essential_items:
        if os.path.exists(item):
            dest_path = os.path.join(transfer_dir, item)
            
            # إنشاء المجلد الأب إذا لم يكن موجوداً
            dest_parent = os.path.dirname(dest_path)
            if dest_parent and not os.path.exists(dest_parent):
                os.makedirs(dest_parent)
            
            if os.path.isfile(item):
                shutil.copy2(item, dest_path)
                print(f"✅ نسخ ملف: {item}")
            elif os.path.isdir(item):
                shutil.copytree(item, dest_path, dirs_exist_ok=True)
                print(f"✅ نسخ مجلد: {item}")
            
            copied_items.append(item)
        else:
            print(f"⚠️ غير موجود: {item}")
    
    # إنشاء ملف تعليمات التشغيل
    create_setup_instructions(transfer_dir)
    
    # إنشاء ملف تشغيل سريع
    create_quick_start_script(transfer_dir)
    
    # ضغط المجلد
    zip_filename = f"{transfer_dir}.zip"
    create_zip_package(transfer_dir, zip_filename)
    
    print("\n" + "=" * 50)
    print("✅ تم تحضير المشروع للنقل بنجاح!")
    print(f"📦 ملف النقل: {zip_filename}")
    print(f"📁 مجلد النقل: {transfer_dir}")
    print("=" * 50)
    
    return zip_filename, transfer_dir

def create_setup_instructions(transfer_dir):
    """إنشاء ملف تعليمات الإعداد"""
    
    instructions = """
# 🚀 تعليمات تشغيل نظام التدريب على جهاز جديد

## الخطوة 1: تثبيت Python
1. ثبت Python 3.11+ من الموقع الرسمي
2. تأكد من إضافة Python إلى PATH أثناء التثبيت

## الخطوة 2: فك ضغط المشروع
1. فك ضغط الملف في أي مكان تريده
2. افتح Command Prompt/Terminal في مجلد المشروع

## الخطوة 3: تنشيط البيئة الافتراضية
### Windows:
```
venv\\Scripts\\activate
```

### Linux/Mac:
```
source venv/bin/activate
```

## الخطوة 4: تشغيل النظام
```
python START.py
```

أو استخدم الملفات المساعدة:
- Windows: انقر مزدوج على `start_app.bat`
- أو شغل: `python GO.py`

## الخطوة 5: الوصول للنظام
- افتح المتصفح واذهب إلى: http://localhost:5000
- اسم المستخدم: admin
- كلمة المرور: admin

## في حالة وجود مشاكل:
1. تأكد من تثبيت Python بشكل صحيح
2. شغل: `pip install -r requirements.txt`
3. تأكد من وجود ملف قاعدة البيانات

## للدعم:
- تحقق من ملف README.md
- راجع ملفات التشخيص في المجلد
"""
    
    with open(os.path.join(transfer_dir, "تعليمات_التشغيل.md"), 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ تم إنشاء ملف التعليمات")

def create_quick_start_script(transfer_dir):
    """إنشاء سكريبت تشغيل سريع"""
    
    # سكريبت Windows
    windows_script = """@echo off
echo ========================================
echo    نظام التدريب والتأهيل
echo    Training and Qualification System
echo ========================================
echo.

cd /d "%~dp0"

echo تنشيط البيئة الافتراضية...
call venv\\Scripts\\activate.bat

echo.
echo تشغيل النظام...
python START.py

pause
"""
    
    with open(os.path.join(transfer_dir, "تشغيل_سريع.bat"), 'w', encoding='utf-8') as f:
        f.write(windows_script)
    
    # سكريبت Linux/Mac
    linux_script = """#!/bin/bash
echo "========================================"
echo "   نظام التدريب والتأهيل"
echo "   Training and Qualification System"
echo "========================================"
echo

cd "$(dirname "$0")"

echo "تنشيط البيئة الافتراضية..."
source venv/bin/activate

echo
echo "تشغيل النظام..."
python START.py

read -p "اضغط Enter للخروج..."
"""
    
    with open(os.path.join(transfer_dir, "تشغيل_سريع.sh"), 'w', encoding='utf-8') as f:
        f.write(linux_script)
    
    # جعل السكريبت قابل للتنفيذ
    try:
        os.chmod(os.path.join(transfer_dir, "تشغيل_سريع.sh"), 0o755)
    except:
        pass
    
    print("✅ تم إنشاء سكريبتات التشغيل السريع")

def create_zip_package(source_dir, zip_filename):
    """ضغط المجلد إلى ملف ZIP"""
    
    print(f"📦 ضغط المشروع إلى {zip_filename}...")
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, source_dir)
                zipf.write(file_path, arc_name)
    
    # حساب حجم الملف
    size_mb = os.path.getsize(zip_filename) / (1024 * 1024)
    print(f"✅ تم الضغط بنجاح - الحجم: {size_mb:.1f} MB")

if __name__ == "__main__":
    try:
        zip_file, transfer_dir = create_transfer_package()
        
        print("\n🎯 الخطوات التالية:")
        print("1. انقل الملف المضغوط إلى الجهاز الآخر")
        print("2. فك ضغط الملف")
        print("3. اتبع التعليمات في ملف 'تعليمات_التشغيل.md'")
        print("4. شغل 'تشغيل_سريع.bat' (Windows) أو 'تشغيل_سريع.sh' (Linux/Mac)")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")
