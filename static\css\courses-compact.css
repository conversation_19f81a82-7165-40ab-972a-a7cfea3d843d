/* 
🎯 تصميم مدمج ومرتب لصفحة الدورات
Compact & Organized Courses Design
*/

/* إعادة تعريف شامل للبطاقات - أصغر وأكثر تنظيماً */
.courses-page .course-card {
    background: white !important;
    border-radius: 10px !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid #e5e7eb !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    height: 320px !important;
    display: flex !important;
    flex-direction: column !important;
    margin-bottom: 15px !important;
}

.courses-page .course-card:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12) !important;
    border-color: #3b82f6 !important;
}

/* تحسين الصورة - أصغر */
.courses-page .course-image {
    height: 110px !important;
    background-size: cover !important;
    background-position: center !important;
    position: relative !important;
    flex-shrink: 0 !important;
}

/* تحسين المحتوى - padding أصغر */
.courses-page .course-body {
    padding: 12px !important;
    display: flex !important;
    flex-direction: column !important;
    flex-grow: 1 !important;
    justify-content: space-between !important;
}

/* تحسين العنوان - حجم أصغر */
.courses-page .course-title {
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin-bottom: 6px !important;
    line-height: 1.3 !important;
    text-align: center !important;
    height: 32px !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    padding: 0 8px !important;
}

/* تحسين المدرب - حجم أصغر */
.courses-page .course-instructor {
    font-size: 0.7rem !important;
    color: #6b7280 !important;
    margin-bottom: 6px !important;
    text-align: center !important;
    font-weight: 500 !important;
}

/* تحسين الوصف - حجم خط صغير جداً وقوي */
.courses-page .course-card .course-description,
.courses-page .course-description,
.course-card .course-description,
p.course-description {
    font-size: 0.55rem !important;
    color: #9ca3af !important;
    line-height: 1.2 !important;
    text-align: center !important;
    margin-bottom: 8px !important;
    height: 28px !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    padding: 0 6px !important;
    font-weight: 400 !important;
    font-family: 'Cairo', 'Poppins', sans-serif !important;
}

/* تحسين المعلومات الإضافية - أصغر */
.courses-page .course-meta {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-top: auto !important;
    padding-top: 8px !important;
    border-top: 1px solid #f3f4f6 !important;
    font-size: 0.6rem !important;
}

.courses-page .course-students {
    color: #9ca3af !important;
    font-size: 0.6rem !important;
}

/* تحسين الشارات - أصغر */
.courses-page .badge {
    font-size: 0.55rem !important;
    padding: 2px 6px !important;
    font-weight: 600 !important;
    border-radius: 6px !important;
}

/* تحسين Footer - أصغر */
.courses-page .card-footer {
    padding: 8px 12px !important;
    background: #f9fafb !important;
    border-top: 1px solid #f3f4f6 !important;
    text-align: center !important;
    flex-shrink: 0 !important;
}

.courses-page .card-footer .btn {
    font-size: 0.7rem !important;
    padding: 5px 12px !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    width: 100% !important;
}

/* تحسين الشارة العلوية */
.courses-page .course-category {
    position: absolute !important;
    top: 8px !important;
    right: 8px !important;
    font-size: 0.65rem !important;
    padding: 3px 8px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* تحسين الشبكة */
.courses-page .row {
    margin: 0 -10px !important;
}

.courses-page .col-md-4 {
    padding: 0 10px !important;
    margin-bottom: 20px !important;
}

/* تحسين الاستجابة - أحجام أصغر مع تركيز على الوصف */
@media (max-width: 576px) {
    .courses-page .course-card {
        height: 280px !important;
    }

    .courses-page .course-image {
        height: 90px !important;
    }

    .courses-page .course-title {
        font-size: 0.75rem !important;
        height: 28px !important;
    }

    .courses-page .course-card .course-description,
    .courses-page .course-description,
    .course-card .course-description,
    p.course-description {
        font-size: 0.5rem !important;
        height: 24px !important;
        -webkit-line-clamp: 2 !important;
        line-height: 1.1 !important;
    }

    .courses-page .course-body {
        padding: 8px !important;
    }

    .courses-page .course-instructor {
        font-size: 0.65rem !important;
    }
}

@media (min-width: 577px) and (max-width: 768px) {
    .courses-page .course-card {
        height: 300px !important;
    }

    .courses-page .course-image {
        height: 100px !important;
    }

    .courses-page .course-title {
        font-size: 0.8rem !important;
    }

    .courses-page .course-card .course-description,
    .courses-page .course-description,
    .course-card .course-description,
    p.course-description {
        font-size: 0.52rem !important;
        height: 26px !important;
    }
}

@media (min-width: 769px) and (max-width: 992px) {
    .courses-page .course-card {
        height: 310px !important;
    }

    .courses-page .course-image {
        height: 105px !important;
    }

    .courses-page .course-title {
        font-size: 0.82rem !important;
    }

    .courses-page .course-card .course-description,
    .courses-page .course-description,
    .course-card .course-description,
    p.course-description {
        font-size: 0.53rem !important;
        height: 27px !important;
    }
}

@media (min-width: 993px) {
    .courses-page .course-card {
        height: 320px !important;
    }

    .courses-page .course-image {
        height: 110px !important;
    }

    .courses-page .course-title {
        font-size: 0.85rem !important;
    }

    .courses-page .course-card .course-description,
    .courses-page .course-description,
    .course-card .course-description,
    p.course-description {
        font-size: 0.55rem !important;
        height: 28px !important;
    }
}

/* تحسين الألوان والتباين */
.courses-page .course-card .course-title:hover {
    color: #3b82f6 !important;
}

.courses-page .course-card:hover .course-description {
    color: #6b7280 !important;
}

/* تحسين الخطوط */
.courses-page * {
    font-family: 'Cairo', 'Poppins', sans-serif !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* تحسين التباعد - أصغر */
.courses-page .course-instructor {
    margin-bottom: 4px !important;
}

.courses-page .course-description {
    margin-bottom: 6px !important;
}

/* تحسين إضافي للتنسيق المدمج */
.courses-page .course-card .course-content {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    justify-content: space-between !important;
}

.courses-page .course-card .course-header {
    text-align: center !important;
    margin-bottom: 8px !important;
}

.courses-page .course-card .course-footer-info {
    margin-top: auto !important;
    padding-top: 6px !important;
}

/* تحسين الأزرار */
.courses-page .btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
    border: none !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.courses-page .btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%) !important;
    transform: translateY(-1px) !important;
}

/* تحسين التنسيق العام */
.courses-page .main-container {
    padding: 30px !important;
}

.courses-page .filter-section {
    margin-bottom: 25px !important;
    padding: 20px !important;
}

/* تحسين العنوان الرئيسي */
.courses-page .courses-main-title {
    font-size: 2.5rem !important;
    margin-bottom: 25px !important;
}

/* تحسين ترقيم الصفحات */
.courses-page .pagination {
    margin-top: 30px !important;
}

.courses-page .pagination .page-link {
    font-size: 0.9rem !important;
    padding: 8px 12px !important;
}

/* تحسين التنبيهات */
.courses-page .alert {
    font-size: 1rem !important;
    padding: 15px !important;
    border-radius: 12px !important;
}

/* تحسين نهائي للتخطيط */
.courses-page .course-card {
    position: relative !important;
}

.courses-page .course-card::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 2px !important;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8) !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.courses-page .course-card:hover::before {
    opacity: 1 !important;
}

/* تحسين التركيز */
.courses-page .course-card:focus-within {
    outline: 2px solid #3b82f6 !important;
    outline-offset: 2px !important;
}

/* تحسين إمكانية الوصول */
.courses-page .course-card .btn:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
}

/* تحسين التحميل */
.courses-page .course-card.loading {
    opacity: 0.7 !important;
    pointer-events: none !important;
}

.courses-page .course-card.loading::after {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 20px !important;
    height: 20px !important;
    border: 2px solid #f3f4f6 !important;
    border-top: 2px solid #3b82f6 !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
    transform: translate(-50%, -50%) !important;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* CSS قوي إضافي لضمان تطبيق حجم الخط الصغير للوصف */
body .courses-page .course-card .course-description,
body .courses-page .course-description,
body .course-card .course-description,
body p.course-description,
.courses-page .card .course-description,
.courses-page .course-card p.course-description {
    font-size: 0.55rem !important;
    line-height: 1.2 !important;
    height: 28px !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    text-overflow: ellipsis !important;
    color: #9ca3af !important;
    font-weight: 400 !important;
    margin-bottom: 8px !important;
    padding: 0 6px !important;
    text-align: center !important;
}

/* تأكيد إضافي للشاشات الكبيرة */
@media (min-width: 1200px) {
    body .courses-page .course-card .course-description,
    body .courses-page .course-description,
    body .course-card .course-description,
    body p.course-description {
        font-size: 0.55rem !important;
        height: 28px !important;
        line-height: 1.2 !important;
    }
}

/* تأكيد للشاشات المتوسطة */
@media (min-width: 768px) and (max-width: 1199px) {
    body .courses-page .course-card .course-description,
    body .courses-page .course-description,
    body .course-card .course-description,
    body p.course-description {
        font-size: 0.53rem !important;
        height: 26px !important;
        line-height: 1.1 !important;
    }
}

/* تأكيد للهواتف */
@media (max-width: 767px) {
    body .courses-page .course-card .course-description,
    body .courses-page .course-description,
    body .course-card .course-description,
    body p.course-description {
        font-size: 0.5rem !important;
        height: 24px !important;
        line-height: 1.1 !important;
        -webkit-line-clamp: 2 !important;
    }
}
