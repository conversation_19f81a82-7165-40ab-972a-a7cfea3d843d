#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع لنظام التقييم
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Course, CourseParticipant

def test_evaluation_endpoints():
    """اختبار نقاط النهاية للتقييم"""
    
    with app.app_context():
        print("🔍 اختبار نقاط النهاية للتقييم...")
        
        # البحث عن أول دورة ومشارك
        course = Course.query.first()
        if not course:
            print("❌ لا توجد دورات")
            return
        
        participant = CourseParticipant.query.filter_by(course_id=course.id).first()
        if not participant:
            print("❌ لا يوجد مشاركين في الدورة")
            return
        
        print(f"📚 الدورة: {course.title} (ID: {course.id})")
        print(f"👤 المشارك: {participant.personal_data.full_name if participant.personal_data else 'غير محدد'} (ID: {participant.id})")
        
        # اختبار إنشاء تقييم مباشر في قاعدة البيانات
        try:
            # حذف أي تقييمات سابقة للاختبار
            db.engine.execute("""
                DELETE FROM evaluation_details WHERE evaluation_id IN (
                    SELECT id FROM participant_evaluations WHERE course_id = ? AND participant_id = ?
                )
            """, (course.id, participant.id))
            
            db.engine.execute("""
                DELETE FROM participant_evaluations WHERE course_id = ? AND participant_id = ?
            """, (course.id, participant.id))
            
            # إنشاء تقييم جديد
            evaluation_data = {
                'course_id': course.id,
                'participant_id': participant.id,
                'evaluator_id': 1,  # افتراض وجود مستخدم برقم 1
                'total_score': 95.5,
                'percentage': 83.0,
                'grade': 'جيد جداً',
                'notes': 'تقييم اختباري',
                'is_final': 1
            }
            
            # إدراج التقييم
            result = db.engine.execute("""
                INSERT INTO participant_evaluations 
                (course_id, participant_id, evaluator_id, total_score, percentage, grade, notes, is_final)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                evaluation_data['course_id'],
                evaluation_data['participant_id'],
                evaluation_data['evaluator_id'],
                evaluation_data['total_score'],
                evaluation_data['percentage'],
                evaluation_data['grade'],
                evaluation_data['notes'],
                evaluation_data['is_final']
            ))
            
            # الحصول على معرف التقييم
            evaluation_id_result = db.engine.execute("SELECT last_insert_rowid()").fetchone()
            evaluation_id = evaluation_id_result[0]
            
            print(f"✅ تم إنشاء تقييم برقم: {evaluation_id}")
            
            # إضافة تفاصيل التقييم
            criteria_scores = [
                (evaluation_id, 1, 18.0, 'حضور ممتاز'),  # الحضور والانضباط
                (evaluation_id, 2, 19.0, 'مشاركة فعالة'),  # المشاركة والتفاعل
                (evaluation_id, 3, 27.0, 'أداء جيد'),  # الاختبار النظري
                (evaluation_id, 4, 26.5, 'تطبيق ممتاز'),  # الاختبار العملي
                (evaluation_id, 6, 14.0, 'مهارات جيدة')  # المهارات الشخصية
            ]
            
            for eval_id, criteria_id, score, notes in criteria_scores:
                db.engine.execute("""
                    INSERT INTO evaluation_details (evaluation_id, criteria_id, score, notes)
                    VALUES (?, ?, ?, ?)
                """, (eval_id, criteria_id, score, notes))
            
            print("✅ تم إضافة تفاصيل التقييم")
            
            # اختبار استعلام الطباعة
            print("\n🖨️ اختبار استعلام الطباعة...")
            
            result = db.engine.execute("""
                SELECT pe.*, cp.*, pd.full_name, pd.national_number, pd.military_number,
                       ed1.score as attendance_score, ed1.notes as attendance_notes,
                       ed2.score as participation_score, ed2.notes as participation_notes,
                       ed3.score as theory_score, ed3.notes as theory_notes,
                       ed4.score as practical_score, ed4.notes as practical_notes,
                       ed5.score as personal_score, ed5.notes as personal_notes,
                       u.username as evaluator_name
                FROM participant_evaluations pe
                JOIN course_participant cp ON pe.participant_id = cp.id
                JOIN person_data pd ON cp.personal_data_id = pd.id
                LEFT JOIN evaluation_details ed1 ON pe.id = ed1.evaluation_id AND ed1.criteria_id = 1
                LEFT JOIN evaluation_details ed2 ON pe.id = ed2.evaluation_id AND ed2.criteria_id = 2
                LEFT JOIN evaluation_details ed3 ON pe.id = ed3.evaluation_id AND ed3.criteria_id = 3
                LEFT JOIN evaluation_details ed4 ON pe.id = ed4.evaluation_id AND ed4.criteria_id = 4
                LEFT JOIN evaluation_details ed5 ON pe.id = ed5.evaluation_id AND ed5.criteria_id = 6
                LEFT JOIN user u ON pe.evaluator_id = u.id
                WHERE pe.course_id = ? AND pe.participant_id = ?
                ORDER BY pe.created_at DESC
                LIMIT 1
            """, (course.id, participant.id)).fetchone()
            
            if result:
                print("✅ استعلام الطباعة يعمل بنجاح")
                print(f"   - المشارك: {result['full_name']}")
                print(f"   - الدرجة الإجمالية: {result['total_score']}")
                print(f"   - النسبة المئوية: {result['percentage']}%")
                print(f"   - التقدير: {result['grade']}")
                print(f"   - الحضور: {result['attendance_score']}")
                print(f"   - المشاركة: {result['participation_score']}")
                print(f"   - النظري: {result['theory_score']}")
                print(f"   - العملي: {result['practical_score']}")
                print(f"   - المهارات: {result['personal_score']}")
            else:
                print("❌ فشل في استعلام الطباعة")
            
            # اختبار URL الطباعة
            print(f"\n🔗 رابط الطباعة:")
            print(f"   http://127.0.0.1:5000/course/{course.id}/evaluation/print?participants={participant.id}")
            
            print(f"\n🔗 رابط صفحة المشاركين:")
            print(f"   http://127.0.0.1:5000/course/{course.id}/participants")
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_evaluation_endpoints()
