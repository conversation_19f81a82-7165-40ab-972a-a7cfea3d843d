# نظام إدارة التدريب - إعداد النظام
# Training System Setup

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    نظام إدارة التدريب - إعداد النظام" -ForegroundColor Yellow
Write-Host "    Training System Setup" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التأكد من وجود Python
Write-Host "🔧 فحص Python..." -ForegroundColor Blue
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python متوفر: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python غير مثبت! يرجى تثبيت Python أولاً" -ForegroundColor Red
    Write-Host "❌ Python not installed! Please install Python first" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

Write-Host ""

# فحص البيئة الافتراضية
Write-Host "🔧 فحص البيئة الافتراضية..." -ForegroundColor Blue
if (Test-Path ".venv") {
    Write-Host "✅ البيئة الافتراضية موجودة" -ForegroundColor Green
} else {
    Write-Host "📦 إنشاء البيئة الافتراضية..." -ForegroundColor Yellow
    python -m venv .venv
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم إنشاء البيئة الافتراضية بنجاح" -ForegroundColor Green
    } else {
        Write-Host "❌ فشل في إنشاء البيئة الافتراضية" -ForegroundColor Red
        Read-Host "اضغط Enter للخروج..."
        exit 1
    }
}

Write-Host ""

# تفعيل البيئة الافتراضية
Write-Host "🔧 تفعيل البيئة الافتراضية..." -ForegroundColor Blue
& ".venv\Scripts\Activate.ps1"

# تحديث pip
Write-Host "📦 تحديث pip..." -ForegroundColor Blue
python -m pip install --upgrade pip

# تثبيت المكتبات
Write-Host "📦 تثبيت المكتبات المطلوبة..." -ForegroundColor Blue
$packages = @(
    "Flask==2.3.3",
    "Flask-SQLAlchemy==3.0.5", 
    "Flask-Login==0.6.3",
    "Flask-WTF==1.1.1",
    "WTForms==3.0.1",
    "pandas==2.1.1",
    "openpyxl==3.1.2",
    "xlsxwriter==3.1.9",
    "SQLAlchemy==2.0.21",
    "arabic-reshaper==3.0.0",
    "python-bidi==0.4.2",
    "tqdm==4.66.1",
    "email-validator==2.0.0"
)

foreach ($package in $packages) {
    Write-Host "  📦 تثبيت $package..." -ForegroundColor Cyan
    pip install $package
}

Write-Host ""

# فحص قاعدة البيانات
Write-Host "🔧 فحص قاعدة البيانات..." -ForegroundColor Blue
if (Test-Path "training_system.db") {
    Write-Host "✅ قاعدة البيانات موجودة" -ForegroundColor Green
} else {
    Write-Host "📊 إنشاء قاعدة البيانات..." -ForegroundColor Yellow
    try {
        python -c "from app import app, db; app.app_context().push(); db.create_all(); print('✅ تم إنشاء قاعدة البيانات بنجاح')"
        Write-Host "✅ تم إنشاء قاعدة البيانات بنجاح" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ سيتم إنشاء قاعدة البيانات عند أول تشغيل" -ForegroundColor Yellow
    }
}

Write-Host ""

# إنشاء المجلدات المطلوبة
Write-Host "📁 فحص المجلدات المطلوبة..." -ForegroundColor Blue
$folders = @("static", "static\css", "static\js", "static\libs", "static\libs\bootstrap", "uploads", "exports")
foreach ($folder in $folders) {
    if (!(Test-Path $folder)) {
        New-Item -ItemType Directory -Path $folder -Force | Out-Null
        Write-Host "  📁 تم إنشاء مجلد: $folder" -ForegroundColor Cyan
    }
}
Write-Host "✅ جميع المجلدات جاهزة" -ForegroundColor Green

Write-Host ""

# فحص الملفات الأساسية
Write-Host "📄 فحص الملفات الأساسية..." -ForegroundColor Blue
$requiredFiles = @("app.py", "templates")
$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (!(Test-Path $file)) {
        Write-Host "❌ ملف مفقود: $file" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if ($allFilesExist) {
    Write-Host "✅ جميع الملفات الأساسية موجودة" -ForegroundColor Green
} else {
    Write-Host "❌ بعض الملفات الأساسية مفقودة" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "       ✅ تم إعداد النظام بنجاح!" -ForegroundColor Green
Write-Host "       ✅ System setup completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "الآن يمكنك تشغيل النظام باستخدام:" -ForegroundColor Yellow
Write-Host "Now you can run the system using:" -ForegroundColor Yellow
Write-Host ""
Write-Host "  python app.py" -ForegroundColor Cyan
Write-Host ""
Write-Host "أو استخدم:" -ForegroundColor Yellow
Write-Host "Or use:" -ForegroundColor Yellow
Write-Host ""
Write-Host "  .\quick_start.ps1" -ForegroundColor Cyan
Write-Host ""

Read-Host "اضغط Enter للمتابعة..."
