#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وظائف إدارة المستخدمين
"""

import requests
import re
import json
from datetime import datetime

def test_user_functions():
    print("🧪 اختبار وظائف إدارة المستخدمين")
    print("=" * 50)
    
    session = requests.Session()
    
    # تسجيل الدخول كمدير
    print("1️⃣ تسجيل الدخول كمدير...")
    
    # الحصول على CSRF token
    login_page = session.get('http://localhost:5000/login')
    csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
    csrf_token = csrf_match.group(1) if csrf_match else None
    
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    if csrf_token:
        login_data['csrf_token'] = csrf_token
    
    login_response = session.post('http://localhost:5000/login', data=login_data)
    
    if 'dashboard' not in login_response.url:
        print("❌ فشل تسجيل الدخول")
        return
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # اختبار إنشاء مستخدم جديد
    print("\n2️⃣ اختبار إنشاء مستخدم جديد...")
    
    timestamp = datetime.now().strftime("%H%M%S")
    new_user_data = {
        'username': f'test_user_{timestamp}',
        'email': f'test_{timestamp}@test.com',
        'password': 'test123',
        'confirm_password': 'test123',
        'role': 'viewer',
        'first_name': 'مستخدم',
        'last_name': 'اختبار',
        'department': 'قسم الاختبار',
        'position': 'مختبر',
        'phone': '777123000',
        'is_active': True
    }
    
    # الحصول على CSRF token من صفحة إدارة المستخدمين
    users_page = session.get('http://localhost:5000/admin/users')
    if users_page.status_code == 200:
        csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', users_page.text)
        csrf_token = csrf_match.group(1) if csrf_match else None
        
        if csrf_token:
            new_user_data['csrf_token'] = csrf_token
    
    create_response = session.post(
        'http://localhost:5000/admin/users/create',
        json=new_user_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"   Status: {create_response.status_code}")
    if create_response.status_code == 200:
        try:
            result = create_response.json()
            if result.get('success'):
                print(f"   ✅ تم إنشاء المستخدم: {new_user_data['username']}")
            else:
                print(f"   ❌ فشل إنشاء المستخدم: {result.get('message')}")
        except:
            print("   ❌ خطأ في تحليل الاستجابة")
    else:
        print(f"   ❌ خطأ HTTP: {create_response.status_code}")
        print(f"   Response: {create_response.text[:200]}")
    
    # اختبار إعادة تعيين كلمة المرور
    print("\n3️⃣ اختبار إعادة تعيين كلمة المرور...")
    
    reset_response = session.post('http://localhost:5000/admin/users/2/reset-password')
    
    print(f"   Status: {reset_response.status_code}")
    if reset_response.status_code == 200:
        try:
            result = reset_response.json()
            if result.get('success'):
                print(f"   ✅ تم إعادة تعيين كلمة المرور: {result.get('new_password')}")
            else:
                print(f"   ❌ فشل إعادة تعيين كلمة المرور: {result.get('message')}")
        except:
            print("   ❌ خطأ في تحليل الاستجابة")
    else:
        print(f"   ❌ خطأ HTTP: {reset_response.status_code}")
        print(f"   Response: {reset_response.text[:200]}")
    
    # اختبار تغيير حالة المستخدم
    print("\n4️⃣ اختبار تغيير حالة المستخدم...")
    
    toggle_response = session.post(
        'http://localhost:5000/admin/users/12/toggle-status',
        json={'status': True},
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"   Status: {toggle_response.status_code}")
    if toggle_response.status_code == 200:
        try:
            result = toggle_response.json()
            if result.get('success'):
                print(f"   ✅ تم تغيير حالة المستخدم")
            else:
                print(f"   ❌ فشل تغيير حالة المستخدم: {result.get('message')}")
        except:
            print("   ❌ خطأ في تحليل الاستجابة")
    else:
        print(f"   ❌ خطأ HTTP: {toggle_response.status_code}")
        print(f"   Response: {toggle_response.text[:200]}")
    
    # اختبار صفحة إدارة المستخدمين
    print("\n5️⃣ اختبار صفحة إدارة المستخدمين...")
    
    users_page = session.get('http://localhost:5000/admin/users')
    print(f"   Status: {users_page.status_code}")
    
    if users_page.status_code == 200:
        print("   ✅ صفحة إدارة المستخدمين تعمل")
        
        # فحص المحتوى
        if 'إدارة المستخدمين' in users_page.text:
            print("   ✅ العنوان موجود")
        
        if 'إجمالي المستخدمين' in users_page.text:
            print("   ✅ الإحصائيات موجودة")
        
        if 'user-row' in users_page.text:
            print("   ✅ جدول المستخدمين موجود")
        
        if 'addUserModal' in users_page.text:
            print("   ✅ نموذج إضافة المستخدم موجود")
    else:
        print("   ❌ مشكلة في صفحة إدارة المستخدمين")

def main():
    try:
        # التحقق من تشغيل الخادم
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code != 200:
            print("❌ الخادم غير متاح")
            return
    except:
        print("❌ لا يمكن الوصول للخادم")
        return
    
    test_user_functions()

if __name__ == '__main__':
    main()
