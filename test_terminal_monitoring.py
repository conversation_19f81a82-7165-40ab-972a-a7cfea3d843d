#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مراقبة التيرمنال وسجلات الخادم
"""

import requests
import re
import json
import time
import threading

def monitor_server_logs():
    """مراقبة سجلات الخادم"""
    print("📊 مراقبة سجلات الخادم...")
    print("=" * 50)
    
    session = requests.Session()
    
    # تسجيل الدخول
    print("🔐 تسجيل الدخول...")
    login_page = session.get('http://localhost:5000/login')
    csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
    csrf_token = csrf_match.group(1) if csrf_match else None
    
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    if csrf_token:
        login_data['csrf_token'] = csrf_token
    
    response = session.post('http://localhost:5000/login', data=login_data)
    
    if 'dashboard' not in response.url:
        print("❌ فشل تسجيل الدخول")
        return
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # اختبار كل API مع مراقبة النتائج
    tests = [
        {
            'name': 'شجرة النظام',
            'url': '/api/system-tree',
            'method': 'GET',
            'expected_keys': ['dashboard', 'users_management']
        },
        {
            'name': 'قائمة الأدوار',
            'url': '/api/roles',
            'method': 'GET',
            'expected_keys': ['id', 'name']
        },
        {
            'name': 'محتوى إدارة الأدوار',
            'url': '/api/roles-management-content',
            'method': 'GET',
            'expected_content': 'table'
        },
        {
            'name': 'محتوى إدارة المستخدمين',
            'url': '/api/users-management-content',
            'method': 'GET',
            'expected_content': 'table'
        },
        {
            'name': 'فحص نظام الأدوار',
            'url': '/api/check-roles-system',
            'method': 'GET',
            'expected_keys': ['overall_status', 'checks']
        },
        {
            'name': 'فحص نظام المستخدمين',
            'url': '/api/check-users-system',
            'method': 'GET',
            'expected_keys': ['overall_status', 'checks']
        }
    ]
    
    print("\n🧪 اختبار APIs مع مراقبة التيرمنال...")
    
    for i, test in enumerate(tests, 1):
        print(f"\n{i}️⃣ اختبار {test['name']}...")
        
        try:
            # إرسال الطلب
            if test['method'] == 'GET':
                response = session.get(f"http://localhost:5000{test['url']}")
            
            print(f"   📡 الطلب: {test['method']} {test['url']}")
            print(f"   📊 الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                # فحص المحتوى
                if 'expected_content' in test:
                    if test['expected_content'].lower() in response.text.lower():
                        print(f"   ✅ المحتوى المتوقع موجود: {test['expected_content']}")
                    else:
                        print(f"   ❌ المحتوى المتوقع مفقود: {test['expected_content']}")
                
                elif 'expected_keys' in test:
                    try:
                        data = response.json()
                        if isinstance(data, dict):
                            # فحص المفاتيح في كائن
                            for key in test['expected_keys']:
                                if key in data:
                                    print(f"   ✅ المفتاح موجود: {key}")
                                else:
                                    print(f"   ❌ المفتاح مفقود: {key}")
                        elif isinstance(data, list) and len(data) > 0:
                            # فحص المفاتيح في أول عنصر من المصفوفة
                            first_item = data[0]
                            for key in test['expected_keys']:
                                if key in first_item:
                                    print(f"   ✅ المفتاح موجود: {key}")
                                else:
                                    print(f"   ❌ المفتاح مفقود: {key}")
                        
                        print(f"   📋 حجم البيانات: {len(data) if isinstance(data, (list, dict)) else 'غير محدد'}")
                        
                    except json.JSONDecodeError:
                        print(f"   ❌ خطأ في تحليل JSON")
                
                print(f"   ✅ نجح الاختبار")
            else:
                print(f"   ❌ فشل الاختبار: {response.status_code}")
                print(f"   📄 محتوى الخطأ: {response.text[:200]}...")
        
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار: {e}")
        
        # انتظار قصير بين الاختبارات
        time.sleep(1)
    
    # اختبار إنشاء وحذف دور
    print(f"\n7️⃣ اختبار إنشاء دور جديد...")
    
    new_role_data = {
        'name': f'test_terminal_role_{int(time.time())}',
        'description': 'دور اختبار مراقبة التيرمنال',
        'permissions': ['dashboard.view', 'reports.view']
    }
    
    try:
        create_response = session.post(
            'http://localhost:5000/api/roles',
            json=new_role_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   📡 الطلب: POST /api/roles")
        print(f"   📊 الاستجابة: {create_response.status_code}")
        
        if create_response.status_code == 200:
            result = create_response.json()
            if result.get('success'):
                role_id = result.get('role_id')
                print(f"   ✅ تم إنشاء الدور: {new_role_data['name']} (ID: {role_id})")
                
                # اختبار حذف الدور
                print(f"\n8️⃣ اختبار حذف الدور...")
                
                delete_response = session.delete(
                    f'http://localhost:5000/api/roles/{role_id}',
                    headers={'Content-Type': 'application/json'}
                )
                
                print(f"   📡 الطلب: DELETE /api/roles/{role_id}")
                print(f"   📊 الاستجابة: {delete_response.status_code}")
                
                if delete_response.status_code == 200:
                    delete_result = delete_response.json()
                    if delete_result.get('success'):
                        print(f"   ✅ تم حذف الدور بنجاح")
                    else:
                        print(f"   ❌ فشل حذف الدور: {delete_result.get('message')}")
                else:
                    print(f"   ❌ خطأ في حذف الدور: {delete_response.status_code}")
            else:
                print(f"   ❌ فشل إنشاء الدور: {result.get('message')}")
        else:
            print(f"   ❌ خطأ في إنشاء الدور: {create_response.status_code}")
    
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الدور: {e}")
    
    print("\n" + "="*50)
    print("📊 ملخص مراقبة التيرمنال:")
    print("✅ تم اختبار جميع APIs")
    print("✅ تم مراقبة الاستجابات")
    print("✅ تم اختبار إنشاء وحذف الأدوار")
    
    print("\n📋 تعليمات مراقبة التيرمنال:")
    print("1. راقب نافذة التيرمنال التي تشغل python app.py")
    print("2. ستظهر سجلات الطلبات والاستجابات")
    print("3. تحقق من عدم وجود أخطاء في السجلات")
    print("4. راقب أداء الاستجابة")

def test_ui_buttons():
    """اختبار أزرار الواجهة"""
    print("\n🖱️ اختبار أزرار الواجهة...")
    print("=" * 50)
    
    print("📋 الأزرار المتاحة للاختبار:")
    print("1. 🌳 عرض شجرة النظام - يجب أن يظهر مودال مع شجرة كاملة")
    print("2. 🛡️ عرض الأدوار - يجب أن يحمل جدول الأدوار")
    print("3. 🛡️ إضافة دور جديد - يجب أن يظهر نموذج مع شجرة الصلاحيات")
    print("4. 🔍 فحص نظام الأدوار - يجب أن يظهر نتائج الفحص")
    print("5. 👥 عرض المستخدمين - يجب أن يحمل جدول المستخدمين")
    print("6. 👥 إضافة مستخدم جديد - يجب أن يظهر نموذج إضافة")
    print("7. 🔍 فحص نظام المستخدمين - يجب أن يظهر نتائج الفحص")
    
    print("\n🎯 خطوات الاختبار اليدوي:")
    print("1. افتح http://localhost:5000/admin/advanced-users")
    print("2. اضغط على كل زر وتحقق من النتيجة")
    print("3. راقب console المتصفح للأخطاء (F12)")
    print("4. راقب التيرمنال للسجلات")
    
    print("\n✅ علامات النجاح:")
    print("- لا توجد أخطاء في console المتصفح")
    print("- جميع المودالات تفتح بشكل صحيح")
    print("- البيانات تحمل بشكل صحيح")
    print("- أزرار الفحص تظهر نتائج مفصلة")

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من تشغيل الخادم
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code != 200:
            print("❌ الخادم غير متاح")
            return
    except:
        print("❌ لا يمكن الوصول للخادم على localhost:5000")
        print("يرجى التأكد من تشغيل الخادم بـ: python app.py")
        return
    
    print("🚀 بدء مراقبة النظام المتقدم")
    print("=" * 60)
    
    # مراقبة سجلات الخادم
    monitor_server_logs()
    
    # اختبار أزرار الواجهة
    test_ui_buttons()
    
    print("\n🎉 انتهت مراقبة النظام!")
    print("\n📝 ملاحظات مهمة:")
    print("- تأكد من مراقبة التيرمنال أثناء استخدام الواجهة")
    print("- اختبر جميع الأزرار يدوياً")
    print("- تحقق من عدم وجود أخطاء في console المتصفح")

if __name__ == '__main__':
    main()
