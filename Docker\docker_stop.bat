@echo off
chcp 65001 > nul
title إيقاف نظام التدريب والتأهيل - Docker

echo.
echo ========================================
echo    إيقاف نظام التدريب والتأهيل
echo ========================================
echo.

echo 🔍 البحث عن الحاويات المشغلة...
docker ps --filter "name=training_system" --format "table {{.Names}}\t{{.Ports}}\t{{.Status}}"

echo.
echo 🛑 إيقاف جميع حاويات النظام...

REM إيقاف الحاويات المختلفة
docker stop training_system 2>nul
docker stop training_system_5000 2>nul
docker stop training_system_5001 2>nul
docker stop training_system_8080 2>nul

REM إيقاف حاويات docker-compose
docker-compose down 2>nul
docker-compose -f docker-compose-5001.yml down 2>nul
docker-compose -f docker-compose-8080.yml down 2>nul

echo.
echo 🧹 تنظيف الحاويات المتوقفة...
docker container prune -f

echo.
echo ========================================
echo ✅ تم إيقاف النظام بنجاح
echo ========================================
echo.

echo 📋 للتحقق من الحالة:
echo    docker ps
echo.
echo 🚀 لإعادة التشغيل:
echo    docker_start.bat
echo.

pause
