{% extends "layout.html" %}

{% block styles %}
<style>
    .course-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .course-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        color: #ffcc00;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    }
    
    .course-title {
        font-size: 1.8rem;
        margin-bottom: 10px;
    }
    
    .course-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-top: 15px;
    }
    
    .course-info-item {
        background-color: rgba(255, 255, 255, 0.2);
        padding: 8px 15px;
        border-radius: 10px;
        font-size: 0.9rem;
    }
    
    .course-info-item i {
        margin-left: 5px;
    }
    
    .form-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .form-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: bold;
        margin-bottom: 8px;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        padding: 12px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #4a6bff;
        box-shadow: 0 0 0 0.25rem rgba(74, 107, 255, 0.25);
    }
    
    .form-text {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 5px;
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
    }
    
    .btn-cancel {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-cancel:hover {
        transform: translateY(-2px);
    }
    
    .invalid-feedback {
        color: #dc3545;
        font-size: 0.9rem;
        margin-top: 5px;
    }
    
    .schedule-item-info {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .schedule-item-time {
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .schedule-item-title {
        font-size: 1.1rem;
        margin-bottom: 5px;
    }
    
    .schedule-item-description {
        color: #6c757d;
        margin-bottom: 10px;
    }
    
    .schedule-item-material {
        color: #4a6bff;
        text-decoration: underline;
    }
    
    .schedule-item-break {
        background-color: #f8d7da;
        color: #721c24;
        padding: 5px 10px;
        border-radius: 5px;
        display: inline-block;
        margin-top: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="course-header">
        <div class="course-number">{{ course.course_number }}</div>
        <div class="course-title">{{ course.title }}</div>
        <div class="course-info">
            <div class="course-info-item"><i class="fas fa-building"></i> {{ course.agency or 'غير محدد' }}</div>
            <div class="course-info-item"><i class="fas fa-map-marker-alt"></i> {{ course.center_name or 'غير محدد' }}</div>
            <div class="course-info-item"><i class="fas fa-users"></i> {{ course.participant_type or 'غير محدد' }}</div>
            <div class="course-info-item"><i class="fas fa-calendar-alt"></i> {{ course.start_date.strftime('%Y-%m-%d') }} إلى {{ course.end_date.strftime('%Y-%m-%d') }}</div>
            <div class="course-info-item"><i class="fas fa-clock"></i> {{ course.duration_days }} يوم</div>
        </div>
    </div>
    
    <div class="form-card">
        <div class="form-header">
            <h4><i class="fas fa-edit me-2"></i> تعديل فترة في الجدول الزمني</h4>
            <p class="text-muted">تعديل بيانات الفترة في الجدول الزمني</p>
        </div>
        
        <div class="schedule-item-info">
            <div class="schedule-item-time">
                <i class="fas fa-clock me-2"></i> اليوم {{ schedule_item.day_number }}: {{ schedule_item.start_time }} - {{ schedule_item.end_time }}
            </div>
            <div class="schedule-item-title">{{ schedule_item.title }}</div>
            <div class="schedule-item-description">{{ schedule_item.description or 'لا يوجد وصف' }}</div>
            {% if schedule_item.material %}
            <div class="schedule-item-material">
                <i class="fas fa-file-alt me-1"></i> المادة المرتبطة: {{ schedule_item.material.title }}
            </div>
            {% endif %}
            {% if schedule_item.is_break %}
            <div class="schedule-item-break">
                <i class="fas fa-coffee me-1"></i> استراحة
            </div>
            {% endif %}
        </div>
        
        <form method="POST">
            {{ form.hidden_tag() }}
            
            <div class="form-group">
                {{ form.day_number.label(class="form-label") }}
                {% if form.day_number.errors %}
                    {{ form.day_number(class="form-select is-invalid") }}
                    <div class="invalid-feedback">
                        {% for error in form.day_number.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.day_number(class="form-select") }}
                {% endif %}
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.start_time.label(class="form-label") }}
                        {% if form.start_time.errors %}
                            {{ form.start_time(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.start_time.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.start_time(class="form-control", placeholder="08:00") }}
                        {% endif %}
                        <small class="form-text">بتنسيق 24 ساعة (HH:MM)</small>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.end_time.label(class="form-label") }}
                        {% if form.end_time.errors %}
                            {{ form.end_time(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.end_time.errors %}
                                    <span>{{ error }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.end_time(class="form-control", placeholder="09:00") }}
                        {% endif %}
                        <small class="form-text">بتنسيق 24 ساعة (HH:MM)</small>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                {{ form.title.label(class="form-label") }}
                {% if form.title.errors %}
                    {{ form.title(class="form-control is-invalid") }}
                    <div class="invalid-feedback">
                        {% for error in form.title.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.title(class="form-control", placeholder="عنوان الفترة") }}
                {% endif %}
            </div>
            
            <div class="form-group">
                {{ form.description.label(class="form-label") }}
                {% if form.description.errors %}
                    {{ form.description(class="form-control is-invalid", rows=3) }}
                    <div class="invalid-feedback">
                        {% for error in form.description.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.description(class="form-control", rows=3, placeholder="وصف الفترة") }}
                {% endif %}
            </div>
            
            <div class="form-group">
                {{ form.material_id.label(class="form-label") }}
                {% if form.material_id.errors %}
                    {{ form.material_id(class="form-select is-invalid") }}
                    <div class="invalid-feedback">
                        {% for error in form.material_id.errors %}
                            <span>{{ error }}</span>
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.material_id(class="form-select") }}
                {% endif %}
            </div>
            
            <div class="form-check mb-3">
                {{ form.is_break(class="form-check-input") }}
                {{ form.is_break.label(class="form-check-label") }}
            </div>
            
            <div class="d-flex justify-content-between mt-4">
                <a href="{{ url_for('course_schedule', course_id=course.id) }}" class="btn btn-secondary btn-cancel">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
                {{ form.submit(class="btn btn-primary btn-submit") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
