#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الدورات مباشرة من Flask
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_courses_direct():
    """اختبار الدورات مباشرة"""
    try:
        from app import app, db, Course, User
        
        with app.app_context():
            print("🔍 اختبار الدورات مباشرة من Flask")
            print("=" * 50)
            
            # فحص الدورات
            courses = Course.query.all()
            print(f"📚 عدد الدورات في قاعدة البيانات: {len(courses)}")
            
            if courses:
                print("\n📋 تفاصيل الدورات:")
                for i, course in enumerate(courses, 1):
                    print(f"   {i}. رقم الدورة: {course.course_number}")
                    print(f"      العنوان: {course.title}")
                    print(f"      التصنيف: {course.category}")
                    print(f"      المستوى: {course.level}")
                    print(f"      المدرب ID: {course.trainer_id}")
                    print(f"      تاريخ البدء: {course.start_date}")
                    print("      ---")
            
            # فحص المستخدمين
            users = User.query.all()
            print(f"\n👥 عدد المستخدمين: {len(users)}")
            
            admin_users = User.query.filter_by(role='admin').all()
            print(f"👑 عدد المديرين: {len(admin_users)}")
            
            if admin_users:
                print("📋 المديرين:")
                for admin in admin_users:
                    print(f"   - {admin.username} (ID: {admin.id})")
            
            # اختبار مسار الدورات
            print(f"\n🌐 اختبار مسار الدورات...")
            
            with app.test_client() as client:
                # محاولة الوصول إلى صفحة الدورات بدون تسجيل دخول
                response = client.get('/courses')
                print(f"📋 استجابة /courses بدون تسجيل دخول: {response.status_code}")
                
                if response.status_code == 302:
                    print("🔐 تم إعادة التوجيه للتسجيل (طبيعي)")
                
                # محاولة الوصول إلى الصفحة الرئيسية
                home_response = client.get('/')
                print(f"📋 استجابة الصفحة الرئيسية: {home_response.status_code}")
            
            return len(courses), len(admin_users)
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0, 0

def test_course_route():
    """اختبار مسار الدورات بالتفصيل"""
    try:
        from app import app, Course
        
        with app.app_context():
            print(f"\n🔧 اختبار مسار الدورات بالتفصيل:")
            print("=" * 50)
            
            # محاكاة استدعاء دالة courses()
            courses = Course.query.all()
            print(f"🔍 Course.query.all() أرجع: {len(courses)} دورة")
            
            # فحص كل دورة
            for course in courses:
                print(f"   ✅ دورة صالحة: {course.id} - {course.title}")
                
                # فحص الحقول المطلوبة
                required_fields = ['course_number', 'title', 'description', 'category', 'level']
                for field in required_fields:
                    value = getattr(course, field, None)
                    if value:
                        print(f"      {field}: ✅")
                    else:
                        print(f"      {field}: ❌ فارغ")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المسار: {str(e)}")
        return False

def check_template():
    """فحص قالب الدورات"""
    try:
        print(f"\n📄 فحص قالب courses.html:")
        print("=" * 50)
        
        template_path = 'templates/courses.html'
        if os.path.exists(template_path):
            print("✅ ملف القالب موجود")
            
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص العناصر المهمة
            checks = [
                ('{% if courses %}', 'فحص وجود الدورات'),
                ('{% for course in courses %}', 'حلقة عرض الدورات'),
                ('{{ course.title }}', 'عرض عنوان الدورة'),
                ('{{ course.course_number }}', 'عرض رقم الدورة'),
                ('لا توجد دورات تدريبية متاحة حالياً', 'رسالة عدم وجود دورات')
            ]
            
            for check, description in checks:
                if check in content:
                    print(f"   ✅ {description}")
                else:
                    print(f"   ❌ {description}")
            
            return True
        else:
            print("❌ ملف القالب غير موجود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص القالب: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل للدورات")
    print("=" * 60)
    
    # اختبار مباشر
    courses_count, admin_count = test_courses_direct()
    
    # اختبار المسار
    route_ok = test_course_route()
    
    # فحص القالب
    template_ok = check_template()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص الاختبار الشامل:")
    
    print(f"📚 الدورات في قاعدة البيانات: {courses_count}")
    print(f"👑 المديرين: {admin_count}")
    
    if route_ok:
        print("✅ مسار الدورات يعمل بشكل صحيح")
    else:
        print("❌ مشكلة في مسار الدورات")
    
    if template_ok:
        print("✅ قالب الدورات صحيح")
    else:
        print("❌ مشكلة في قالب الدورات")
    
    if courses_count >= 3 and admin_count >= 1 and route_ok and template_ok:
        print("\n🎉 كل شيء يعمل بشكل مثالي!")
        print("💡 المشكلة الوحيدة هي الحاجة لتسجيل الدخول")
        print("\n🔗 للوصول إلى الدورات:")
        print("   1. افتح: http://127.0.0.1:5000/login")
        print("   2. سجل دخول بـ: admin / admin")
        print("   3. اذهب إلى: http://127.0.0.1:5000/courses")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى إصلاح:")
        if courses_count < 3:
            print("   - عدد الدورات أقل من المتوقع")
        if admin_count < 1:
            print("   - لا يوجد مستخدم admin")
        if not route_ok:
            print("   - مشكلة في مسار الدورات")
        if not template_ok:
            print("   - مشكلة في قالب الدورات")

if __name__ == "__main__":
    main()
