#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, Course, CourseParticipant, PersonData
from sqlalchemy import text

def debug_course_participants():
    with app.app_context():
        print("🔍 فحص المشاركين في الدورة...")
        print("=" * 50)
        
        # البحث عن الدورة
        course = Course.query.filter_by(course_number='3455667').first()
        if not course:
            print("❌ لم يتم العثور على الدورة")
            return
        
        print(f"✅ الدورة: {course.title} (ID: {course.id})")
        
        # الطريقة الأولى: استخدام ORM
        participants_orm = CourseParticipant.query.filter_by(course_id=course.id).all()
        print(f"📊 عدد المشاركين (ORM): {len(participants_orm)}")
        
        if participants_orm:
            print("\n👥 أول 3 مشاركين (ORM):")
            for i, p in enumerate(participants_orm[:3], 1):
                name = p.personal_data.full_name if p.personal_data else f'ID: {p.personal_data_id}'
                print(f"   {i}. {name} (Person ID: {p.personal_data_id})")
        
        # الطريقة الثانية: استخدام SQL مباشر
        try:
            course_participants_query = db.session.execute(text("""
                SELECT pd.id, pd.full_name, pd.national_number, pd.phone, pd.military_number,
                       cp.id as participant_id, cp.status
                FROM course_participant cp
                JOIN person_data pd ON cp.personal_data_id = pd.id
                WHERE cp.course_id = :course_id
            """), {'course_id': course.id})
            
            course_participants = [dict(row._mapping) for row in course_participants_query]
            print(f"\n📊 عدد المشاركين (SQL): {len(course_participants)}")
            
            if course_participants:
                print("\n👥 أول 3 مشاركين (SQL):")
                for i, p in enumerate(course_participants[:3], 1):
                    print(f"   {i}. {p['full_name']} (Person ID: {p['id']})")
                
                # إنشاء فهرس بناءً على ID
                participants_by_id = {}
                for p in course_participants:
                    if p.get('id'):
                        participants_by_id[p['id']] = p
                
                print(f"\n📋 فهرس المشاركين بناءً على ID: {len(participants_by_id)} مشارك")
                
                # اختبار البحث
                test_ids = [917, 792, 909, 1, 2, 3]  # IDs من الاختبار السابق
                print("\n🔍 اختبار البحث:")
                for test_id in test_ids:
                    found = participants_by_id.get(test_id)
                    print(f"   ID {test_id}: {'موجود' if found else 'غير موجود'}")
                    if found:
                        print(f"      الاسم: {found['full_name']}")
            
        except Exception as e:
            print(f"❌ خطأ في SQL: {e}")

if __name__ == "__main__":
    debug_course_participants()
