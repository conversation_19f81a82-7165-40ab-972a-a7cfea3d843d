#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار عرض الدورات في النظام
"""

import sqlite3
import requests

def check_database_courses():
    """فحص الدورات في قاعدة البيانات مباشرة"""
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        print("🔍 فحص الدورات في قاعدة البيانات:")
        print("=" * 50)
        
        # فحص جدول course
        cursor.execute("SELECT COUNT(*) FROM course")
        courses_count = cursor.fetchone()[0]
        print(f"📚 عدد الدورات: {courses_count}")
        
        if courses_count > 0:
            cursor.execute("""
                SELECT id, course_number, title, trainer_id, start_date, category, level
                FROM course
                ORDER BY id
            """)
            courses = cursor.fetchall()
            
            print("\n📋 تفاصيل الدورات:")
            for course in courses:
                print(f"   - ID: {course[0]}")
                print(f"     رقم الدورة: {course[1]}")
                print(f"     العنوان: {course[2]}")
                print(f"     المدرب ID: {course[3]}")
                print(f"     تاريخ البدء: {course[4]}")
                print(f"     التصنيف: {course[5]}")
                print(f"     المستوى: {course[6]}")
                print("     ---")
        
        conn.close()
        return courses_count
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")
        return 0

def test_courses_page():
    """اختبار صفحة الدورات في النظام"""
    try:
        print("\n🌐 اختبار صفحة الدورات:")
        print("=" * 50)
        
        # اختبار الوصول إلى صفحة الدورات
        response = requests.get('http://127.0.0.1:5000/courses', allow_redirects=False, timeout=10)
        print(f"📋 حالة الصفحة: {response.status_code}")
        
        if response.status_code == 302:
            print("🔐 تم إعادة التوجيه للتسجيل (طبيعي)")
            return True
        elif response.status_code == 200:
            print("✅ الصفحة تعمل بنجاح")
            
            # فحص المحتوى للبحث عن الدورات
            content = response.text
            if 'المبيعات2' in content:
                print("✅ تم العثور على دورة المبيعات2")
            else:
                print("❌ لم يتم العثور على دورة المبيعات2")
            
            if 'تدريب الذكاء الاصطناعي' in content:
                print("✅ تم العثور على دورة الذكاء الاصطناعي")
            else:
                print("❌ لم يتم العثور على دورة الذكاء الاصطناعي")
            
            return True
        else:
            print(f"❌ خطأ غير متوقع: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الصفحة: {str(e)}")
        return False

def test_course_template():
    """فحص قالب الدورات"""
    try:
        print("\n📄 فحص قالب courses.html:")
        print("=" * 50)
        
        with open('templates/courses.html', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # البحث عن حلقة عرض الدورات
        if 'for course in courses' in content:
            print("✅ تم العثور على حلقة عرض الدورات")
        else:
            print("❌ لم يتم العثور على حلقة عرض الدورات")
        
        # البحث عن عرض عنوان الدورة
        if 'course.title' in content:
            print("✅ تم العثور على عرض عنوان الدورة")
        else:
            print("❌ لم يتم العثور على عرض عنوان الدورة")
        
        # البحث عن عرض رقم الدورة
        if 'course.course_number' in content:
            print("✅ تم العثور على عرض رقم الدورة")
        else:
            print("❌ لم يتم العثور على عرض رقم الدورة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص القالب: {str(e)}")
        return False

def check_user_permissions():
    """فحص صلاحيات المستخدم"""
    try:
        conn = sqlite3.connect('training_system.db')
        cursor = conn.cursor()
        
        print("\n👤 فحص المستخدمين:")
        print("=" * 50)
        
        cursor.execute("SELECT id, username, role FROM user")
        users = cursor.fetchall()
        
        admin_count = 0
        for user in users:
            print(f"   - ID: {user[0]}, اسم المستخدم: {user[1]}, الدور: {user[2]}")
            if user[2] == 'admin':
                admin_count += 1
        
        print(f"\n📊 عدد المديرين: {admin_count}")
        
        conn.close()
        return admin_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص المستخدمين: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص مشكلة عدم ظهور الدورات")
    print("=" * 60)
    
    # فحص قاعدة البيانات
    courses_count = check_database_courses()
    
    # فحص المستخدمين
    has_admin = check_user_permissions()
    
    # فحص القالب
    template_ok = test_course_template()
    
    # اختبار الصفحة
    page_ok = test_courses_page()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📋 ملخص التشخيص:")
    
    if courses_count > 0:
        print(f"✅ الدورات موجودة في قاعدة البيانات: {courses_count}")
    else:
        print("❌ لا توجد دورات في قاعدة البيانات")
    
    if has_admin:
        print("✅ يوجد مستخدم admin")
    else:
        print("❌ لا يوجد مستخدم admin")
    
    if template_ok:
        print("✅ قالب الدورات يبدو صحيحاً")
    else:
        print("❌ مشكلة في قالب الدورات")
    
    if page_ok:
        print("✅ صفحة الدورات تعمل")
    else:
        print("❌ مشكلة في صفحة الدورات")
    
    print("\n🔗 للوصول إلى الدورات: http://127.0.0.1:5000/courses")
    
    if courses_count > 0 and has_admin and template_ok:
        print("\n💡 التوصية: تسجيل الدخول كمدير وزيارة صفحة الدورات")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى إصلاح")

if __name__ == "__main__":
    main()
