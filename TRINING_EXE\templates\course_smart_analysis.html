{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<style>
    .analysis-card {
        border: 2px solid #007bff;
        border-radius: 15px;
        box-shadow: 0 4px 8px rgba(0,123,255,0.1);
        transition: all 0.3s ease;
    }

    .analysis-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0,123,255,0.2);
    }

    .upload-area {
        border: 3px dashed #007bff;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        transition: all 0.3s ease;
    }

    .upload-area:hover {
        border-color: #0056b3;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    }

    .feature-icon {
        font-size: 3rem;
        color: #007bff;
        margin-bottom: 1rem;
    }

    .feature-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .btn-analyze {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        padding: 12px 30px;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 25px;
        transition: all 0.3s ease;
    }

    .btn-analyze:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0,123,255,0.3);
    }

    .info-badge {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        margin: 5px;
        display: inline-block;
    }

    .step-guide {
        display: flex;
        align-items: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 3px solid #28a745;
        transition: all 0.3s ease;
    }

    .step-guide:hover {
        background: #e8f5e9;
        transform: translateX(3px);
    }

    .step-number-small {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 12px;
        margin-left: 10px;
        flex-shrink: 0;
    }

    .step-text {
        flex: 1;
        font-size: 14px;
    }

    .course-info-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
    }

    .stats-card {
        background: white;
        color: #333;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
    }

    .category-card {
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .person-item {
        background: white;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
        border: 1px solid #e0e0e0;
    }

    .field-display {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.85em;
        margin: 2px;
        display: inline-block;
    }

    .field-display.filled {
        background: #d4edda;
        border: 1px solid #28a745;
        color: #155724;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- معلومات الدورة -->
            <div class="course-info-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-2">
                            <i class="fas fa-brain"></i> الاستيراد الذكي للمشاركين - {{ course.title }}
                        </h1>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><i class="fas fa-calendar"></i> <strong>تاريخ البداية:</strong> {{ course.start_date.strftime('%Y-%m-%d') if course.start_date else 'غير محدد' }}</p>
                                <p class="mb-1"><i class="fas fa-clock"></i> <strong>المدة:</strong> {{ course.duration }} أيام</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><i class="fas fa-map-marker-alt"></i> <strong>المكان:</strong> {{ course.location or 'غير محدد' }}</p>
                                <p class="mb-1"><i class="fas fa-user-tie"></i> <strong>المدرب:</strong> {{ course.trainer or 'غير محدد' }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="stats-card">
                            <h2 class="text-primary mb-2">{{ current_participants_count }}</h2>
                            <p class="mb-0"><i class="fas fa-users text-primary"></i> مشارك حالي</p>
                        </div>
                    </div>
                </div>
            </div>

            {% if results %}
            <!-- عرض نتائج التحليل -->
            <!-- إحصائيات التحليل -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); border-left: 4px solid #28a745;">
                        <h3 class="text-success">{{ results.statistics.new_count }}</h3>
                        <p class="mb-0"><i class="fas fa-user-plus"></i> أشخاص جدد</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-left: 4px solid #007bff;">
                        <h3 class="text-primary">{{ results.statistics.existing_available_count }}</h3>
                        <p class="mb-0"><i class="fas fa-user-check"></i> متاحين للإضافة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border-left: 4px solid #ffc107;">
                        <h3 class="text-warning">{{ results.statistics.already_participants_count }}</h3>
                        <p class="mb-0"><i class="fas fa-user-times"></i> مشاركين بالفعل</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-left: 4px solid #dc3545;">
                        <h3 class="text-danger">{{ results.statistics.corrected_count }}</h3>
                        <p class="mb-0"><i class="fas fa-edit"></i> أسماء مصححة</p>
                    </div>
                </div>
            </div>

            <!-- أدوات التحكم الرئيسية -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0"><i class="fas fa-tools"></i> مركز التحكم الذكي</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                                        <button class="btn btn-success" onclick="selectAll()" style="border-radius: 20px; padding: 8px 16px;">
                                            <i class="fas fa-check-double"></i> اختيار الكل
                                        </button>
                                        <button class="btn btn-success" onclick="selectCategory('new_people')" style="border-radius: 20px; padding: 8px 16px;">
                                            <i class="fas fa-user-plus"></i> اختيار الجدد
                                        </button>
                                        <button class="btn btn-primary" onclick="selectCategory('existing_available')" style="border-radius: 20px; padding: 8px 16px;">
                                            <i class="fas fa-user-check"></i> اختيار المتاحين
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <button class="btn btn-success btn-lg" onclick="addSelected()">
                                        <i class="fas fa-plus-circle"></i> إضافة المختارين للدورة
                                    </button>
                                    <button class="btn btn-warning btn-lg ms-2" onclick="clearSelection()">
                                        <i class="fas fa-times-circle"></i> إلغاء الاختيار
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عرض البيانات -->
            <div id="results-display">
                <!-- سيتم ملء هذا القسم بـ JavaScript -->
            </div>

            <!-- أزرار التنقل -->
            <div class="text-center mt-4">
                <a href="/course/{{ course.id }}/import_participants" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-arrow-right"></i> العودة للاستيراد
                </a>
                <a href="/manage_participants/{{ course.id }}/" class="btn btn-outline-primary btn-lg ms-3">
                    <i class="fas fa-users"></i> إدارة المشاركين
                </a>
            </div>

            {% else %}
            <!-- لا توجد نتائج - إعادة توجيه للاستيراد -->
            <div class="alert alert-warning text-center">
                <h4><i class="fas fa-exclamation-triangle"></i> لا توجد نتائج تحليل</h4>
                <p>يرجى العودة لصفحة الاستيراد وتحليل ملف Excel أولاً</p>
                <a href="/course/{{ course.id }}/import_participants" class="btn btn-primary btn-lg">
                    <i class="fas fa-upload"></i> العودة للاستيراد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
{% if results and course %}
// بيانات النتائج من الخادم
const courseId = {{ course.id }};
const results = {{ results | tojson }};

console.log('📊 Course Analysis Data:', results);

// دوال التحكم في الاختيار
function selectAll() {
    $('.person-checkbox').prop('checked', true);
    updateSelectionCount();
}

function selectCategory(category) {
    $('.person-checkbox').prop('checked', false);
    $(`.person-checkbox[data-category="${category}"]`).prop('checked', true);
    updateSelectionCount();
}

function clearSelection() {
    $('.person-checkbox').prop('checked', false);
    updateSelectionCount();
}

function updateSelectionCount() {
    const selectedCount = $('.person-checkbox:checked').length;
    console.log('📋 Selected count:', selectedCount);
}

// إضافة المختارين للدورة
function addSelected() {
    const selectedNew = [];
    const selectedExisting = [];

    $('.person-checkbox:checked').each(function() {
        const category = $(this).data('category');
        const personId = $(this).data('person-id');
        const name = $(this).val();

        if (category === 'new_people') {
            // البحث عن بيانات الشخص الجديد
            const person = results.new_people[personId - 1];
            if (person) {
                selectedNew.push(person);
            }
        } else if (category === 'existing_available') {
            // البحث عن بيانات الشخص الموجود
            const person = results.existing_available.find(p => p.person_id == personId);
            if (person) {
                selectedExisting.push(personId);
            }
        }
    });

    if (selectedNew.length === 0 && selectedExisting.length === 0) {
        alert('يرجى اختيار أشخاص للإضافة أولاً');
        return;
    }

    console.log('🚀 Adding to course:', {
        new_people: selectedNew,
        existing_people: selectedExisting
    });

    // إرسال الطلب للخادم
    $.ajax({
        url: `/add_selected_to_course`,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            course_id: courseId,
            selected_new: selectedNew,
            selected_existing: selectedExisting
        }),
        success: function(response) {
            if (response.success) {
                alert(`✅ تم بنجاح!\n📊 تم إضافة ${response.results.added_new} شخص جديد\n👥 تم إضافة ${response.results.added_existing} شخص موجود\n📈 إجمالي المضافين: ${response.results.added_new + response.results.added_existing}`);

                // إعادة توجيه لصفحة إدارة المشاركين
                window.location.href = `/manage_participants/${courseId}/`;
            } else {
                alert('❌ خطأ: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ AJAX Error:', {xhr, status, error});
            alert('حدث خطأ في الإضافة: ' + error);
        }
    });
}

// عرض البيانات
function displayResults() {
    let html = '';

    // الأشخاص الجدد
    if (results.new_people && results.new_people.length > 0) {
        html += `
        <div class="category-card" style="background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); border-left: 4px solid #28a745;">
            <h4 class="text-success mb-3">
                <i class="fas fa-user-plus"></i> أشخاص جدد (${results.statistics.new_count})
                <small class="text-muted">- سيتم إضافتهم لقاعدة البيانات والدورة</small>
            </h4>`;

        results.new_people.forEach((person, index) => {
            html += `
            <div class="person-item">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <input type="checkbox" class="form-check-input person-checkbox"
                               data-category="new_people" data-person-id="${index + 1}"
                               value="${person.name}">
                    </div>
                    <div class="col-md-3">
                        <strong>${person.name}</strong>
                        ${person.original_name ? `<br><small class="text-muted">الأصلي: ${person.original_name}</small>` : ''}
                    </div>
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                ${person.nickname ? `<span class="field-display filled">${person.nickname}</span>` : ''}
                                ${person.age ? `<span class="field-display filled">${person.age} سنة</span>` : ''}
                                ${person.governorate ? `<span class="field-display filled">${person.governorate}</span>` : ''}
                                ${person.directorate ? `<span class="field-display filled">${person.directorate}</span>` : ''}
                                ${person.village ? `<span class="field-display filled">${person.village}</span>` : ''}
                            </div>
                            <div class="col-md-6">
                                ${person.qualification ? `<span class="field-display filled">${person.qualification}</span>` : ''}
                                ${person.job ? `<span class="field-display filled">${person.job}</span>` : ''}
                                ${person.national_id ? `<span class="field-display filled">${person.national_id}</span>` : ''}
                                ${person.military_number ? `<span class="field-display filled">${person.military_number}</span>` : ''}
                                ${person.phone ? `<span class="field-display filled">${person.phone}</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>`;
        });

        html += '</div>';
    }

    // الأشخاص المتاحين للإضافة
    if (results.existing_available && results.existing_available.length > 0) {
        html += `
        <div class="category-card" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-left: 4px solid #007bff;">
            <h4 class="text-primary mb-3">
                <i class="fas fa-user-check"></i> متاحين للإضافة (${results.statistics.existing_available_count})
                <small class="text-muted">- موجودين في قاعدة البيانات وغير مشاركين في الدورة</small>
            </h4>`;

        results.existing_available.forEach((person) => {
            html += `
            <div class="person-item">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <input type="checkbox" class="form-check-input person-checkbox"
                               data-category="existing_available" data-person-id="${person.person_id}"
                               value="${person.name}">
                    </div>
                    <div class="col-md-3">
                        <strong>${person.name}</strong>
                        ${person.original_name ? `<br><small class="text-muted">الأصلي: ${person.original_name}</small>` : ''}
                    </div>
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                ${person.nickname ? `<span class="field-display filled">${person.nickname}</span>` : ''}
                                ${person.age ? `<span class="field-display filled">${person.age} سنة</span>` : ''}
                                ${person.governorate ? `<span class="field-display filled">${person.governorate}</span>` : ''}
                                ${person.directorate ? `<span class="field-display filled">${person.directorate}</span>` : ''}
                                ${person.village ? `<span class="field-display filled">${person.village}</span>` : ''}
                            </div>
                            <div class="col-md-6">
                                ${person.qualification ? `<span class="field-display filled">${person.qualification}</span>` : ''}
                                ${person.job ? `<span class="field-display filled">${person.job}</span>` : ''}
                                ${person.national_id ? `<span class="field-display filled">${person.national_id}</span>` : ''}
                                ${person.military_number ? `<span class="field-display filled">${person.military_number}</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>`;
        });

        html += '</div>';
    }

    // المشاركين بالفعل
    if (results.already_participants && results.already_participants.length > 0) {
        html += `
        <div class="category-card" style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border-left: 4px solid #ffc107;">
            <h4 class="text-warning mb-3">
                <i class="fas fa-user-times"></i> مشاركين بالفعل (${results.statistics.already_participants_count})
                <small class="text-muted">- موجودين في الدورة حالياً</small>
            </h4>`;

        results.already_participants.forEach((person) => {
            html += `
            <div class="person-item" style="opacity: 0.7;">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <i class="fas fa-check-circle text-warning"></i>
                    </div>
                    <div class="col-md-3">
                        <strong>${person.name}</strong>
                        ${person.original_name ? `<br><small class="text-muted">الأصلي: ${person.original_name}</small>` : ''}
                    </div>
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                ${person.nickname ? `<span class="field-display" style="background: #f8f9fa; border: 1px solid #e0e0e0;">${person.nickname}</span>` : ''}
                                ${person.age ? `<span class="field-display" style="background: #f8f9fa; border: 1px solid #e0e0e0;">${person.age} سنة</span>` : ''}
                                ${person.governorate ? `<span class="field-display" style="background: #f8f9fa; border: 1px solid #e0e0e0;">${person.governorate}</span>` : ''}
                                ${person.directorate ? `<span class="field-display" style="background: #f8f9fa; border: 1px solid #e0e0e0;">${person.directorate}</span>` : ''}
                                ${person.village ? `<span class="field-display" style="background: #f8f9fa; border: 1px solid #e0e0e0;">${person.village}</span>` : ''}
                            </div>
                            <div class="col-md-6">
                                ${person.qualification ? `<span class="field-display" style="background: #f8f9fa; border: 1px solid #e0e0e0;">${person.qualification}</span>` : ''}
                                ${person.job ? `<span class="field-display" style="background: #f8f9fa; border: 1px solid #e0e0e0;">${person.job}</span>` : ''}
                                ${person.national_id ? `<span class="field-display" style="background: #f8f9fa; border: 1px solid #e0e0e0;">${person.national_id}</span>` : ''}
                                ${person.military_number ? `<span class="field-display" style="background: #f8f9fa; border: 1px solid #e0e0e0;">${person.military_number}</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>`;
        });

        html += '</div>';
    }

    // الأسماء المصححة
    if (results.corrected_names && results.corrected_names.length > 0) {
        html += `
        <div class="category-card" style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-left: 4px solid #dc3545;">
            <h4 class="text-danger mb-3">
                <i class="fas fa-edit"></i> أسماء تم تصحيحها (${results.statistics.corrected_count})
                <small class="text-muted">- تصحيحات تلقائية للأخطاء الإملائية</small>
            </h4>`;

        results.corrected_names.forEach((correction) => {
            html += `
            <div class="person-item">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <i class="fas fa-edit text-danger"></i>
                    </div>
                    <div class="col-md-5">
                        <span class="badge bg-danger">${correction.original}</span>
                    </div>
                    <div class="col-md-1 text-center">
                        <i class="fas fa-arrow-left text-success"></i>
                    </div>
                    <div class="col-md-5">
                        <span class="badge bg-success">${correction.corrected}</span>
                    </div>
                </div>
            </div>`;
        });

        html += '</div>';
    }

    $('#results-display').html(html);

    // تحديث عداد الاختيار عند تغيير أي checkbox
    $('.person-checkbox').change(function() {
        updateSelectionCount();
    });
}

$(document).ready(function() {
    displayResults();
    console.log('✅ Course Smart Analysis page loaded');
    console.log('📊 Statistics:', results.statistics);
});

{% endif %}
</script>
{% endblock %}