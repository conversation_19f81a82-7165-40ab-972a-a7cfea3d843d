#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تهيئة نظام إدارة المستخدمين والصلاحيات
Initialize Users and Permissions Management System
"""

from app import app, db, User, Permission, Role, RolePermission, UserRole
from werkzeug.security import generate_password_hash
from permissions_manager import PermissionsManager
from datetime import datetime, timezone
import random
import string

def create_sample_users():
    """إنشاء مستخدمين تجريبيين"""
    
    sample_users = [
        {
            'username': 'admin',
            'email': '<EMAIL>',
            'password': 'admin123',
            'role': 'admin',
            'first_name': 'مدير',
            'last_name': 'النظام',
            'department': 'إدارة النظام',
            'position': 'مدير عام',
            'is_active': True
        },
        {
            'username': 'manager1',
            'email': '<EMAIL>',
            'password': 'manager123',
            'role': 'manager',
            'first_name': 'أحمد',
            'last_name': 'المدير',
            'department': 'إدارة التدريب',
            'position': 'مدير التدريب',
            'is_active': True
        },
        {
            'username': 'trainer1',
            'email': '<EMAIL>',
            'password': 'trainer123',
            'role': 'trainer',
            'first_name': 'محمد',
            'last_name': 'المدرب',
            'department': 'التدريب التقني',
            'position': 'مدرب أول',
            'is_active': True
        },
        {
            'username': 'trainer2',
            'email': '<EMAIL>',
            'password': 'trainer123',
            'role': 'trainer',
            'first_name': 'فاطمة',
            'last_name': 'المدربة',
            'department': 'التدريب الإداري',
            'position': 'مدربة',
            'is_active': True
        },
        {
            'username': 'data_entry1',
            'email': '<EMAIL>',
            'password': 'data123',
            'role': 'data_entry',
            'first_name': 'علي',
            'last_name': 'البيانات',
            'department': 'إدخال البيانات',
            'position': 'مدخل بيانات',
            'is_active': True
        },
        {
            'username': 'data_entry2',
            'email': '<EMAIL>',
            'password': 'data123',
            'role': 'data_entry',
            'first_name': 'مريم',
            'last_name': 'الإدخال',
            'department': 'إدخال البيانات',
            'position': 'مدخلة بيانات',
            'is_active': True
        },
        {
            'username': 'viewer1',
            'email': '<EMAIL>',
            'password': 'viewer123',
            'role': 'viewer',
            'first_name': 'خالد',
            'last_name': 'المشاهد',
            'department': 'المراجعة',
            'position': 'مراجع',
            'is_active': True
        },
        {
            'username': 'viewer2',
            'email': '<EMAIL>',
            'password': 'viewer123',
            'role': 'viewer',
            'first_name': 'نورا',
            'last_name': 'المراجعة',
            'department': 'المراجعة',
            'position': 'مراجعة',
            'is_active': True
        },
        {
            'username': 'inactive_user',
            'email': '<EMAIL>',
            'password': 'inactive123',
            'role': 'viewer',
            'first_name': 'مستخدم',
            'last_name': 'معطل',
            'department': 'اختبار',
            'position': 'مختبر',
            'is_active': False
        }
    ]
    
    created_users = []
    
    for user_data in sample_users:
        # التحقق من عدم وجود المستخدم
        existing_user = User.query.filter_by(username=user_data['username']).first()
        if existing_user:
            print(f"⚠️ المستخدم {user_data['username']} موجود بالفعل")
            continue
        
        # إنشاء المستخدم
        user = User(
            username=user_data['username'],
            email=user_data['email'],
            password=generate_password_hash(user_data['password']),
            role=user_data['role'],
            first_name=user_data['first_name'],
            last_name=user_data['last_name'],
            department=user_data['department'],
            position=user_data['position'],
            is_active=user_data['is_active'],
            phone=f"777{random.randint(100000, 999999)}",
            notes=f"مستخدم تجريبي - {user_data['role']}"
        )
        
        db.session.add(user)
        created_users.append(user_data['username'])
    
    db.session.commit()
    return created_users

def assign_additional_roles():
    """تعيين أدوار إضافية للمستخدمين"""
    
    # إعطاء المدير دور مدرب أيضاً
    manager = User.query.filter_by(username='manager1').first()
    trainer_role = Role.query.filter_by(name='trainer').first()
    
    if manager and trainer_role:
        user_role = UserRole(
            user_id=manager.id,
            role_id=trainer_role.id,
            is_active=True,
            assigned_by=1  # المدير العام
        )
        db.session.add(user_role)
    
    # إعطاء المدرب الأول دور مدخل بيانات
    trainer = User.query.filter_by(username='trainer1').first()
    data_entry_role = Role.query.filter_by(name='data_entry').first()
    
    if trainer and data_entry_role:
        user_role = UserRole(
            user_id=trainer.id,
            role_id=data_entry_role.id,
            is_active=True,
            assigned_by=1  # المدير العام
        )
        db.session.add(user_role)
    
    db.session.commit()

def test_permissions():
    """اختبار الصلاحيات"""
    print("\n🧪 اختبار الصلاحيات:")
    
    # اختبار صلاحيات المدير
    admin = User.query.filter_by(username='admin').first()
    if admin:
        print(f"✅ المدير {admin.username} - صلاحية إنشاء المستخدمين: {admin.has_permission('users.create')}")
        print(f"✅ المدير {admin.username} - صلاحية حذف الدورات: {admin.has_permission('courses.delete')}")
    
    # اختبار صلاحيات المدرب
    trainer = User.query.filter_by(username='trainer1').first()
    if trainer:
        print(f"✅ المدرب {trainer.username} - صلاحية عرض الدورات: {trainer.has_permission('courses.view')}")
        print(f"❌ المدرب {trainer.username} - صلاحية حذف المستخدمين: {trainer.has_permission('users.delete')}")
    
    # اختبار صلاحيات المشاهد
    viewer = User.query.filter_by(username='viewer1').first()
    if viewer:
        print(f"✅ المشاهد {viewer.username} - صلاحية عرض التقارير: {viewer.has_permission('reports.view')}")
        print(f"❌ المشاهد {viewer.username} - صلاحية إنشاء الدورات: {viewer.has_permission('courses.create')}")

def print_users_summary():
    """طباعة ملخص المستخدمين"""
    print("\n📊 ملخص المستخدمين:")
    print("=" * 60)
    
    users = User.query.all()
    for user in users:
        status = "🟢 نشط" if user.is_active else "🔴 معطل"
        roles = [user.role]
        
        # إضافة الأدوار الإضافية
        for user_role in user.user_roles:
            if user_role.is_active:
                roles.append(user_role.role.name)
        
        print(f"👤 {user.get_full_name()} (@{user.username})")
        print(f"   📧 {user.email}")
        print(f"   🏢 {user.department} - {user.position}")
        print(f"   🎭 الأدوار: {', '.join(roles)}")
        print(f"   {status}")
        print(f"   🔑 كلمة المرور: {user.username}123")
        print("-" * 60)

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تهيئة نظام إدارة المستخدمين والصلاحيات")
    print("=" * 60)
    
    with app.app_context():
        try:
            # إنشاء الجداول
            print("📋 إنشاء جداول قاعدة البيانات...")
            db.create_all()
            
            # تهيئة الصلاحيات والأدوار
            print("🔐 تهيئة الصلاحيات والأدوار...")
            PermissionsManager.init_permissions(app, db)
            
            # إنشاء المستخدمين التجريبيين
            print("👥 إنشاء المستخدمين التجريبيين...")
            created_users = create_sample_users()
            print(f"✅ تم إنشاء {len(created_users)} مستخدم: {', '.join(created_users)}")
            
            # تعيين أدوار إضافية
            print("🎭 تعيين أدوار إضافية...")
            assign_additional_roles()
            
            # اختبار الصلاحيات
            test_permissions()
            
            # طباعة ملخص المستخدمين
            print_users_summary()
            
            print("\n🎉 تم إكمال تهيئة النظام بنجاح!")
            print("\n📝 معلومات تسجيل الدخول:")
            print("   المدير العام: <EMAIL> / admin123")
            print("   مدير التدريب: <EMAIL> / manager123")
            print("   المدرب: <EMAIL> / trainer123")
            print("   مدخل البيانات: <EMAIL> / data123")
            print("   المشاهد: <EMAIL> / viewer123")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة النظام: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    main()
