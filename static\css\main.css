/* ===== ملف CSS بسيط ونظيف ===== */

/* إعدادات أساسية */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif !important;
    font-size: 14px;
    line-height: 1.6;
    direction: rtl;
    text-align: right;
    color: #1f2937;
    margin: 0;
    padding: 0;
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif !important;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

h1 { font-size: 2rem; }
h2 { font-size: 1.75rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 { font-size: 1rem; }

/* الأزرار */
.btn {
    font-family: 'Cairo', sans-serif !important;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
}

/* النماذج */
.form-control, .form-label {
    font-family: 'Cairo', sans-serif !important;
    font-size: 14px;
}

/* الجداول */
.table {
    font-family: 'Cairo', sans-serif !important;
    font-size: 14px;
}

/* حماية أيقونات Font Awesome */
i, .fa, .fas, .far, .fal, .fab, [class*="fa-"] {
    font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Pro', 'Font Awesome 6 Brands', 'FontAwesome' !important;
}

/* تحسين النصوص */
p {
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* تحسين الروابط */
a {
    color: #1e40af;
    text-decoration: none;
}

a:hover {
    color: #1e3a8a;
    text-decoration: underline;
}

/* تحسين البطاقات */
.card {
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 600;
}

/* تحسين التنبيهات */
.alert {
    border-radius: 8px;
    font-size: 14px;
}

/* تحسين الأزرار الأساسية */
.btn-primary {
    background-color: #1e40af;
    border-color: #1e40af;
    color: white;
}

.btn-primary:hover {
    background-color: #1e3a8a;
    border-color: #1e3a8a;
}

.btn-success {
    background-color: #059669;
    border-color: #059669;
}

.btn-danger {
    background-color: #dc2626;
    border-color: #dc2626;
}

/* تحسين الجداول */
.table thead th {
    background-color: #f8fafc;
    border-bottom: 2px solid #e5e7eb;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8fafc;
}

/* تحسين النماذج */
.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
}

.form-label {
    font-weight: 600;
    color: #374151;
}

/* تحسين التنقل */
.navbar {
    font-size: 14px;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
}

/* تحسين القائمة المنسدلة */
.dropdown-menu {
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
    font-size: 14px;
}

.dropdown-item:hover {
    background-color: #f3f4f6;
}

/* تحسين المحتوى */
.container {
    max-width: 1200px;
}

/* تحسين التذييل */
footer {
    font-size: 14px;
    margin-top: 2rem;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    body {
        font-size: 13px;
    }
    
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .btn {
        font-size: 13px;
        padding: 0.5rem 1rem;
    }
}

/* تحسين التمرير */
html {
    scroll-behavior: smooth;
}

/* تحسين التركيز */
:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* تحسين الانتقالات */
.btn, .card, .alert, .form-control {
    transition: all 0.2s ease-in-out;
}

/* إزالة التأثيرات المعقدة */
.card:hover {
    transform: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn:hover {
    transform: none;
}

/* تحسين الطباعة */
@media print {
    .navbar, .btn, .alert {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        color: black;
        background: white;
    }
}
