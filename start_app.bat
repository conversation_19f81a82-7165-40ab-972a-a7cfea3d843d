@echo off
echo ========================================
echo    تشغيل نظام تحليل الأسماء
echo ========================================
echo.

cd /d "E:\app\TRINING"

echo تفعيل البيئة الافتراضية...
call .venv\Scripts\activate.bat

echo.
echo فحص المكتبات المطلوبة...
python -c "import flask, flask_sqlalchemy, pandas, openpyxl" 2>nul
if errorlevel 1 (
    echo تثبيت المكتبات المفقودة...
    pip install flask flask-sqlalchemy flask-login flask-wtf wtforms pandas openpyxl xlsxwriter sqlalchemy arabic-reshaper python-bidi tqdm email-validator
)

echo.
echo ========================================
echo    بدء تشغيل التطبيق...
echo ========================================
echo.
echo يمكنك الوصول للتطبيق على:
echo http://localhost:5000
echo.
echo لإيقاف التطبيق اضغط Ctrl+C
echo.

python app.py

pause
