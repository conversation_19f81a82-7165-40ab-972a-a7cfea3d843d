/**
 * 🎨 تصميم نظام البحث الذكي والسريع
 * يدعم الوضع المظلم والفاتح مع تأثيرات متقدمة
 */

/* متغيرات CSS للألوان والمقاسات */
:root {
    --search-primary: #007bff;
    --search-success: #28a745;
    --search-warning: #ffc107;
    --search-danger: #dc3545;
    --search-info: #17a2b8;
    --search-light: #f8f9fa;
    --search-dark: #343a40;
    --search-border: #dee2e6;
    --search-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --search-radius: 8px;
    --search-transition: all 0.3s ease;
}

/* حاوي البحث الرئيسي */
.smart-search-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

/* حقل البحث */
.smart-search-input {
    width: 100%;
    padding: 12px 20px 12px 50px;
    font-size: 16px;
    border: 2px solid var(--search-border);
    border-radius: var(--search-radius);
    background: white;
    transition: var(--search-transition);
    box-shadow: var(--search-shadow);
}

.smart-search-input:focus {
    outline: none;
    border-color: var(--search-primary);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* أيقونة البحث */
.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 18px;
    z-index: 2;
}

/* حاوي النتائج */
.search-results-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--search-border);
    border-radius: var(--search-radius);
    box-shadow: 0 8px 32px rgba(0,0,0,0.15); /* ظل أقوى */
    max-height: 70vh; /* استخدام viewport height للتكيف */
    min-height: 150px;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 9999;
    display: none;
    margin-top: 5px;
    backdrop-filter: blur(10px); /* تأثير ضبابي */
}

/* رأس النتائج */
.search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: var(--search-light);
    border-bottom: 1px solid var(--search-border);
    font-size: 14px;
}

.search-info {
    display: flex;
    gap: 15px;
    align-items: center;
}

.results-count {
    font-weight: 600;
    color: var(--search-primary);
}

.search-time {
    color: #6c757d;
    font-size: 12px;
}

/* قائمة النتائج */
.search-results-list {
    max-height: calc(70vh - 60px); /* ارتفاع ديناميكي */
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth; /* تمرير سلس */
}

/* عنصر نتيجة البحث - تصميم مضغوط للغاية */
.search-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px; /* padding مضغوط جداً */
    border-bottom: 1px solid #f8f9fa;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    min-height: 45px; /* ارتفاع مضغوط */
    max-height: 60px;
    flex-shrink: 0;
    overflow: hidden;
    position: relative;
}

.search-result-item:hover {
    background: #f8f9fa;
    transform: translateX(-2px);
}

.search-result-item.selected {
    background: #e3f2fd;
    border-left: 4px solid var(--search-primary);
}

.search-result-item:last-child {
    border-bottom: none;
}

/* معلومات الشخص الرئيسية */
.person-main-info {
    flex: 1;
    min-width: 0;
    overflow: hidden; /* منع تجاوز المحتوى */
}

.person-name {
    font-size: 13px; /* خط صغير جداً */
    font-weight: 600;
    color: #333;
    margin-bottom: 1px; /* مسافة ضئيلة */
    line-height: 1.1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px; /* عرض محدود */
}

.person-name mark {
    background: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 700;
}

.person-details {
    display: flex;
    flex-wrap: nowrap; /* منع التفاف العناصر */
    gap: 3px; /* مسافة ضئيلة */
    margin-bottom: 0; /* بدون مسافة سفلية */
    overflow: hidden;
}

.person-details span {
    font-size: 9px; /* خط صغير جداً */
    padding: 1px 4px; /* padding ضئيل */
    border-radius: 6px;
    background: #f8f9fa;
    color: #6c757d;
    white-space: nowrap;
    max-width: 80px; /* عرض محدود جداً */
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 0;
}

.nickname {
    background: #d4edda !important;
    color: #155724 !important;
}

.national-id {
    background: #cce5ff !important;
    color: #004085 !important;
}

.military-number {
    background: #fff3cd !important;
    color: #856404 !important;
}

.person-additional {
    display: none; /* إخفاء المعلومات الإضافية لتوفير مساحة */
}

/* إظهار المعلومات الإضافية عند التمرير فقط */
.search-result-item:hover .person-additional {
    display: flex;
    flex-wrap: nowrap;
    gap: 3px;
    margin-top: 1px;
    overflow: hidden;
    position: absolute;
    bottom: 2px;
    left: 12px;
    background: rgba(248, 249, 250, 0.9);
    padding: 1px 4px;
    border-radius: 4px;
    z-index: 10;
}

.person-additional span {
    font-size: 8px; /* خط صغير جداً */
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 1px;
    white-space: nowrap;
    max-width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* إجراءات الشخص */
.person-actions {
    display: flex;
    flex-direction: row; /* ترتيب أفقي لتوفير مساحة */
    align-items: center;
    gap: 6px;
    margin-left: 8px;
    flex-shrink: 0;
}

/* درجة التطابق */
.match-score {
    text-align: center;
    min-width: 35px; /* عرض صغير جداً */
    order: 2; /* ترتيب بعد الزر */
}

.score-value {
    font-size: 12px;
    font-weight: 600;
    color: var(--search-primary);
}

.score-bar {
    width: 50px;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 2px;
}

.score-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    transition: width 0.3s ease;
}

/* أزرار الإجراءات - تصميم جديد وجذاب */
.add-person-btn, .remove-person-btn {
    min-width: 70px;
    font-size: 10px;
    padding: 4px 8px;
    border-radius: 20px; /* زر دائري */
    transition: all 0.3s ease;
    white-space: nowrap;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    order: 1; /* ترتيب قبل درجة التطابق */
}

.add-person-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.add-person-btn:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.remove-person-btn {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    color: white;
}

.remove-person-btn:hover {
    background: linear-gradient(135deg, #fd7e14, #ffc107);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.add-person-btn:hover {
    transform: scale(1.05);
}

.remove-person-btn:hover {
    transform: scale(1.05);
}

/* حالات خاصة للنتائج */
.search-loading, .no-results, .search-error, .search-welcome {
    text-align: center;
    padding: 30px 20px;
}

.search-loading i, .no-results i, .search-error i, .search-welcome i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

.search-welcome {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.search-tips {
    margin-top: 15px;
    padding: 10px;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 5px;
}

/* الأشخاص المختارون */
.selected-persons-container {
    margin-top: 20px;
    border: 1px solid var(--search-border);
    border-radius: var(--search-radius);
    background: white;
    box-shadow: var(--search-shadow);
    position: relative;
    z-index: 1; /* أقل من نتائج البحث */
}

/* تحسين التخطيط العام */
.smart-search-wrapper {
    position: relative;
    margin-bottom: 30px; /* مساحة إضافية لتجنب التداخل */
}

/* تحسين مساحة البحث */
.search-section {
    margin-bottom: 40px;
    position: relative;
    z-index: 10;
}

/* تحسين مساحة قائمة المشاركين */
.participants-section {
    margin-top: 40px;
    position: relative;
    z-index: 1;
}

.selected-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: var(--search-light);
    border-bottom: 1px solid var(--search-border);
}

.selected-header h6 {
    margin: 0;
    color: var(--search-dark);
}

.selected-list {
    max-height: 300px;
    overflow-y: auto;
}

.selected-person-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: var(--search-transition);
}

.selected-person-item:hover {
    background: #f8f9fa;
}

.selected-person-item:last-child {
    border-bottom: none;
}

.no-selection {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-selection i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.search-results-container {
    animation: fadeIn 0.3s ease;
}

.search-result-item {
    animation: slideIn 0.3s ease;
}

/* تحسينات إضافية لمنع التداخل */
.search-results-container.show {
    display: block !important;
    animation: slideDown 0.3s ease;
}

.search-results-container.hide {
    animation: slideUp 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 600px; /* زيادة الارتفاع الأقصى */
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
        max-height: 600px; /* زيادة الارتفاع الأقصى */
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
}

/* تحسين التباعد للعناصر المجاورة */
.participants-section {
    margin-top: 60px !important; /* مساحة إضافية لتجنب التداخل */
}

.search-section + * {
    margin-top: 50px; /* مساحة بعد قسم البحث */
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .smart-search-container {
        max-width: 100%;
    }

    .search-result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .person-actions {
        flex-direction: row;
        width: 100%;
        justify-content: space-between;
        margin-left: 0;
    }

    .person-details {
        flex-direction: column;
        gap: 5px;
    }

    .selected-person-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .participants-section {
        margin-top: 40px !important;
    }

    .search-results-container {
        max-height: 300px; /* تقليل الارتفاع على الشاشات الصغيرة */
    }
}

/* الوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --search-light: #2c3e50;
        --search-dark: #ecf0f1;
        --search-border: #34495e;
    }

    .smart-search-input {
        background: #34495e;
        color: white;
        border-color: #4a5f7a;
    }

    .search-results-container {
        background: #2c3e50;
        border-color: #4a5f7a;
    }

    .search-result-item {
        background: #2c3e50;
        color: white;
    }

    .search-result-item:hover {
        background: #34495e;
    }

    .person-name {
        color: white;
    }
}

/* تحسينات الطباعة */
@media print {
    .search-results-container,
    .smart-search-input {
        display: none !important;
    }

    .selected-persons-container {
        box-shadow: none;
        border: 1px solid #000;
    }
}

/* تأثيرات إضافية للتفاعل */
.search-result-item:active {
    transform: scale(0.98);
}

.btn:active {
    transform: scale(0.95);
}

/* شريط التمرير المخصص والجذاب */
.search-results-list::-webkit-scrollbar,
.selected-list::-webkit-scrollbar {
    width: 8px;
}

.search-results-list::-webkit-scrollbar-track,
.selected-list::-webkit-scrollbar-track {
    background: linear-gradient(180deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    box-shadow: inset 0 0 3px rgba(0,0,0,0.1);
}

.search-results-list::-webkit-scrollbar-thumb,
.selected-list::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #007bff, #0056b3);
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0,123,255,0.3);
}

.search-results-list::-webkit-scrollbar-thumb:hover,
.selected-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #0056b3, #004085);
    box-shadow: 0 4px 12px rgba(0,123,255,0.5);
}

/* إضافة مؤشر للمزيد من النتائج */
.search-results-list::after {
    content: "⬇️ مرر لأسفل لرؤية المزيد";
    display: block;
    text-align: center;
    padding: 10px;
    font-size: 11px;
    color: #6c757d;
    background: linear-gradient(180deg, transparent, #f8f9fa);
    position: sticky;
    bottom: 0;
    z-index: 10;
}
