#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار استيراد 10,000 شخص إلى النظام
"""

import pandas as pd
import requests
import time
import json
from datetime import datetime

def test_import_10k_persons(excel_file):
    """اختبار استيراد 10,000 شخص"""
    
    print("🧪 اختبار استيراد 10,000 شخص")
    print("=" * 50)
    
    # قراءة ملف Excel
    print("📖 قراءة ملف Excel...")
    try:
        df = pd.read_excel(excel_file)
        print(f"   ✅ تم قراءة {len(df)} شخص من الملف")
    except Exception as e:
        print(f"   ❌ خطأ في قراءة الملف: {e}")
        return False
    
    # تسجيل الدخول
    print("🔐 تسجيل الدخول...")
    session = requests.Session()
    
    login_page = session.get('http://localhost:5000/login')
    csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
    csrf_token = csrf_match.group(1) if csrf_match else None
    
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    if csrf_token:
        login_data['csrf_token'] = csrf_token
    
    response = session.post('http://localhost:5000/login', data=login_data)
    
    if 'dashboard' not in response.url:
        print("   ❌ فشل تسجيل الدخول")
        return False
    
    print("   ✅ تم تسجيل الدخول بنجاح")
    
    # اختبار الاستيراد على دفعات
    batch_sizes = [100, 500, 1000, 2000, 5000, 10000]
    
    for batch_size in batch_sizes:
        print(f"\n📦 اختبار استيراد {batch_size} شخص...")
        
        # أخذ عينة من البيانات
        sample_df = df.head(batch_size)
        
        # تحويل إلى JSON
        persons_data = sample_df.to_dict('records')
        
        # قياس الوقت
        start_time = time.time()
        
        try:
            # إرسال البيانات
            response = session.post(
                'http://localhost:5000/api/import-persons',
                json={'persons': persons_data},
                headers={'Content-Type': 'application/json'}
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ نجح الاستيراد في {duration:.2f} ثانية")
                    print(f"      📊 تم استيراد: {result.get('imported', 0)} شخص")
                    print(f"      ⚠️ تم تخطي: {result.get('skipped', 0)} شخص")
                    print(f"      🚀 معدل الاستيراد: {batch_size/duration:.0f} شخص/ثانية")
                else:
                    print(f"   ❌ فشل الاستيراد: {result.get('message')}")
            else:
                print(f"   ❌ خطأ HTTP: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ خطأ في الاستيراد: {e}")
    
    return True

if __name__ == '__main__':
    import re
    
    # البحث عن ملف Excel
    import glob
    excel_files = glob.glob('اختبار_استيراد_10000_شخص_*.xlsx')
    
    if excel_files:
        latest_file = max(excel_files)
        print(f"📁 استخدام الملف: {latest_file}")
        test_import_10k_persons(latest_file)
    else:
        print("❌ لم يتم العثور على ملف Excel للاختبار")
        print("يرجى تشغيل generate_10k_persons.py أولاً")
