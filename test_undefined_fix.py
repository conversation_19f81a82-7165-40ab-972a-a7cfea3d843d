#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 اختبار إصلاح مشكلة undefined في URLs
"""

def test_url_fixes():
    """اختبار إصلاح URLs"""
    print("🔧 اختبار إصلاح مشكلة undefined في URLs")
    print("=" * 60)
    
    # فحص الملفات المُصلحة
    files_to_check = [
        'templates/course_import_participants.html',
        'templates/course_import_results.html',
        'templates/manage_course_participants.html'
    ]
    
    for file_path in files_to_check:
        print(f"\n📄 فحص {file_path}:")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص url_for القديم
            url_for_count = content.count('url_for(')
            if url_for_count > 0:
                print(f"   ⚠️ لا يزال يحتوي على {url_for_count} مرجع url_for")
                
                # البحث عن السطور التي تحتوي على url_for
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if 'url_for(' in line and ('import_participants' in line or 'manage_course' in line):
                        print(f"      السطر {i}: {line.strip()}")
            else:
                print(f"   ✅ لا يحتوي على مراجع url_for مشكوك فيها")
            
            # فحص URLs المباشرة الجديدة
            direct_urls = [
                '/course/{{ course.id }}/import_participants',
                '/course/{{ course.id }}/import_participants_api',
                '/course/{{ course.id }}/process_import',
                '/manage_participants/{{ course.id }}/'
            ]
            
            found_urls = []
            for url in direct_urls:
                if url in content:
                    found_urls.append(url)
            
            if found_urls:
                print(f"   ✅ يحتوي على URLs مباشرة صحيحة:")
                for url in found_urls:
                    print(f"      - {url}")
            else:
                print(f"   ⚠️ لا يحتوي على URLs مباشرة")
                
        except FileNotFoundError:
            print(f"   ❌ الملف غير موجود")
        except Exception as e:
            print(f"   ❌ خطأ في قراءة الملف: {e}")

def show_testing_urls():
    """عرض URLs للاختبار"""
    print("\n🌐 URLs للاختبار:")
    print("=" * 60)
    
    urls = [
        ("صفحة إدارة المشاركين", "http://localhost:5001/manage_participants/1/"),
        ("زر الاستيراد الذكي", "يجب أن يؤدي إلى /course/1/import_participants"),
        ("صفحة الاستيراد الذكي", "http://localhost:5001/course/1/import_participants"),
        ("API تحليل الملف", "http://localhost:5001/course/1/import_participants_api"),
        ("صفحة النتائج", "http://localhost:5001/course/1/import_results"),
        ("API معالجة النتائج", "http://localhost:5001/course/1/process_import"),
    ]
    
    for name, url in urls:
        print(f"📋 {name}:")
        print(f"   {url}")

def show_expected_behavior():
    """عرض السلوك المتوقع"""
    print("\n🎯 السلوك المتوقع بعد الإصلاح:")
    print("=" * 60)
    
    behaviors = [
        "✅ زر 'استيراد ذكي' يعمل بدون أخطاء undefined",
        "✅ صفحة الاستيراد تفتح بشكل طبيعي",
        "✅ رفع ملف Excel يعمل بدون مشاكل",
        "✅ تحليل الملف ينتج نتائج صحيحة",
        "✅ صفحة النتائج تعرض البيانات بشكل صحيح",
        "✅ أزرار الإضافة تعمل بدون أخطاء",
        "✅ العودة للصفحات السابقة تعمل",
        "✅ لا توجد أخطاء JavaScript في console",
        "✅ لا توجد أخطاء 404 أو undefined في URLs"
    ]
    
    for behavior in behaviors:
        print(f"   {behavior}")

def show_troubleshooting():
    """عرض خطوات استكشاف الأخطاء"""
    print("\n🔍 استكشاف الأخطاء:")
    print("=" * 60)
    
    steps = [
        "1. إذا ظهر خطأ undefined:",
        "   - افتح Developer Tools في المتصفح (F12)",
        "   - تحقق من Console للأخطاء JavaScript",
        "   - تحقق من Network tab للطلبات الفاشلة",
        "",
        "2. إذا ظهر خطأ 404:",
        "   - تأكد من أن الخادم يعمل على localhost:5001",
        "   - تحقق من أن route مسجل في app.py",
        "   - تأكد من أن URL صحيح",
        "",
        "3. إذا لم تعمل الأزرار:",
        "   - تحقق من أن jQuery محمل بشكل صحيح",
        "   - تأكد من أن JavaScript لا يحتوي على أخطاء syntax",
        "   - تحقق من أن CSRF token موجود",
        "",
        "4. إذا فشل رفع الملف:",
        "   - تأكد من أن الملف Excel صالح",
        "   - تحقق من أن حجم الملف مناسب",
        "   - تأكد من أن API endpoint يعمل"
    ]
    
    for step in steps:
        print(f"   {step}")

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح مشكلة undefined في URLs")
    print("=" * 70)
    
    test_url_fixes()
    show_testing_urls()
    show_expected_behavior()
    show_troubleshooting()
    
    print("\n" + "=" * 70)
    print("📊 خلاصة الإصلاح:")
    print("=" * 70)
    
    print("✅ تم استبدال جميع url_for المشكوك فيها بـ URLs مباشرة")
    print("✅ تم إصلاح مراجع JavaScript للـ APIs")
    print("✅ تم إصلاح روابط التنقل بين الصفحات")
    print("✅ تم التأكد من صحة جميع المسارات")
    
    print("\n🎯 للاختبار الآن:")
    print("   1. افتح: http://localhost:5001/manage_participants/1/")
    print("   2. اضغط: زر '🚀 استيراد ذكي'")
    print("   3. تأكد: من عدم ظهور undefined في URL")
    print("   4. اختبر: رفع ملف Excel والتحليل")
    
    print("\n🎉 يجب أن يعمل النظام الآن بدون أخطاء undefined! ✅")

if __name__ == "__main__":
    main()
