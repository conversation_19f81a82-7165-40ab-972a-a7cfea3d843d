#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص قواعد البيانات المختلفة في النظام
"""

import sqlite3
import os
from datetime import datetime

def check_database(db_path):
    """فحص قاعدة بيانات واحدة"""
    if not os.path.exists(db_path):
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # معلومات أساسية
        size = os.path.getsize(db_path)
        modified = datetime.fromtimestamp(os.path.getmtime(db_path))
        
        # جلب أسماء الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        # فحص الجداول المهمة
        important_tables = ['user', 'course', 'person_data', 'enrollment']
        table_counts = {}
        
        for table in important_tables:
            if table in tables:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                table_counts[table] = cursor.fetchone()[0]
            else:
                table_counts[table] = None
        
        conn.close()
        
        return {
            'path': db_path,
            'size': size,
            'modified': modified,
            'tables_count': len(tables),
            'all_tables': tables,
            'important_tables': table_counts
        }
        
    except Exception as e:
        return {
            'path': db_path,
            'error': str(e)
        }

def main():
    print("=" * 80)
    print("🔍 فحص قواعد البيانات في النظام")
    print("=" * 80)
    
    # قواعد البيانات المحتملة
    databases = [
        'training_system.db',
        'instance/training_system.db', 
        'database.db',
        'training_system.db.bak_20250503_044906',
        'training_system_backup_20250529_034725.db',
        'training_system_backup_columns_20250529_035306.db',
        'training_system_before_copy_20250529_040141.db'
    ]
    
    results = []
    
    for db_path in databases:
        result = check_database(db_path)
        if result:
            results.append(result)
    
    # ترتيب النتائج حسب الحجم (الأكبر أولاً)
    results.sort(key=lambda x: x.get('size', 0), reverse=True)
    
    print(f"\n📊 تم العثور على {len(results)} قاعدة بيانات:")
    print("=" * 80)
    
    for i, result in enumerate(results, 1):
        if 'error' in result:
            print(f"\n{i}. ❌ {result['path']}")
            print(f"   خطأ: {result['error']}")
            continue
            
        print(f"\n{i}. 📁 {result['path']}")
        print(f"   📏 الحجم: {result['size']:,} بايت ({result['size']/1024:.1f} KB)")
        print(f"   📅 آخر تعديل: {result['modified'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   📋 عدد الجداول: {result['tables_count']}")
        
        print("   📊 الجداول المهمة:")
        for table, count in result['important_tables'].items():
            if count is not None:
                print(f"      ✅ {table}: {count} سجل")
            else:
                print(f"      ❌ {table}: غير موجود")
    
    # تحديد قاعدة البيانات المعتمدة
    print("\n" + "=" * 80)
    print("🎯 تحليل قاعدة البيانات المعتمدة:")
    print("=" * 80)
    
    # البحث عن قاعدة البيانات الرئيسية
    main_db = None
    instance_db = None
    
    for result in results:
        if result['path'] == 'training_system.db':
            main_db = result
        elif result['path'] == 'instance/training_system.db':
            instance_db = result
    
    print("\n📋 حسب إعدادات app.py:")
    print("   المسار المحدد: sqlite:///training_system.db")
    print("   هذا يشير إلى: training_system.db في المجلد الرئيسي")
    
    if main_db:
        print(f"\n✅ قاعدة البيانات الرئيسية موجودة:")
        print(f"   📁 المسار: {main_db['path']}")
        print(f"   📏 الحجم: {main_db['size']:,} بايت")
        print(f"   👥 المستخدمين: {main_db['important_tables'].get('user', 0)}")
        print(f"   📚 الدورات: {main_db['important_tables'].get('course', 0)}")
        print(f"   👤 بيانات الأشخاص: {main_db['important_tables'].get('person_data', 0)}")
        print(f"   📝 التسجيلات: {main_db['important_tables'].get('enrollment', 0)}")
    
    if instance_db:
        print(f"\n📂 قاعدة بيانات instance موجودة أيضاً:")
        print(f"   📁 المسار: {instance_db['path']}")
        print(f"   📏 الحجم: {instance_db['size']:,} بايت")
        print(f"   👥 المستخدمين: {instance_db['important_tables'].get('user', 0)}")
        print(f"   📚 الدورات: {instance_db['important_tables'].get('course', 0)}")
        print(f"   👤 بيانات الأشخاص: {instance_db['important_tables'].get('person_data', 0)}")
        print(f"   📝 التسجيلات: {instance_db['important_tables'].get('enrollment', 0)}")
    
    # التوصيات
    print("\n" + "=" * 80)
    print("💡 التوصيات:")
    print("=" * 80)
    
    if main_db and instance_db:
        if main_db['size'] == instance_db['size']:
            print("✅ قواعد البيانات متطابقة في الحجم - يمكن حذف إحداهما")
        else:
            print("⚠️ قواعد البيانات مختلفة - تحقق من المحتوى قبل الحذف")
    
    print("\n🎯 قاعدة البيانات المعتمدة حالياً:")
    print("   📁 training_system.db (في المجلد الرئيسي)")
    print("   🔧 هذا ما يستخدمه النظام حسب إعدادات app.py")
    
    print("\n🗑️ ملفات يمكن حذفها بأمان:")
    backup_files = [r for r in results if 'backup' in r['path'] or '.bak' in r['path']]
    if backup_files:
        for backup in backup_files:
            print(f"   📦 {backup['path']} (نسخة احتياطية)")
    
    if instance_db and main_db and main_db['size'] >= instance_db['size']:
        print(f"   📂 instance/training_system.db (مكررة)")
    
    print("\n⚠️ تحذير: احتفظ بنسخة احتياطية قبل حذف أي ملف!")

if __name__ == '__main__':
    main()
    input("\nاضغط Enter للخروج...")
