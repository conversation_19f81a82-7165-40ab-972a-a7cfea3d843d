@echo off
echo 📦 تثبيت المكتبات المطلوبة...
echo.

cd /d "%~dp0"

echo 🔧 تفعيل البيئة الافتراضية...
call .venv\Scripts\activate.bat

echo 📦 تحديث pip...
python -m pip install --upgrade pip

echo 📦 تثبيت المكتبات الأساسية...
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.0.5
pip install Flask-Login==0.6.3
pip install Flask-WTF==1.1.1
pip install WTForms==3.0.1

echo 📦 تثبيت مكتبات البيانات...
pip install pandas==2.1.1
pip install openpyxl==3.1.2
pip install xlsxwriter==3.1.9
pip install SQLAlchemy==2.0.21

echo 📦 تثبيت مكتبات العربية...
pip install arabic-reshaper==3.0.0
pip install python-bidi==0.4.2

echo 📦 تثبيت مكتبات إضافية...
pip install tqdm==4.66.1
pip install email-validator==2.0.0

echo.
echo ✅ تم تثبيت جميع المكتبات!
echo.
echo الآن يمكنك تشغيل النظام:
echo python app.py
echo.
pause
