import sqlite3
import os
from werkzeug.security import generate_password_hash

print("🔧 إصلاح قاعدة البيانات الحقيقية...")

# التحقق من ملفات قاعدة البيانات
db_files = [
    './training_system.db',
    './instance/training_system.db'
]

for db_file in db_files:
    if os.path.exists(db_file):
        size = os.path.getsize(db_file)
        print(f"📁 {db_file} - حجم: {size:,} بايت")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # عرض المستخدمين الحاليين
        try:
            cursor.execute("SELECT id, username, email, role FROM user")
            users = cursor.fetchall()
            print(f"  👥 المستخدمين في {db_file}:")
            for user in users:
                print(f"    - ID: {user[0]}, اسم المستخدم: {user[1]}, البريد: {user[2]}, الدور: {user[3]}")
        except Exception as e:
            print(f"  ❌ خطأ في قراءة المستخدمين: {e}")
        
        conn.close()

print("\n🔧 إصلاح قاعدة البيانات في مجلد instance...")

# إصلاح قاعدة البيانات في مجلد instance
instance_db = './instance/training_system.db'

if os.path.exists(instance_db):
    conn = sqlite3.connect(instance_db)
    cursor = conn.cursor()
    
    # حذف جميع المستخدمين
    cursor.execute("DELETE FROM user")
    print("🗑️ تم حذف جميع المستخدمين من قاعدة البيانات الحقيقية")
    
    # إنشاء مدير جديد
    password_hash = generate_password_hash('admin123')
    cursor.execute("""
        INSERT INTO user (username, email, password, role, created_at)
        VALUES (?, ?, ?, ?, datetime('now'))
    """, ('admin', '<EMAIL>', password_hash, 'admin'))
    
    conn.commit()
    print("✅ تم إنشاء المدير الجديد في قاعدة البيانات الحقيقية")
    
    # التحقق
    cursor.execute("SELECT id, username, email, role FROM user")
    users = cursor.fetchall()
    print(f"👥 المستخدمين الجدد:")
    for user in users:
        print(f"  - ID: {user[0]}, اسم المستخدم: {user[1]}, البريد: {user[2]}, الدور: {user[3]}")
    
    # اختبار كلمة المرور
    cursor.execute("SELECT password FROM user WHERE email = ?", ('<EMAIL>',))
    stored_password = cursor.fetchone()[0]
    
    from werkzeug.security import check_password_hash
    if check_password_hash(stored_password, 'admin123'):
        print("✅ كلمة المرور صحيحة")
    else:
        print("❌ كلمة المرور خاطئة")
    
    conn.close()
else:
    print("❌ ملف قاعدة البيانات في instance غير موجود")

print("\n🌐 يمكنك الآن تسجيل الدخول:")
print("📧 البريد: <EMAIL>")
print("🔑 كلمة المرور: admin123")
