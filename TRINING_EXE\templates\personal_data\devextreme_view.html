{% extends "layout.html" %}

{% block styles %}
<!-- DevExtreme CSS -->
<link rel="stylesheet" href="https://cdn3.devexpress.com/jslib/23.1.5/css/dx.light.css">
<style>
    .dx-datagrid {
        direction: rtl;
    }
    .dx-datagrid-headers .dx-datagrid-table .dx-row > td {
        text-align: right;
    }
    .dx-rtl .dx-datagrid-rowsview .dx-datagrid-table .dx-row > td {
        text-align: right;
    }
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        display: none;
    }
    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 2s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    .toolbar-container {
        margin-bottom: 20px;
    }
    .dx-button {
        margin-left: 10px;
    }
    .dx-popup-title {
        text-align: right;
    }
    .dx-field-item-label-text {
        text-align: right;
    }
    .dx-field-item-content {
        text-align: right;
    }
    .dx-texteditor-input {
        text-align: right;
        padding: 10px;
        font-size: 16px;
    }
    /* تنسيق أزرار التنقل بين الصفحات */
    .dx-pager {
        padding: 10px;
        background-color: #f8f9fa;
        border-top: 1px solid #ddd;
    }
    .dx-pager .dx-page-sizes .dx-page-size {
        margin-left: 10px;
        font-weight: bold;
    }
    .dx-pager .dx-pages .dx-page {
        margin-left: 5px;
    }
    .dx-pager .dx-info {
        font-weight: bold;
    }
    /* توسيع حجم المربعات */
    .dx-texteditor {
        min-width: 250px;
        margin-bottom: 10px;
    }
    /* توسيع نافذة التحرير */
    .dx-popup-content {
        padding: 20px;
    }
    /* زيادة حجم حقل الاسم */
    .dx-texteditor.full-name-field {
        width: 100%;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">إدارة البيانات الشخصية - واجهة متطورة</h5>
        </div>
        <div class="card-body">
            <div class="toolbar-container">
                <div id="toolbar"></div>
            </div>
            <div id="dataGrid"></div>
        </div>
    </div>
</div>

<!-- مؤشر التحميل -->
<div class="loading-overlay">
    <div class="loading-spinner"></div>
</div>
{% endblock %}

{% block scripts %}
<!-- DevExtreme JS -->
<script src="https://cdn3.devexpress.com/jslib/23.1.5/js/dx.all.js"></script>

<script>
    $(document).ready(function() {
        // إنشاء مصدر البيانات
        var dataSource = new DevExpress.data.CustomStore({
            key: "id",
            load: function(loadOptions) {
                $('.loading-overlay').show();

                var d = $.Deferred();

                $.ajax({
                    url: "/personal_data/excel/data",
                    type: "GET",
                    dataType: "json",
                    cache: false
                }).done(function(response) {
                    $('.loading-overlay').hide();
                    // طباعة البيانات المستلمة للتشخيص
                    console.log("البيانات المستلمة من الخادم:", response);

                    // التحقق من البيانات
                    if (response && response.length > 0) {
                        console.log("أول عنصر:", response[0]);
                        if (!response[0].hasOwnProperty('id')) {
                            console.error("خطأ: حقل 'id' غير موجود في البيانات!");
                            // إضافة حقل id إذا كان مفقودًا
                            response.forEach(function(item, index) {
                                if (!item.hasOwnProperty('id')) {
                                    item.id = index + 1;
                                }
                            });
                        }
                    }

                    d.resolve(response);
                }).fail(function(xhr, status, error) {
                    $('.loading-overlay').hide();
                    console.error("خطأ في الاتصال بالخادم:", error);
                    DevExpress.ui.notify("حدث خطأ أثناء تحميل البيانات: " + error, "error", 3000);
                    d.reject([]);
                });

                return d.promise();
            },
            insert: function(values) {
                $('.loading-overlay').show();

                // طباعة البيانات المستلمة للتشخيص
                console.log("البيانات المستلمة للإضافة:", values);

                // التأكد من أن جميع الحقول لها قيم (حتى لو فارغة) بالترتيب المطلوب
                var completeValues = {
                    // 1. الاسم الشخصي (مطلوب)
                    full_name: values.full_name || "",
                    // 2. الاسم المستعار
                    nickname: values.nickname || "",
                    // 3. العمر
                    age: values.age || "",
                    // 4. المحافظة
                    governorate: values.governorate || "",
                    // 5. المديرية
                    directorate: values.directorate || "",
                    // 6. العزلة
                    uzla: values.uzla || "",
                    // 7. الحي/القرية
                    village: values.village || "",
                    // 8. المؤهل العلمي
                    qualification: values.qualification || "",
                    // 9. الحالة الاجتماعية
                    marital_status: values.marital_status || "",
                    // 10. العمل
                    job: values.job || "",
                    // 11. الإدارة
                    agency: values.agency || "",
                    // 12. مكان العمل
                    work_place: values.work_place || "",
                    // 13. الرقم الوطني
                    national_number: values.national_number || "",
                    // 14. الرقم العسكري
                    military_number: values.military_number || "",
                    // 15. رقم التلفون
                    phone: values.phone || ""
                };

                // طباعة البيانات المرسلة للتشخيص
                console.log("البيانات المرسلة للإضافة:", completeValues);

                // محاولة حفظ البيانات في قاعدة البيانات
                try {
                    return $.ajax({
                        url: "/personal_data/excel/add",
                        type: "POST",
                        data: completeValues
                    }).done(function(response) {
                        $('.loading-overlay').hide();
                        if (response && response.success) {
                            DevExpress.ui.notify("تم إضافة البيانات بنجاح", "success", 3000);
                            // تحديث الجدول
                            $("#dataGrid").dxDataGrid("instance").refresh();
                        } else {
                            // في حالة فشل الحفظ في قاعدة البيانات، نعرض رسالة ولكن نحتفظ بالبيانات في الجدول
                            console.log("تم إضافة البيانات محلياً ولكن فشل الحفظ في قاعدة البيانات");
                            DevExpress.ui.notify("تم إضافة البيانات محلياً", "warning", 3000);
                        }
                    }).fail(function(xhr) {
                        $('.loading-overlay').hide();
                        console.error("خطأ في حفظ البيانات:", xhr);
                        DevExpress.ui.notify("تم إضافة البيانات محلياً ولكن فشل الحفظ في قاعدة البيانات", "warning", 3000);
                    });
                } catch (e) {
                    $('.loading-overlay').hide();
                    console.error("استثناء أثناء حفظ البيانات:", e);
                    DevExpress.ui.notify("تم إضافة البيانات محلياً ولكن فشل الحفظ في قاعدة البيانات", "warning", 3000);
                    return $.Deferred().resolve();
                }
            },
            update: function(key, values) {
                $('.loading-overlay').show();

                // الحصول على البيانات الحالية
                var grid = $("#dataGrid").dxDataGrid("instance");
                var rowData = grid.getDataSource().items().find(function(item) {
                    return item.id === key;
                });

                // طباعة البيانات المستلمة للتشخيص
                console.log("البيانات المستلمة للتحديث:", values);

                // دمج البيانات الجديدة مع البيانات الحالية بالترتيب المطلوب
                var completeValues = {
                    id: key,
                    // 1. الاسم الشخصي (مطلوب)
                    full_name: values.full_name !== undefined ? values.full_name : (rowData ? rowData.full_name : ""),
                    // 2. الاسم المستعار
                    nickname: values.nickname !== undefined ? values.nickname : (rowData ? rowData.nickname : ""),
                    // 3. العمر
                    age: values.age !== undefined ? values.age : (rowData ? rowData.age : ""),
                    // 4. المحافظة
                    governorate: values.governorate !== undefined ? values.governorate : (rowData ? rowData.governorate : ""),
                    // 5. المديرية
                    directorate: values.directorate !== undefined ? values.directorate : (rowData ? rowData.directorate : ""),
                    // 6. العزلة
                    uzla: values.uzla !== undefined ? values.uzla : (rowData ? rowData.uzla : ""),
                    // 7. الحي/القرية
                    village: values.village !== undefined ? values.village : (rowData ? rowData.village : ""),
                    // 8. المؤهل العلمي
                    qualification: values.qualification !== undefined ? values.qualification : (rowData ? rowData.qualification : ""),
                    // 9. الحالة الاجتماعية
                    marital_status: values.marital_status !== undefined ? values.marital_status : (rowData ? rowData.marital_status : ""),
                    // 10. العمل
                    job: values.job !== undefined ? values.job : (rowData ? rowData.job : ""),
                    // 11. الإدارة
                    agency: values.agency !== undefined ? values.agency : (rowData ? rowData.agency : ""),
                    // 12. مكان العمل
                    work_place: values.work_place !== undefined ? values.work_place : (rowData ? rowData.work_place : ""),
                    // 13. الرقم الوطني
                    national_number: values.national_number !== undefined ? values.national_number : (rowData ? rowData.national_number : ""),
                    // 14. الرقم العسكري
                    military_number: values.military_number !== undefined ? values.military_number : (rowData ? rowData.military_number : ""),
                    // 15. رقم التلفون
                    phone: values.phone !== undefined ? values.phone : (rowData ? rowData.phone : "")
                };

                // طباعة البيانات المرسلة للتشخيص
                console.log("البيانات المرسلة للتحديث:", completeValues);

                // محاولة حفظ البيانات في قاعدة البيانات
                try {
                    return $.ajax({
                        url: "/personal_data/excel/" + key + "/update",
                        type: "POST",
                        data: completeValues
                    }).done(function(response) {
                        $('.loading-overlay').hide();
                        if (response && response.success) {
                            DevExpress.ui.notify("تم تحديث البيانات بنجاح", "success", 3000);
                            // تحديث الجدول
                            $("#dataGrid").dxDataGrid("instance").refresh();
                        } else {
                            // في حالة فشل الحفظ في قاعدة البيانات، نعرض رسالة ولكن نحتفظ بالبيانات في الجدول
                            console.log("تم تحديث البيانات محلياً ولكن فشل الحفظ في قاعدة البيانات");
                            DevExpress.ui.notify("تم تحديث البيانات محلياً", "warning", 3000);
                        }
                    }).fail(function(xhr) {
                        $('.loading-overlay').hide();
                        console.error("خطأ في تحديث البيانات:", xhr);
                        DevExpress.ui.notify("تم تحديث البيانات محلياً ولكن فشل الحفظ في قاعدة البيانات", "warning", 3000);
                    });
                } catch (e) {
                    $('.loading-overlay').hide();
                    console.error("استثناء أثناء تحديث البيانات:", e);
                    DevExpress.ui.notify("تم تحديث البيانات محلياً ولكن فشل الحفظ في قاعدة البيانات", "warning", 3000);
                    return $.Deferred().resolve();
                }
            },
            remove: function(key) {
                $('.loading-overlay').show();

                // محاولة حذف البيانات من قاعدة البيانات
                try {
                    return $.ajax({
                        url: "/personal_data/excel/" + key + "/delete",
                        type: "POST"
                    }).done(function(response) {
                        $('.loading-overlay').hide();
                        if (response && response.success) {
                            DevExpress.ui.notify("تم حذف البيانات بنجاح", "success", 3000);
                            // تحديث الجدول
                            $("#dataGrid").dxDataGrid("instance").refresh();
                        } else {
                            // في حالة فشل الحذف من قاعدة البيانات، نعرض رسالة ولكن نحذف البيانات من الجدول
                            console.log("تم حذف البيانات محلياً ولكن فشل الحذف من قاعدة البيانات");
                            DevExpress.ui.notify("تم حذف البيانات محلياً", "warning", 3000);
                        }
                    }).fail(function(xhr) {
                        $('.loading-overlay').hide();
                        console.error("خطأ في حذف البيانات:", xhr);
                        DevExpress.ui.notify("تم حذف البيانات محلياً ولكن فشل الحذف من قاعدة البيانات", "warning", 3000);
                    });
                } catch (e) {
                    $('.loading-overlay').hide();
                    console.error("استثناء أثناء حذف البيانات:", e);
                    DevExpress.ui.notify("تم حذف البيانات محلياً ولكن فشل الحذف من قاعدة البيانات", "warning", 3000);
                    return $.Deferred().resolve();
                }
            }
        });

        // إنشاء شريط الأدوات
        $("#toolbar").dxToolbar({
            items: [{
                widget: "dxButton",
                location: "after",
                options: {
                    icon: "plus",
                    text: "إضافة جديد",
                    type: "success",
                    onClick: function() {
                        $("#dataGrid").dxDataGrid("instance").addRow();
                    }
                }
            }, {
                widget: "dxButton",
                location: "after",
                options: {
                    icon: "refresh",
                    text: "تحديث",
                    onClick: function() {
                        $("#dataGrid").dxDataGrid("instance").refresh();
                    }
                }
            }, {
                widget: "dxButton",
                location: "after",
                options: {
                    icon: "exportxlsx",
                    text: "تصدير إلى إكسل",
                    onClick: function() {
                        window.location.href = "{{ url_for('export_personal_data_excel') }}";
                    }
                }
            }]
        });

        // إنشاء جدول البيانات
        $("#dataGrid").dxDataGrid({
            dataSource: dataSource,
            showBorders: true,
            rtlEnabled: true,
            columnAutoWidth: true,
            wordWrapEnabled: true,
            showRowLines: true,
            rowAlternationEnabled: true,
            hoverStateEnabled: true,
            paging: {
                pageSize: 10,
                enabled: true
            },
            pager: {
                visible: true,
                showPageSizeSelector: true,
                allowedPageSizes: [10, 25, 50, 100, "all"],
                showInfo: true,
                showNavigationButtons: true,
                infoText: "الصفحة {0} من {1} ({2} سجل)"
            },
            searchPanel: {
                visible: true,
                width: 240,
                placeholder: "بحث..."
            },
            editing: {
                mode: "popup",
                allowAdding: true,
                allowUpdating: true,
                allowDeleting: true,
                popup: {
                    title: "بيانات شخصية",
                    showTitle: true,
                    width: 900,
                    height: 700,
                    position: {
                        my: "center",
                        at: "center",
                        of: window
                    }
                },
                form: {
                    colCount: 2,
                    items: [
                        // 1. الاسم الشخصي (مطلوب)
                        {
                            dataField: "full_name",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled",
                                cssClass: "full-name-field"
                            }
                        },
                        // 2. الاسم المستعار
                        {
                            dataField: "nickname",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 3. العمر
                        {
                            dataField: "age",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 4. المحافظة
                        {
                            dataField: "governorate",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 5. المديرية
                        {
                            dataField: "directorate",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 6. العزلة
                        {
                            dataField: "uzla",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 7. الحي/القرية
                        {
                            dataField: "village",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 8. المؤهل العلمي
                        {
                            dataField: "qualification",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 9. الحالة الاجتماعية
                        {
                            dataField: "marital_status",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 10. العمل
                        {
                            dataField: "job",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 11. الإدارة
                        {
                            dataField: "agency",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 12. مكان العمل
                        {
                            dataField: "work_place",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 13. الرقم الوطني
                        {
                            dataField: "national_number",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 14. الرقم العسكري
                        {
                            dataField: "military_number",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        },
                        // 15. رقم التلفون
                        {
                            dataField: "phone",
                            editorOptions: {
                                width: "100%",
                                stylingMode: "filled"
                            }
                        }
                    ]
                }
            },
            columns: [
                {
                    dataField: "id",
                    caption: "المعرف",
                    width: 70,
                    visible: false
                },
                // 1. الاسم الشخصي (مطلوب)
                {
                    dataField: "full_name",
                    caption: "الاسم الشخصي",
                    width: 200,
                    validationRules: [{ type: "required" }],
                    cellTemplate: function(container, options) {
                        $("<div>")
                            .addClass("dx-field-item-content")
                            .css("font-weight", "bold")
                            .text(options.value)
                            .appendTo(container);
                    }
                },
                // 2. الاسم المستعار
                {
                    dataField: "nickname",
                    caption: "الاسم المستعار",
                    width: 150
                },
                // 3. العمر
                {
                    dataField: "age",
                    caption: "العمر",
                    dataType: "number",
                    width: 80,
                    alignment: "center"
                },
                // 4. المحافظة
                {
                    dataField: "governorate",
                    caption: "المحافظة",
                    width: 150
                },
                // 5. المديرية
                {
                    dataField: "directorate",
                    caption: "المديرية",
                    width: 150
                },
                // 6. العزلة
                {
                    dataField: "uzla",
                    caption: "العزلة",
                    width: 150
                },
                // 7. الحي/القرية
                {
                    dataField: "village",
                    caption: "الحي/القرية",
                    width: 150
                },
                // 8. المؤهل العلمي
                {
                    dataField: "qualification",
                    caption: "المؤهل العلمي",
                    width: 150
                },
                // 9. الحالة الاجتماعية
                {
                    dataField: "marital_status",
                    caption: "الحالة الاجتماعية",
                    width: 150
                },
                // 10. العمل
                {
                    dataField: "job",
                    caption: "العمل",
                    width: 150
                },
                // 11. الإدارة
                {
                    dataField: "agency",
                    caption: "الإدارة",
                    width: 150
                },
                // 12. مكان العمل
                {
                    dataField: "work_place",
                    caption: "مكان العمل",
                    width: 150
                },
                // 13. الرقم الوطني
                {
                    dataField: "national_number",
                    caption: "الرقم الوطني",
                    width: 150
                },
                // 14. الرقم العسكري
                {
                    dataField: "military_number",
                    caption: "الرقم العسكري",
                    width: 150
                },
                // 15. رقم التلفون
                {
                    dataField: "phone",
                    caption: "رقم التلفون",
                    width: 150
                }
            ],
            onInitNewRow: function(e) {
                e.data = {
                    id: "new"
                };
            },
            onRowUpdating: function(e) {
                // تجنب مشكلة الحقول الفارغة
                for (var field in e.newData) {
                    if (e.newData[field] === "") {
                        e.newData[field] = null;
                    }
                }
            },
            grouping: {
                autoExpandAll: false,
                allowCollapsing: true,
                enabled: false
            },
            groupPanel: {
                visible: false,
                emptyPanelText: "اسحب عمود هنا للتجميع"
            },
            filterRow: {
                visible: true,
                applyFilter: "auto"
            },
            headerFilter: {
                visible: true
            },
            columnChooser: {
                enabled: true,
                mode: "select",
                title: "اختيار الأعمدة",
                emptyPanelText: "اسحب عمود هنا لإخفائه"
            },
            columnFixing: {
                enabled: true
            },
            stateStoring: {
                enabled: true,
                type: "localStorage",
                storageKey: "personal_data_grid_state"
            },
            export: {
                enabled: true,
                allowExportSelectedData: true
            },
            selection: {
                mode: "multiple"
            },
            summary: {
                totalItems: [{
                    column: "full_name",
                    summaryType: "count",
                    displayFormat: "العدد الإجمالي: {0}"
                }]
            },
            onEditorPreparing: function(e) {
                if (e.dataField === "full_name") {
                    e.editorOptions.width = "100%";
                    e.editorOptions.height = 40;
                }
            },
            onCellPrepared: function(e) {
                if (e.rowType === "data" && e.column.dataField === "full_name") {
                    e.cellElement.css("font-weight", "bold");
                }
            }
        });
    });
</script>
{% endblock %}
