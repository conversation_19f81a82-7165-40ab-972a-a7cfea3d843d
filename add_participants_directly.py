#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from app import app, db, CourseParticipant, Course, PersonData
from datetime import datetime

def add_participants_directly():
    with app.app_context():
        print("🔧 إضافة المشاركين مباشرة...")
        print("=" * 60)
        
        # البحث عن الدورة الموجودة (رقم 464)
        course = Course.query.get(1)  # الدورة الوحيدة الموجودة
        
        if not course:
            print("❌ لم يتم العثور على أي دورة")
            return
        
        print(f"✅ تم العثور على الدورة:")
        print(f"   ID: {course.id}")
        print(f"   رقم الدورة: {course.course_number}")
        print(f"   اسم الدورة: {course.title}")
        
        # تحديث رقم الدورة إلى 3455667
        course.course_number = '3455667'
        course.title = 'المبيعات'
        
        # إنشاء أسماء المشاركين الإضافيين (لنصل إلى 14 مشارك)
        additional_participants = [
            "محمد أحمد علي الحميري",
            "سارة محمد قاسم الزهراني", 
            "خالد عبدالله صالح المقطري",
            "نورا أحمد محمد العامري",
            "عبدالرحمن علي أحمد الشهري",
            "فاطمة قاسم عبدالله الحربي",
            "أحمد محمد علي القحطاني",
            "مريم عبدالله أحمد الدوسري",
            "سعد علي محمد الغامدي",
            "هند أحمد قاسم العتيبي",
            "عبدالعزيز محمد علي الرشيد",
            "نوال قاسم عبدالله الخالدي"
        ]
        
        print(f"\n👥 إضافة المشاركين الجدد...")
        added_count = 0
        
        for name in additional_participants:
            # التحقق من وجود الشخص في قاعدة البيانات
            person = PersonData.query.filter_by(full_name=name).first()
            
            if not person:
                # إنشاء شخص جديد
                person = PersonData(full_name=name)
                db.session.add(person)
                db.session.flush()
                print(f"   ✅ تم إنشاء شخص جديد: {name}")
            else:
                print(f"   📋 الشخص موجود: {name}")
            
            # التحقق من عدم وجود المشارك في الدورة مسبقاً
            existing_participant = CourseParticipant.query.filter_by(
                course_id=course.id,
                personal_data_id=person.id
            ).first()
            
            if not existing_participant:
                # إضافة المشارك للدورة
                participant = CourseParticipant(
                    course_id=course.id,
                    personal_data_id=person.id,
                    status='active',
                    entry_date=datetime.now()
                )
                db.session.add(participant)
                added_count += 1
                print(f"   ➕ تم إضافة {name} للدورة")
            else:
                print(f"   ⚠️ {name} مضاف مسبقاً للدورة")
        
        # تحديث عدد المشاركين في الدورة
        total_participants = CourseParticipant.query.filter_by(course_id=course.id).count()
        course.total_participants = total_participants
        
        # حفظ جميع التغييرات
        db.session.commit()
        
        print(f"\n✅ تم الانتهاء!")
        print(f"   رقم الدورة الجديد: {course.course_number}")
        print(f"   اسم الدورة الجديد: {course.title}")
        print(f"   إجمالي المشاركين في الدورة: {course.total_participants}")
        print(f"   المشاركين المضافين الجدد: {added_count}")
        print(f"   رابط الدورة: http://127.0.0.1:5000/course/{course.id}/participants")

if __name__ == '__main__':
    add_participants_directly()
