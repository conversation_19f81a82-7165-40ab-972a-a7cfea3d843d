{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<style>
    .stats-card {
        background: #007bff;
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        margin-bottom: 20px;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin: 10px 0;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .result-section {
        background: white;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .section-header {
        background: #6c757d;
        color: white;
        padding: 15px 20px;
        margin-bottom: 0;
        font-weight: 600;
    }

    .name-item {
        background: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 10px;
        border-right: 4px solid #28a745;
    }

    .corrected-name {
        border-right-color: #ffc107;
        background: #fff3cd;
    }

    .similar-name {
        border-right-color: #17a2b8;
        background: #d1ecf1;
    }

    .new-name {
        border-right-color: #dc3545;
        background: #f8d7da;
    }

    .btn-export {
        background: #28a745;
        border: none;
        padding: 10px 20px;
        color: white;
        border-radius: 5px;
    }

    .btn-export:hover {
        background: #218838;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="text-primary">
                        <i class="fas fa-database"></i> نتائج تحليل البيانات الشخصية
                    </h1>
                    <p class="text-muted">
                        <i class="fas fa-file-excel"></i> الملف: {{ excel_filename }}
                    </p>
                </div>
                    <div>
                        <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-export me-2">
                            <i class="fas fa-download"></i> تصدير النتائج الكاملة
                        </a>
                        <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-success me-2">
                            <i class="fas fa-file-excel"></i> تصدير الأسماء الجديدة فقط
                        </a>
                        {% if results.new_records or results.corrected_names %}
                        <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-warning me-2"
                                    onclick="return confirm('هل تريد استيراد البيانات الجديدة إلى قاعدة البيانات؟')">
                                <i class="fas fa-database"></i> إضافة للقاعدة
                            </button>
                        </form>
                        {% endif %}
                        <a href="{{ url_for('person_data.name_analysis') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> تحليل جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number">{{ results.statistics.get('total_processed', 0) }}</div>
                    <div class="stat-label">إجمالي السجلات المعالجة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number">{{ results.statistics.get('corrected_count', 0) }}</div>
                    <div class="stat-label">الأسماء المصححة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number">{{ results.statistics.get('exact_matches_count', 0) }}</div>
                    <div class="stat-label">موجود في قاعدة البيانات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number">{{ results.statistics.get('new_records_count', 0) }}</div>
                    <div class="stat-label">غير موجود في قاعدة البيانات</div>
                </div>
            </div>
        </div>

        <!-- Advanced Duplicate Detection Cards -->
        {% if results.statistics.get('has_national_id_column', False) or results.statistics.get('has_phone_column', False) or results.statistics.get('has_military_id_column', False) %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-search"></i> نتائج فحص التطابق المتقدم
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="card text-center border-danger">
                                    <div class="card-body">
                                        <h5 class="text-danger">{{ results.statistics.get('blocked_duplicates_count', 0) }}</h5>
                                        <small class="text-muted">سجلات مرفوضة</small>
                                        <br><small class="text-danger">تطابق في البيانات</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card text-center border-success">
                                    <div class="card-body">
                                        <h5 class="text-success">{{ results.statistics.get('allowed_duplicates_count', 0) }}</h5>
                                        <small class="text-muted">أسماء مكررة مسموحة</small>
                                        <br><small class="text-success">بيانات مختلفة</small>
                                    </div>
                                </div>
                            </div>
                            {% if results.statistics.get('has_national_id_column', False) %}
                            <div class="col-md-2">
                                <div class="card text-center border-info">
                                    <div class="card-body">
                                        <h5 class="text-info">{{ results.statistics.get('name_national_id_matches', 0) + results.statistics.get('national_id_only_matches', 0) }}</h5>
                                        <small class="text-muted">تطابق رقم وطني</small>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            {% if results.statistics.get('has_phone_column', False) %}
                            <div class="col-md-2">
                                <div class="card text-center border-info">
                                    <div class="card-body">
                                        <h5 class="text-info">{{ results.statistics.get('name_phone_matches', 0) + results.statistics.get('phone_only_matches', 0) }}</h5>
                                        <small class="text-muted">تطابق رقم هاتف</small>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            {% if results.statistics.get('has_military_id_column', False) %}
                            <div class="col-md-2">
                                <div class="card text-center border-info">
                                    <div class="card-body">
                                        <h5 class="text-info">{{ results.statistics.get('name_military_id_matches', 0) + results.statistics.get('military_id_only_matches', 0) }}</h5>
                                        <small class="text-muted">تطابق رقم عسكري</small>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Similarity Statistics -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="text-info">{{ results.statistics.get('triple_similarity_count', 0) }}</h5>
                        <small class="text-muted">تشابه ثلاثي</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="text-info">{{ results.statistics.get('quadruple_similarity_count', 0) }}</h5>
                        <small class="text-muted">تشابه رباعي</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="text-info">{{ results.statistics.get('quintuple_similarity_count', 0) }}</h5>
                        <small class="text-muted">تشابه خماسي</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="text-info">{{ results.statistics.get('full_with_title_count', 0) }}</h5>
                        <small class="text-muted">تشابه كامل</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="text-info">{{ results.statistics.get('six_plus_count', 0) }}</h5>
                        <small class="text-muted">أسماء طويلة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="text-success">{{ results.total_db_names }}</h5>
                        <small class="text-muted">في قاعدة البيانات</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Corrected Names Section -->
        {% if results.corrected_names %}
        <div class="result-section">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-spell-check"></i> الأسماء المصححة
                    <span class="badge bg-warning">{{ results.corrected_names|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="row">
                    {% for correction in results.corrected_names[:10] %}
                    <div class="col-md-6 mb-2">
                        <div class="name-item corrected-name">
                            <strong>الأصلي:</strong> {{ correction.original }}<br>
                            <strong>المصحح:</strong> <span class="text-success">{{ correction.corrected }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if results.corrected_names|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 أسماء فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Exact Matches Section -->
        {% if results.exact_matches %}
        <div class="result-section">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-check-circle"></i> موجود في قاعدة البيانات
                    <span class="badge bg-success">{{ results.exact_matches|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="row">
                    {% for match in results.exact_matches[:10] %}
                    <div class="col-md-12 mb-3">
                        <div class="card border-success">
                            <div class="card-body p-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-success mb-2">
                                            <i class="fas fa-check-circle"></i> من ملف Excel:
                                        </h6>
                                        <p class="mb-1"><strong>{{ match.excel_name }}</strong></p>
                                        <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                                        {% if match.excel_record.national_id %}
                                        <br><small class="badge bg-info">رقم وطني: {{ match.excel_record.national_id }}</small>
                                        {% endif %}
                                        {% if match.excel_record.phone %}
                                        <br><small class="badge bg-success">هاتف: {{ match.excel_record.phone }}</small>
                                        {% endif %}
                                        {% if match.excel_record.military_id %}
                                        <br><small class="badge bg-warning">عسكري: {{ match.excel_record.military_id }}</small>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-primary mb-2">
                                            <i class="fas fa-database"></i> من قاعدة البيانات:
                                        </h6>
                                        <p class="mb-1"><strong>{{ match.db_name }}</strong></p>
                                        <small class="text-muted">ID: {{ match.db_record.id }}</small>
                                        {% if match.db_record.national_id %}
                                        <br><small class="badge bg-info">رقم وطني: {{ match.db_record.national_id }}</small>
                                        {% endif %}
                                        {% if match.db_record.phone %}
                                        <br><small class="badge bg-success">هاتف: {{ match.db_record.phone }}</small>
                                        {% endif %}
                                        {% if match.db_record.military_id %}
                                        <br><small class="badge bg-warning">عسكري: {{ match.db_record.military_id }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if results.exact_matches|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 مطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Blocked Duplicates Section -->
        {% if results.blocked_duplicates %}
        <div class="result-section">
            <div class="section-header" style="background: linear-gradient(45deg, #dc3545, #c82333);">
                <h4 class="mb-0">
                    <i class="fas fa-ban"></i> السجلات المرفوضة (تطابق في البيانات الشخصية)
                    <span class="badge bg-light text-dark">{{ results.blocked_duplicates|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تحذير:</strong> هذه السجلات لن يتم استيرادها بسبب التطابق في البيانات الشخصية
                </div>
                {% for blocked in results.blocked_duplicates[:10] %}
                <div class="card mb-3 border-danger">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">السجل من Excel:</h6>
                                <p><strong>الاسم:</strong> {{ blocked.corrected_name }}</p>
                                {% if blocked.excel_record.national_id %}
                                <p><strong>الرقم الوطني:</strong> {{ blocked.excel_record.national_id }}</p>
                                {% endif %}
                                {% if blocked.excel_record.phone %}
                                <p><strong>الهاتف:</strong> {{ blocked.excel_record.phone }}</p>
                                {% endif %}
                                {% if blocked.excel_record.military_id %}
                                <p><strong>الرقم العسكري:</strong> {{ blocked.excel_record.military_id }}</p>
                                {% endif %}
                                <small class="text-muted">الصف: {{ blocked.excel_record.row_index }}</small>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-warning">السجل المطابق في قاعدة البيانات:</h6>
                                <p><strong>الاسم:</strong> {{ blocked.db_match.name }}</p>
                                {% if blocked.db_match.national_id %}
                                <p><strong>الرقم الوطني:</strong> {{ blocked.db_match.national_id }}</p>
                                {% endif %}
                                {% if blocked.db_match.phone %}
                                <p><strong>الهاتف:</strong> {{ blocked.db_match.phone }}</p>
                                {% endif %}
                                {% if blocked.db_match.military_id %}
                                <p><strong>الرقم العسكري:</strong> {{ blocked.db_match.military_id }}</p>
                                {% endif %}
                                <small class="text-muted">ID: {{ blocked.db_match.id }}</small>
                            </div>
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-danger">{{ blocked.reason }}</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% if results.blocked_duplicates|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 سجلات مرفوضة فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- New Records Section -->
        {% if results.new_records %}
        <div class="result-section">
            <div class="section-header" style="background: linear-gradient(45deg, #28a745, #20c997);">
                <h4 class="mb-0">
                    <i class="fas fa-plus-circle"></i> غير موجود في قاعدة البيانات - سيتم إضافته
                    <span class="badge bg-light text-dark">{{ results.new_records|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="alert alert-success">
                    <i class="fas fa-info-circle"></i>
                    <strong>إجراء:</strong> هذه السجلات جديدة وسيتم إضافتها إلى قاعدة البيانات
                </div>
                {% for record in results.new_records[:10] %}
                <div class="card mb-2 border-success">
                    <div class="card-body p-2">
                        <div class="row">
                            <div class="col-md-8">
                                <strong class="text-success">{{ record.corrected_name }}</strong>
                                <small class="text-muted d-block">الصف: {{ record.row_index }}</small>
                                {% if record.national_id %}
                                <small class="badge bg-info me-1">رقم وطني: {{ record.national_id }}</small>
                                {% endif %}
                                {% if record.phone %}
                                <small class="badge bg-success me-1">هاتف: {{ record.phone }}</small>
                                {% endif %}
                                {% if record.military_id %}
                                <small class="badge bg-warning me-1">عسكري: {{ record.military_id }}</small>
                                {% endif %}
                            </div>
                            <div class="col-md-4 text-end">
                                <span class="badge bg-success">سجل جديد</span>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% if results.new_records|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 سجلات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Similar Names Section -->
        {% if results.similar_names %}
        <div class="result-section">
            <div class="section-header" style="background: linear-gradient(45deg, #17a2b8, #6c757d);">
                <h4 class="mb-0">
                    <i class="fas fa-search"></i> أسماء متشابهة
                    <span class="badge bg-light text-dark">{{ results.similar_names|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> هذه الأسماء متشابهة مع أسماء موجودة في قاعدة البيانات
                </div>
                {% for similar in results.similar_names[:10] %}
                <div class="card mb-3 border-info">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-info">من ملف Excel:</h6>
                                <p><strong>{{ similar.excel_name }}</strong></p>
                                <small class="text-muted">الصف: {{ similar.excel_record.row_index }}</small>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">أسماء متشابهة في قاعدة البيانات:</h6>
                                {% for match in similar.similar_matches[:3] %}
                                <p class="mb-1">
                                    <strong>{{ match.name }}</strong>
                                    <small class="text-muted">(ID: {{ match.id }})</small>
                                    <span class="badge bg-info">{{ similar.similarity_type }}</span>
                                </p>
                                {% endfor %}
                                {% if similar.similar_matches|length > 3 %}
                                <small class="text-muted">و {{ similar.similar_matches|length - 3 }} أسماء أخرى...</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% if results.similar_names|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 أسماء متشابهة فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Advanced Duplicate Matches Sections -->
        {% for match_type, matches in results.duplicate_matches.items() %}
        {% if matches %}
        <div class="result-section">
            <div class="section-header" style="background: linear-gradient(45deg, #fd7e14, #e55a4e);">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    {% if match_type == 'name_national_id' %}
                    تطابق في الاسم والرقم الوطني
                    {% elif match_type == 'name_phone' %}
                    تطابق في الاسم ورقم الهاتف
                    {% elif match_type == 'name_military_id' %}
                    تطابق في الاسم والرقم العسكري
                    {% elif match_type == 'national_id_only' %}
                    تطابق في الرقم الوطني فقط (أسماء مختلفة)
                    {% elif match_type == 'phone_only' %}
                    تطابق في رقم الهاتف فقط (أسماء مختلفة)
                    {% elif match_type == 'military_id_only' %}
                    تطابق في الرقم العسكري فقط (أسماء مختلفة)
                    {% endif %}
                    <span class="badge bg-light text-dark">{{ matches|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i>
                    <strong>تنبيه:</strong> {{ matches[0].match_details if matches }}
                </div>
                {% for match in matches[:10] %}
                <div class="card mb-3 border-warning">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">السجل من Excel:</h6>
                                <p><strong>الاسم:</strong> {{ match.excel_record.corrected_name }}</p>
                                {% if match.excel_record.national_id %}
                                <p><strong>الرقم الوطني:</strong> {{ match.excel_record.national_id }}</p>
                                {% endif %}
                                {% if match.excel_record.phone %}
                                <p><strong>الهاتف:</strong> {{ match.excel_record.phone }}</p>
                                {% endif %}
                                {% if match.excel_record.military_id %}
                                <p><strong>الرقم العسكري:</strong> {{ match.excel_record.military_id }}</p>
                                {% endif %}
                                <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-warning">السجل المطابق في قاعدة البيانات:</h6>
                                <p><strong>الاسم:</strong> {{ match.db_record.name }}</p>
                                {% if match.db_record.national_id %}
                                <p><strong>الرقم الوطني:</strong> {{ match.db_record.national_id }}</p>
                                {% endif %}
                                {% if match.db_record.phone %}
                                <p><strong>الهاتف:</strong> {{ match.db_record.phone }}</p>
                                {% endif %}
                                {% if match.db_record.military_id %}
                                <p><strong>الرقم العسكري:</strong> {{ match.db_record.military_id }}</p>
                                {% endif %}
                                <small class="text-muted">ID: {{ match.db_record.id }}</small>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% if matches|length > 10 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 10 مطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}
        {% endfor %}

        <!-- Similarity Sections -->
        {% for similarity_type, similarity_data in results.similarity_matches.items() %}
        {% if similarity_data and similarity_type != 'six_plus' %}
        <div class="result-section">
            <div class="section-header">
                <h4 class="mb-0">
                    <i class="fas fa-layer-group"></i>
                    {% if similarity_type == 'triple' %}التشابه الثلاثي{% endif %}
                    {% if similarity_type == 'quadruple' %}التشابه الرباعي{% endif %}
                    {% if similarity_type == 'quintuple' %}التشابه الخماسي{% endif %}
                    {% if similarity_type == 'full_with_title' %}التشابه الكامل مع اللقب{% endif %}
                    <span class="badge bg-info">{{ similarity_data|length }}</span>
                </h4>
            </div>
            <div class="p-3">
                {% for match in similarity_data[:5] %}
                <div class="name-item similar-name mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>من Excel:</strong> {{ match.excel_name }}<br>
                            <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                        </div>
                        <div class="col-md-6">
                            <strong>متشابه مع:</strong>
                            {% for similar in match.similar_names[:3] %}
                            <br>• {{ similar.name }} <small class="text-muted">(ID: {{ similar.id }})</small>
                            {% endfor %}
                            {% if match.similar_names|length > 3 %}
                            <br><small class="text-info">و {{ match.similar_names|length - 3 }} أسماء أخرى...</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% if similarity_data|length > 5 %}
                <p class="text-muted text-center mt-3">
                    <i class="fas fa-info-circle"></i>
                    يتم عرض أول 5 مطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
                </p>
                {% endif %}
            </div>
        </div>
        {% endif %}
        {% endfor %}

        <!-- Export and Import Options Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-gradient-primary text-white text-center">
                        <h5 class="mb-0">
                            <i class="fas fa-tools"></i> خيارات التصدير والاستيراد للبيانات الشخصية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <!-- إضافة البيانات الجديدة -->
                            <div class="col-lg-4">
                                <div class="text-center p-4 border rounded-3 h-100" style="background: linear-gradient(135deg, #ffeaa7, #fdcb6e);">
                                    <div class="mb-3">
                                        <i class="fas fa-database fa-3x text-warning"></i>
                                    </div>
                                    <h6 class="fw-bold">إضافة البيانات الجديدة</h6>
                                    <p class="small text-muted mb-3">
                                        إضافة الأشخاص الجدد مع بياناتهم
                                        إلى قاعدة البيانات الرئيسية
                                    </p>
                                    {% if results.new_records %}
                                    <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-warning fw-bold"
                                                onclick="return confirm('هل تريد إضافة {{ results.new_records|length }} شخص جديد إلى قاعدة البيانات؟')">
                                            <i class="fas fa-plus-circle"></i> إضافة الأشخاص والبيانات
                                        </button>
                                    </form>
                                    {% else %}
                                    <button class="btn btn-secondary" disabled>
                                        <i class="fas fa-ban"></i> لا توجد بيانات جديدة
                                    </button>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- التقرير الكامل للبيانات -->
                            <div class="col-lg-4">
                                <div class="text-center p-4 border rounded-3 h-100" style="background: linear-gradient(135deg, #a8e6cf, #81c784);">
                                    <div class="mb-3">
                                        <i class="fas fa-file-excel fa-3x text-success"></i>
                                    </div>
                                    <h6 class="fw-bold">التقرير الكامل للبيانات</h6>
                                    <p class="small text-muted mb-3">
                                        تصدير تقرير شامل يحتوي على جميع
                                        النتائج والإحصائيات والتفاصيل
                                    </p>
                                    <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-success fw-bold">
                                        <i class="fas fa-download"></i> تحميل
                                    </a>
                                </div>
                            </div>

                            <!-- التقرير المختصر للبيانات الجديدة -->
                            <div class="col-lg-4">
                                <div class="text-center p-4 border rounded-3 h-100" style="background: linear-gradient(135deg, #fab1a0, #e17055);">
                                    <div class="mb-3">
                                        <i class="fas fa-file-download fa-3x text-danger"></i>
                                    </div>
                                    <h6 class="fw-bold">التقرير المختصر للبيانات الجديدة</h6>
                                    <p class="small text-muted mb-3">
                                        تصدير ملف يحتوي على البيانات الجديدة
                                        فقط مع جميع الإحصائيات والتفاصيل
                                    </p>
                                    <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-danger fw-bold">
                                        <i class="fas fa-file-excel"></i> تحميل
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Bar for Data Analysis -->
                        <div class="mt-4">
                            <h6 class="text-center mb-3">
                                <i class="fas fa-chart-pie"></i> ملخص تحليل البيانات بالنسب المئوية
                            </h6>
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="progress mb-2" style="height: 20px;">
                                        {% set found_percentage = (results.statistics.exact_matches_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                        <div class="progress-bar bg-info" style="width: {{ found_percentage }}%"></div>
                                    </div>
                                    <small class="fw-bold text-info">{{ "%.1f"|format(found_percentage) }}%</small><br>
                                    <small class="text-muted">بيانات موجودة</small>
                                </div>
                                <div class="col-3">
                                    <div class="progress mb-2" style="height: 20px;">
                                        {% set new_percentage = (results.statistics.new_records_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                        <div class="progress-bar bg-success" style="width: {{ new_percentage }}%"></div>
                                    </div>
                                    <small class="fw-bold text-success">{{ "%.1f"|format(new_percentage) }}%</small><br>
                                    <small class="text-muted">بيانات جديدة جاهزة</small>
                                </div>
                                <div class="col-3">
                                    <div class="progress mb-2" style="height: 20px;">
                                        {% set corrected_percentage = (results.statistics.corrected_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                        <div class="progress-bar bg-warning" style="width: {{ corrected_percentage }}%"></div>
                                    </div>
                                    <small class="fw-bold text-warning">{{ "%.1f"|format(corrected_percentage) }}%</small><br>
                                    <small class="text-muted">أسماء تم تصحيحها</small>
                                </div>
                                <div class="col-3">
                                    <div class="progress mb-2" style="height: 20px;">
                                        {% set blocked_percentage = (results.statistics.blocked_duplicates_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                        <div class="progress-bar bg-danger" style="width: {{ blocked_percentage }}%"></div>
                                    </div>
                                    <small class="fw-bold text-danger">{{ "%.1f"|format(blocked_percentage) }}%</small><br>
                                    <small class="text-muted">بيانات مكررة مرفوضة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
{% endblock %}
