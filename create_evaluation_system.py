#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء نظام تقييم المشاركين
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import Column, Integer, String, Text, Float, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime, timezone

def create_evaluation_tables():
    """إنشاء جداول نظام التقييم"""
    
    with app.app_context():
        print("🔧 إنشاء جداول نظام التقييم...")
        
        # إنشاء الجداول باستخدام SQL مباشرة
        
        # جدول معايير التقييم
        db.engine.execute("""
        CREATE TABLE IF NOT EXISTS evaluation_criteria (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            max_score FLOAT NOT NULL DEFAULT 100.0,
            weight FLOAT NOT NULL DEFAULT 1.0,
            category VARCHAR(50),
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # جدول تقييمات المشاركين
        db.engine.execute("""
        CREATE TABLE IF NOT EXISTS participant_evaluations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            course_id INTEGER NOT NULL,
            participant_id INTEGER NOT NULL,
            evaluator_id INTEGER NOT NULL,
            evaluation_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            total_score FLOAT,
            percentage FLOAT,
            grade VARCHAR(10),
            notes TEXT,
            is_final BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES course (id),
            FOREIGN KEY (participant_id) REFERENCES course_participant (id),
            FOREIGN KEY (evaluator_id) REFERENCES user (id)
        )
        """)
        
        # جدول تفاصيل التقييم
        db.engine.execute("""
        CREATE TABLE IF NOT EXISTS evaluation_details (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            evaluation_id INTEGER NOT NULL,
            criteria_id INTEGER NOT NULL,
            score FLOAT NOT NULL,
            notes TEXT,
            FOREIGN KEY (evaluation_id) REFERENCES participant_evaluations (id),
            FOREIGN KEY (criteria_id) REFERENCES evaluation_criteria (id)
        )
        """)
        
        # جدول قوالب التقييم
        db.engine.execute("""
        CREATE TABLE IF NOT EXISTS evaluation_templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            course_type VARCHAR(50),
            is_default BOOLEAN DEFAULT 0,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES user (id)
        )
        """)
        
        # جدول ربط القوالب بالمعايير
        db.engine.execute("""
        CREATE TABLE IF NOT EXISTS template_criteria (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            template_id INTEGER NOT NULL,
            criteria_id INTEGER NOT NULL,
            order_index INTEGER DEFAULT 0,
            FOREIGN KEY (template_id) REFERENCES evaluation_templates (id),
            FOREIGN KEY (criteria_id) REFERENCES evaluation_criteria (id)
        )
        """)
        
        print("✅ تم إنشاء جداول نظام التقييم")
        
        # إضافة معايير التقييم الأساسية
        print("📝 إضافة معايير التقييم الأساسية...")
        
        criteria_data = [
            ('الحضور والانضباط', 'التزام المشارك بالحضور والمواعيد', 20.0, 1.0, 'سلوكي'),
            ('المشاركة والتفاعل', 'مستوى مشاركة المشارك في الأنشطة والنقاشات', 20.0, 1.0, 'سلوكي'),
            ('الاختبار النظري', 'درجة الاختبار النظري للمادة العلمية', 30.0, 1.5, 'أكاديمي'),
            ('الاختبار العملي', 'درجة الاختبار العملي والتطبيقي', 30.0, 1.5, 'أكاديمي'),
            ('التقييم الشامل', 'التقييم العام لأداء المشارك', 100.0, 1.0, 'عام'),
            ('المهارات الشخصية', 'تقييم المهارات الشخصية والتواصل', 15.0, 1.0, 'مهاري'),
            ('القيادة والعمل الجماعي', 'قدرة المشارك على القيادة والعمل ضمن فريق', 15.0, 1.0, 'مهاري'),
            ('الإبداع والابتكار', 'مستوى الإبداع والابتكار في الحلول المقترحة', 10.0, 1.0, 'مهاري'),
            ('التطبيق العملي', 'قدرة المشارك على تطبيق ما تعلمه عملياً', 25.0, 1.2, 'تطبيقي'),
            ('المشروع النهائي', 'تقييم المشروع أو البحث النهائي', 35.0, 1.3, 'تطبيقي')
        ]
        
        for name, desc, max_score, weight, category in criteria_data:
            db.engine.execute("""
            INSERT OR IGNORE INTO evaluation_criteria 
            (name, description, max_score, weight, category) 
            VALUES (?, ?, ?, ?, ?)
            """, (name, desc, max_score, weight, category))
        
        print("✅ تم إضافة معايير التقييم")
        
        # إنشاء قالب تقييم افتراضي
        print("📋 إنشاء قالب التقييم الافتراضي...")
        
        db.engine.execute("""
        INSERT OR IGNORE INTO evaluation_templates 
        (name, description, course_type, is_default) 
        VALUES ('القالب الأساسي', 'قالب التقييم الأساسي للدورات العامة', 'عام', 1)
        """)
        
        # ربط المعايير بالقالب الافتراضي
        template_criteria_data = [
            (1, 1, 1),  # الحضور والانضباط
            (1, 2, 2),  # المشاركة والتفاعل
            (1, 3, 3),  # الاختبار النظري
            (1, 4, 4),  # الاختبار العملي
            (1, 6, 5),  # المهارات الشخصية
        ]
        
        for template_id, criteria_id, order_index in template_criteria_data:
            db.engine.execute("""
            INSERT OR IGNORE INTO template_criteria 
            (template_id, criteria_id, order_index) 
            VALUES (?, ?, ?)
            """, (template_id, criteria_id, order_index))
        
        print("✅ تم إنشاء القالب الافتراضي")
        print("🎉 تم إنشاء نظام التقييم بنجاح!")

if __name__ == "__main__":
    create_evaluation_tables()
