#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار مباشر لـ route البحث
"""

import requests
import time

def test_route_direct():
    """اختبار مباشر لـ route البحث"""
    print("🔍 اختبار مباشر لـ route البحث...")
    
    urls_to_test = [
        "http://localhost:5001/course/1/search_people?q=علي",
        "http://localhost:5001/manage_participants/1/",
        "http://localhost:5001/",
        "http://localhost:5002/course/1/search_people?q=علي"
    ]
    
    for url in urls_to_test:
        print(f"\n🌐 اختبار: {url}")
        try:
            response = requests.get(url, timeout=10)
            print(f"   📡 رمز الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                if 'search_people' in url:
                    try:
                        data = response.json()
                        if isinstance(data, list):
                            print(f"   ✅ نتائج البحث: {len(data)} شخص")
                            
                            # البحث عن "علي صالح محمد الحميري1"
                            found_ali = any("علي صالح محمد الحميري1" in str(item.get('name', '')) for item in data)
                            if found_ali:
                                print("   ❌ تم العثور على 'علي صالح محمد الحميري1' في النتائج!")
                            else:
                                print("   ✅ لم يتم العثور على 'علي صالح محمد الحميري1' - الاستثناء يعمل!")
                        else:
                            print(f"   📄 استجابة غير متوقعة: {type(data)}")
                    except Exception as e:
                        print(f"   📄 استجابة نصية: {response.text[:100]}...")
                else:
                    print(f"   ✅ الصفحة تعمل")
            else:
                print(f"   ❌ خطأ: {response.status_code}")
                print(f"   📄 الاستجابة: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   ❌ خطأ في الطلب: {e}")
        
        time.sleep(1)

def test_with_different_queries():
    """اختبار بكلمات بحث مختلفة"""
    print("\n🔍 اختبار بكلمات بحث مختلفة...")
    
    queries = ['علي', 'محمد', 'فاطمة', 'أحمد']
    
    for query in queries:
        print(f"\n🔍 البحث عن: '{query}'")
        
        # اختبار التطبيق الأصلي
        try:
            response = requests.get(f"http://localhost:5001/course/1/search_people?q={query}", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"   📊 التطبيق الأصلي: {len(data)} نتيجة")
            else:
                print(f"   ❌ التطبيق الأصلي: خطأ {response.status_code}")
        except Exception as e:
            print(f"   ❌ التطبيق الأصلي: {e}")
        
        # اختبار التطبيق البسيط
        try:
            response = requests.get(f"http://localhost:5002/course/1/search_people?q={query}", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"   📊 التطبيق البسيط: {len(data)} نتيجة")
            else:
                print(f"   ❌ التطبيق البسيط: خطأ {response.status_code}")
        except Exception as e:
            print(f"   ❌ التطبيق البسيط: {e}")
        
        time.sleep(0.5)

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار مباشر شامل للـ routes")
    print("=" * 50)
    
    # اختبار routes مباشرة
    test_route_direct()
    
    # اختبار بكلمات مختلفة
    test_with_different_queries()
    
    print("\n" + "=" * 50)
    print("🎉 انتهى الاختبار المباشر!")

if __name__ == "__main__":
    main()
