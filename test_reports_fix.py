#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح التقارير
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Course, CourseParticipant, TrainingCenter, Agency
import requests
import json

def test_reports_api():
    """اختبار API التقارير"""
    
    with app.app_context():
        print("🔍 اختبار API التقارير...")
        
        # فحص البيانات الموجودة
        total_courses = Course.query.count()
        total_participants = CourseParticipant.query.count()
        total_centers = TrainingCenter.query.count()
        total_agencies = Agency.query.count()
        
        print(f"📊 البيانات الموجودة:")
        print(f"   - الدورات: {total_courses}")
        print(f"   - المشاركين: {total_participants}")
        print(f"   - المراكز: {total_centers}")
        print(f"   - الجهات: {total_agencies}")
        
        if total_courses == 0:
            print("❌ لا توجد دورات!")
            return
        
        # فحص عينة من الدورات
        sample_courses = Course.query.limit(5).all()
        print(f"\n📋 عينة من الدورات:")
        for course in sample_courses:
            print(f"   - {course.title}")
            print(f"     المستوى: {course.level}")
            print(f"     المركز: {course.center.name if course.center else 'غير محدد'}")
            print(f"     الجهة: {course.agency.name if course.agency else 'غير محدد'}")
            print(f"     المشاركين: {course.total_participants}")
            print(f"     الخريجين: {course.total_graduates}")
            
            # فحص المشاركين
            participants = CourseParticipant.query.filter_by(course_id=course.id).limit(3).all()
            print(f"     عينة مشاركين:")
            for p in participants:
                print(f"       - {p.personal_data.full_name if p.personal_data else 'غير محدد'} ({p.status})")
            print()
        
        # فحص توزيع المستويات
        print(f"📊 توزيع الدورات حسب المستوى:")
        levels = {}
        for course in Course.query.all():
            level = course.level or 'غير محدد'
            if level not in levels:
                levels[level] = 0
            levels[level] += 1
        
        for level, count in levels.items():
            print(f"   - {level}: {count} دورة")
        
        # فحص توزيع حالات المشاركين
        print(f"\n👥 توزيع المشاركين حسب الحالة:")
        statuses = {}
        for participant in CourseParticipant.query.all():
            status = participant.status or 'غير محدد'
            if status not in statuses:
                statuses[status] = 0
            statuses[status] += 1
        
        for status, count in statuses.items():
            print(f"   - {status}: {count} مشارك")

def test_manual_calculation():
    """حساب يدوي للتحقق من صحة البيانات"""
    
    with app.app_context():
        print("\n🧮 حساب يدوي للإحصائيات:")
        
        # حساب إجمالي المشاركين حسب المستوى
        level1_participants = 0
        level2_participants = 0
        level3_participants = 0
        
        for course in Course.query.all():
            participants_count = course.total_participants or 0
            
            if course.level:
                level_name = course.level
                if level_name in ['مبتدئ', 'أساسي', 'beginner', '1'] or 'أول' in level_name:
                    level1_participants += participants_count
                elif level_name in ['متوسط', 'intermediate', '2'] or 'ثان' in level_name:
                    level2_participants += participants_count
                elif level_name in ['متقدم', 'advanced', '3'] or 'ثالث' in level_name:
                    level3_participants += participants_count
        
        print(f"   - المستوى الأول: {level1_participants} مشارك")
        print(f"   - المستوى الثاني: {level2_participants} مشارك")
        print(f"   - المستوى الثالث: {level3_participants} مشارك")
        
        # حساب المشاركين حسب المراكز
        center_participants = {}
        for course in Course.query.all():
            center_name = (course.center.name if course.center else None) or 'غير محدد'
            if center_name not in center_participants:
                center_participants[center_name] = 0
            center_participants[center_name] += course.total_participants or 0
        
        print(f"\n🏢 توزيع المشاركين حسب المراكز:")
        for center, count in center_participants.items():
            print(f"   - {center}: {count} مشارك")
        
        # حساب الخريجين
        total_graduates = 0
        for course in Course.query.all():
            total_graduates += course.total_graduates or 0
        
        print(f"\n🎓 إجمالي الخريجين: {total_graduates}")
        
        # حساب الخريجين من قاعدة البيانات مباشرة
        db_graduates = CourseParticipant.query.filter_by(status='مكتمل').count()
        print(f"🎓 الخريجين من قاعدة البيانات: {db_graduates}")

def simulate_api_call():
    """محاكاة استدعاء API"""
    
    print("\n🌐 محاكاة استدعاء API...")
    
    # بيانات الطلب
    data = {
        'start_date': '2024-01-01',
        'end_date': '2024-12-31'
    }
    
    try:
        # محاولة استدعاء API محلياً
        with app.test_client() as client:
            # تسجيل دخول كمدير
            with client.session_transaction() as sess:
                sess['user_id'] = 1  # افتراض أن المستخدم رقم 1 هو مدير
            
            response = client.post('/reports/dashboard', 
                                 data=json.dumps(data),
                                 content_type='application/json')
            
            if response.status_code == 200:
                result = response.get_json()
                if result and result.get('success'):
                    print("✅ API يعمل بشكل صحيح!")
                    print(f"📊 إجمالي الدورات: {result.get('totalCourses', 0)}")
                    print(f"👥 إجمالي المشاركين: {result.get('totalParticipants', 0)}")
                    print(f"🏢 إجمالي المراكز: {result.get('totalCenters', 0)}")
                    print(f"🎓 إجمالي الخريجين: {result.get('totalGraduates', 0)}")
                    
                    chart_data = result.get('chartData', {})
                    print(f"\n📈 بيانات الرسوم البيانية:")
                    print(f"   - المستوى الأول: {chart_data.get('level1', 0)}")
                    print(f"   - المستوى الثاني: {chart_data.get('level2', 0)}")
                    print(f"   - المستوى الثالث: {chart_data.get('level3', 0)}")
                    
                    center_names = chart_data.get('centerNames', [])
                    center_participants = chart_data.get('centerParticipants', [])
                    print(f"\n🏢 توزيع المراكز:")
                    for i, name in enumerate(center_names):
                        participants = center_participants[i] if i < len(center_participants) else 0
                        print(f"   - {name}: {participants} مشارك")
                else:
                    print(f"❌ فشل API: {result.get('message', 'خطأ غير معروف')}")
            else:
                print(f"❌ خطأ HTTP: {response.status_code}")
                
    except Exception as e:
        print(f"❌ خطأ في محاكاة API: {str(e)}")

if __name__ == "__main__":
    test_reports_api()
    test_manual_calculation()
    simulate_api_call()
