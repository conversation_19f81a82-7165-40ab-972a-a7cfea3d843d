/**
 * 🚀 نظام البحث الذكي والسريع للأشخاص
 * يدعم البحث المرن والاختيار المتعدد والإحصائيات
 */

class SmartPersonSearch {
    constructor(options = {}) {
        this.searchInput = document.getElementById(options.searchInputId || 'smart-search-input');
        this.resultsContainer = document.getElementById(options.resultsContainerId || 'search-results');
        this.selectedContainer = document.getElementById(options.selectedContainerId || 'selected-persons');
        this.statsContainer = document.getElementById(options.statsContainerId || 'search-stats');

        this.selectedPersons = new Map();
        this.debounceTimer = null;
        this.currentQuery = '';
        this.searchHistory = [];

        // إعدادات البحث
        this.settings = {
            debounceDelay: 200, // تسريع الاستجابة
            minQueryLength: 1, // البحث من حرف واحد
            maxResults: 100, // عرض 100 نتيجة!
            threshold: 30, // عتبة منخفضة جداً للحصول على نتائج أكثر
            enableStats: true,
            enableHistory: true,
            enableVirtualScroll: true, // تمرير افتراضي للأداء
            ...options
        };

        this.init();
    }

    init() {
        if (!this.searchInput) {
            console.error('❌ عنصر البحث غير موجود');
            return;
        }

        this.setupEventListeners();
        this.loadSearchHistory();
        this.showWelcomeMessage();

        console.log('✅ تم تهيئة نظام البحث الذكي');
    }

    setupEventListeners() {
        // البحث التفاعلي
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        // البحث عند الضغط على Enter
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.performSearch(this.searchInput.value, true);
            }
        });

        // إخفاء النتائج عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!this.resultsContainer.contains(e.target) &&
                !this.searchInput.contains(e.target) &&
                !e.target.closest('.smart-search-container')) {
                this.hideResults();
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                this.searchInput.focus();
            }
        });
    }

    handleSearchInput(query) {
        this.currentQuery = query.trim();

        // مسح المؤقت السابق
        clearTimeout(this.debounceTimer);

        if (this.currentQuery.length < this.settings.minQueryLength) {
            this.hideResults();
            return;
        }

        // تأخير البحث لتقليل الطلبات
        this.debounceTimer = setTimeout(() => {
            this.performSearch(this.currentQuery);
        }, this.settings.debounceDelay);
    }

    async performSearch(query, forceSearch = false) {
        if (!query || (!forceSearch && query.length < this.settings.minQueryLength)) {
            return;
        }

        this.showLoadingState();

        try {
            const startTime = performance.now();

            // استخدام API البحث السريع
            const response = await fetch(
                `/api/search_persons_fast?q=${encodeURIComponent(query)}&limit=${this.settings.maxResults}&threshold=${this.settings.threshold}`
            );

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const results = await response.json();
            const searchTime = performance.now() - startTime;

            this.displayResults(results, query, searchTime);
            this.addToSearchHistory(query, results.length, searchTime);

        } catch (error) {
            console.error('❌ خطأ في البحث:', error);
            this.showErrorMessage('حدث خطأ في البحث. يرجى المحاولة مرة أخرى.');
        }
    }

    displayResults(results, query, searchTime) {
        if (!results || results.length === 0) {
            this.showNoResults(query);
            return;
        }

        const resultsHTML = `
            <div class="search-results-header">
                <div class="search-info">
                    <span class="results-count">🔍 ${results.length} نتيجة</span>
                    <span class="search-time">⚡ ${searchTime.toFixed(0)}ms</span>
                    ${results.length >= this.settings.maxResults ?
                        '<span class="max-results-indicator">📊 الحد الأقصى</span>' : ''}
                </div>
                <button class="btn btn-sm btn-outline-secondary" onclick="smartSearch.hideResults()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-results-list" id="search-results-list">
                ${results.map(person => this.createPersonResultHTML(person, query)).join('')}
            </div>
        `;

        this.resultsContainer.innerHTML = resultsHTML;
        this.resultsContainer.classList.remove('hide');
        this.resultsContainer.classList.add('show');
        this.resultsContainer.style.display = 'block';

        // إضافة مستمعي الأحداث للنتائج
        this.attachResultListeners();
    }

    createPersonResultHTML(person, query) {
        const isSelected = this.selectedPersons.has(person.id);
        const matchScore = person.match_score || 0;

        // تمييز النص المطابق
        const highlightedName = this.highlightMatch(person.name, query);

        return `
            <div class="search-result-item ${isSelected ? 'selected' : ''}" data-person-id="${person.id}">
                <div class="person-main-info">
                    <div class="person-name">${highlightedName}</div>
                    <div class="person-details">
                        ${person.nickname ? `<span class="nickname">الكنية: ${person.nickname}</span>` : ''}
                        ${person.national_id ? `<span class="national-id">الرقم الوطني: ${person.national_id}</span>` : ''}
                        ${person.military_number ? `<span class="military-number">الرقم العسكري: ${person.military_number}</span>` : ''}
                    </div>
                    <div class="person-additional">
                        ${person.governorate ? `<span class="governorate"><i class="fas fa-map-marker-alt"></i> ${person.governorate}</span>` : ''}
                        ${person.agency ? `<span class="agency"><i class="fas fa-building"></i> ${person.agency}</span>` : ''}
                        ${person.job ? `<span class="job"><i class="fas fa-briefcase"></i> ${person.job}</span>` : ''}
                    </div>
                </div>
                <div class="person-actions">
                    <div class="match-score">
                        <span class="score-value">${matchScore}%</span>
                        <div class="score-bar">
                            <div class="score-fill" style="width: ${matchScore}%"></div>
                        </div>
                    </div>
                    <button class="btn btn-sm ${isSelected ? 'btn-danger remove-person-btn' : 'btn-success add-person-btn'}"
                            data-person-id="${person.id}">
                        <i class="fas ${isSelected ? 'fa-minus' : 'fa-plus'}"></i>
                        ${isSelected ? 'إزالة' : 'إضافة'}
                    </button>
                </div>
            </div>
        `;
    }

    highlightMatch(text, query) {
        if (!query || !text) return text;

        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    attachResultListeners() {
        console.log('🔗 ربط مستمعي الأحداث للنتائج...');

        // أزرار الإضافة
        document.querySelectorAll('.add-person-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                const personId = parseInt(btn.dataset.personId);
                console.log('➕ نقر على زر الإضافة للشخص:', personId);
                this.addSelectedPerson(personId);
            });
        });

        // أزرار الإزالة
        document.querySelectorAll('.remove-person-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                const personId = parseInt(btn.dataset.personId);
                console.log('➖ نقر على زر الإزالة للشخص:', personId);
                this.removeSelectedPerson(personId);
            });
        });

        // النقر على النتيجة لإضافتها
        document.querySelectorAll('.search-result-item').forEach(item => {
            item.addEventListener('click', (e) => {
                // تجنب التفعيل إذا تم النقر على الزر
                if (e.target.closest('.add-person-btn, .remove-person-btn')) {
                    return;
                }

                const personId = parseInt(item.dataset.personId);
                console.log('👆 نقر على النتيجة للشخص:', personId);

                if (this.selectedPersons.has(personId)) {
                    this.removeSelectedPerson(personId);
                } else {
                    this.addSelectedPerson(personId);
                }
            });
        });

        console.log(`✅ تم ربط ${document.querySelectorAll('.search-result-item').length} نتيجة`);
    }

    addSelectedPerson(personId) {
        console.log('🎯 محاولة إضافة الشخص:', personId);

        const resultItem = document.querySelector(`[data-person-id="${personId}"]`);
        if (!resultItem) {
            console.error('❌ لم يتم العثور على عنصر النتيجة للشخص:', personId);
            this.showNotification('❌ خطأ في العثور على بيانات الشخص', 'error');
            return;
        }

        // استخراج بيانات الشخص من العنصر
        const personData = this.extractPersonDataFromElement(resultItem);
        console.log('📋 بيانات الشخص المستخرجة:', personData);

        if (!personData || !personData.name) {
            console.error('❌ بيانات الشخص غير صحيحة:', personData);
            this.showNotification('❌ بيانات الشخص غير صحيحة', 'error');
            return;
        }

        this.selectedPersons.set(personId, personData);
        this.updateSelectedPersonsDisplay();
        this.updateResultItemState(personId, true);

        // إشعار بالإضافة
        this.showNotification(`✅ تم اختيار ${personData.name}`, 'success');

        // إرسال حدث مخصص
        this.dispatchEvent('personAdded', { personId, personData });

        console.log('✅ تم إضافة الشخص بنجاح. العدد الحالي:', this.selectedPersons.size);
    }

    removeSelectedPerson(personId) {
        const personData = this.selectedPersons.get(personId);
        if (!personData) return;

        this.selectedPersons.delete(personId);
        this.updateSelectedPersonsDisplay();
        this.updateResultItemState(personId, false);

        // إشعار بالإزالة
        this.showNotification(`تم إزالة ${personData.name}`, 'warning');

        // إرسال حدث مخصص
        this.dispatchEvent('personRemoved', { personId, personData });
    }

    extractPersonDataFromElement(element) {
        return {
            id: parseInt(element.dataset.personId),
            name: element.querySelector('.person-name').textContent.replace(/<[^>]*>/g, ''),
            nickname: element.querySelector('.nickname')?.textContent.replace('الكنية: ', '') || '',
            national_id: element.querySelector('.national-id')?.textContent.replace('الرقم الوطني: ', '') || '',
            military_number: element.querySelector('.military-number')?.textContent.replace('الرقم العسكري: ', '') || '',
            governorate: element.querySelector('.governorate')?.textContent || '',
            agency: element.querySelector('.agency')?.textContent || '',
            job: element.querySelector('.job')?.textContent || ''
        };
    }

    updateResultItemState(personId, isSelected) {
        const resultItem = document.querySelector(`[data-person-id="${personId}"]`);
        if (!resultItem) return;

        const button = resultItem.querySelector('.add-person-btn, .remove-person-btn');
        const icon = button.querySelector('i');

        if (isSelected) {
            resultItem.classList.add('selected');
            button.className = 'btn btn-sm btn-danger remove-person-btn';
            button.dataset.personId = personId;
            icon.className = 'fas fa-minus';
            button.innerHTML = '<i class="fas fa-minus"></i> إزالة';
        } else {
            resultItem.classList.remove('selected');
            button.className = 'btn btn-sm btn-success add-person-btn';
            button.dataset.personId = personId;
            icon.className = 'fas fa-plus';
            button.innerHTML = '<i class="fas fa-plus"></i> إضافة';
        }
    }

    updateSelectedPersonsDisplay() {
        if (!this.selectedContainer) return;

        if (this.selectedPersons.size === 0) {
            this.selectedContainer.innerHTML = `
                <div class="no-selection">
                    <i class="fas fa-users text-muted"></i>
                    <p class="text-muted">لم يتم اختيار أي أشخاص بعد</p>
                </div>
            `;
            return;
        }

        const selectedHTML = `
            <div class="selected-header">
                <h6><i class="fas fa-users"></i> الأشخاص المختارون (${this.selectedPersons.size})</h6>
                <button class="btn btn-sm btn-outline-danger" onclick="smartSearch.clearAllSelected()">
                    <i class="fas fa-trash"></i> مسح الكل
                </button>
            </div>
            <div class="selected-list">
                ${Array.from(this.selectedPersons.values()).map(person => `
                    <div class="selected-person-item" data-person-id="${person.id}">
                        <div class="person-info">
                            <strong>${person.name}</strong>
                            <small class="text-muted">${person.national_id || person.military_number || 'غير محدد'}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="smartSearch.removeSelectedPerson(${person.id})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `).join('')}
            </div>
        `;

        this.selectedContainer.innerHTML = selectedHTML;
    }

    clearAllSelected() {
        this.selectedPersons.clear();
        this.updateSelectedPersonsDisplay();

        // تحديث حالة جميع النتائج
        document.querySelectorAll('.search-result-item.selected').forEach(item => {
            const personId = parseInt(item.dataset.personId);
            this.updateResultItemState(personId, false);
        });

        this.showNotification('تم مسح جميع الاختيارات', 'info');
        this.dispatchEvent('allPersonsCleared');
    }

    showLoadingState() {
        this.resultsContainer.innerHTML = `
            <div class="search-loading">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">جاري البحث...</span>
                </div>
                <span class="ms-2">جاري البحث...</span>
            </div>
        `;
        this.resultsContainer.style.display = 'block';
    }

    showNoResults(query) {
        this.resultsContainer.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search text-muted"></i>
                <p class="text-muted">لا توجد نتائج للبحث عن "${query}"</p>
                <small class="text-muted">جرب استخدام كلمات مختلفة أو تحقق من الإملاء</small>
            </div>
        `;
        this.resultsContainer.style.display = 'block';
    }

    showErrorMessage(message) {
        this.resultsContainer.innerHTML = `
            <div class="search-error">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                <p class="text-warning">${message}</p>
            </div>
        `;
        this.resultsContainer.style.display = 'block';
    }

    showWelcomeMessage() {
        if (!this.resultsContainer) return;

        this.resultsContainer.innerHTML = `
            <div class="search-welcome">
                <i class="fas fa-search text-primary"></i>
                <h6>البحث الذكي والسريع</h6>
                <p class="text-muted">ابدأ بكتابة اسم الشخص أو الرقم الوطني أو العسكري</p>
                <div class="search-tips">
                    <small class="text-muted">
                        <i class="fas fa-lightbulb"></i>
                        نصائح: يمكنك البحث بالاسم الجزئي أو حتى مع وجود أخطاء إملائية
                    </small>
                </div>
            </div>
        `;
        this.resultsContainer.style.display = 'block';
    }

    hideResults() {
        if (this.resultsContainer) {
            this.resultsContainer.classList.remove('show');
            this.resultsContainer.classList.add('hide');

            // إخفاء النتائج بعد انتهاء الحركة
            setTimeout(() => {
                this.resultsContainer.style.display = 'none';
                this.resultsContainer.classList.remove('hide');
            }, 300);
        }
    }

    addToSearchHistory(query, resultsCount, searchTime) {
        if (!this.settings.enableHistory) return;

        const historyItem = {
            query,
            resultsCount,
            searchTime,
            timestamp: new Date().toISOString()
        };

        this.searchHistory.unshift(historyItem);

        // الاحتفاظ بآخر 50 عملية بحث فقط
        if (this.searchHistory.length > 50) {
            this.searchHistory = this.searchHistory.slice(0, 50);
        }

        // حفظ في localStorage
        try {
            localStorage.setItem('smartSearchHistory', JSON.stringify(this.searchHistory));
        } catch (e) {
            console.warn('⚠️ لا يمكن حفظ تاريخ البحث:', e);
        }
    }

    loadSearchHistory() {
        if (!this.settings.enableHistory) return;

        try {
            const saved = localStorage.getItem('smartSearchHistory');
            if (saved) {
                this.searchHistory = JSON.parse(saved);
            }
        } catch (e) {
            console.warn('⚠️ لا يمكن تحميل تاريخ البحث:', e);
            this.searchHistory = [];
        }
    }

    showNotification(message, type = 'info') {
        // إنشاء إشعار بسيط
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار تلقائياً بعد 3 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    dispatchEvent(eventName, data = {}) {
        const event = new CustomEvent(`smartSearch:${eventName}`, {
            detail: { ...data, searchInstance: this }
        });
        document.dispatchEvent(event);
    }

    // دوال مساعدة للوصول من الخارج
    getSelectedPersons() {
        return Array.from(this.selectedPersons.values());
    }

    getSelectedPersonIds() {
        return Array.from(this.selectedPersons.keys());
    }

    clearSearch() {
        this.searchInput.value = '';
        this.hideResults();
        this.currentQuery = '';
    }

    focusSearch() {
        this.searchInput.focus();
    }
}

// إنشاء مثيل عام
let smartSearch;

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود العناصر المطلوبة
    if (document.getElementById('smart-search-input')) {
        smartSearch = new SmartPersonSearch();

        // إضافة مستمعي الأحداث المخصصة
        document.addEventListener('smartSearch:personAdded', function(e) {
            console.log('تم إضافة شخص:', e.detail.personData.name);
        });

        document.addEventListener('smartSearch:personRemoved', function(e) {
            console.log('تم إزالة شخص:', e.detail.personData.name);
        });
    }
});
