{% extends "layout.html" %}

{% block styles %}
<style>
    .reference-card {
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .reference-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }
    
    .reference-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .reference-table th {
        background-color: #ffc107;
        color: black;
        padding: 12px;
        text-align: center;
    }
    
    .reference-table td {
        padding: 12px;
        text-align: center;
        border: 1px solid #e9ecef;
    }
    
    .reference-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    
    .reference-table tr:hover {
        background-color: #e9ecef;
    }
    
    .btn-add {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s;
        color: black;
        text-decoration: none;
    }
    
    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
        color: black;
    }
    
    .btn-edit {
        background-color: #17a2b8;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 5px 10px;
        font-size: 0.8rem;
        margin-right: 5px;
        text-decoration: none;
    }
    
    .btn-edit:hover {
        background-color: #138496;
        color: white;
    }
    
    .btn-delete {
        background-color: #dc3545;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 5px 10px;
        font-size: 0.8rem;
        text-decoration: none;
    }
    
    .btn-delete:hover {
        background-color: #c82333;
        color: white;
    }
    
    .empty-message {
        text-align: center;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
        margin-top: 20px;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>أنواع البطاقات</h2>
        <div>
            <a href="{{ url_for('reference_tables') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى الجداول الترميزية
            </a>
            <a href="{{ url_for('add_card_type') }}" class="btn-add">
                <i class="fas fa-plus-circle me-1"></i> إضافة نوع بطاقة
            </a>
        </div>
    </div>
    
    <div class="reference-card">
        <div class="reference-header d-flex justify-content-between align-items-center">
            <div>
                <h4><i class="fas fa-id-card me-2"></i> قائمة أنواع البطاقات</h4>
                <p class="text-muted">إدارة أنواع البطاقات في النظام</p>
            </div>
        </div>
        
        {% if card_types %}
        <div class="table-responsive">
            <table class="reference-table">
                <thead>
                    <tr>
                        <th width="10%">#</th>
                        <th width="70%">نوع البطاقة</th>
                        <th width="20%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for card_type in card_types %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ card_type.name }}</td>
                        <td>
                            <a href="{{ url_for('edit_card_type', card_type_id=card_type.id) }}" class="btn-edit">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{{ url_for('delete_card_type', card_type_id=card_type.id) }}" class="btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذا النوع؟');">
                                <i class="fas fa-trash-alt"></i> حذف
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-message">
            <i class="fas fa-info-circle me-2"></i> لا توجد أنواع بطاقات مضافة حالياً.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
