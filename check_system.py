#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص النظام - التأكد من جاهزية جميع المكونات
System Check - Verify all components are ready
"""

import os
import sys
import importlib

def print_header():
    print("=" * 50)
    print("🔍 فحص نظام إدارة التدريب")
    print("   System Check")
    print("=" * 50)
    print()

def check_python():
    print("🐍 فحص Python...")
    version = sys.version_info
    print(f"   الإصدار: {version.major}.{version.minor}.{version.micro}")
    if version.major >= 3 and version.minor >= 8:
        print("   ✅ Python جاهز")
        return True
    else:
        print("   ❌ يحتاج Python 3.8+")
        return False

def check_files():
    print("\n📁 فحص الملفات الأساسية...")
    required_files = [
        'app.py',
        'person_data_routes.py', 
        'reports_generator.py',
        'backup_utils.py',
        'requirements.txt'
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} مفقود")
            all_exist = False
    
    return all_exist

def check_directories():
    print("\n📂 فحص المجلدات...")
    required_dirs = [
        'templates',
        'static',
        'uploads',
        '.venv'
    ]
    
    all_exist = True
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"   ✅ {directory}")
        else:
            print(f"   ❌ {directory} مفقود")
            all_exist = False
    
    return all_exist

def check_libraries():
    print("\n📦 فحص المكتبات...")
    required_libs = [
        'flask',
        'flask_sqlalchemy', 
        'flask_login',
        'flask_wtf',
        'pandas',
        'openpyxl',
        'xlsxwriter',
        'arabic_reshaper',
        'bidi'
    ]
    
    all_imported = True
    for lib in required_libs:
        try:
            importlib.import_module(lib)
            print(f"   ✅ {lib}")
        except ImportError:
            print(f"   ❌ {lib} غير مثبت")
            all_imported = False
    
    return all_imported

def check_database():
    print("\n💾 فحص قاعدة البيانات...")
    db_files = ['training_system.db', 'instance/training_system.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"   ✅ {db_file}")
            return True
    
    print("   ⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها")
    return True

def test_import():
    print("\n🧪 اختبار استيراد التطبيق...")
    try:
        from app import app, db
        print("   ✅ تم استيراد التطبيق بنجاح")
        
        # اختبار إنشاء قاعدة البيانات
        with app.app_context():
            db.create_all()
            print("   ✅ قاعدة البيانات جاهزة")
        
        return True
    except Exception as e:
        print(f"   ❌ خطأ في الاستيراد: {e}")
        return False

def main():
    print_header()
    
    checks = [
        ("Python", check_python),
        ("الملفات", check_files),
        ("المجلدات", check_directories), 
        ("المكتبات", check_libraries),
        ("قاعدة البيانات", check_database),
        ("التطبيق", test_import)
    ]
    
    all_passed = True
    for name, check_func in checks:
        result = check_func()
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 النظام جاهز للتشغيل!")
        print("يمكنك الآن تشغيل النظام باستخدام:")
        print("   python app.py")
        print("   أو")
        print("   run_now.bat")
    else:
        print("❌ هناك مشاكل تحتاج إلى حل")
        print("يرجى إصلاح المشاكل أعلاه أولاً")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
