{% extends "layout.html" %}

{% block styles %}
<style>
    body {
        background: url('{{ url_for("static", filename="img/training-bg.jpg") }}') no-repeat center center fixed;
        background-size: cover;
    }
    
    .login-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .login-card {
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        max-width: 500px;
        width: 100%;
    }
    
    .login-header {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 20px;
        text-align: center;
    }
    
    .login-body {
        padding: 30px;
    }
    
    .form-control {
        border-radius: 10px;
        padding: 12px;
        margin-bottom: 20px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }
    
    .form-control:focus {
        border-color: #4a6bff;
        box-shadow: 0 0 0 0.25rem rgba(74, 107, 255, 0.25);
    }
    
    .btn-login {
        background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(74, 107, 255, 0.4);
    }
    
    .login-icon {
        font-size: 80px;
        color: #4a6bff;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>نظام التدريب والتأهيل</h2>
        </div>
        <div class="login-body text-center">
            <div class="login-icon">
                <i class="fas fa-user-circle"></i>
            </div>
            <h3 class="mb-4">تسجيل الدخول</h3>
            
            <form method="POST" action="">
                {{ form.hidden_tag() }}
                <div class="form-group text-end">
                    {{ form.email.label(class="form-label") }}
                    {% if form.email.errors %}
                        {{ form.email(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.email.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.email(class="form-control", placeholder="أدخل بريدك الإلكتروني") }}
                    {% endif %}
                </div>
                
                <div class="form-group text-end">
                    {{ form.password.label(class="form-label") }}
                    {% if form.password.errors %}
                        {{ form.password(class="form-control is-invalid") }}
                        <div class="invalid-feedback">
                            {% for error in form.password.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        {{ form.password(class="form-control", placeholder="أدخل كلمة المرور") }}
                    {% endif %}
                </div>
                
                <div class="form-check text-end mb-4">
                    {{ form.remember(class="form-check-input") }}
                    {{ form.remember.label(class="form-check-label") }}
                </div>
                
                <div class="d-grid">
                    {{ form.submit(class="btn btn-primary btn-login btn-lg") }}
                </div>
            </form>
            
            <div class="mt-4">
                <small class="text-muted">نسيت كلمة المرور؟ <a href="#">اضغط هنا</a></small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
