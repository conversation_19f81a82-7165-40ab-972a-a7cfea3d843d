<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعليمات تشغيل نظام التدريب والتأهيل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #4a6bff;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #4a6bff;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #4a6bff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .command {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            direction: ltr;
            text-align: left;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .links {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .links a {
            display: inline-block;
            background: #4a6bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: all 0.3s;
        }
        .links a:hover {
            background: #2541b2;
            transform: translateY(-2px);
        }
        .icon {
            font-size: 1.2em;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 نظام التدريب والتأهيل</h1>
        
        <h2>📋 تعليمات التشغيل</h2>
        
        <div class="step success">
            <strong>✅ تم إصلاح جميع المشاكل:</strong>
            <ul>
                <li>إصلاح لون الخط في قائمة البحث</li>
                <li>إصلاح مشكلة عرض الأخطاء في أعلى الصفحة</li>
                <li>إضافة API البحث المفقود</li>
                <li>إصلاح جميع الروابط المكسورة</li>
                <li>إنشاء قائمة أشخاص بسيطة وسريعة</li>
            </ul>
        </div>

        <h2>🔧 طرق التشغيل</h2>
        
        <div class="step">
            <strong>الطريقة الأولى (الموصى بها):</strong>
            <div class="command">python start_server.py</div>
        </div>
        
        <div class="step">
            <strong>الطريقة الثانية:</strong>
            <div class="command">python run_app.py</div>
        </div>
        
        <div class="step">
            <strong>الطريقة الثالثة:</strong>
            <div class="command">python app.py</div>
        </div>
        
        <div class="step">
            <strong>للاختبار البسيط:</strong>
            <div class="command">python test_simple.py</div>
        </div>

        <h2>🌐 الروابط</h2>
        
        <div class="links">
            <strong>جرب هذه الروابط بعد تشغيل الخادم:</strong><br><br>
            
            <a href="http://localhost:5000" target="_blank">
                🏠 الصفحة الرئيسية
            </a>
            
            <a href="http://127.0.0.1:5000" target="_blank">
                🏠 الصفحة الرئيسية (بديل)
            </a>
            
            <a href="http://localhost:5000/login" target="_blank">
                🔐 تسجيل الدخول
            </a>
            
            <a href="http://localhost:5000/person_data_table" target="_blank">
                📊 جدول بيانات الأشخاص
            </a>
            
            <a href="http://localhost:5000/simple_person_list" target="_blank">
                📋 قائمة الأشخاص البسيطة
            </a>
            
            <a href="http://localhost:5000/manage_participants/1" target="_blank">
                👥 إدارة المشاركين
            </a>
        </div>

        <h2>🔑 بيانات تسجيل الدخول</h2>
        
        <div class="step success">
            <strong>اسم المستخدم:</strong> admin<br>
            <strong>كلمة المرور:</strong> admin
        </div>

        <h2>🆕 المميزات الجديدة</h2>
        
        <div class="step">
            <strong>قائمة الأشخاص البسيطة:</strong>
            <ul>
                <li>عرض بطاقات أنيقة للأشخاص</li>
                <li>بحث سريع وفعال</li>
                <li>تصفح بين الصفحات</li>
                <li>إحصائيات فورية</li>
                <li>إضافة وحذف مباشر</li>
                <li>تصميم متجاوب وجميل</li>
            </ul>
        </div>

        <h2>🔧 حل المشاكل</h2>
        
        <div class="step warning">
            <strong>إذا لم يعمل الخادم:</strong>
            <ol>
                <li>تأكد من تثبيت Python و Flask</li>
                <li>تأكد من عدم استخدام المنفذ 5000 من برنامج آخر</li>
                <li>جرب تشغيل الأمر كمدير (Run as Administrator)</li>
                <li>تأكد من إعدادات جدار الحماية</li>
                <li>جرب منفذ مختلف (8080, 3000, 8000)</li>
            </ol>
        </div>
        
        <div class="step error">
            <strong>إذا ظهر خطأ Internal Server Error:</strong>
            <ol>
                <li>شغل الخادم مع debug mode</li>
                <li>تحقق من سجل الأخطاء في Terminal</li>
                <li>تأكد من وجود جميع الملفات المطلوبة</li>
                <li>تأكد من صحة قاعدة البيانات</li>
            </ol>
        </div>

        <h2>📞 الدعم</h2>
        
        <div class="step">
            إذا واجهت أي مشاكل، تأكد من:
            <ul>
                <li>وجود جميع الملفات في نفس المجلد</li>
                <li>تثبيت جميع المتطلبات</li>
                <li>عدم وجود أخطاء في Terminal</li>
                <li>صحة إعدادات الشبكة</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>🎉 نظام التدريب والتأهيل - جاهز للاستخدام!</p>
        </div>
    </div>
</body>
</html>
