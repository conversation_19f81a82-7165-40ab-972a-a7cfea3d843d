{% extends "layout.html" %}
{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">تعديل رتبة عسكرية</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                {{ form.hidden_tag() }}
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.name.label(class="form-label") }}
                            {% if form.name.errors %}
                                {{ form.name(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.name(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.code.label(class="form-label") }}
                            {% if form.code.errors %}
                                {{ form.code(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.code.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.code(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="form-group mt-4">
                    {{ form.submit(class="btn btn-primary") }}
                    <a href="{{ url_for('military_ranks') }}" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
