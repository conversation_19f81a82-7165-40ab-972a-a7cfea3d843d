#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص حالة المكتبات المحلية
Check local libraries status
"""

import os
import requests
import time

def check_file_exists(file_path):
    """فحص وجود الملف"""
    return os.path.exists(file_path)

def check_file_size(file_path):
    """فحص حجم الملف"""
    if os.path.exists(file_path):
        return os.path.getsize(file_path)
    return 0

def check_server_response(url):
    """فحص استجابة الخادم"""
    try:
        response = requests.get(url, timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """فحص حالة المكتبات"""
    print("🔍 فحص حالة المكتبات المحلية...")
    print("=" * 60)
    
    # قائمة الملفات المطلوبة
    required_files = [
        'static/libs/bootstrap/bootstrap.rtl.min.css',
        'static/libs/bootstrap/bootstrap.bundle.min.js',
        'static/libs/fontawesome/all.min.css',
        'static/libs/fontawesome/webfonts/fa-solid-900.woff2',
        'static/libs/jquery/jquery-3.6.0.min.js',
        'static/libs/chartjs/chart.min.js',
        'static/libs/datatables/jquery.dataTables.min.js',
        'static/libs/select2/select2.min.js',
        'static/libs/devextreme/dx.light.css',
        'static/libs/other/jspdf.umd.min.js',
        'static/libs/other/xlsx.full.min.js',
        'static/libs/other/jszip.min.js'
    ]
    
    print("📁 فحص الملفات المحلية:")
    missing_files = []
    total_size = 0
    
    for file_path in required_files:
        if check_file_exists(file_path):
            size = check_file_size(file_path)
            total_size += size
            size_mb = size / (1024 * 1024)
            print(f"   ✅ {file_path} ({size_mb:.2f} MB)")
        else:
            print(f"   ❌ {file_path} - مفقود")
            missing_files.append(file_path)
    
    print(f"\n📊 الإحصائيات:")
    print(f"   - الملفات الموجودة: {len(required_files) - len(missing_files)}/{len(required_files)}")
    print(f"   - الحجم الإجمالي: {total_size / (1024 * 1024):.2f} MB")
    
    if missing_files:
        print(f"\n⚠️ الملفات المفقودة:")
        for file in missing_files:
            print(f"   - {file}")
    
    # فحص الخادم
    print(f"\n🌐 فحص الخادم:")
    server_urls = [
        'http://localhost:5000',
        'http://localhost:5000/static/libs/bootstrap/bootstrap.rtl.min.css',
        'http://localhost:5000/static/libs/jquery/jquery-3.6.0.min.js'
    ]
    
    for url in server_urls:
        if check_server_response(url):
            print(f"   ✅ {url}")
        else:
            print(f"   ❌ {url}")
    
    # النتيجة النهائية
    print(f"\n" + "=" * 60)
    if len(missing_files) == 0:
        print("🎉 جميع المكتبات موجودة وجاهزة للاستخدام!")
        print("✅ يمكن نسخ المشروع إلى أي جهاز وسيعمل بدون إنترنت")
    else:
        print(f"⚠️ يوجد {len(missing_files)} ملف مفقود")
        print("💡 شغل 'python download_libraries.py' لتنزيل الملفات المفقودة")

if __name__ == "__main__":
    main()
