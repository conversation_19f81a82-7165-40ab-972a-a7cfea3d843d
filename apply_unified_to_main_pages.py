#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تطبيق التصميم الموحد على الصفحات الرئيسية
"""

import os
import re
from pathlib import Path

def update_main_pages():
    """تحديث الصفحات الرئيسية بالتصميم الموحد"""
    
    print("🎨 تطبيق التصميم الموحد على الصفحات الرئيسية")
    print("=" * 60)
    
    # قائمة الصفحات الرئيسية للتحديث
    main_pages = [
        {
            'file': 'templates/dashboard.html',
            'title': 'لوحة التحكم',
            'icon': 'fas fa-tachometer-alt',
            'subtitle': 'نظرة شاملة على إحصائيات النظام والأنشطة الحديثة'
        },
        {
            'file': 'templates/admin/users.html',
            'title': 'إدارة المستخدمين',
            'icon': 'fas fa-users',
            'subtitle': 'إدارة حسابات المستخدمين وصلاحياتهم'
        },
        {
            'file': 'templates/courses/index.html',
            'title': 'إدارة الدورات التدريبية',
            'icon': 'fas fa-graduation-cap',
            'subtitle': 'إدارة الدورات التدريبية والبرامج التعليمية'
        },
        {
            'file': 'templates/courses/add.html',
            'title': 'إضافة دورة تدريبية جديدة',
            'icon': 'fas fa-plus-circle',
            'subtitle': 'إنشاء دورة تدريبية جديدة مع تحديد جميع التفاصيل'
        },
        {
            'file': 'templates/reports/index.html',
            'title': 'التقارير والإحصائيات',
            'icon': 'fas fa-chart-bar',
            'subtitle': 'عرض التقارير المختلفة وتحليل البيانات'
        },
        {
            'file': 'templates/reference_tables/index.html',
            'title': 'الجداول المرجعية',
            'icon': 'fas fa-table',
            'subtitle': 'إدارة البيانات المرجعية والتصنيفات'
        }
    ]
    
    updated_count = 0
    
    for page_info in main_pages:
        file_path = Path(page_info['file'])
        
        if not file_path.exists():
            print(f"⚠️ الملف غير موجود: {file_path}")
            continue
        
        print(f"\n🔧 تحديث: {file_path}")
        
        try:
            # قراءة المحتوى الحالي
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إنشاء نسخة احتياطية
            backup_path = file_path.with_suffix('.html.backup')
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # تحديث المحتوى
            updated_content = update_page_content(content, page_info)
            
            # حفظ المحتوى المحدث
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print(f"   ✅ تم التحديث بنجاح")
            updated_count += 1
            
        except Exception as e:
            print(f"   ❌ خطأ في التحديث: {e}")
    
    print(f"\n📊 ملخص التحديث:")
    print(f"   تم تحديث {updated_count} من {len(main_pages)} صفحة")
    
    return updated_count

def update_page_content(content, page_info):
    """تحديث محتوى الصفحة بالتصميم الموحد"""
    
    # التحقق من وجود extends layout
    if '{% extends "layout.html" %}' not in content:
        content = '{% extends "layout.html" %}\n\n' + content
    
    # إضافة أو تحديث block title
    title_pattern = r'{% block title %}.*?{% endblock %}'
    title_block = f'{{% block title %}}{page_info["title"]}{{% endblock %}}'
    
    if re.search(title_pattern, content):
        content = re.sub(title_pattern, title_block, content)
    else:
        # إضافة title block بعد extends
        content = re.sub(
            r'({% extends "layout.html" %})',
            r'\1\n\n' + title_block,
            content
        )
    
    # إضافة أو تحديث block styles
    styles_block = '''{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-pages.css') }}">
{% endblock %}'''
    
    styles_pattern = r'{% block styles %}.*?{% endblock %}'
    if re.search(styles_pattern, content, re.DOTALL):
        content = re.sub(styles_pattern, styles_block, content, flags=re.DOTALL)
    else:
        # إضافة styles block بعد title
        content = re.sub(
            r'({% block title %}.*?{% endblock %})',
            r'\1\n\n' + styles_block,
            content
        )
    
    # تحديث block content
    content_pattern = r'{% block content %}(.*?){% endblock %}'
    content_match = re.search(content_pattern, content, re.DOTALL)
    
    if content_match:
        original_content = content_match.group(1).strip()
        
        # إنشاء المحتوى الجديد مع التصميم الموحد
        new_content_block = f'''{{% block content %}}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title">
            <i class="{page_info['icon']} icon-lg"></i>
            {page_info['title']}
        </div>
        <div class="page-subtitle">
            {page_info['subtitle']}
        </div>
    </div>

    <div class="content-wrapper">
        <!-- المحتوى الأصلي -->
{original_content}
    </div>
</div>
{{% endblock %}}'''
        
        content = re.sub(content_pattern, new_content_block, content, flags=re.DOTALL)
    
    return content

def update_dashboard_specifically():
    """تحديث لوحة التحكم بشكل خاص"""
    
    print("\n🎯 تحديث لوحة التحكم بشكل خاص...")
    
    dashboard_path = Path('templates/dashboard.html')
    
    if not dashboard_path.exists():
        print("⚠️ ملف لوحة التحكم غير موجود")
        return
    
    try:
        with open(dashboard_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إنشاء نسخة احتياطية
        backup_path = dashboard_path.with_suffix('.html.backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # تحديث خاص للوحة التحكم
        updated_content = '''{% extends "layout.html" %}

{% block title %}لوحة التحكم{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-pages.css') }}">
<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.chart-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
}

.recent-activities {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title">
            <i class="fas fa-tachometer-alt icon-lg"></i>
            لوحة التحكم
        </div>
        <div class="page-subtitle">
            نظرة شاملة على إحصائيات النظام والأنشطة الحديثة
        </div>
    </div>

    <div class="content-wrapper">
        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stats-card primary">
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-number" id="totalUsers">{{ total_users or 0 }}</div>
                <div class="stats-label">إجمالي المستخدمين</div>
            </div>
            
            <div class="stats-card success">
                <div class="stats-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="stats-number" id="totalCourses">{{ total_courses or 0 }}</div>
                <div class="stats-label">إجمالي الدورات</div>
            </div>
            
            <div class="stats-card warning">
                <div class="stats-icon">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <div class="stats-number" id="totalParticipants">{{ total_participants or 0 }}</div>
                <div class="stats-label">إجمالي المشاركين</div>
            </div>
            
            <div class="stats-card info">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-number" id="completionRate">{{ completion_rate or 0 }}%</div>
                <div class="stats-label">معدل الإنجاز</div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="row g-4 mb-4">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-bar text-primary"></i>
                        إحصائيات الدورات الشهرية
                    </h5>
                    <canvas id="coursesChart" height="300"></canvas>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-pie-chart text-success"></i>
                        توزيع المستخدمين
                    </h5>
                    <canvas id="usersChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- الأنشطة الحديثة -->
        <div class="recent-activities">
            <h5 class="mb-3">
                <i class="fas fa-clock text-info"></i>
                الأنشطة الحديثة
            </h5>
            
            <div id="recentActivities">
                <!-- سيتم تحميل الأنشطة هنا -->
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                    <p class="text-muted mt-2">جاري تحميل الأنشطة...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تحديث الإحصائيات
function updateStats() {
    fetch('/api/dashboard-stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('totalUsers').textContent = data.total_users || 0;
            document.getElementById('totalCourses').textContent = data.total_courses || 0;
            document.getElementById('totalParticipants').textContent = data.total_participants || 0;
            document.getElementById('completionRate').textContent = (data.completion_rate || 0) + '%';
        })
        .catch(error => console.error('خطأ في تحميل الإحصائيات:', error));
}

// تحميل الأنشطة الحديثة
function loadRecentActivities() {
    fetch('/api/recent-activities')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('recentActivities');
            if (data.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">لا توجد أنشطة حديثة</p>';
                return;
            }
            
            container.innerHTML = data.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon bg-${activity.type}">
                        <i class="${activity.icon} text-white"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${activity.title}</div>
                        <div class="text-muted small">${activity.description}</div>
                    </div>
                    <div class="text-muted small">${activity.time}</div>
                </div>
            `).join('');
        })
        .catch(error => {
            console.error('خطأ في تحميل الأنشطة:', error);
            document.getElementById('recentActivities').innerHTML = 
                '<p class="text-danger text-center">خطأ في تحميل الأنشطة</p>';
        });
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateStats();
    loadRecentActivities();
    
    // تحديث البيانات كل 30 ثانية
    setInterval(updateStats, 30000);
    setInterval(loadRecentActivities, 60000);
});
</script>
{% endblock %}'''
        
        # حفظ المحتوى المحدث
        with open(dashboard_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("   ✅ تم تحديث لوحة التحكم بنجاح")
        
    except Exception as e:
        print(f"   ❌ خطأ في تحديث لوحة التحكم: {e}")

def main():
    """الدالة الرئيسية"""
    
    try:
        # تحديث الصفحات الرئيسية
        updated_count = update_main_pages()
        
        # تحديث لوحة التحكم بشكل خاص
        update_dashboard_specifically()
        
        print(f"\n🎉 تم الانتهاء من تطبيق التصميم الموحد!")
        print(f"📊 تم تحديث {updated_count} صفحة رئيسية")
        print(f"✅ تم تحديث لوحة التحكم بتصميم خاص")
        
        print(f"\n📋 ما تم إنجازه:")
        print("1. ✅ إضافة نظام التصميم الموحد")
        print("2. ✅ توحيد العناوين والأيقونات")
        print("3. ✅ إضافة page headers موحدة")
        print("4. ✅ تطبيق CSS الموحد")
        print("5. ✅ تحسين لوحة التحكم")
        
        print(f"\n🔗 الخطوات التالية:")
        print("1. اختبر الصفحات المحدثة")
        print("2. تأكد من عمل التصميم الموحد")
        print("3. راجع الاستجابة للشاشات المختلفة")
        print("4. طبق التحسينات على باقي الصفحات")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == '__main__':
    main()
